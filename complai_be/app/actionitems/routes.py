from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from app.actionitems.service import action_item_service
from datetime import date, datetime

router = APIRouter()

# --- Pydantic Models ---

class ActionableItem(BaseModel):
    """Represents an individual actionable item from a regulatory document."""
    guideline: str
    obligation: str
    action_item: str

class RegulatoryDocument(BaseModel):
    """Base model for regulatory documents with action items."""
    document_number: str
    document_title: str
    actionables: List[Dict[str, Any]]  # Using Dict to match the exact JSON structure
    s3_url: str  # Required field, not optional
    _id: Optional[str] = None

class UserRegulatoryDocument(RegulatoryDocument):
    """Regulatory document with user-specific customizations."""
    username: str

class EditUserDocumentRequest(BaseModel):
    """Request model for editing a user's document."""
    username: str
    updated_actionables: Dict[str, Any]  # Using Dict to preserve the exact JSON structure
    changed_date: Optional[datetime] = Field(default_factory=datetime.now)
    
class DocumentSearchRequest(BaseModel):
    """Request model for document search."""
    query: str = Field(..., alias="doc_name")

class DocumentItemRequest(BaseModel):
    """Request model for retrieving a specific document."""
    doc_id: str
    username: Optional[str] = None

class ActionItemsRequest(BaseModel):
    """Request model for retrieving all action items."""
    username: Optional[str] = None

class RecentDocumentsRequest(BaseModel):
    """Request model for retrieving recent documents."""
    limit: int = Field(default=5, ge=1, le=50)

# --- Routes ---

@router.post("/search")
def search_document(payload: DocumentSearchRequest):
    """Search for documents based on title or number."""
    return action_item_service.search_document(payload.query)

@router.post("/item")
def get_document(payload: DocumentItemRequest):
    """Get a specific document by ID, optionally filtered by username."""
    return action_item_service.get_document(payload.doc_id, username=payload.username)

@router.post("/action-items")
def get_all_action_items(payload: ActionItemsRequest):
    """Get all action items, optionally filtered by username."""
    return action_item_service.get_all_action_items(username=payload.username)

@router.post("/edit-user-item")
def edit_user_action_item(request: EditUserDocumentRequest):
    """Edit or create a user-specific document."""
    # Pass the raw dictionary to ensure the JSON structure is preserved exactly
    return action_item_service.edit_action_item(
        username=request.username,
        updated_actionables=request.updated_actionables,
        changed_date=request.changed_date
    )

@router.post("/download/xlsx")
def download_excel(payload: DocumentItemRequest):
    """Generate and download document action items as Excel."""
    return action_item_service.generate_excel(doc_id=payload.doc_id, username=payload.username)

@router.post("/download/docx")
def download_word(payload: DocumentItemRequest):
    """Generate and download document action items as Word document."""
    return action_item_service.generate_word(doc_id=payload.doc_id, username=payload.username)

@router.post("/recent-docs")
def get_recent_documents(payload: RecentDocumentsRequest):
    """Get the most recent documents sorted by date_of_issue."""
    return action_item_service.get_recent_documents(limit=payload.limit)
