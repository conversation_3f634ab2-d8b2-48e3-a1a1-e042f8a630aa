import fitz  # PyMuPDF
import pytesseract
import numpy as np
import cv2

def find_paragraph_locations(pdf_path, paragraph, zoom=2):
    """
    Finds pixel coordinates of a given paragraph in a PDF.
    
    It first uses the PDF text layer to search for the paragraph and converts the resulting
    rectangles (in PDF points) to pixel coordinates using the provided zoom factor.
    
    If the text layer search fails (e.g. scanned image pages), it converts the page to an image,
    runs OCR via pytesseract, and then attempts a rudimentary search within the OCR results.
    
    Parameters:
        pdf_path (str): Path to the PDF file.
        paragraph (str): The paragraph to search for.
        zoom (int or float): The scaling factor to convert PDF points to pixels.
    
    Returns:
        dict: A dictionary where keys are page numbers (1-indexed) and values are lists of
              fitz.Rect objects (pixel coordinates) indicating where the paragraph was found.
    """
    doc = fitz.open(pdf_path)
    results = {}

    # Iterate over each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_results = []

        # --- Step 1: Use the text layer ---
        # searchFor returns rectangles in PDF coordinate space (points)
        text_rects = page.search_for(paragraph)
        if text_rects:
            # Convert from points to pixels by multiplying with zoom factor.
            converted_rects = [
                fitz.Rect(r.x0 * zoom, r.y0 * zoom, r.x1 * zoom, r.y1 * zoom)
                for r in text_rects
            ]
            page_results.extend(converted_rects)
        
        # --- Step 2: Use OCR if text search did not find the paragraph ---
        # (This step is useful for scanned pages or pages where the text layer is missing.)
        if not text_rects:
            # Render the page to an image with the specified zoom.
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            # Convert pixmap samples to a NumPy array
            img = np.frombuffer(pix.samples, dtype=np.uint8)
            img = img.reshape(pix.height, pix.width, pix.n)
            if pix.n == 4:  # Convert RGBA to RGB if needed.
                img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
            
            # Use pytesseract to get OCR data including word-level bounding boxes.
            ocr_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)
            n_boxes = len(ocr_data['level'])
            
            # Group words by line using (block_num, par_num, line_num) as key.
            lines = {}
            for i in range(n_boxes):
                key = (ocr_data['block_num'][i], ocr_data['par_num'][i], ocr_data['line_num'][i])
                if key not in lines:
                    lines[key] = {"words": [], "boxes": []}
                word_text = ocr_data['text'][i].strip()
                if word_text:  # Skip empty strings
                    lines[key]["words"].append(word_text)
                    left = ocr_data['left'][i]
                    top = ocr_data['top'][i]
                    width = ocr_data['width'][i]
                    height = ocr_data['height'][i]
                    lines[key]["boxes"].append((left, top, left + width, top + height))
            
            # Check each line (or group of consecutive lines) for the paragraph.
            # Here we simply look for a line containing the paragraph text.
            for key, line_info in lines.items():
                line_text = " ".join(line_info["words"])
                if paragraph in line_text:
                    # Compute a union of all word bounding boxes in the line.
                    xs = [box[0] for box in line_info["boxes"]]
                    ys = [box[1] for box in line_info["boxes"]]
                    xes = [box[2] for box in line_info["boxes"]]
                    yes = [box[3] for box in line_info["boxes"]]
                    ocr_rect = fitz.Rect(min(xs), min(ys), max(xes), max(yes))
                    page_results.append(ocr_rect)
        
        if page_results:
            results[page_num + 1] = page_results  # Use 1-indexed page numbers
    
    return results

# Example usage:
if __name__ == "__main__":
    pdf_file = "/Users/<USER>/test_rbi_notification.pdf"
    target_paragraph = "a) To safeguard applications against risks emanating from insecure APIs"
    locations = find_paragraph_locations(pdf_file, target_paragraph, zoom=2)
    
    for page, rects in locations.items():
        print(f"Page {page}:")
        for rect in rects:
            print(f"  Bounding box (pixels): {rect}")
