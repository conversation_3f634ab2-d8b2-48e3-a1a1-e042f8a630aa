"use client" // Add this at the top

import React, { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from "react"
import { UserProfile } from "../types/chat"
import { setCookie, getCookie, eraseCookie } from "@/app/utils/cookies"
interface AuthContextType {
  user: UserProfile | null
  login: (profile: UserProfile) => void
  logout: () => void
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(() => {
    const storedUser = getCookie("user")
    // console.log("Retrieved user from cookie:", storedUser)
    return storedUser ? JSON.parse(storedUser) : null
  })

  const login = (profile: UserProfile) => {
    // console.log("Logging in user:", profile)
    setUser(profile)
  }

  const logout = () => {
    setUser(null)
  }

  useEffect(() => {
    if (user) {
      // console.log("Setting cookie for user:", user)
      setCookie("user", JSON.stringify(user), 7) // Store user in cookie for 7 days
    } else {
      // console.log("Erasing user cookie")
      eraseCookie("user")
    }
  }, [user])

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
