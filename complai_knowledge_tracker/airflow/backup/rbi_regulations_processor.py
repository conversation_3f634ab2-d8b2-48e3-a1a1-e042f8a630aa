"""
Refactored RBI Regulations Processing DAG
Clean, maintainable DAG for processing RBI regulation PDFs with multi-vector Qdrant ingestion
"""

from datetime import datetime, timedelta
import logging
import os
from typing import List, Dict, Any

import boto3
from airflow import DAG
from airflow.decorators import task

# Import our refactored utilities
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "utils"))

from utils.config import config
from utils.document_pipeline import document_processor

logger = logging.getLogger("airflow.task")

# DAG Configuration
DAG_ID = "rbi_regulations_processor"
DESCRIPTION = "Process RBI regulation PDFs with multi-vector Qdrant ingestion"
SCHEDULE_INTERVAL = None  # Manual trigger
MAX_ACTIVE_RUNS = 4
CONCURRENCY = 8

# Default arguments
default_args = {
    "owner": "airflow",
    "start_date": datetime(2025, 1, 1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
}

def create_dag():
    """Create the DAG with proper configuration"""
    
    dag = DAG(
        DAG_ID,
        default_args=default_args,
        description=DESCRIPTION,
        schedule_interval=SCHEDULE_INTERVAL,
        catchup=False,
        max_active_runs=MAX_ACTIVE_RUNS,
        concurrency=CONCURRENCY,
        tags=["rbi", "regulations", "pdf", "qdrant", "processing"]
    )
    
    @task(dag=dag)
    def fetch_s3_files() -> List[str]:
        """Fetch list of PDF files from S3 bucket"""
        try:
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=config.s3.access_key_id,
                aws_secret_access_key=config.s3.secret_access_key
            )
            
            paginator = s3_client.get_paginator("list_objects_v2")
            pages = paginator.paginate(Bucket=config.s3.bucket_name)
            
            pdf_keys = []
            for page in pages:
                for obj in page.get("Contents", []):
                    key = obj.get("Key", "")
                    #     if key.startswith("Circulars/") and key.lower().endswith(".pdf"):
                    pdf_keys.append(key)
            
            logger.info(f"Found {len(pdf_keys)} PDF files in S3 bucket")
            return pdf_keys
            
        except Exception as e:
            logger.error(f"Failed to fetch S3 files: {e}")
            raise
    

    
    @task(dag=dag)
    def process_batch_summary(results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize batch processing results"""
        total_files = len(results)
        successful = sum(1 for r in results if r.get("success", False))
        failed_files = [r for r in results if not r.get("success", False)]

        total_chunks = sum(r.get("chunks_count", 0) for r in results)
        total_obligations = sum(r.get("obligations_count", 0) for r in results)

        # Log failed files
        if failed_files:
            logger.warning(f"Failed to process {len(failed_files)} files:")
            for failed in failed_files:
                logger.warning(f"  - {failed.get('file_key', 'unknown')}: {failed.get('error_message', 'unknown error')}")

        summary = {
            "total_files": total_files,
            "successful": successful,
            "failed": failed_files[0] if len(failed_files) == 1 else len(failed_files),  # Show single failure details or count
            "total_chunks": total_chunks,
            "total_obligations": total_obligations,
            "success_rate": (successful / total_files * 100) if total_files > 0 else 0
        }

        logger.info(f"Batch processing summary: {summary}")
        return summary

    @task(dag=dag)
    def process_batch_of_files(batch: List[str]) -> List[Dict[str, Any]]:
        """Process a batch of files sequentially"""
        results = []
        for file_key in batch:
            try:
                # Download file from S3
                s3_client = boto3.client(
                    "s3",
                    aws_access_key_id=config.s3.access_key_id,
                    aws_secret_access_key=config.s3.secret_access_key
                )

                local_path = f"/tmp/{os.path.basename(file_key)}"
                s3_client.download_file(config.s3.bucket_name, file_key, local_path)
                logger.info(f"Downloaded {file_key} to {local_path}")

                # Prepare source information
                source_info = {
                    "s3_key": file_key,
                    "s3_url": f"https://{config.s3.bucket_name}.s3.amazonaws.com/{file_key}",
                    "source_type": "s3"
                }

                # Determine collection name based on document type
                collection_name = f"{config.qdrant.collection_prefix}_regulations"

                # Process the PDF using our pipeline
                result = document_processor.process_pdf_from_path(
                    pdf_path=local_path,
                    collection_name=collection_name,
                    source_info=source_info
                )

                # Clean up local file
                try:
                    os.unlink(local_path)
                except OSError:
                    pass

                # Prepare result for logging
                result_dict = {
                    "success": result.success,
                    "document_id": result.document_id,
                    "chunks_count": result.chunks_count,
                    "obligations_count": result.obligations_count,
                    "file_key": file_key
                }

                if result.error_message:
                    result_dict["error_message"] = result.error_message

                if result.success:
                    logger.info(f"Successfully processed {file_key}: {result.document_id}")
                else:
                    logger.error(f"Failed to process {file_key}: {result.error_message}")

                results.append(result_dict)

            except Exception as e:
                logger.error(f"Unexpected error processing {file_key}: {e}")
                results.append({
                    "success": False,
                    "document_id": "unknown",
                    "file_key": file_key,
                    "error_message": str(e),
                    "chunks_count": 0,
                    "obligations_count": 0
                })

        return results
    
    # Define task dependencies
    files = fetch_s3_files()
    
    # Process files in batches to avoid overwhelming the system
    batch_size = config.processing.batch_size
    
    # Split files into batches
    @task(dag=dag)
    def create_batches(file_list: List[str]) -> List[List[str]]:
        """Split file list into processing batches"""
        batches = []
        for i in range(0, len(file_list), batch_size):
            batch = file_list[i:i + batch_size]
            batches.append(batch)
        
        logger.info(f"Created {len(batches)} batches of max {batch_size} files each")
        return batches
    
    # Create batches and process them
    batches = create_batches(files)

    # Process batches using dynamic task mapping
    # This will create one task per batch, processing files sequentially within each batch
    batch_results = process_batch_of_files.expand(batch=batches)

    # Flatten results from all batches for summary
    @task(dag=dag)
    def flatten_batch_results(batch_results_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Flatten results from multiple batches into a single list"""
        flattened = []
        for batch_result in batch_results_list:
            flattened.extend(batch_result)
        return flattened

    all_results = flatten_batch_results(batch_results)
    final_summary = process_batch_summary(all_results)
    
    return dag

# Create the DAG
dag = create_dag()
