// MainMenu.tsx
"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ChatIcon from '@/components/assets/icons/chat-icon.svg';
import DocIcon from '@/components/assets/icons/doc-icon.svg';
import UserIcon from '@/components/assets/icons/user-icon.svg';
import { useAuth } from '@/contexts/AuthContext';
import FLogo from "@/components/assets/icons/federalbanklogo.jpg"

export function MainMenu() {
  const [showLogout, setShowLogout] = useState(false);
  const logoutRef = useRef<HTMLDivElement>(null);
  const userIconRef = useRef<HTMLDivElement>(null);
  const { logout } = useAuth();
  const router = useRouter();

  // Close the logout menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (logoutRef.current && 
          userIconRef.current && 
          !logoutRef.current.contains(event.target as Node) &&
          !userIconRef.current.contains(event.target as Node)) {
        setShowLogout(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleUserClick = () => {
    setShowLogout(!showLogout);
  };

  const handleLogout = () => {
    logout();
    setShowLogout(false);
    router.push('/signin');
  };

  return (
    <div className="w-[88px] h-full bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] border-r border-[#D1C3BE] flex flex-col justify-between items-center py-9">
      <div className="flex flex-col items-center gap-6">
        {/* Logo/Avatar */}
        <img className="w-12 h-12 rounded-lg" src={FLogo.src || FLogo} alt="Logo" />
        
        {/* Navigation items */}
        <div className="w-[72px] flex flex-col items-start gap-3">
          {/* Active Chat item */}
          <div className="w-[72px] px-5 py-3 rounded-xl bg-white bg-opacity-40 flex flex-col justify-center items-center gap-0">
            <img src={ChatIcon.src || ChatIcon} alt="Chat" className="w-30 h-10 text-[#7C6F5F]" />
            <div className="text-[#7C6F5F] text-base font-semibold" style={{ fontFamily: 'Figtree' }}>Chats</div>
          </div>
          
          {/* Inactive Docs item */}
          <div className="w-[72px] px-5 py-3 rounded-xl flex flex-col justify-center items-center gap-0">
            <img src={DocIcon.src || DocIcon} alt="Docs" className="w-30 h-14 text-[#7C6F5F]" />
            <div className="text-[#7C6F5F] text-base font-semibold" style={{ fontFamily: 'Figtree' }}>Docs</div>
          </div>
        </div>
      </div>
      
      {/* User profile icon with logout popup */}
      <div className="relative">
        <div 
          ref={userIconRef}
          className="w-[72px] px-5 py-3 rounded-xl flex flex-col justify-center items-center gap-2 cursor-pointer hover:bg-white hover:bg-opacity-20 transition-colors duration-200"
          onClick={handleUserClick}
        >
          <img src={UserIcon.src || UserIcon} alt="User" className="w-30 h-10 text-[#7C6F5F]" />
        </div>
        
        {/* Logout popup */}
        {showLogout && (
          <div 
            ref={logoutRef}
            className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 bg-white rounded-xl shadow-md overflow-hidden transition-all duration-200 w-24"
            style={{ fontFamily: 'Figtree' }}
          >
            <div 
              className="px-6 py-3 text-sm text-[#7C6F5F] font-medium hover:bg-[#F1E9E6] cursor-pointer transition-colors duration-150 text-center"
              onClick={handleLogout}
            >
              Logout
            </div>
          </div>
        )}
      </div>
    </div>
  );
}