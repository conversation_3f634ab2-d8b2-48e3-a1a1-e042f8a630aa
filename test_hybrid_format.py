#!/usr/bin/env python3
"""
Test the hybrid format detection and SPLADE vector addition
"""

from qdrant_client import QdrantClient, models
from qdrant_client.http.models import PointStruct

def test_hybrid_format():
    """Test hybrid format detection and SPLADE vector addition"""
    
    client = QdrantClient(url="http://localhost:6333")
    collection_name = "rbi_master_direction"
    
    try:
        # Check collection format
        info = client.get_collection(collection_name)
        vectors = info.config.params.vectors
        sparse_vectors = getattr(info.config.params, 'sparse_vectors', None)
        
        print(f"Collection: {collection_name}")
        print(f"Dense vectors type: {type(vectors)}")
        print(f"Sparse vectors: {list(sparse_vectors.keys()) if sparse_vectors else 'None'}")
        
        # Determine format
        if isinstance(vectors, dict):
            format_type = "named"
        elif isinstance(vectors, models.VectorParams):
            if sparse_vectors and len(sparse_vectors) > 0:
                format_type = "hybrid"
            else:
                format_type = "single"
        else:
            format_type = "unknown"
            
        print(f"Detected format: {format_type}")
        
        # Get a sample point to see current structure
        scroll_result = client.scroll(
            collection_name=collection_name,
            limit=1,
            with_vectors=True
        )
        
        if scroll_result[0]:
            point = scroll_result[0][0]
            print(f"\nSample point structure:")
            print(f"Point ID: {point.id}")
            if hasattr(point, 'vector') and isinstance(point.vector, dict):
                print(f"Vector keys: {list(point.vector.keys())}")
                for key, vector in point.vector.items():
                    if hasattr(vector, 'indices'):
                        print(f"  - '{key}': SparseVector with {len(vector.indices)} indices")
                    else:
                        print(f"  - '{key}': Dense vector with {len(vector)} dimensions")
            
            # Check if SPLADE vector exists
            has_splade = False
            if hasattr(point, 'vector') and isinstance(point.vector, dict):
                has_splade = 'fast-sparse-bm25-splade' in point.vector
            
            print(f"Has SPLADE vector: {has_splade}")
            
            if not has_splade:
                print("\n✅ This confirms SPLADE vectors are missing and need to be added")
            else:
                print("\n✅ SPLADE vectors already exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_hybrid_format()
