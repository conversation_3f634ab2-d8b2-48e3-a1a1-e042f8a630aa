//rightsidebar.tsx
import { NotificationSidebar } from "./NotificationSidebar"
import { ResourcesSidebar } from "./ResourcesSidebar"
import type { RegulationInfo, ReferenceItem } from "@/types/types"

interface RightSidebarProps {
  activeView: "home" | "chat";
  selectedDocCode: string | null;
  showDetailView: boolean
  setShowDetailView: (showDetailView:boolean) => void;
  currentRegulation: RegulationInfo | null
  onClose: () => void
  references: ReferenceItem[]
  selectedSpecificRef?: ReferenceItem | null; // Add this line
  onCollapseChat?: () => void
}

export function RightSidebar({ 
  activeView,
  selectedDocCode,
  showDetailView, 
  setShowDetailView,
  currentRegulation, 
  onClose, 
  references,
  selectedSpecificRef, // Add this line
  onCollapseChat
}: RightSidebarProps) {
  // console.log("showDetailView at rightsidebar:",showDetailView)
  // If no regulation is selected, show the NotificationSidebar (home view)
  if (activeView === "home") {
    return <NotificationSidebar
      onCollapseChat={onCollapseChat}
      onClose={onClose}
    />
  }
  
  // Otherwise, show the ResourcesSidebar (chat view)
  return (
  <ResourcesSidebar 
    showDetailView={showDetailView}
    setShowDetailView={setShowDetailView}
    selectedDocCode={selectedDocCode}
    currentRegulation={currentRegulation}
    onClose={onClose}
    references={references}
    selectedSpecificRef={selectedSpecificRef} // Add this line
    onCollapseChat={onCollapseChat}
  />
)
}