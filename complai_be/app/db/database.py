import pymongo
import requests
import urllib.parse
import os
from pathlib import Path
import sys

# Add the project root to sys.path to allow importing from parent directory
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import settings from config.py
from config import settings

# MongoDB URI (use the URI you provided in main.py)
DATABASE_NAME = "complai"
PEM_PATH = "/home/<USER>/global-bundle.pem"


username = "selkeaadmin"
password = "selkea123"
pem_path = "/home/<USER>/global-bundle.pem"
escaped_username = urllib.parse.quote_plus(username)
escaped_password = urllib.parse.quote_plus(password)
escaped_pem_path = urllib.parse.quote_plus(pem_path)

def download_tls_ca_file():
    # Only download if not in local development environment
    if settings.ENVIRONMENT != "local":
        try:
            response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
            response.raise_for_status()
            with open(PEM_PATH, "wb") as file:
                file.write(response.content)
            print(f"Downloaded TLS certificate to {PEM_PATH}")
        except requests.RequestException as e:
            print(f"Failed to download TLS certificate: {e}")
            raise
    else:
        print(f"Skipping TLS certificate download in {settings.ENVIRONMENT} environment")

# Call download function based on environment
# download_tls_ca_file()  # Keeping this commented as it was in the original

client: pymongo.MongoClient = None
# Dependency to get the MongoDB database client
def get_db(database_name=DATABASE_NAME):
    global client
    if client is None:
        # Use different connection based on environment
        if settings.ENVIRONMENT == "development" or settings.ENVIRONMENT == "local":
            # Local development connection
            client = pymongo.MongoClient(
                "mongodb://localhost:27017/",
                tls=False
            )
        else:
            # Production or staging connection
            # Download TLS certificate if needed
            download_tls_ca_file()
            client = pymongo.MongoClient(
                settings.MONGODB_URI,
                tls=True,
                tlsCAFile=PEM_PATH,
                replicaSet="rs0",
                readPreference="secondaryPreferred",
                retryWrites=False
            )
    return client[database_name]

def get_action_db(database_name=DATABASE_NAME):
    global client
    if client is None:
        # Use different connection based on environment
        if settings.ENVIRONMENT == "local":
            # Local development connection
            client = pymongo.MongoClient(
                "mongodb://localhost:27017/",
                tls=False
            )
        else:
            # Production or staging connection
            # Download TLS certificate if needed
            download_tls_ca_file()
            client = pymongo.MongoClient(
                settings.MONGODB_URI,
                tls=True,
                tlsCAFile=PEM_PATH,
                replicaSet="rs0",
                readPreference="secondaryPreferred",
                retryWrites=False
            )
    return client[database_name]

# Dependency to ensure a clean connection when shutdown
def close_db():
    if client:
        client.close()