#!/usr/bin/env python3
"""
Test script to verify Qdrant point ID generation
"""

import sys
from pathlib import Path
import uuid
import re

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))

def test_point_id_generation():
    """Test point ID generation for Qdrant compatibility"""
    print("🧪 Testing Point ID Generation...")
    
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        # Test cases with problematic IDs
        test_cases = [
            "229e71a4-c9b5-4813-ade7-b43497d66108_chunk9",
            "document_123_chunk_5",
            "RBI-2025-001_chunk_10",
            "test-doc-with-hyphens_chunk_1",
            "simple_doc_chunk_0"
        ]
        
        print("Testing point ID generation:")
        for test_id in test_cases:
            valid_id = generate_valid_point_id(test_id)
            
            # Check if it's a valid UUID
            is_uuid = False
            try:
                uuid.UUID(valid_id)
                is_uuid = True
            except ValueError:
                pass
            
            # Check if it's a valid integer
            is_int = False
            try:
                int(valid_id)
                is_int = True
            except ValueError:
                pass
            
            status = "✅" if (is_uuid or is_int) else "❌"
            id_type = "UUID" if is_uuid else ("INT" if is_int else "INVALID")
            
            print(f"  {status} {test_id[:30]:<30} -> {valid_id} ({id_type})")
        
        return True
        
    except Exception as e:
        print(f"❌ Point ID generation test failed: {e}")
        return False

def test_uuid_validation():
    """Test UUID validation patterns"""
    print("\n🧪 Testing UUID Validation...")
    
    # Valid UUID pattern
    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)
    
    test_uuids = [
        str(uuid.uuid4()),
        str(uuid.uuid5(uuid.NAMESPACE_DNS, "test")),
        "229e71a4-c9b5-4813-ade7-b43497d66108",
        "invalid-uuid-format",
        "229e71a4-c9b5-4813-ade7-b43497d66108_chunk9"  # This was the problematic one
    ]
    
    print("Testing UUID validation:")
    for test_uuid in test_uuids:
        is_valid = bool(uuid_pattern.match(test_uuid))
        status = "✅" if is_valid else "❌"
        print(f"  {status} {test_uuid}")
    
    return True

def test_qdrant_point_id_compatibility():
    """Test Qdrant point ID compatibility"""
    print("\n🧪 Testing Qdrant Point ID Compatibility...")
    
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        # Generate IDs for typical use cases
        test_scenarios = [
            ("Article ID", "4000b9fe-3973-4fa9-b6c1-bb0a388d9734"),
            ("Chunk ID", "229e71a4-c9b5-4813-ade7-b43497d66108_chunk9"),
            ("Document ID", "RBI-2025-001"),
            ("Complex ID", "master_direction_2025_01_chunk_15"),
            ("Simple ID", "doc123")
        ]
        
        print("Testing Qdrant compatibility:")
        for scenario, test_id in test_scenarios:
            valid_id = generate_valid_point_id(test_id)
            
            # Test if it would be accepted by Qdrant
            # Qdrant accepts: unsigned integers or UUIDs
            is_valid = False
            id_type = "UNKNOWN"
            
            # Check if it's a valid UUID
            try:
                uuid.UUID(valid_id)
                is_valid = True
                id_type = "UUID"
            except ValueError:
                # Check if it's a valid unsigned integer
                try:
                    int_val = int(valid_id)
                    if int_val >= 0:
                        is_valid = True
                        id_type = "UINT"
                except ValueError:
                    pass
            
            status = "✅" if is_valid else "❌"
            print(f"  {status} {scenario:<20} {test_id[:30]:<30} -> {valid_id[:36]} ({id_type})")
        
        return True
        
    except Exception as e:
        print(f"❌ Qdrant compatibility test failed: {e}")
        return False

def test_id_consistency():
    """Test that the same input always generates the same ID"""
    print("\n🧪 Testing ID Generation Consistency...")
    
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        test_input = "229e71a4-c9b5-4813-ade7-b43497d66108_chunk9"
        
        # Generate the same ID multiple times
        ids = [generate_valid_point_id(test_input) for _ in range(5)]
        
        # Check if all IDs are the same
        all_same = all(id == ids[0] for id in ids)
        
        status = "✅" if all_same else "❌"
        print(f"  {status} Consistency test: {all_same}")
        print(f"      Input: {test_input}")
        print(f"      Output: {ids[0]}")
        
        if not all_same:
            print(f"      All outputs: {ids}")
        
        return all_same
        
    except Exception as e:
        print(f"❌ ID consistency test failed: {e}")
        return False

def test_collision_resistance():
    """Test that different inputs generate different IDs"""
    print("\n🧪 Testing Collision Resistance...")
    
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        # Generate IDs for similar but different inputs
        test_inputs = [
            "doc_chunk_1",
            "doc_chunk_2", 
            "doc_chunk_10",
            "doc_chunk_11",
            "different_doc_chunk_1"
        ]
        
        generated_ids = [generate_valid_point_id(inp) for inp in test_inputs]
        unique_ids = set(generated_ids)
        
        no_collisions = len(unique_ids) == len(generated_ids)
        status = "✅" if no_collisions else "❌"
        
        print(f"  {status} Collision resistance: {no_collisions}")
        print(f"      Inputs: {len(test_inputs)}, Unique IDs: {len(unique_ids)}")
        
        if not no_collisions:
            print("      Collisions detected:")
            for i, id1 in enumerate(generated_ids):
                for j, id2 in enumerate(generated_ids[i+1:], i+1):
                    if id1 == id2:
                        print(f"        {test_inputs[i]} == {test_inputs[j]} -> {id1}")
        
        return no_collisions
        
    except Exception as e:
        print(f"❌ Collision resistance test failed: {e}")
        return False

def main():
    """Run all point ID tests"""
    print("🚀 Starting Point ID Generation Tests\n")
    
    tests = [
        ("Point ID Generation", test_point_id_generation),
        ("UUID Validation", test_uuid_validation),
        ("Qdrant Compatibility", test_qdrant_point_id_compatibility),
        ("ID Consistency", test_id_consistency),
        ("Collision Resistance", test_collision_resistance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 POINT ID TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All point ID tests passed!")
        print("\n💡 Point ID Generation Features:")
        print("   ✅ Generates valid Qdrant point IDs (UUIDs or unsigned integers)")
        print("   ✅ Consistent output for same input")
        print("   ✅ Collision resistant for different inputs")
        print("   ✅ Handles problematic characters and formats")
    else:
        print("⚠️ Some tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
