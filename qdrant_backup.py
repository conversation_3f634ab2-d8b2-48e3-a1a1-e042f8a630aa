import os, requests, boto3
from qdrant_client import Qdrant<PERSON><PERSON>
from qdrant_client.http.exceptions import ResponseHandlingException

# CONFIG
QDRANT_URL = "http://15.207.202.105:6333"
AWS_KEY = "********************"
AWS_SECRET = "2FfZsDlPIYjAz8EEMlxOrX54hYMN90qB1Kk0pDJ+"

# aws_access_key_id = ""
# aws_secret_access_key = "2FfZsDlPIYjAz8EEMlxOrX54hYMN90qB1Kk0pDJ+"
 
S3_BUCKET = "awstestdump"
SNAPSHOT_DIR = "snapshots"

os.makedirs(SNAPSHOT_DIR, exist_ok=True)
client = QdrantClient(QDRANT_URL)
s3 = boto3.client("s3", aws_access_key_id=AWS_KEY, aws_secret_access_key=AWS_SECRET)

for col in client.get_collections().collections:
    name = col.name
    print(f"\n➡️ Collection: {name}")

    try:
        snap = client.create_snapshot(name, wait=True)
        snap_name = os.path.basename(snap.name)

        # delete all older colelctions
        snaps = client.list_snapshots(name)  # returns a list :contentReference[oaicite:1]{index=1}
        for s in snaps:
            if s.name != snap_name:
                client.delete_snapshot(name, s.name)

        snap_url =f"{QDRANT_URL}/collections/{name}/snapshots/{snap_name}"
    except Exception as e:
        print("An error occurred while creating a snapshot, {e}", e)
        snaps = client.list_snapshots(name)  # returns a list :contentReference[oaicite:1]{index=1}
        if not snaps:
            print("❌ No snapshots available, skipping.")
            continue
        snap_name = snaps[-1].name
        snap_url = f"{QDRANT_URL}/collections/{name}/snapshots/{snap_name}"
        print(f"⚠️ Using fallback snapshot: {snap_name}")

    local = os.path.join(SNAPSHOT_DIR, f"{name}-{snap_name}")
    print(f"📥 Downloading from: {snap_url}")

    try:
        resp = requests.get(snap_url, timeout=120)
        resp.raise_for_status()
        with open(local, "wb") as f:
            f.write(resp.content)
        print(f"✅ Saved locally to: {local}")
    except Exception as e:
        print(f"❌ Download failed: {e}")
        continue

    s3_key = f"backups/{name}/{snap_name}"
    try:
        s3.upload_file(local, S3_BUCKET, s3_key)
        print(f"☁️ Uploaded to S3://{S3_BUCKET}/{s3_key}")
    except Exception as e:
        print(f"❌ Upload failed: {e}")
    finally:
        if os.path.exists(local):
            os.remove(local)
