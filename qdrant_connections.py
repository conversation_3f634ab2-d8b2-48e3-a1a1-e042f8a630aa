
collection = ["rbi_master_direction", "rbi_master_circular", "rbi_circular", "rbi_notification", "rbi_press_release", "rbi_speech", "rbi_publication", "rbi_other"]

import logging
from qdrant_client import QdrantClient
from qdrant_client.http import models
 
# --- Configuration ---
QDRANT_HOST = "*************"
QDRANT_PORT = 6333
COLLECTION_NAME = "rbi_master_direction"
BATCH_SIZE = 100
LOG_FILE = "s3_urls.log"
 
# --- Set up logger ---
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    filemode="w"
)
 
# --- Initialize Qdrant client ---
client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
 
# --- Scroll through all points and log s3_urls ---
scroll_offset = None
total_logged = 0
 
url = []
for col in collection:
    while True:
        points_batch, scroll_offset = client.scroll(
            collection_name=col,
            limit=BATCH_SIZE,
            with_payload=True,
            offset=scroll_offset
        )
    
        if not points_batch:
            break
    
        for point in points_batch:
            point_id = point.id
            metadata = point.payload.get("metadata", {})
            s3_url = metadata.get("s3_url")
    
            if s3_url:
                logging.info(f"ID: {point_id} - s3_url: {s3_url}")
                total_logged += 1
    
        if scroll_offset is None:
            break
 
print(f"✅ Logged {total_logged} s3_url(s) to '{LOG_FILE}'")
 