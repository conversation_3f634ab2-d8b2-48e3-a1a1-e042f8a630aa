import unittest
import sys
import os

# Add the parent directory to the path to import the module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from airflow.dags.utils.date_utils import normalize_date

class TestDateNormalization(unittest.TestCase):
    
    def test_indian_date_format(self):
        """Test DD/MM/YYYY format (Indian standard)"""
        self.assertEqual(normalize_date("15/08/2023"), "2023-08-15")
        self.assertEqual(normalize_date("01/01/2023"), "2023-01-01")
        self.assertEqual(normalize_date("31/12/2022"), "2022-12-31")
    
    def test_iso_date_format(self):
        """Test ISO format (YYYY-MM-DD) - should remain unchanged"""
        self.assertEqual(normalize_date("2023-08-15"), "2023-08-15")
        self.assertEqual(normalize_date("2022-12-31"), "2022-12-31")
    
    def test_us_date_format(self):
        """Test MM/DD/YYYY format (US standard)"""
        # Note: This might be ambiguous with DD/MM/YYYY for some dates
        self.assertEqual(normalize_date("12/31/2022"), "2022-12-31")
        self.assertEqual(normalize_date("08/15/2023"), "2023-08-15")
    
    def test_date_with_dashes(self):
        """Test DD-MM-YYYY format"""
        self.assertEqual(normalize_date("15-08-2023"), "2023-08-15")
        self.assertEqual(normalize_date("31-12-2022"), "2022-12-31")
    
    def test_date_with_dots(self):
        """Test DD.MM.YYYY format"""
        self.assertEqual(normalize_date("15.08.2023"), "2023-08-15")
        self.assertEqual(normalize_date("31.12.2022"), "2022-12-31")
    
    def test_date_with_full_month_name(self):
        """Test dates with full month names"""
        self.assertEqual(normalize_date("15 August 2023"), "2023-08-15")
        self.assertEqual(normalize_date("August 15, 2023"), "2023-08-15")
        self.assertEqual(normalize_date("31 December 2022"), "2022-12-31")
    
    def test_date_with_abbreviated_month_name(self):
        """Test dates with abbreviated month names"""
        self.assertEqual(normalize_date("15 Aug 2023"), "2023-08-15")
        self.assertEqual(normalize_date("Aug 15, 2023"), "2023-08-15")
        self.assertEqual(normalize_date("31 Dec 2022"), "2022-12-31")
    
    def test_invalid_dates(self):
        """Test invalid date formats"""
        self.assertIsNone(normalize_date(""))
        self.assertIsNone(normalize_date(None))
        self.assertIsNone(normalize_date("not a date"))
        self.assertIsNone(normalize_date("32/01/2023"))  # Invalid day
        self.assertIsNone(normalize_date("15/13/2023"))  # Invalid month
    
    def test_edge_cases(self):
        """Test edge cases like whitespace and case sensitivity"""
        self.assertEqual(normalize_date("  15/08/2023  "), "2023-08-15")  # Extra whitespace
        self.assertEqual(normalize_date("15 AUGUST 2023"), "2023-08-15")  # Uppercase month

if __name__ == "__main__":
    unittest.main()