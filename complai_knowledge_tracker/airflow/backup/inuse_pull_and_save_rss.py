from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
import logging
from dateutil import parser

from utils.doc_db import download_tls_ca_file

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



def parse_date(date_string):
    try:
        # First try the expected format
        return datetime.strptime(date_string, "%a, %d %b %Y %H:%M:%S")
    except ValueError:
        try:
            # If that fails, use dateutil parser which is more flexible
            return parser.parse(date_string)
        except Exception as e:
            logger.error(f"Failed to parse date string '{date_string}': {e}")
            return None

# Function to update MongoDB documents
def update_mongo_connection():
    download_tls_ca_file()
    client = None
    try:
        client = MongoClient(
            "mongodb://selkeaadmin:<EMAIL>:27017/",
            tls=True,
            tlsCAFile="/tmp/global-bundle.pem",
            replicaSet="rs0",
            readPreference="secondaryPreferred",
            retryWrites=False
        )
        db = client["rss_feed_db"]
        collection_name = "articles"

        if collection_name not in db.list_collection_names():
            collection = db.create_collection(collection_name)
        else:
            collection = db[collection_name]

        # Update all document fields that need datetime conversion
        query = {
            "$or": [
                {"published_date": {"$type": "string"}},
                {"ingestion_timestamp": {"$type": "string"}},
                {"published_date": {"$type": "object", "$exists": True}},
                {"ingestion_timestamp": {"$type": "object", "$exists": True}}
            ]
        }

        for doc in collection.find(query):
            updates = {}
            
            # Handle published_date
            if "published_date" in doc:
                if isinstance(doc["published_date"], str):
                    parsed_date = parse_date(doc["published_date"])
                    if parsed_date:
                        updates["published_date"] = parsed_date
                elif isinstance(doc["published_date"], dict) and "$date" in doc["published_date"]:
                    try:
                        updates["published_date"] = datetime.fromisoformat(doc["published_date"]["$date"].replace("Z", "+00:00"))
                    except Exception as e:
                        logger.error(f"Failed to parse published_date object: {e}")

            # Handle ingestion_timestamp
            if "ingestion_timestamp" in doc:
                if isinstance(doc["ingestion_timestamp"], str):
                    parsed_timestamp = parse_date(doc["ingestion_timestamp"])
                    if parsed_timestamp:
                        updates["ingestion_timestamp"] = parsed_timestamp
                elif isinstance(doc["ingestion_timestamp"], dict) and "$date" in doc["ingestion_timestamp"]:
                    try:
                        updates["ingestion_timestamp"] = datetime.fromisoformat(doc["ingestion_timestamp"]["$date"].replace("Z", "+00:00"))
                    except Exception as e:
                        logger.error(f"Failed to parse ingestion_timestamp object: {e}")

            # Update document if we have any changes
            if updates:
                try:
                    result = collection.update_one(
                        {"_id": doc["_id"]},
                        {"$set": updates}
                    )
                    if result.modified_count > 0:
                        logger.info(f"Successfully updated document {doc['_id']}")
                    else:
                        logger.warning(f"No changes made to document {doc['_id']}")
                except Exception as e:
                    logger.error(f"Failed to update document {doc['_id']}: {e}")

        logger.info("MongoDB update operation completed successfully")
    except Exception as e:
        logger.error(f"MongoDB operation failed: {e}")
        raise
    finally:
        if client:
            client.close()
# Define default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 1, 1, 0, 30),
    'email_on_failure': True,
    'email_on_retry': True,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'update_mongodb_dates_dag',
    default_args=default_args,
    description='DAG to update MongoDB document dates to proper format',
    schedule_interval=timedelta(hours=1),
    catchup=False
)

# Define the task
update_mongo_task = PythonOperator(
    task_id='update_mongodb_dates',
    python_callable=update_mongo_connection,
    dag=dag,
)
