{"cells": [{"cell_type": "code", "execution_count": 3, "id": "c9cbac6f", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "id": "2d8c7665", "metadata": {}, "outputs": [], "source": ["csv1 = \"/Users/<USER>/Downloads/all_documents_summary(in).csv\""]}, {"cell_type": "code", "execution_count": 5, "id": "50e32b17", "metadata": {}, "outputs": [], "source": ["csv2 = \"./csv_missing_qdrant.csv\""]}, {"cell_type": "code", "execution_count": 10, "id": "beda09d4", "metadata": {}, "outputs": [], "source": ["removed = pd.read_csv(csv1)\n", "df2 = pd.read_csv(csv2)"]}, {"cell_type": "code", "execution_count": 16, "id": "3599faef", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Folder', 'Missing File URL', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4'], dtype='object')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.columns"]}, {"cell_type": "code", "execution_count": 28, "id": "9ce59359", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9249 9073\n"]}], "source": ["print(len(df2), len(df3))"]}, {"cell_type": "code", "execution_count": 29, "id": "89cddd4d", "metadata": {}, "outputs": [{"data": {"text/plain": ["557"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(removed)"]}, {"cell_type": "code", "execution_count": 19, "id": "91c67182", "metadata": {}, "outputs": [], "source": ["df3 = df2[~df2[\"Missing File URL\"].isin(removed.s3_url.to_list())]"]}, {"cell_type": "code", "execution_count": 30, "id": "83ad3b1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["master_circulars\n", "391\n", "master_directions\n", "88\n", "notifications\n", "8594\n"]}], "source": ["for grp, grp_df in df3.groupby(\"Folder\"):\n", "    print(grp)\n", "    # print(grp_df['Missing File URL'].unique())\n", "    print(len(grp_df['Missing File URL'].unique()))\n", "    # write to a csv with \"\" on both side of the string in \"Missing File URL\"\n", "    grp_df.to_csv(f\"{grp}.csv\", index=False, quoting=1)\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "cc0582fe", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "selkea_be", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}