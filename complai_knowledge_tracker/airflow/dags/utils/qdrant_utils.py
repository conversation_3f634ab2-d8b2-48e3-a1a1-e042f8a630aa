"""
Enhanced Qdrant utilities with multi-vector support (dense, sparse, splade)
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Union
from qdrant_client import QdrantClient, models
from qdrant_client.http.models import PointStruct, VectorParams, Distance
from langchain_qdrant import QdrantVectorStore, RetrievalMode, FastEmbedSparse

# Configure HTTP backend for Hugging Face to bypass SSL issues
try:
    import requests
    from huggingface_hub import configure_http_backend

    def create_session_factory():
        def session_factory():
            session = requests.Session()
            session.verify = False  # Bypass SSL verification
            return session
        return session_factory

    # Configure the HTTP backend
    configure_http_backend(backend_factory=create_session_factory())
    logger = logging.getLogger(__name__)
    logger.info("Configured Hugging Face HTTP backend to bypass SSL verification")
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Could not configure Hugging Face HTTP backend - SSL issues may occur")

# Optional imports for SPLADE functionality
try:
    import torch
    from transformers import AutoTokenizer, AutoModelForMaskedLM
    SPLADE_AVAILABLE = True
except ImportError:
    SPLADE_AVAILABLE = False

from .config import config
from .openai_utils import en_embeddings, strip_data_attributes

logger = logging.getLogger(__name__)

def convert_to_qdrant_sparse_vector(sparse_vector) -> models.SparseVector:
    """
    Convert various sparse vector formats to Qdrant's SparseVector format
    """
    if isinstance(sparse_vector, models.SparseVector):
        # Already in correct format
        return sparse_vector
    elif hasattr(sparse_vector, 'indices') and hasattr(sparse_vector, 'values'):
        # langchain_qdrant SparseVector object
        return models.SparseVector(
            indices=sparse_vector.indices,
            values=sparse_vector.values
        )
    elif isinstance(sparse_vector, dict) and 'indices' in sparse_vector and 'values' in sparse_vector:
        # Dictionary format (e.g., from SPLADE)
        return models.SparseVector(
            indices=sparse_vector['indices'],
            values=sparse_vector['values']
        )
    elif isinstance(sparse_vector, list):
        # List of floats - convert to dense-like sparse vector
        indices = [i for i, val in enumerate(sparse_vector) if val != 0]
        values = [val for val in sparse_vector if val != 0]
        return models.SparseVector(indices=indices, values=values)
    else:
        logger.warning(f"Unknown sparse vector format: {type(sparse_vector)}")
        return models.SparseVector(indices=[], values=[])

def generate_valid_point_id(identifier: str) -> str:
    """
    Generate a valid Qdrant point ID from a string identifier
    Qdrant accepts either unsigned integers or UUIDs
    """
    try:
        # Try to generate a UUID from the identifier for consistency
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, identifier))
    except Exception:
        # Fallback to hash-based integer ID
        return str(abs(hash(identifier)) % (2**31))

class SpladeEncoder:
    """SPLADE sparse vector encoder"""

    def __init__(self):
        if not SPLADE_AVAILABLE:
            logger.warning("SPLADE dependencies not available. SPLADE encoding will be disabled.")
            self.model_name = None
            self.tokenizer = None
            self.model = None
            return

        self.model_name = config.qdrant.splade_model
        self.tokenizer = None
        self.model = None
        self._load_model()

    def _load_model(self):
        """Lazy load the SPLADE model"""
        if not SPLADE_AVAILABLE:
            return

        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForMaskedLM.from_pretrained(self.model_name)
            logger.info(f"Loaded SPLADE model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to load SPLADE model: {e}")
            raise

    def encode(self, text: str) -> models.SparseVector:
        """Generate SPLADE sparse vector for text"""
        if not SPLADE_AVAILABLE:
            logger.warning("SPLADE encoding requested but dependencies not available")
            return models.SparseVector(indices=[], values=[])

        if not self.model or not self.tokenizer:
            self._load_model()

        with torch.no_grad():
            tokens = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                max_length=512
            )
            logits = self.model(**tokens).logits[0]
            max_vals, _ = torch.max(torch.relu(logits), dim=0)
            nonzero = max_vals > 0
            indices = nonzero.nonzero(as_tuple=True)[0]
            values = max_vals[indices]

            return models.SparseVector(
                indices=indices.cpu().tolist(),
                values=values.cpu().tolist()
            )
    
    def encode_to_list(self, text: str) -> models.SparseVector:
        """Generate SPLADE sparse vector for text - backward compatibility method"""
        return self.encode(text)

# Global SPLADE encoder instance
splade_encoder = SpladeEncoder()

def splade_sparse(text: str) -> models.SparseVector:
    """Generate SPLADE sparse vector - backward compatibility function"""
    return splade_encoder.encode(text)

class QdrantManager:
    """Enhanced Qdrant client manager with multi-vector support"""

    def __init__(self):
        self.config = config.qdrant
        logger.info(f"Initializing QdrantManager with URL: {self.config.full_url}")

        try:
            self.client = QdrantClient(url=self.config.full_url)
            # Test the connection immediately
            self.client.get_collections()
            logger.info(f"✅ QdrantClient initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize QdrantClient with URL {self.config.full_url}: {e}")
            # Try alternative initialization for Docker environments
            try:
                if self.config.host == "host.docker.internal":
                    logger.info("Attempting alternative connection method for Docker environment")
                    self.client = QdrantClient(host=self.config.host, port=self.config.port)
                    self.client.get_collections()
                    logger.info(f"✅ QdrantClient connected using host/port method")
                else:
                    raise e
            except Exception as e2:
                logger.error(f"❌ Alternative connection method also failed: {e2}")
                raise e2

        try:
            self.sparse_embedder = FastEmbedSparse(
                model_name=self.config.sparse_model,
            )
            logger.info(f"✅ Sparse embedder initialized with model: {self.config.sparse_model}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize sparse embedder: {e}")
            raise e

    def test_conn(self):
        """Test connection to Qdrant"""
        try:
            logger.info(f"Testing Qdrant connection on {self.config.full_url}")
            logger.info(f"Qdrant URL: {self.config.full_url}")
            self.client.get_collections()
            logger.info("Qdrant connection test passed")
            return True
        except Exception as e:
            logger.error(f"Qdrant connection test failed: {e}")
            return False

    def ensure_collection(self, collection_name: str) -> bool:
        """Ensure collection exists with proper vector configuration"""
        try:
            if self.client.collection_exists(collection_name):
                # Check if existing collection has the right format
                try:
                    collection_info = self.client.get_collection(collection_name)
                    vectors_config = collection_info.config.params.vectors

                    # Check if it has named vectors (new format)
                    if isinstance(vectors_config, dict) and "dense" in vectors_config:
                        logger.info(f"Collection {collection_name} already exists with named vectors")
                        return True
                    else:
                        logger.warning(f"Collection {collection_name} exists but uses old format. Consider recreating it.")
                        # For now, we'll work with the existing collection
                        return True

                except Exception as e:
                    logger.warning(f"Could not check collection format: {e}")
                    return True

            # Set up sparse model for the client
            self.client.set_sparse_model(self.config.sparse_model)
            sparse_params = self.client.get_fastembed_sparse_vector_params()

            # Create collection with named vectors for multi-vector support
            vectors_config = {
                # Dense vector for semantic similarity
                "dense": models.VectorParams(
                    size=self.config.embedding_size,
                    distance=models.Distance.COSINE
                )
            }

            # Sparse vectors configuration
            sparse_vectors_config = {
                # BM25 sparse vector
                self.config.sparse_vector_name: sparse_params[self.config.sparse_vector_name],
                # SPLADE sparse vector
                self.config.splade_vector_name: models.SparseVectorParams()
            }

            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=vectors_config,
                sparse_vectors_config=sparse_vectors_config,
                hnsw_config=models.HnswConfigDiff(
                    m=32,
                    ef_construct=150,
                    full_scan_threshold=10000
                )
            )

            logger.info(f"Created collection {collection_name} with multi-vector support")
            return True

        except Exception as e:
            logger.error(f"Failed to ensure collection {collection_name}: {e}")
            return False


    def _check_collection_format(self, collection_name: str) -> str:
        """Check if collection uses named vectors or single vector format."""
        try:
            info = self.client.get_collection(collection_name)
            logger.info(f"Collection info for {collection_name}: vectors={info.config.params.vectors}")

            vectors = info.config.params.vectors
            sparse_vectors = getattr(info.config.params, 'sparse_vectors', None)

            if isinstance(vectors, dict):
                # True named vectors mode - dense vectors are in a dict
                self._named_vector_keys = list(vectors.keys())
                return "named"
            elif isinstance(vectors, VectorParams):
                # Single vector format for dense, but check if it has sparse vectors
                if sparse_vectors and len(sparse_vectors) > 0:
                    # Hybrid format: single dense + sparse vectors
                    # We can work with this format without upgrading
                    logger.info(f"Collection {collection_name} uses hybrid format (single dense + sparse)")
                    return "hybrid"
                else:
                    # Pure single vector format
                    return "single"
            else:
                return "unknown"
        except Exception as e:
            logger.warning(f"Error checking collection format: {e}")
            return "unknown"

    def _ensure_sparse_vectors_in_collection(self, collection_name: str) -> bool:
        """Ensure all required sparse vectors are configured in the collection schema"""
        try:
            info = self.client.get_collection(collection_name)
            sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}

            # Check which sparse vectors are missing
            missing_vectors = []

            if self.config.sparse_vector_name not in sparse_vectors:
                missing_vectors.append(self.config.sparse_vector_name)

            if self.config.splade_vector_name not in sparse_vectors:
                missing_vectors.append(self.config.splade_vector_name)

            if missing_vectors:
                logger.warning(f"Collection {collection_name} is missing sparse vectors: {missing_vectors}")
                logger.warning(f"Cannot add sparse vectors to existing collection via update_collection API")
                logger.warning(f"The collection needs to be recreated with proper schema")
                logger.warning(f"For now, skipping SPLADE vector generation for this collection")
                return False
            else:
                logger.info(f"All required sparse vectors exist in collection {collection_name}")
                return True

        except Exception as e:
            logger.error(f"❌ Failed to check sparse vectors in collection {collection_name}: {e}")
            return False

    def upsert_point(
        self,
        collection_name: str,
        point_id: Union[str, int],
        payload: Dict[str, Any],
        dense_vector: Optional[List[float]] = None,
        sparse_vectors: Optional[Dict[str, Any]] = None,
        text_for_embedding: Optional[str] = None
    ) -> bool:
        """
        Upsert a point with dense + sparse (BM25 + SPLADE) vectors into a Qdrant collection.
        Automatically detects collection format (named vectors vs single vector) and uses appropriate format.
        """
        new_collection_name = None
        try:
            if not self.ensure_collection(collection_name):
                return False

            # Generate embeddings if needed
            if text_for_embedding:
                if not dense_vector:
                    dense_vector = en_embeddings.embed_query(text_for_embedding)
                if not sparse_vectors:
                    sparse_vectors = {}

                if self.config.sparse_vector_name not in sparse_vectors:
                    try:
                        bm25_vector = self.sparse_embedder.embed_query(text_for_embedding)
                        sparse_vectors[self.config.sparse_vector_name] = convert_to_qdrant_sparse_vector(bm25_vector)
                    except Exception as e:
                        logger.warning(f"BM25 vector failed: {e}")
                if self.config.splade_vector_name not in sparse_vectors:
                    try:
                        splade_vector = splade_encoder.encode(text_for_embedding)
                        sparse_vectors[self.config.splade_vector_name] = splade_vector
                    except Exception as e:
                        logger.warning(f"SPLADE vector failed: {e}")

            # Detect format
            collection_format = self._check_collection_format(collection_name)
            logger.info(f"Collection format: {collection_format}")

            if collection_format == "named":
                vectors: Dict[str, Any] = {}

                if dense_vector:
                    vectors["dense"] = dense_vector
                if sparse_vectors:
                    for k, v in sparse_vectors.items():
                        vectors[k] = v

                # Safe logging
                vector_info = {
                    k: (
                        f"indices: {v['indices'][:2]}..." if isinstance(v, dict) and "indices" in v else "dense/sparse vector"
                    )
                    for k, v in vectors.items()
                }
                logger.info(f"✅ Final vector structure for point {point_id}: {vector_info}")

                point = PointStruct(id=point_id, vector=vectors, payload=payload)
                self.client.upsert(collection_name=collection_name, points=[point], wait=True)
                logger.info(f"✅ Using named vectors format for point {point_id}")

            elif collection_format == "hybrid":
                # Hybrid format: single dense vector + sparse vectors
                # Check if all required sparse vectors are configured in the collection
                has_all_sparse_vectors = self._ensure_sparse_vectors_in_collection(collection_name)

                vectors: Dict[str, Any] = {}

                if dense_vector:
                    # For hybrid format, dense vector goes in the main vector field (empty key)
                    vectors[""] = dense_vector

                if sparse_vectors and has_all_sparse_vectors:
                    # Only add sparse vectors that are configured in the collection
                    info = self.client.get_collection(collection_name)
                    existing_sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}

                    for k, v in sparse_vectors.items():
                        if k in existing_sparse_vectors:
                            vectors[k] = v
                        else:
                            new_collection_name = f"{collection_name}_{k}"
                            # create new collation with new sparse vector
                            # check if collection with new sparse vector exists
                            if self.client.collection_exists(new_collection_name):
                                logger.info(f"Collection {new_collection_name} already exists")
                            else:

                                logger.info(f"Adding new sparse vector: {k}")
                                new_collection_name = f"{collection_name}_{k}"
                                self.client.create_collection(
                                    collection_name=new_collection_name,
                                    vectors_config={"dense": models.VectorParams(
                                        size=self.config.embedding_size,
                                        distance=models.Distance.COSINE
                                    )},
                                    sparse_vectors_config={k: models.SparseVectorParams()}
                                )
                                logger.info(f"Created new collection: {new_collection_name}")
                            vectors[k] = v
                            logger.info(f"Added sparse vector: {k}")

                elif sparse_vectors:
                    # Add only the sparse vectors that exist in the collection schema
                    if new_collection_name:
                        collection_name = new_collection_name

                    info = self.client.get_collection(collection_name)
                    existing_sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}

                    for k, v in sparse_vectors.items():
                        if k in existing_sparse_vectors:
                            vectors[k] = v
                            logger.info(f"Adding existing sparse vector: {k}")
                        else:
                            logger.warning(f"Skipping sparse vector '{k}' - not configured in collection schema")

                logger.info(f"✅ Using hybrid format for point {point_id} with vectors: {list(vectors.keys())}")
                point = PointStruct(id=point_id, vector=vectors, payload=payload)
                self.client.upsert(collection_name=collection_name, points=[point], wait=True)

            else:
                # For single vector format, ensure we have all required vectors
                if not dense_vector and text_for_embedding:
                    dense_vector = en_embeddings.embed_query(text_for_embedding)
                
                if not sparse_vectors and text_for_embedding:
                    sparse_vectors = {}
                    try:
                        bm25_vector = self.sparse_embedder.embed_query(text_for_embedding)
                        sparse_vectors[self.config.sparse_vector_name] = convert_to_qdrant_sparse_vector(bm25_vector)
                        splade_vector = splade_encoder.encode(text_for_embedding)
                        sparse_vectors[self.config.splade_vector_name] = splade_vector
                    except Exception as e:
                        logger.warning(f"Failed to generate sparse vectors: {e}")

                from qdrant_client.http.models import SparseVectorParams

                logger.warning(f"Collection '{collection_name}' is single‑vector. Upgrading to named‑vector...")

                # Build vector configs
                vectors_config = {
                    "dense": VectorParams(size=len(dense_vector), distance=Distance.COSINE)
                }
                sparse_vectors_config = {
                    self.config.sparse_vector_name: SparseVectorParams(),
                    self.config.splade_vector_name: SparseVectorParams()
                }

                try:
                    self.client.update_collection(
                        collection_name=collection_name,
                        vectors_config=vectors_config,
                        sparse_vectors_config=sparse_vectors_config
                    )
                    logger.info("✅ Upgraded collection to named-vector format")
                except Exception as e:
                    logger.error(f"❌ Failed to upgrade collection: {e}")
                    return False

                # Retry upsert with named vectors
                vectors = {"dense": dense_vector}
                if sparse_vectors:
                    vectors.update(sparse_vectors)

                if new_collection_name:
                    collection_name = new_collection_name

                point = PointStruct(id=point_id, vector=vectors, payload=payload)
                self.client.upsert(collection_name=collection_name, points=[point], wait=True)
                logger.info(f"✅ Retried named-vector upsert for point {point_id}")
                return True

            return True

        except Exception as e:
            logger.error(f"❌ Failed to upsert point {point_id}: {e}")
            return False

# Global Qdrant manager instance
qdrant_manager = QdrantManager()

def upsert_to_qdrant(
    collection: str,
    point_id: Union[str, int],
    payload: Dict[str, Any],
    qdrant_url: Optional[str] = None,
    vectors: Optional[List[float]] = None,
    sparse_vectors: Optional[Dict[str, Any]] = None,
    **kwargs
) -> bool:
    """
    Backward compatibility function for upserting to Qdrant
    """
    # Ensure point_id is valid for Qdrant
    if isinstance(point_id, str):
        valid_point_id = generate_valid_point_id(point_id)
    else:
        valid_point_id = point_id

    return qdrant_manager.upsert_point(
        collection_name=collection,
        point_id=valid_point_id,
        payload=payload,
        dense_vector=vectors,
        sparse_vectors=sparse_vectors
    )

def ingest_chunks_to_qdrant(
    collection: str,
    chunks_payload: List[Dict],
    document_id: str,
    document_summary: str,
    topics: List[str],
    document_metadata: Dict[str, Any],
    **kwargs
) -> bool:
    """
    Enhanced chunk ingestion with multi-vector support
    """
    try:
        # Ensure collection exists
        if not qdrant_manager.ensure_collection(collection):
            logger.error(f"Failed to ensure collection {collection}")
            return False

        # Process chunks individually with multi-vector support
        for idx, payload in enumerate(chunks_payload):
            # Clean the content for embedding
            content = payload.get("content", "")
            cleaned_content = strip_data_attributes(content)

            # Create enriched text for embedding
            chunk_text = (
                f"Document Summary: {document_summary}\n\n"
                f"Topics: {', '.join(topics)}\n\n"
                f"Content: {cleaned_content}"
            )

            # Create metadata with proper nesting structure
            meta = {
                "document_id": document_id,
                "chunk_index": idx,
                "positions": payload.get("positions", []),
                "raw_content": content,
                "cleaned_content": cleaned_content,
                "chunk_text": chunk_text,
                "metadata": document_metadata
            }

            logger.info(f"Processing chunk {idx} for document {document_id}")

            # Generate valid point ID
            point_id_str = f"{document_id}_chunk_{idx}"
            point_id = generate_valid_point_id(point_id_str)

            # Use our multi-vector upsert method
            success = qdrant_manager.upsert_point(
                collection_name=collection,
                point_id=point_id,
                payload=meta,
                text_for_embedding=chunk_text
            )

            if not success:
                logger.error(f"Failed to upsert chunk {idx} for document {document_id}")
                return False

        logger.info(f"Successfully ingested {len(chunks_payload)} chunks of '{document_id}' into collection '{collection}'")
        return True

    except Exception as e:
        logger.error(f"Failed to ingest chunks for document {document_id}: {e}")
        return False

def ingest_single_document(
    collection: str,
    document_id: str,
    content: str,
    metadata: Dict[str, Any]
) -> bool:
    """
    Ingest a single document with multi-vector support
    """
    try:
        # Clean content for embedding
        cleaned_content = strip_data_attributes(content)

        # Generate all vector types
        success = qdrant_manager.upsert_point(
            collection_name=collection,
            point_id=document_id,
            payload={
                "content": content,
                "cleaned_content": cleaned_content,
                "metadata": metadata
            },
            text_for_embedding=cleaned_content
        )

        if success:
            logger.info(f"Successfully ingested document {document_id} into collection {collection}")

        return success

    except Exception as e:
        logger.error(f"Failed to ingest document {document_id}: {e}")
        return False
