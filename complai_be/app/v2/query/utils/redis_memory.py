from langchain.memory import BaseMemory
from utils.memory_manager import memory_manager

class RedisMemory(BaseMemory):
    @property
    def memory_variables(self):
        return ["chat_history"]
    
    def __init__(self, session_id: str):
        self.session_id = session_id
    
    def load_memory_variables(self, inputs: dict) -> dict:
        chat_history = memory_manager.get_conversation_context(self.session_id)
        return {"chat_history": chat_history}
    
    def save_context(self, inputs: dict, outputs: dict) -> None:
        memory_manager.update_session_memory(self.session_id, "User", inputs.get("query", ""))
        memory_manager.update_session_memory(self.session_id, "Assistant", outputs.get("final_answer", ""))
