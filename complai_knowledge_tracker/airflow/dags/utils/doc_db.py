import requests
from logging import getLogger

logger = getLogger(__name__) 


# Function to download the TLS CA file
def download_tls_ca_file():
    try:
        response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        response.raise_for_status()
        with open("/tmp/global-bundle.pem", "wb") as file:
            file.write(response.content)
        logger.info("TLS CA file downloaded successfully")
    except requests.RequestException as e:
        logger.error(f"Failed to download TLS CA file: {e}")
        raise