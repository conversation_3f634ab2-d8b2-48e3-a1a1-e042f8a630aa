# Configure HTTP backend for Hugging Face to bypass SSL issues
try:
    import requests
    from huggingface_hub import configure_http_backend

    def create_session_factory():
        def session_factory():
            session = requests.Session()
            session.verify = False  # Bypass SSL verification
            return session
        return session_factory

    # Configure the HTTP backend
    configure_http_backend(backend_factory=create_session_factory())
    print("Configured Hugging Face HTTP backend to bypass SSL verification")
except ImportError:
    print("Could not configure Hugging Face HTTP backend - SSL issues may occur")

import requests
from tqdm import tqdm
import torch
from transformers import AutoTokenizer, AutoModelForMaskedLM


# Setup
QDRANT_URL = "http://localhost:6333"  # Update to your Qdrant instance
COLLECTION_NAMES = [
    "rbi_3"
    # ...
]
SPLADE_VECTOR_NAME = "fast-sparse-bm25-splade"

tokenizer = AutoTokenizer.from_pretrained("naver/efficient-splade-VI-BT-large-doc")
model = AutoModelForMaskedLM.from_pretrained("naver/efficient-splade-VI-BT-large-doc")

def splade_sparse(text):
    with torch.no_grad():
        tokens = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
        logits = model(**tokens).logits[0]
        max_vals, _ = torch.max(torch.relu(logits), dim=0)
        nonzero = max_vals > 0
        indices = nonzero.nonzero(as_tuple=True)[0]
        values = max_vals[indices]
        print(f"Generated sparse vector with {len(indices)} non-zero entries.")
        return {
            "indices": indices.cpu().tolist(),
            "values": values.cpu().tolist()
        }

def ensure_sparse_vector_field(collection):
    # Fetch current collection config
    resp = requests.get(f"{QDRANT_URL}/collections/{collection}")
    resp.raise_for_status()
    config = resp.json()["result"]["config"]["params"]
    # Check if SPLADE_VECTOR_NAME is present
    sparse_vectors = config.get("sparse_vectors", {})
    if SPLADE_VECTOR_NAME not in sparse_vectors:
        print(f"Adding sparse vector field '{SPLADE_VECTOR_NAME}' to collection '{collection}'...")
        # Add new sparse vector field (empty config is fine)
        patch = {
            "sparse_vectors": {
                SPLADE_VECTOR_NAME: {}
            }
        }
        resp = requests.patch(f"{QDRANT_URL}/collections/{collection}", json={"params": patch})
        resp.raise_for_status()
        print(f"Added sparse vector '{SPLADE_VECTOR_NAME}' to '{collection}'.")

def get_all_points(collection, with_vectors=False):
    points = []
    offset = None
    limit = 1000
    while True:
        payload = {
            "limit": limit,
            "with_payload": True,
            "with_vectors": True,
        }
        if offset is not None:
            payload["offset"] = offset
        resp = requests.post(f"{QDRANT_URL}/collections/{collection}/points/scroll", json=payload)
        resp.raise_for_status()
        result = resp.json()["result"]
        batch = result["points"]
        if not batch:
            break
        points.extend(batch)
        offset = result.get("next_page_offset")
        if not offset:
            break
    return points

def update_point_sparse_vector(collection, point, splade_vec):
    # Determine if the point uses "vector" or "vectors"
    if "vectors" in point and point["vectors"] is not None:
        vectors_field = {"vectors": point["vectors"]}
    elif "vector" in point and point["vector"] is not None:
        vectors_field = {"vector": point["vector"]}
    else:
        vectors_field = {}

    # Prepare full sparse_vectors dict
    sparse_vectors = point.get("sparse_vectors", {})
    sparse_vectors = dict(sparse_vectors)  # copy to avoid mutating original
    sparse_vectors[SPLADE_VECTOR_NAME] = splade_vec

    payload = {
        "id": point["id"],
        **vectors_field,
        "sparse_vectors": sparse_vectors,
        "payload": point.get("payload", {}),
    }

    resp = requests.put(
        f"{QDRANT_URL}/collections/{collection}/points",
        json={"points": [payload]}
    )
    if resp.status_code != 200:
        print(f"Error updating point {point['id']}: {resp.text}")

def main():
    for collection in COLLECTION_NAMES:
        print(f"\n--- Processing collection: {collection} ---")
        ensure_sparse_vector_field(collection)
        points = get_all_points(collection)
        print(f"Found {len(points)} points in collection '{collection}'")
        for point in tqdm(points):
            point_id = point["id"]
            payload = point.get("payload", {})
            text = payload.get("page_content")
            if not text:
                print(f"Skipping {point_id}, no page_content.")
                continue
            try:
                splade_vec = splade_sparse(text)
                update_point_sparse_vector(collection, point, splade_vec)
            except Exception as e:
                print(f"Error on point {point_id}: {e}")

if __name__ == "__main__":
    main()
