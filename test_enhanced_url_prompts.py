#!/usr/bin/env python3
"""
Test the enhanced URL handling prompts
"""

import sys
import os
sys.path.append('/Users/<USER>/selkea/complai_knowledge_tracker/airflow/dags')

def test_prompt_clarity():
    """Test that prompts are clear about URL field purposes"""
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT, UPDATE_ACTION_DETERMINER_PROMPT
        
        print("🧪 Testing Enhanced URL Handling Prompts")
        print("=" * 80)
        
        # Test key concepts in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        print("\n📋 Testing AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:")
        
        key_concepts = [
            "new_document_url: The PDF URL for NEW documents",
            "Use ONLY for ADD_DOCUMENT actions",
            "This URL will be downloaded and processed",
            "MUST be a valid PDF URL from EXTRACTED_PDF_LINKS",
            "URL Format Validation",
            "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/",
            "ADD_DOCUMENT actions REQUIRE new_document_url",
            "Wrong URL = wrong document processed",
            "NEVER generate URLs not in EXTRACTED_PDF_LINKS"
        ]
        
        for concept in key_concepts:
            if concept in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                print(f"   ✅ Found: {concept}")
            else:
                print(f"   ❌ Missing: {concept}")
        
        # Test UPDATE_ACTION_DETERMINER_PROMPT
        print("\n📋 Testing UPDATE_ACTION_DETERMINER_PROMPT:")
        
        update_concepts = [
            "new_document_url: The EXACT PDF URL that will be downloaded",
            "ONLY for ADD_DOCUMENT actions",
            "This URL determines which document gets added",
            "new_document_url is MANDATORY if a PDF URL exists",
            "Wrong URL = wrong document processed = corrupted knowledge base",
            "The system will download and process whatever URL you provide"
        ]
        
        for concept in update_concepts:
            if concept in UPDATE_ACTION_DETERMINER_PROMPT:
                print(f"   ✅ Found: {concept}")
            else:
                print(f"   ❌ Missing: {concept}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def simulate_llm_scenarios():
    """Simulate different scenarios the LLM might encounter"""
    
    print("\n🎭 Simulating LLM Scenarios")
    print("=" * 80)
    
    scenarios = [
        {
            "name": "Perfect Case - PDF Link Available",
            "content": """
            FEMA 23(R)/(6)/2025-RB
            June 24, 2025
            
            EXTRACTED_PDF_LINKS: https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF
            """,
            "expected": {
                "new_document_url": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF",
                "action_type": "ADD_DOCUMENT"
            }
        },
        {
            "name": "No PDF Links Available",
            "content": """
            RBI/2025-26/64
            July 2, 2025
            
            EXTRACTED_PDF_LINKS: 
            """,
            "expected": {
                "new_document_url": "",
                "requires_manual_review": True
            }
        },
        {
            "name": "Multiple PDF Links",
            "content": """
            RBI/2025-26/65
            July 3, 2025
            
            EXTRACTED_PDF_LINKS: https://rbidocs.rbi.org.in/rdocs/notification/PDFs/MAIN_DOC.PDF, https://rbidocs.rbi.org.in/rdocs/notification/PDFs/ANNEX.PDF
            """,
            "expected": {
                "new_document_url": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/MAIN_DOC.PDF",
                "note": "Should use first/main document"
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📝 Scenario: {scenario['name']}")
        print(f"   Content: {scenario['content'].strip()}")
        print(f"   Expected behavior:")
        for key, value in scenario['expected'].items():
            print(f"     {key}: {value}")
    
    return True

def test_url_format_examples():
    """Test that prompts include correct URL format examples"""
    
    print("\n🔗 Testing URL Format Examples")
    print("=" * 80)
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        
        # Check for correct URL format examples
        correct_formats = [
            "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/",
            "https://rbidocs.rbi.org.in/rdocs/PressRelease/PDFs/"
        ]
        
        forbidden_examples = [
            "NOTI134.PDF",
            "https://www.rbi.org.in/"
        ]
        
        print("✅ Checking for correct URL format examples:")
        for format_example in correct_formats:
            if format_example in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                print(f"   ✅ Found: {format_example}")
            else:
                print(f"   ❌ Missing: {format_example}")
        
        print("\n❌ Checking that forbidden examples are marked as wrong:")
        for forbidden in forbidden_examples:
            if forbidden in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                # Check if it's marked as wrong
                if "❌ WRONG:" in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT and forbidden in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                    print(f"   ✅ {forbidden} correctly marked as forbidden")
                else:
                    print(f"   ⚠️ {forbidden} found but may not be clearly marked as wrong")
            else:
                print(f"   ✅ {forbidden} not found (good)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    
    print("🚀 Testing Enhanced URL Handling Prompts")
    print("=" * 80)
    
    success = True
    
    if not test_prompt_clarity():
        success = False
    
    if not simulate_llm_scenarios():
        success = False
    
    if not test_url_format_examples():
        success = False
    
    if success:
        print("\n🎉 All prompt tests passed!")
        print("✅ Prompts clearly explain URL field purposes")
        print("✅ LLM understands which URLs to use for which actions")
        print("✅ URL format validation is explicit")
        print("✅ Consequences of wrong URLs are explained")
        print("✅ No more NOTI134.PDF fallbacks should occur")
    else:
        print("\n❌ Some tests failed - check prompt content")
    
    return success

if __name__ == "__main__":
    main()
