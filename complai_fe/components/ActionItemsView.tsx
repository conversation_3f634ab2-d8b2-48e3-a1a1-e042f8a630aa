// "use client";

// import { useState, useEffect } from "react";
// import { RegulationInfo, ReferenceItem } from "@/types/types";
// import { ActionItemsDetail } from "@/components/ActionItemsDetail";
// // import { useAuth } from "@/contexts/AuthContext";
// import { ACTION_ITEMS_SEARCH_ENDPOINT, ACTION_ITEMS_ITEM_ENDPOINT } from "@/app/utils/Api";

// interface ActionItemsViewProps {
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   onViewDetail: () => void;
//   setReferences: (references: ReferenceItem[]) => void;
//   showDetailView: boolean;
// }

// export function ActionItemsView({
//   setActiveView,
//   setCurrentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   onViewDetail,
//   setReferences,
//   showDetailView,
// }: ActionItemsViewProps) {
//   const [searchValue, setSearchValue] = useState("");
//   const [regulations, setRegulations] = useState<RegulationInfo[]>([]);
//   const [selectedRegulation, setSelectedRegulation] =
//     useState<RegulationInfo | null>(null);
//   const [actionItemsData, setActionItemsData] = useState(null);
//   const [isLoading, setIsLoading] = useState(false);
//   const [isLoadingActionItems, setIsLoadingActionItems] = useState(false);
//   const { user } = useAuth();
//   const username = user?.username;

//   // Fetch regulations on initial load
//   useEffect(() => {
//     fetchRegulations();
//   }, []);

//   // Function to fetch regulations
//   const fetchRegulations = async () => {
//     setIsLoading(true);
//     // Updated regulations data based on your requirements
//     const mockRegulations: RegulationInfo[] = [
//       {
//         id: "RBI/2020-21/73 DOR.FIN.HFC.CC.No.120/03.10.136/2020-21",
//         title:
//           "Master Direction - Non-Banking Financial Company - Housing Finance Company (Reserve Bank) Directions, 2021",
//         date: "Mar 19, 2025",
//         department: "Department of Regulation",
//         docCode: "RBI/2020-21/73 DOR.FIN.HFC.CC.No.120/03.10.136/2020-21",
//         content: "",
//       },
//       {
//         id: "RBI/DOR/2023-24/XX",
//         title:
//           "Acquisition and Holding of Shares or Voting Rights in Banking Companies Directions, 2023",
//         date: "Mar 18, 2025",
//         department: "Department of Regulation",
//         docCode: "RBI/DOR/2023-24/XX",
//         content: "",
//       },
//       {
//         id: "RBI/2021-22/MD/01",
//         title:
//           "Master Direction - Reserve Bank of India (Certificate of Deposit) Directions, 2021",
//         date: "Mar 17, 2025",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/2021-22/MD/01",
//         content: "",
//       },
//       {
//         id: "RBI/2021-22/78 FMRD.DIRD.01/14.01.001/2021-22",
//         title:
//           "Master Direction - Reserve Bank of India (Call, Notice and Term Money Markets) Directions, 2021",
//         date: "Mar 16, 2025",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/2021-22/78 FMRD.DIRD.01/14.01.001/2021-22",
//         content: "",
//       },
//     ];

//     // Simulate API delay
//     setTimeout(() => {
//       setRegulations(mockRegulations);
//       setIsLoading(false);
//     }, 500);
//   };

//   // New function to search regulations via API
//   const searchRegulations = async (query: string) => {
//     setIsLoading(true);
//     try {
//       // Use the search API endpoint
//       const response = await fetch(ACTION_ITEMS_SEARCH_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_name: query
//         }),
//       });

//       if (!response.ok) {
//         // If API fails, revert to mock data that's filtered
//         const filteredMockRegs = regulations.filter((reg) =>
//           reg.title.toLowerCase().includes(query.toLowerCase()) ||
//           reg.docCode.toLowerCase().includes(query.toLowerCase())
//         );
//         return filteredMockRegs;
//       }
      
//       const data = await response.json();
      
//       // Map the API response to match our RegulationInfo format
//       const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
//         id: doc._id,
//         title: doc.document_title || "",
//         date: "Mar 2025", // You might want to fetch this from API too
//         department: "Department of Regulation", // You might want to fetch this from API too 
//         docCode: doc.document_number || "",
//         content: "",
//       }));
      
//       return mappedRegulations;
//     } catch (error) {
//       console.error("Error searching regulations:", error);
//       // Fallback to existing regulations in case of error
//       const filteredMockRegs = regulations.filter((reg) =>
//         reg.title.toLowerCase().includes(query.toLowerCase()) ||
//         reg.docCode.toLowerCase().includes(query.toLowerCase())
//       );
//       return filteredMockRegs;
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Function to fetch action items for a specific regulation
//   const fetchActionItems = async (documentNumber: string) => {
//     setIsLoadingActionItems(true);
//     try {
//       const response = await fetch(ACTION_ITEMS_ITEM_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_id: documentNumber,
//           username: username
//         }),
//       });

//       if (!response.ok) {
//         throw new Error("Failed to fetch action items");
//       }
//       const data = await response.json();
//       setActionItemsData(data);
//       setselectedDocCode(documentNumber);

//       console.log(data);

//       // Map API response to ReferenceItem format
//       if (data && data.actionables) {
//         const mappedReferences: ReferenceItem[] = data.actionables.map(
//           (item: any, index: number) => ({
//             Type: "",
//             addressee: "",
//             chunk_id: index,
//             date_of_issue: "",
//             dept: "",
//             document_code: data.document_number || "",
//             id: `ref_${index}`,
//             pdf_filename: "",
//             pdf_link: "",
//             positions: [],
//             revision_s3_url: data.s3_url || "",
//             section_summary: item.obligation || "",
//             title: item.para || "",
//             document_id: data.document_number || "",
//           })
//         );
//         console.log("mappedReferences:",mappedReferences);
//         setReferences(mappedReferences);
//       } else {
//         // Set empty references if no actionables
//         setReferences([]);
//       }
//     } catch (error) {
//       console.error("Error fetching action items:", error);
//       // Fallback to mock data in case of error
//       setActionItemsData(null);
//       setReferences([]);
//     } finally {
//       setIsLoadingActionItems(false);
//     }
//   };

//   // Handle search input change with debounce
//   useEffect(() => {
//     const delayDebounce = setTimeout(async () => {
//       if (searchValue.trim() !== "") {
//         const searchResults = await searchRegulations(searchValue);
//         setRegulations(searchResults);
//       } else {
//         // If search is cleared, fetch default regulations
//         fetchRegulations();
//       }
//     }, 500); // 500ms debounce

//     return () => clearTimeout(delayDebounce);
//   }, [searchValue]);

//   // Handle regulation selection
//   const handleRegulationSelect = async (regulation: RegulationInfo) => {
//     setSelectedRegulation(regulation);
//     setCurrentRegulation(regulation);

//     // Fetch action items for the selected regulation
//     await fetchActionItems(regulation.docCode);

//     // onViewDetail(); // Show the detail panel
//   };

//   // Go back to home
//   const handleBackToHome = () => {
//     setActiveView("home");
//   };

//   if (selectedRegulation) {
//     return (
//       <ActionItemsDetail
//         regulation={selectedRegulation}
//         actionItemsData={actionItemsData}
//         isLoading={isLoadingActionItems}
//         onBack={() => {
//           setSelectedRegulation(null);
//           setActionItemsData(null);
//         }}
//         onViewDetail={onViewDetail}
//         showDetailView={showDetailView}
//       />
//     );
//   }

//   return (
//     <div className="flex flex-col items-start w-full px-4 bg-transparent">
//       {/* Back button */}

//       {/* Main container with exact Figma specs */}
//       <div
//         className="flex flex-col items-start w-full max-w-[635px] mx-auto py-5 px-8 gap-1 border border-[#A5998F] rounded-xl bg-transparent"
//         style={{ minHeight: "832px" }}
//       >
//         <div className="w-full">
//           <h1 className="text-[#494949] text-2xl md:text-3xl font-semibold text-center mb-8">
//             Select the Regulation to <br />
//             Generate Action Items for
//           </h1>

//           {/* Search Input - exact Figma specs */}
//           <div className="relative w-full mb-6">
//             <div className="flex items-center gap-1 w-full p-3 md:p-4 rounded-xl border border-[#A5998F] bg-opacity-10">
//               <div className="flex items-center justify-center">
//                 <svg
//                   width="16"
//                   height="16"
//                   viewBox="0 0 16 16"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                   className="text-[#7B7B7B]"
//                 >
//                   <path
//                     d="M7.25 12.5C10.1495 12.5 12.5 10.1495 12.5 7.25C12.5 4.35051 10.1495 2 7.25 2C4.35051 2 2 4.35051 2 7.25C2 10.1495 4.35051 12.5 7.25 12.5Z"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                   <path
//                     d="M10.9622 10.9625L13.9997 14.0001"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                 </svg>
//               </div>
//               <input
//                 type="text"
//                 placeholder="Search Regulations"
//                 value={searchValue}
//                 onChange={(e) => setSearchValue(e.target.value)}
//                 className="w-full bg-transparent outline-none text-[#7B7B7B] pl-1"
//               />
//             </div>
//           </div>

//           {/* Latest Regulations Header */}
//           <div className="text-[#7B7B7B] text-sm font-medium mb-4">
//             Latest Regulations
//           </div>

//           {/* Regulations List */}
//           <div className="flex flex-col gap-4">
//             {isLoading ? (
//               <div className="text-center py-6">Loading regulations...</div>
//             ) : regulations.length === 0 ? (
//               <div className="text-center py-6 text-[#7B7B7B]">
//                 No regulations found
//               </div>
//             ) : (
//               regulations.map((regulation) => (
//                 <div
//                   key={regulation.id}
//                   onClick={() => handleRegulationSelect(regulation)}
//                   className="flex flex-col justify-center items-start gap-2 p-3 w-full rounded-lg bg-opacity-10 hover:bg-white-opacity-20 cursor-pointer transition-all"
//                 >
//                   <h3 className="text-[#494949] text-base md:text-lg font-medium">
//                     {regulation.title}
//                   </h3>
//                   <p className="text-[#7B7B7B] text-xs">{regulation.date}</p>
//                 </div>
//               ))
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

// "use client";

// import { useState, useEffect } from "react";
// import { RegulationInfo, ReferenceItem } from "@/types/types";
// import { ActionItemsDetail } from "@/components/ActionItemsDetail";
// import { useAuth } from "@/contexts/AuthContext";
// import { ACTION_ITEMS_SEARCH_ENDPOINT, ACTION_ITEMS_ITEM_ENDPOINT } from "@/app/utils/Api";

// interface ActionItemsViewProps {
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   onViewDetail: () => void;
//   setReferences: (references: ReferenceItem[]) => void;
//   showDetailView: boolean;
// }

// export function ActionItemsView({
//   setActiveView,
//   setCurrentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   onViewDetail,
//   setReferences,
//   showDetailView,
// }: ActionItemsViewProps) {
//   const [searchValue, setSearchValue] = useState("");
//   const [regulations, setRegulations] = useState<RegulationInfo[]>([]);
//   const [selectedRegulation, setSelectedRegulation] =
//     useState<RegulationInfo | null>(null);
//   const [actionItemsData, setActionItemsData] = useState(null);
//   const [isLoading, setIsLoading] = useState(false);
//   const [isLoadingActionItems, setIsLoadingActionItems] = useState(false);
//   const { user } = useAuth();
//   const username = user?.username;

//   // Fetch regulations on initial load
//   useEffect(() => {
//     fetchRegulations();
//   }, []);

//   // Function to fetch regulations
//   const fetchRegulations = async () => {
//     setIsLoading(true);
//     // Updated regulations data based on your requirements
//     const mockRegulations: RegulationInfo[] = [
//       {
//         id: "RBI/2020-21/73 DOR.FIN.HFC.CC.No.120/03.10.136/2020-21",
//         title:
//           "Master Direction - Non-Banking Financial Company - Housing Finance Company (Reserve Bank) Directions, 2021",
//         date: "Mar 19, 2025",
//         department: "Department of Regulation",
//         docCode: "RBI/2020-21/73 DOR.FIN.HFC.CC.No.120/03.10.136/2020-21",
//         content: "",
//       },
//       {
//         id: "RBI/DOR/2023-24/XX",
//         title:
//           "Acquisition and Holding of Shares or Voting Rights in Banking Companies Directions, 2023",
//         date: "Mar 18, 2025",
//         department: "Department of Regulation",
//         docCode: "RBI/DOR/2023-24/XX",
//         content: "",
//       },
//       {
//         id: "RBI/2021-22/MD/01",
//         title:
//           "Master Direction - Reserve Bank of India (Certificate of Deposit) Directions, 2021",
//         date: "Mar 17, 2025",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/2021-22/MD/01",
//         content: "",
//       },
//       {
//         id: "RBI/2021-22/78 FMRD.DIRD.01/14.01.001/2021-22",
//         title:
//           "Master Direction - Reserve Bank of India (Call, Notice and Term Money Markets) Directions, 2021",
//         date: "Mar 16, 2025",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/2021-22/78 FMRD.DIRD.01/14.01.001/2021-22",
//         content: "",
//       },
//     ];

//     // Simulate API delay
//     setTimeout(() => {
//       setRegulations(mockRegulations);
//       setIsLoading(false);
//     }, 500);
//   };

//   // New function to search regulations via API
//   const searchRegulations = async (query: string) => {
//     setIsLoading(true);
//     try {
//       // Use the search API endpoint
//       const response = await fetch(ACTION_ITEMS_SEARCH_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_name: query
//         }),
//       });

//       if (!response.ok) {
//         // If API fails, revert to mock data that's filtered
//         const filteredMockRegs = regulations.filter((reg) =>
//           reg.title.toLowerCase().includes(query.toLowerCase()) ||
//           reg.docCode.toLowerCase().includes(query.toLowerCase())
//         );
//         return filteredMockRegs;
//       }
      
//       const data = await response.json();
      
//       // Map the API response to match our RegulationInfo format
//       const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
//         id: doc._id,
//         title: doc.document_title || "",
//         date: "Mar 2025", // You might want to fetch this from API too
//         department: "Department of Regulation", // You might want to fetch this from API too 
//         docCode: doc.document_number || "",
//         content: "",
//       }));
      
//       return mappedRegulations;
//     } catch (error) {
//       console.error("Error searching regulations:", error);
//       // Fallback to existing regulations in case of error
//       const filteredMockRegs = regulations.filter((reg) =>
//         reg.title.toLowerCase().includes(query.toLowerCase()) ||
//         reg.docCode.toLowerCase().includes(query.toLowerCase())
//       );
//       return filteredMockRegs;
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Function to fetch action items for a specific regulation
//   const fetchActionItems = async (documentNumber: string) => {
//     setIsLoadingActionItems(true);
//     try {
//       const response = await fetch(ACTION_ITEMS_ITEM_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_id: documentNumber,
//           username: username
//         }),
//       });

//       if (!response.ok) {
//         throw new Error("Failed to fetch action items");
//       }
//       const data = await response.json();
//       setActionItemsData(data);
//       setselectedDocCode(documentNumber);

//       // console.log(data);

//       // Map API response to ReferenceItem format
//       if (data && data.actionables) {
//         const mappedReferences: ReferenceItem[] = data.actionables.map(
//           (item: any, index: number) => ({
//             Type: "",
//             addressee: "",
//             chunk_id: index,
//             date_of_issue: "",
//             dept: "",
//             document_code: data.document_number || "",
//             id: `ref_${index}`,
//             pdf_filename: "",
//             pdf_link: "",
//             positions: [],
//             revision_s3_url: data.s3_url || "",
//             section_summary: item.obligation || "",
//             title: item.para || "",
//             document_id: data.document_number || "",
//           })
//         );
//         // console.log("mappedReferences:",mappedReferences);
//         onViewDetail();
//         setReferences(mappedReferences);
//       } else {
//         // Set empty references if no actionables
//         setReferences([]);
//       }
//     } catch (error) {
//       console.error("Error fetching action items:", error);
//       // Fallback to mock data in case of error
//       setActionItemsData(null);
//       setReferences([]);
//     } finally {
//       setIsLoadingActionItems(false);
//     }
//   };

//   // Handle search input change with debounce
//   useEffect(() => {
//     const delayDebounce = setTimeout(async () => {
//       if (searchValue.trim() !== "") {
//         const searchResults = await searchRegulations(searchValue);
//         setRegulations(searchResults);
//       } else {
//         // If search is cleared, fetch default regulations
//         fetchRegulations();
//       }
//     }, 500); // 500ms debounce

//     return () => clearTimeout(delayDebounce);
//   }, [searchValue]);

//   // Handle regulation selection
//   const handleRegulationSelect = async (regulation: RegulationInfo) => {
//     setSelectedRegulation(regulation);
//     setCurrentRegulation(regulation);

//     // Fetch action items for the selected regulation
//     await fetchActionItems(regulation.docCode);

//     // onViewDetail(); // Show the detail panel
//   };

//   // Go back to home
//   const handleBackToHome = () => {
//     setActiveView("home");
//   };

//   if (selectedRegulation) {
//     return (
//       <ActionItemsDetail
//         regulation={selectedRegulation}
//         actionItemsData={actionItemsData}
//         isLoading={isLoadingActionItems}
//         onBack={() => {
//           setSelectedRegulation(null);
//           setActionItemsData(null);
//         }}
//         onViewDetail={onViewDetail}
//         showDetailView={showDetailView}
//       />
//     );
//   }

//   return (
//     <div className="flex flex-col items-start w-full px-4 bg-transparent">
//       {/* Back button */}

//       {/* Main container with exact Figma specs */}
//       <div
//         className="flex flex-col items-start w-full max-w-[635px] mx-auto py-5 px-8 gap-1 border border-[#A5998F] rounded-xl bg-transparent"
//         style={{ minHeight: "832px" }}
//       >
//         <div className="w-full">
//           <h1 className="text-[#494949] text-2xl md:text-3xl font-semibold text-center mb-8">
//             Select the Regulation to <br />
//             Generate Action Items for
//           </h1>

//           {/* Search Input - exact Figma specs */}
//           <div className="relative w-full mb-6">
//             <div className="flex items-center gap-1 w-full p-3 md:p-4 rounded-xl border border-[#A5998F] bg-opacity-10">
//               <div className="flex items-center justify-center">
//                 <svg
//                   width="16"
//                   height="16"
//                   viewBox="0 0 16 16"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                   className="text-[#7B7B7B]"
//                 >
//                   <path
//                     d="M7.25 12.5C10.1495 12.5 12.5 10.1495 12.5 7.25C12.5 4.35051 10.1495 2 7.25 2C4.35051 2 2 4.35051 2 7.25C2 10.1495 4.35051 12.5 7.25 12.5Z"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                   <path
//                     d="M10.9622 10.9625L13.9997 14.0001"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                 </svg>
//               </div>
//               <input
//                 type="text"
//                 placeholder="Search Regulations"
//                 value={searchValue}
//                 onChange={(e) => setSearchValue(e.target.value)}
//                 className="w-full bg-transparent outline-none text-[#7B7B7B] pl-1"
//               />
//             </div>
//           </div>

//           {/* Latest Regulations Header */}
//           <div className="text-[#7B7B7B] text-sm font-medium mb-4">
//             Latest Regulations
//           </div>

//           {/* Regulations List */}
//           <div className="flex flex-col gap-4">
//             {isLoading ? (
//               <div className="text-center py-6">Loading regulations...</div>
//             ) : regulations.length === 0 ? (
//               <div className="text-center py-6 text-[#7B7B7B]">
//                 No regulations found
//               </div>
//             ) : (
//               regulations.map((regulation) => (
//                 <div
//                   key={regulation.id}
//                   onClick={() => handleRegulationSelect(regulation)}
//                   className="flex flex-col justify-center items-start gap-2 p-3 w-full rounded-lg group hover:bg-white hover:-translate-y-1 cursor-pointer transition-all duration-200  hover:shadow-md"
//                 >
//                   <h3 className="text-[#494949] text-base md:text-lg font-medium group-hover:text-[#494949]">
//                     {regulation.title}
//                   </h3>
//                   <p className="text-[#7B7B7B] text-xs group-hover:text-[#7B7B7B]">
//                     {regulation.date}
//                   </p>
//                 </div>
//               ))
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

// "use client";

// import { useState, useEffect } from "react";
// import { RegulationInfo, ReferenceItem } from "@/types/types";
// import { ActionItemsDetail } from "@/components/ActionItemsDetail";
// import { useAuth } from "@/contexts/AuthContext";
// import { ACTION_ITEMS_SEARCH_ENDPOINT, ACTION_ITEMS_ITEM_ENDPOINT } from "@/app/utils/Api";

// interface ActionItemsViewProps {
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   onViewDetail: () => void;
//   setReferences: (references: ReferenceItem[]) => void;
//   showDetailView: boolean;
// }

// export function ActionItemsView({
//   setActiveView,
//   setCurrentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   onViewDetail,
//   setReferences,
//   showDetailView,
// }: ActionItemsViewProps) {
//   const [searchValue, setSearchValue] = useState("");
//   const [regulations, setRegulations] = useState<RegulationInfo[]>([]);
//   const [selectedRegulation, setSelectedRegulation] =
//     useState<RegulationInfo | null>(null);
//   const [actionItemsData, setActionItemsData] = useState(null);
//   const [isLoading, setIsLoading] = useState(false);
//   const [isLoadingActionItems, setIsLoadingActionItems] = useState(false);
//   const { user } = useAuth();
//   const username = user?.username;

//   // Fetch regulations on initial load
//   useEffect(() => {
//     fetchRegulations();
//   }, []);

//   // Function to fetch regulations
//   const fetchRegulations = async () => {
//     setIsLoading(true);
//     // Updated regulations data based on your requirements
//     const mockRegulations: RegulationInfo[] = [
//       {
//         id: "RBI/FED/2015-16/9",
//         title:
//           "FED Master Direction No.14/2015-16 - Deposits and Accounts",
//         department: "Department of Regulation",
//         docCode: "RBI/FED/2015-16/9",
//         content: "",
//       },
//       {
//         id: "RBI/DBR/2015-16/21",
//         title:
//           "Master Direction - Issue and Pricing of shares by Private Sector Banks, Directions, 2016",
//         department: "Department of Regulation",
//         docCode: "RBI/DBR/2015-16/21",
//         content: "",
//       },
//       {
//         id: "RBI/FED/2015-16/6  FED Master Direction No.10/2015-16",
//         title:
//           "Master Direction - Establishment of Branch Office (BO)/ Liaison Office (LO)/ Project Office (PO) or any other place of business in India by foreign entities",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/FED/2015-16/6  FED Master Direction No.10/2015-16",
//         content: "",
//       },
//       {
//         id: "RBI/2015-16/240 Master Direction No.DBR.PSBD.No.56/16.13.100/2015-16",
//         title:
//           "PRIOR APPROVAL FOR ACQUISITION OF SHARES OR VOTING RIGHTS IN PRIVATE SECTOR BANKS: DIRECTIONS, 2015",
//         department: "Financial Markets Regulation Department",
//         docCode: "RBI/2015-16/240 Master Direction No.DBR.PSBD.No.56/16.13.100/2015-16",
//         content: "",
//       },
//       {
//         id:  "RBI/FED/2015-16/1",
//         title:
//           "FED Master Direction No.4/2015-16 - Master Direction on Compounding of Contraventions under FEMA, 1999",
//         department: "Department of Regulation",
//         docCode: "RBI/FED/2015-16/1",
//         content: "",
//       },
//     ];

//     // Simulate API delay
//     setTimeout(() => {
//       setRegulations(mockRegulations);
//       setIsLoading(false);
//     }, 500);
//   };

//   // New function to search regulations via API
//   const searchRegulations = async (query: string) => {
//     setIsLoading(true);
//     try {
//       // Use the search API endpoint
//       const response = await fetch(ACTION_ITEMS_SEARCH_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_name: query
//         }),
//       });

//       if (!response.ok) {
//         // If API fails, revert to mock data that's filtered
//         const filteredMockRegs = regulations.filter((reg) =>
//           reg.title.toLowerCase().includes(query.toLowerCase()) ||
//           reg.docCode.toLowerCase().includes(query.toLowerCase())
//         );
//         return filteredMockRegs;
//       }
      
//       const data = await response.json();
      
//       // Map the API response to match our RegulationInfo format
//       const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
//         id: doc._id,
//         title: doc.document_title || "",
//         date: "Mar 2025", // You might want to fetch this from API too
//         department: "Department of Regulation", // You might want to fetch this from API too 
//         docCode: doc.document_number || "",
//         content: "",
//       }));
      
//       return mappedRegulations;
//     } catch (error) {
//       console.error("Error searching regulations:", error);
//       // Fallback to existing regulations in case of error
//       const filteredMockRegs = regulations.filter((reg) =>
//         reg.title.toLowerCase().includes(query.toLowerCase()) ||
//         reg.docCode.toLowerCase().includes(query.toLowerCase())
//       );
//       return filteredMockRegs;
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Function to fetch action items for a specific regulation
//   const fetchActionItems = async (documentNumber: string) => {
//     setIsLoadingActionItems(true);
//     try {
//       const response = await fetch(ACTION_ITEMS_ITEM_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_id: documentNumber,
//           username: username
//         }),
//       });

//       if (!response.ok) {
//         throw new Error("Failed to fetch action items");
//       }
//       const data = await response.json();
//       setActionItemsData(data);
//       setselectedDocCode(documentNumber);

//       // console.log(data);

//       // Map API response to ReferenceItem format using new obligations structure
//        if (data && data.obligations) {
//         const mappedReferences: ReferenceItem[] = data.obligations.map(
//           (item: any, index: number) => ({
//             Type: "",
//             addressee: "",
//             chunk_id: item.positions && item.positions.length > 0 ? item.positions[0].page_number : index,
//             date_of_issue: "",
//             dept: "",
//             document_code: data.document_number || "",
//             id: `ref_${index}`,
//             pdf_filename: "",
//             pdf_link: "",
//             positions: item.positions || [],
//             revision_s3_url: data.s3_url || "",
//             section_summary: item.obligation || "",
//             title: data.document_title || "",
//             document_id: data.document_number || "",
//           })
//         );
//         console.log("mappedReferences:",mappedReferences);
//         onViewDetail();
//         setReferences(mappedReferences);
//       } else {
//         // Set empty references if no obligations
//         setReferences([]);
//       }
//     } catch (error) {
//       console.error("Error fetching action items:", error);
//       // Fallback to mock data in case of error
//       setActionItemsData(null);
//       setReferences([]);
//     } finally {
//       setIsLoadingActionItems(false);
//     }
//   };

//   // Handle search input change with debounce
//   useEffect(() => {
//     const delayDebounce = setTimeout(async () => {
//       if (searchValue.trim() !== "") {
//         const searchResults = await searchRegulations(searchValue);
//         setRegulations(searchResults);
//       } else {
//         // If search is cleared, fetch default regulations
//         fetchRegulations();
//       }
//     }, 500); // 500ms debounce

//     return () => clearTimeout(delayDebounce);
//   }, [searchValue]);

//   // Handle regulation selection
//   const handleRegulationSelect = async (regulation: RegulationInfo) => {
//     setSelectedRegulation(regulation);
//     setCurrentRegulation(regulation);

//     // Fetch action items for the selected regulation
//     await fetchActionItems(regulation.docCode);

//     // onViewDetail(); // Show the detail panel
//   };

//   // Go back to home
//   const handleBackToHome = () => {
//     setActiveView("home");
//   };

//   if (selectedRegulation) {
//     return (
//       <ActionItemsDetail
//         regulation={selectedRegulation}
//         actionItemsData={actionItemsData}
//         isLoading={isLoadingActionItems}
//         onBack={() => {
//           setSelectedRegulation(null);
//           setActionItemsData(null);
//         }}
//         onViewDetail={onViewDetail}
//         showDetailView={showDetailView}
//       />
//     );
//   }

//   return (
//     <div className="flex flex-col items-start w-full px-4 bg-transparent">
//       {/* Back button */}

//       {/* Main container with exact Figma specs */}
//       <div
//         className="flex flex-col items-start w-full max-w-[635px] mx-auto py-5 px-8 gap-1 border border-[#A5998F] rounded-xl bg-transparent"
//         style={{ minHeight: "832px" }}
//       >
//         <div className="w-full">
//           <h1 className="text-[#494949] text-2xl md:text-3xl font-semibold text-center mb-8">
//             Select the Regulation to <br />
//             Generate Action Items for
//           </h1>

//           {/* Search Input - exact Figma specs */}
//           <div className="relative w-full mb-6">
//             <div className="flex items-center gap-1 w-full p-3 md:p-4 rounded-xl border border-[#A5998F] bg-opacity-10">
//               <div className="flex items-center justify-center">
//                 <svg
//                   width="16"
//                   height="16"
//                   viewBox="0 0 16 16"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                   className="text-[#7B7B7B]"
//                 >
//                   <path
//                     d="M7.25 12.5C10.1495 12.5 12.5 10.1495 12.5 7.25C12.5 4.35051 10.1495 2 7.25 2C4.35051 2 2 4.35051 2 7.25C2 10.1495 4.35051 12.5 7.25 12.5Z"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                   <path
//                     d="M10.9622 10.9625L13.9997 14.0001"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                 </svg>
//               </div>
//               <input
//                 type="text"
//                 placeholder="Search Regulations"
//                 value={searchValue}
//                 onChange={(e) => setSearchValue(e.target.value)}
//                 className="w-full bg-transparent outline-none text-[#7B7B7B] pl-1"
//               />
//             </div>
//           </div>

//           {/* Latest Regulations Header */}
//           <div className="text-[#7B7B7B] text-sm font-medium mb-4">
//             Latest Regulations
//           </div>

//           {/* Regulations List */}
//           <div className="flex flex-col gap-4">
//             {isLoading ? (
//               <div className="text-center py-6">Loading regulations...</div>
//             ) : regulations.length === 0 ? (
//               <div className="text-center py-6 text-[#7B7B7B]">
//                 No regulations found
//               </div>
//             ) : (
//               regulations.map((regulation) => (
//                 <div
//                   key={regulation.id}
//                   onClick={() => handleRegulationSelect(regulation)}
//                   className="flex flex-col justify-center items-start gap-2 p-3 w-full rounded-lg group hover:bg-white hover:-translate-y-1 cursor-pointer transition-all duration-200  hover:shadow-md"
//                 >
//                   <h3 className="text-[#494949] text-base md:text-lg font-medium group-hover:text-[#494949]">
//                     {regulation.title}
//                   </h3>
//                   <p className="text-[#7B7B7B] text-xs group-hover:text-[#7B7B7B]">
//                     {regulation.date}
//                   </p>
//                 </div>
//               ))
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

// "use client";

// import { useState, useEffect } from "react";
// import { RegulationInfo, ReferenceItem } from "@/types/types";
// import { ActionItemsDetail } from "@/components/ActionItemsDetail";
// import { useAuth } from "@/contexts/AuthContext";
// import { ACTION_ITEMS_SEARCH_ENDPOINT, ACTION_ITEMS_ITEM_ENDPOINT, ACTION_ITEMS_RECENT_DOCS_ENDPOINT } from "@/app/utils/Api";

// interface ActionItemsViewProps {
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   onViewDetail: () => void;
//   setReferences: (references: ReferenceItem[]) => void;
//   showDetailView: boolean;
//   setSelectedSpecificRef?: (reference: any) => void; // Add this prop
// }

// export function ActionItemsView({
//   setActiveView,
//   setCurrentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   onViewDetail,
//   setReferences,
//   showDetailView,
//   setSelectedSpecificRef,
// }: ActionItemsViewProps) {
//   const [searchValue, setSearchValue] = useState("");
//   const [regulations, setRegulations] = useState<RegulationInfo[]>([]);
//   const [selectedRegulation, setSelectedRegulation] =
//     useState<RegulationInfo | null>(null);
//   const [actionItemsData, setActionItemsData] = useState(null);
//   const [isLoading, setIsLoading] = useState(false);
//   const [isLoadingActionItems, setIsLoadingActionItems] = useState(false);
//   const { user } = useAuth();
//   const username = user?.username;

//   // Fetch regulations on initial load
//   useEffect(() => {
//     fetchRegulations();
//   }, []);

//   // Updated fetchRegulations function to use the recent documents API
//   const fetchRegulations = async () => {
//     setIsLoading(true);
//     try {
//       // Use the recent documents API endpoint
//       const response = await fetch(ACTION_ITEMS_RECENT_DOCS_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           limit: 5 // You can adjust this number based on how many recent documents you want to fetch
//         }),
//       });

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }
      
//       const data = await response.json();
      
//       console.log(data);
//       // Map the API response to match your RegulationInfo format
//       const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
//         id: doc.id || doc.document_number,
//         title: doc.title || "",
//         date: doc.dat || "Mar 2025", // Use actual date from API or fallback
//         department: doc.department || "Department of Regulation", // Use actual department from API or fallback
//         docCode: doc.id || "",
//       }));
      
//       setRegulations(mappedRegulations);
//     } catch (error) {
//       console.error("Error fetching recent regulations:", error);
      
//       // Fallback to recent regulations data in case of API error
//       const mockRegulations: RegulationInfo[] = [
//         {
//           id: "RBI/DCM/2025-26/136",
//           title: "Master Direction on Framework of incentives for Currency Distribution & Exchange Scheme for bank branches including currency chests",
//           date: "2025-04-24",
//           department: "Department of Currency Management",
//           docCode: "RBI/DCM/2025-26/136",
//         },
//         {
//           id: "RBI/FED/2025-26/135",
//           title: "FED Master Direction No.04/2025-26 - Master Directions - Compounding of Contraventions under FEMA, 1999",
//           date: "2025-04-22",
//           department: "Foreign Exchange Department",
//           docCode: "RBI/FED/2025-26/135"
//         },
//         {
//           id: "RBI/DCM/2025-26/131 DCM (CC) No.G-1/03.44.001/2025-26",
//           title: "Master Direction – Scheme of Penalties for bank branches and currency chests for deficiency in rendering customer service to the members of public",
//           date: "2025-04-01",
//           department: "Department of Currency Management",
//           docCode: "RBI/DCM/2025-26/131 DCM (CC) No.G-1/03.44.001/2025-26",
//         },
//         {
//           id: "RBI/DCM/2025-26/130",
//           title: "Master Direction on Penal Provisions in reporting of transactions / balances at Currency Chests",
//           date: "2025-04-01",
//           department: "Department of Currency Management",
//           docCode: "RBI/DCM/2025-26/130"
//         },
//         {
//           id: "RBI/2025-26/132 DCM (FNVD)/G4/16.01.05/2025-26",
//           title: "Master Direction on Counterfeit Notes, 2025 – Detection, Reporting and Monitoring",
//           date: "2025-04-01",
//           department: "Department of Currency Management",
//           docCode: "RBI/2025-26/132 DCM (FNVD)/G4/16.01.05/2025-26"
//         },
//       ];
      
//       setRegulations(mockRegulations);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // New function to search regulations via API
//   const searchRegulations = async (query: string) => {
//     setIsLoading(true);
//     try {
//       // Use the search API endpoint
//       const response = await fetch(ACTION_ITEMS_SEARCH_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_name: query
//         }),
//       });

//       if (!response.ok) {
//         // If API fails, revert to mock data that's filtered
//         const filteredMockRegs = regulations.filter((reg) =>
//           reg.title.toLowerCase().includes(query.toLowerCase()) ||
//           reg.docCode.toLowerCase().includes(query.toLowerCase())
//         );
//         return filteredMockRegs;
//       }
      
//       const data = await response.json();
      
//       // Map the API response to match our RegulationInfo format
//       const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
//         id: doc._id,
//         title: doc.document_title || "",
//         date: "Mar 2025", // You might want to fetch this from API too
//         department: "Department of Regulation", // You might want to fetch this from API too 
//         docCode: doc.document_number || "",
//         content: "",
//       }));
      
//       return mappedRegulations;
//     } catch (error) {
//       console.error("Error searching regulations:", error);
//       // Fallback to existing regulations in case of error
//       const filteredMockRegs = regulations.filter((reg) =>
//         reg.title.toLowerCase().includes(query.toLowerCase()) ||
//         reg.docCode.toLowerCase().includes(query.toLowerCase())
//       );
//       return filteredMockRegs;
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Function to fetch action items for a specific regulation
//   const fetchActionItems = async (documentNumber: string) => {
//     setIsLoadingActionItems(true);
//     try {
//       const response = await fetch(ACTION_ITEMS_ITEM_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           doc_id: documentNumber,
//           username: username
//         }),
//       });

//       if (!response.ok) {
//         throw new Error("Failed to fetch action items");
//       }
//       const data = await response.json();
//       setActionItemsData(data);
//       setselectedDocCode(documentNumber);

//       // console.log(data);

//       // Map API response to ReferenceItem format using new obligations structure
//       if (data && data.obligations) {
//         const mappedReferences: ReferenceItem[] = data.obligations.map(
//           (item: any, index: number) => ({
//             Type: "",
//             addressee: "",
//             chunk_id: item.positions && item.positions.length > 0 ? item.positions[0].page_number : index,
//             date_of_issue: "",
//             dept: "",
//             document_code: data.document_number || "",
//             id: `ref_${index}`,
//             pdf_filename: "",
//             pdf_link: "",
//             // Convert positions to the format expected by ResourcesSidebar
//             positions: item.positions ? item.positions.map((pos: any) => ({
//               page: pos.page_number,
//               bboxes: [pos.bbox]
//             })) : [],
//             revision_s3_url: data.s3_url || "",
//             section_summary: item.obligation || "",
//             title: data.document_title || "",
//             document_id: data.document_number || "",
//           })
//         );
//         console.log("mappedReferences:",mappedReferences);
        
//         // Set all references for the ResourcesSidebar to handle navigation
//         setReferences(mappedReferences);
        
//         // Open the detail view but don't set specific reference yet
//         // The specific reference will be set when source button is clicked
//         onViewDetail();
//       } else {
//         // Set empty references if no obligations
//         setReferences([]);
//       }
//     } catch (error) {
//       console.error("Error fetching action items:", error);
//       // Fallback to mock data in case of error
//       setActionItemsData(null);
//       setReferences([]);
//     } finally {
//       setIsLoadingActionItems(false);
//     }
//   };

//   // Handle search input change with debounce
//   useEffect(() => {
//     const delayDebounce = setTimeout(async () => {
//       if (searchValue.trim() !== "") {
//         const searchResults = await searchRegulations(searchValue);
//         setRegulations(searchResults);
//       } else {
//         // If search is cleared, fetch default regulations
//         fetchRegulations();
//       }
//     }, 500); // 500ms debounce

//     return () => clearTimeout(delayDebounce);
//   }, [searchValue]);

//   // Handle regulation selection
//   const handleRegulationSelect = async (regulation: RegulationInfo) => {
//     setSelectedRegulation(regulation);
//     setCurrentRegulation(regulation);

//     // Fetch action items for the selected regulation
//     await fetchActionItems(regulation.docCode);

//     // onViewDetail(); // Show the detail panel
//   };

//   // Go back to home
//   const handleBackToHome = () => {
//     setActiveView("home");
//   };

//   if (selectedRegulation) {
//     return (
//       <ActionItemsDetail
//         regulation={selectedRegulation}
//         actionItemsData={actionItemsData}
//         isLoading={isLoadingActionItems}
//         onBack={() => {
//           setSelectedRegulation(null);
//           setActionItemsData(null);
//         }}
//         onViewDetail={onViewDetail}
//         showDetailView={showDetailView}
//         setReferences={setReferences}
//         setSelectedSpecificRef={setSelectedSpecificRef || (() => {})}
//       />
//     );
//   }

//   return (
//     <div className="flex flex-col items-start w-full px-4 bg-transparent">
//       {/* Back button */}

//       {/* Main container with exact Figma specs */}
//       <div
//         className="flex flex-col items-start w-full max-w-[635px] mx-auto py-5 px-8 gap-1 border border-[#A5998F] rounded-xl bg-transparent"
//         style={{ minHeight: "832px" }}
//       >
//         <div className="w-full">
//           <h1 className="text-[#494949] text-2xl md:text-3xl font-semibold text-center mb-8">
//             Select the Regulation to <br />
//             Generate Action Items for
//           </h1>

//           {/* Search Input - exact Figma specs */}
//           <div className="relative w-full mb-6">
//             <div className="flex items-center gap-1 w-full p-3 md:p-4 rounded-xl border border-[#A5998F] bg-opacity-10">
//               <div className="flex items-center justify-center">
//                 <svg
//                   width="16"
//                   height="16"
//                   viewBox="0 0 16 16"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                   className="text-[#7B7B7B]"
//                 >
//                   <path
//                     d="M7.25 12.5C10.1495 12.5 12.5 10.1495 12.5 7.25C12.5 4.35051 10.1495 2 7.25 2C4.35051 2 2 4.35051 2 7.25C2 10.1495 4.35051 12.5 7.25 12.5Z"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                   <path
//                     d="M10.9622 10.9625L13.9997 14.0001"
//                     stroke="currentColor"
//                     strokeWidth="1.5"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                   />
//                 </svg>
//               </div>
//               <input
//                 type="text"
//                 placeholder="Search Regulations"
//                 value={searchValue}
//                 onChange={(e) => setSearchValue(e.target.value)}
//                 className="w-full bg-transparent outline-none text-[#7B7B7B] pl-1"
//               />
//             </div>
//           </div>

//           {/* Latest Regulations Header */}
//           <div className="text-[#7B7B7B] text-sm font-medium mb-4">
//             Latest Regulations
//           </div>

//           {/* Regulations List */}
//           <div className="flex flex-col gap-4">
//             {isLoading ? (
//               <div className="text-center py-6">Loading regulations...</div>
//             ) : regulations.length === 0 ? (
//               <div className="text-center py-6 text-[#7B7B7B]">
//                 No regulations found
//               </div>
//             ) : (
//               regulations.map((regulation) => (
//                 <div
//                   key={regulation.id}
//                   onClick={() => handleRegulationSelect(regulation)}
//                   className="flex flex-col justify-center items-start gap-2 p-3 w-full rounded-lg group hover:bg-white hover:-translate-y-1 cursor-pointer transition-all duration-200  hover:shadow-md"
//                 >
//                   <h3 className="text-[#494949] text-base md:text-lg font-medium group-hover:text-[#494949]">
//                     {regulation.title}
//                   </h3>
//                   <p className="text-[#7B7B7B] text-xs group-hover:text-[#7B7B7B]">
//                     {regulation.date}
//                   </p>
//                 </div>
//               ))
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }



"use client";

import { useState, useEffect } from "react";
import { RegulationInfo, ReferenceItem } from "@/types/main_types";
import { ActionItemsDetail } from "@/components/ActionItemsDetail";
import { useAuth } from "@/contexts/AuthContext";
import { ACTION_ITEMS_SEARCH_ENDPOINT, ACTION_ITEMS_ITEM_ENDPOINT, ACTION_ITEMS_RECENT_DOCS_ENDPOINT, ACTION_ITEMS_EDIT_ENDPOINT } from "@/app/utils/Api";

interface ActionItem {
  _id: string;
  document_number: string;
  document_title: string;
  s3_url: string;
  username: string;
  document_id?: string;
}


interface ActionItemsViewProps {
  setActiveView: (view: "home" | "chat" | "action-items") => void;
  setCurrentRegulation: (regulation: RegulationInfo | null) => void;
  selectedDocCode: string | null;
  setselectedDocCode: (doccode: string | null) => void;
  onViewDetail: () => void;
  setReferences: (references: ReferenceItem[]) => void;
  showDetailView: boolean;
  setSelectedSpecificRef?: (reference: any) => void;
  isChatHistoryCollapsed?: boolean;
  onClose: () => void;
  selectedActionItem?: ActionItem | null;
  setSelectedActionItem?: (actionItem: ActionItem | null) => void;
}

export function ActionItemsView({
  setActiveView,
  setCurrentRegulation,
  selectedDocCode,
  setselectedDocCode,
  onViewDetail,
  setReferences,
  showDetailView,
  setSelectedSpecificRef,
  isChatHistoryCollapsed,
  onClose,
  selectedActionItem, // ADD THIS LINE
  setSelectedActionItem, // ADD THIS LINE
}: ActionItemsViewProps) {
  const [searchValue, setSearchValue] = useState("");
  const [regulations, setRegulations] = useState<RegulationInfo[]>([]);
  const [selectedRegulation, setSelectedRegulation] =
    useState<RegulationInfo | null>(null);
  const [actionItemsData, setActionItemsData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingActionItems, setIsLoadingActionItems] = useState(false);
  const { user } = useAuth();
  const username = user?.username;

  // Fetch regulations on initial load
  useEffect(() => {
    fetchRegulations();
  }, []);

  useEffect(() => {
  // Check if we have a selected action item from chat history
  if (selectedActionItem) {
    // Create a regulation object from the selected action item
    const regulation: RegulationInfo = {
      id: selectedActionItem.document_number || selectedActionItem._id,
      title: selectedActionItem.document_title,
      date: "Today", // Using today's date as specified
      department: "Department of Regulation", // Default department
      docCode: selectedActionItem.document_number || selectedActionItem._id,
    };
    
    // Set this as the selected regulation and fetch its action items
    setSelectedRegulation(regulation);
    setCurrentRegulation(regulation);
    
    // Fetch action items for this document
    fetchActionItems(regulation.docCode);
    
    // Clear the selected action item since we've processed it
    if (setSelectedActionItem) {
      setSelectedActionItem(null);
    }
  }
}, [selectedActionItem, setCurrentRegulation, setSelectedActionItem]);
// Add this function to ActionItemsView.tsx

const saveActionItems = async (actionItemsData: any) => {
  if (!username || !actionItemsData) {
    console.log('Missing username or action items data');
    return;
  }

  try {
    // Use the data as-is from the API response (no processing needed for viewing)
    const payload = {
      username: username,
      updated_actionables: {
        document_number: actionItemsData.document_number,
        document_title: actionItemsData.document_title,
        obligations: actionItemsData.obligations, // Use original obligations directly
        s3_url: actionItemsData.s3_url
      }
    };

    const response = await fetch(ACTION_ITEMS_EDIT_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error('Failed to save action items');
    }

    const result = await response.json();
    console.log('Action items saved successfully:', result);
    return result;
  } catch (error) {
    console.error('Error saving action items:', error);
    // Don't throw error here as this is just for recording history
    // The main functionality should continue working
  }
};
  // Updated fetchRegulations function to use the recent documents API
  const fetchRegulations = async () => {
    setIsLoading(true);
    try {
      // Use the recent documents API endpoint
      const response = await fetch(ACTION_ITEMS_RECENT_DOCS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          limit: 5 // You can adjust this number based on how many recent documents you want to fetch
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      console.log(data);
      // Map the API response to match your RegulationInfo format
      const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
        id: doc.id || doc.document_number,
        title: doc.title || "",
        date: doc.dat || "Mar 2025", // Use actual date from API or fallback
        department: doc.department || "Department of Regulation", // Use actual department from API or fallback
        docCode: doc.id || "",
      }));
      
      setRegulations(mappedRegulations);
    } catch (error) {
      console.error("Error fetching recent regulations:", error);
      
      // Fallback to recent regulations data in case of API error
      const mockRegulations: RegulationInfo[] = [
        {
          id: "RBI/DCM/2025-26/136",
          title: "Master Direction on Framework of incentives for Currency Distribution & Exchange Scheme for bank branches including currency chests",
          date: "2025-04-24",
          department: "Department of Currency Management",
          docCode: "RBI/DCM/2025-26/136",
        },
        {
          id: "RBI/FED/2025-26/135",
          title: "FED Master Direction No.04/2025-26 - Master Directions - Compounding of Contraventions under FEMA, 1999",
          date: "2025-04-22",
          department: "Foreign Exchange Department",
          docCode: "RBI/FED/2025-26/135"
        },
        {
          id: "RBI/DCM/2025-26/131 DCM (CC) No.G-1/03.44.001/2025-26",
          title: "Master Direction – Scheme of Penalties for bank branches and currency chests for deficiency in rendering customer service to the members of public",
          date: "2025-04-01",
          department: "Department of Currency Management",
          docCode: "RBI/DCM/2025-26/131 DCM (CC) No.G-1/03.44.001/2025-26",
        },
        {
          id: "RBI/DCM/2025-26/130",
          title: "Master Direction on Penal Provisions in reporting of transactions / balances at Currency Chests",
          date: "2025-04-01",
          department: "Department of Currency Management",
          docCode: "RBI/DCM/2025-26/130"
        },
        {
          id: "RBI/2025-26/132 DCM (FNVD)/G4/16.01.05/2025-26",
          title: "Master Direction on Counterfeit Notes, 2025 – Detection, Reporting and Monitoring",
          date: "2025-04-01",
          department: "Department of Currency Management",
          docCode: "RBI/2025-26/132 DCM (FNVD)/G4/16.01.05/2025-26"
        },
      ];
      
      setRegulations(mockRegulations);
    } finally {
      setIsLoading(false);
    }
  };

  // New function to search regulations via API
  const searchRegulations = async (query: string) => {
    setIsLoading(true);
    try {
      // Use the search API endpoint
      const response = await fetch(ACTION_ITEMS_SEARCH_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doc_name: query
        }),
      });

      if (!response.ok) {
        // If API fails, revert to mock data that's filtered
        const filteredMockRegs = regulations.filter((reg) =>
          reg.title.toLowerCase().includes(query.toLowerCase()) ||
          reg.docCode.toLowerCase().includes(query.toLowerCase())
        );
        return filteredMockRegs;
      }
      
      const data = await response.json();
      
      // Map the API response to match our RegulationInfo format
      const mappedRegulations: RegulationInfo[] = data.map((doc: any) => ({
        id: doc._id,
        title: doc.document_title || "",
        date: "Mar 2025", // You might want to fetch this from API too
        department: "Department of Regulation", // You might want to fetch this from API too 
        docCode: doc.document_number || "",
        content: "",
      }));
      
      return mappedRegulations;
    } catch (error) {
      console.error("Error searching regulations:", error);
      // Fallback to existing regulations in case of error
      const filteredMockRegs = regulations.filter((reg) =>
        reg.title.toLowerCase().includes(query.toLowerCase()) ||
        reg.docCode.toLowerCase().includes(query.toLowerCase())
      );
      return filteredMockRegs;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch action items for a specific regulation
  const fetchActionItems = async (documentNumber: string) => {
    setIsLoadingActionItems(true);
    try {
      const response = await fetch(ACTION_ITEMS_ITEM_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doc_id: documentNumber,
          username: username
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch action items");
      }
      const data = await response.json();
      setActionItemsData(data);
      setselectedDocCode(documentNumber);

      // console.log(data);
      await saveActionItems(data);

      // Map API response to ReferenceItem format using new obligations structure
      if (data && data.obligations) {
        const mappedReferences: ReferenceItem[] = data.obligations.map(
          (item: any, index: number) => ({
            Type: "",
            addressee: "",
            chunk_id: item.positions && item.positions.length > 0 ? item.positions[0].page_number : index,
            date_of_issue: "",
            dept: "",
            document_code: data.document_number || "",
            id: `ref_${index}`,
            pdf_filename: "",
            pdf_link: "",
            // Convert positions to the format expected by ResourcesSidebar
            positions: item.positions ? item.positions.map((pos: any) => ({
              page: pos.page_number,
              bboxes: pos.bboxes,
            })) : [],
            revision_s3_url: data.s3_url || "",
            section_summary: item.obligation || "",
            title: data.document_title || "",
            document_id: data.document_number || "",
          })
        );
        console.log("mappedReferences:",mappedReferences);
        
        // Set all references for the ResourcesSidebar to handle navigation
        setReferences(mappedReferences);
        
        // Open the detail view but don't set specific reference yet
        // The specific reference will be set when source button is clicked
        onViewDetail();
      } else {
        // Set empty references if no obligations
        setReferences([]);
      }
    } catch (error) {
      console.error("Error fetching action items:", error);
      // Fallback to mock data in case of error
      setActionItemsData(null);
      setReferences([]);
    } finally {
      setIsLoadingActionItems(false);
    }
  };

  // Handle search input change with debounce
  useEffect(() => {
    const delayDebounce = setTimeout(async () => {
      if (searchValue.trim() !== "") {
        const searchResults = await searchRegulations(searchValue);
        setRegulations(searchResults);
      } else {
        // If search is cleared, fetch default regulations
        fetchRegulations();
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(delayDebounce);
  }, [searchValue]);

  // Handle regulation selection
const handleRegulationSelect = async (regulation: RegulationInfo) => {
  setSelectedRegulation(regulation);
  setCurrentRegulation(regulation);

  // Clear selected action item when manually selecting a regulation
  if (setSelectedActionItem) {
    setSelectedActionItem(null);
  }

  // Fetch action items for the selected regulation
  await fetchActionItems(regulation.docCode);

  // onViewDetail(); // Show the detail panel
};

  // Go back to home
  const handleBackToHome = () => {
    setActiveView("home");
    onClose();

  };

  if (selectedRegulation) {
    return (
      <ActionItemsDetail
        regulation={selectedRegulation}
        actionItemsData={actionItemsData}
        isLoading={isLoadingActionItems}
        onBack={() => {
          setSelectedRegulation(null);
          setActionItemsData(null);
        }}
        onViewDetail={onViewDetail}
        showDetailView={showDetailView}
        setReferences={setReferences}
        setSelectedSpecificRef={setSelectedSpecificRef || (() => {})}
        onClose={onClose}
      />
    );
  }

  return (
    <div className="flex flex-col items-start w-full px-4 bg-transparent">

      {/* Main container with exact Figma specs */}
      <div
        className="flex flex-col items-start w-full max-w-[635px] mx-auto py-5 px-8 gap-1 border border-[#A5998F] rounded-xl bg-transparent"
        style={{ minHeight: "832px" }}
      >
        <div className="w-full">
          <h1 className="text-[#494949] text-2xl md:text-3xl font-semibold text-center mb-8">
            Select the Regulation to <br />
            Generate Action Items for
          </h1>

          {/* Search Input - exact Figma specs */}
          <div className="relative w-full mb-6">
            <div className="flex items-center gap-1 w-full p-3 md:p-4 rounded-xl border border-[#A5998F] bg-opacity-10">
              <div className="flex items-center justify-center">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-[#7B7B7B]"
                >
                  <path
                    d="M7.25 12.5C10.1495 12.5 12.5 10.1495 12.5 7.25C12.5 4.35051 10.1495 2 7.25 2C4.35051 2 2 4.35051 2 7.25C2 10.1495 4.35051 12.5 7.25 12.5Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M10.9622 10.9625L13.9997 14.0001"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search Regulations"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="w-full bg-transparent outline-none text-[#7B7B7B] pl-1"
              />
            </div>
          </div>

          {/* Latest Regulations Header */}
          <div className="text-[#7B7B7B] text-sm font-medium mb-4">
            Latest Regulations
          </div>

          {/* Regulations List */}
          <div className="flex flex-col gap-4">
            {isLoading ? (
              <div className="text-center py-6">Loading regulations...</div>
            ) : regulations.length === 0 ? (
              <div className="text-center py-6 text-[#7B7B7B]">
                No regulations found
              </div>
            ) : (
              regulations.map((regulation) => (
                <div
                  key={regulation.id}
                  onClick={() => handleRegulationSelect(regulation)}
                  className="flex flex-col justify-center items-start gap-2 p-3 w-full rounded-lg group hover:bg-white hover:-translate-y-1 cursor-pointer transition-all duration-200  hover:shadow-md"
                >
                  <h3 className="text-[#494949] text-base md:text-lg font-medium group-hover:text-[#494949]">
                    {regulation.title}
                  </h3>
                  <p className="text-[#7B7B7B] text-xs group-hover:text-[#7B7B7B]">
                    {regulation.date}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}