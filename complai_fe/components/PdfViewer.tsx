import React, { useC<PERSON>back, useEffect, useRef, useImperativeHandle, forwardRef } from "react";
import {
  PdfLoader,
  PdfHighlighter,
  Highlight,
  AreaHighlight,
  Popup,
  IHighlight,
} from "react-pdf-highlighter";
import "react-pdf-highlighter/dist/style.css";

type Props = {
  url: string;
  highlights: IHighlight[];
};

const TriggerComponent = ({ onLoad }: { onLoad: () => void }) => {
  useEffect(() => {
    setTimeout(() => {
      onLoad?.();
    }, 400); // Keep the same 400ms delay as in the original approach
  }, [onLoad]);

  return null;
};

const resetHash = () => {
  document.location.hash = "";
};

const parseIdFromHash = () =>
  document.location.hash.slice("#highlight-".length);

// Use forwardRef to allow the parent to call methods on this component
const PdfViewer = forwardRef(({ url, highlights = [] }: Props, ref) => {
  const scrollViewerTo = useRef((highlight: IHighlight) => {});

  const getHighlightById = (id: string) => {
    // Following the exact approach from the original implementation:
    // Instead of looking for the specific ID, find the highlight with the minimum page number
    const minPageNumber = Math.min(
      ...highlights.map((h) => h.position.pageNumber)
    );
    const index = highlights.findIndex(
      (highlight) => highlight.position.pageNumber === minPageNumber
    );
    return index !== -1 ? highlights[index] : null;
  };

  const scrollToHighlightFromHash = useCallback(() => {
    const highlight = getHighlightById(parseIdFromHash());
    if (highlight) {
      scrollViewerTo.current(highlight);
    }
  }, [highlights]);

  // Expose the scrollToHighlight method to the parent component
  useImperativeHandle(ref, () => ({
    scrollToHighlight: (highlightId: string) => {
      // Using the same approach as in the original implementation
      const highlight = getHighlightById(highlightId);
      if (highlight) {
        scrollViewerTo.current(highlight);
      }
    },
  }));

  return (
    <div
      className="App"
      style={{ display: "flex", height: "100%", width: "100%" }}
    >
      <div
        style={{
          height: "100%",
          width: "100%",
          position: "relative",
        }}
      >
        <PdfLoader url={url} beforeLoad={<>loading</>}>
          {(pdfDocument) => (
            <>
              <TriggerComponent onLoad={() => scrollToHighlightFromHash()} />

              <PdfHighlighter
                pdfDocument={pdfDocument}
                enableAreaSelection={(event) => event.altKey}
                onScrollChange={resetHash}
                scrollRef={(scrollTo) => {
                  scrollViewerTo.current = scrollTo;
                }}
                onSelectionFinished={() => <></>}
                highlightTransform={(
                  highlight,
                  index,
                  setTip,
                  hideTip,
                  viewportToScaled,
                  screenshot,
                  isScrolledTo
                ) => {
                  const isTextHighlight = !highlight.content?.image;

                  const component = isTextHighlight ? (
                    <Highlight
                      isScrolledTo={isScrolledTo}
                      position={highlight.position}
                      comment={highlight.comment}
                    />
                  ) : (
                    <AreaHighlight
                      isScrolledTo={isScrolledTo}
                      highlight={highlight}
                      onChange={() => {}}
                    />
                  );

                  return (
                    <Popup
                      popupContent={<></>}
                      onMouseOver={(popupContent) =>
                        setTip(highlight, (highlight) => popupContent)
                      }
                      onMouseOut={hideTip}
                      key={index}
                    >
                      {component}
                    </Popup>
                  );
                }}
                highlights={highlights}
              />
            </>
          )}
        </PdfLoader>
      </div>
    </div>
  );
});

export default PdfViewer;
