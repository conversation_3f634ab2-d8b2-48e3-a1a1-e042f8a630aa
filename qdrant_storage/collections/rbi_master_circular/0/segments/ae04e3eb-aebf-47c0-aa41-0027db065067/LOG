2025/06/30-11:26:13.492419 10108 RocksDB version: 9.9.3
2025/06/30-11:26:13.492473 10108 Compile date 2024-12-05 01:25:31
2025/06/30-11:26:13.492474 10108 DB SUMMARY
2025/06/30-11:26:13.492475 10108 Host name (Env):  3ed34e58cde5
2025/06/30-11:26:13.492475 10108 DB Session ID:  HLH6JCEFCE6BMGI16FO5
2025/06/30-11:26:13.492764 10108 CURRENT file:  CURRENT
2025/06/30-11:26:13.492767 10108 MANIFEST file:  MANIFEST-000069 size: 2479 Bytes
2025/06/30-11:26:13.492768 10108 SST files in ./storage/collections/rbi_master_circular/0/segments/ae04e3eb-aebf-47c0-aa41-0027db065067 dir, Total Num: 22, files: 000016.sst 000018.sst 000020.sst 000022.sst 000024.sst 000026.sst 000028.sst 000030.sst 000032.sst 
2025/06/30-11:26:13.492768 10108 Write Ahead Log file in ./storage/collections/rbi_master_circular/0/segments/ae04e3eb-aebf-47c0-aa41-0027db065067: 
2025/06/30-11:26:13.492769 10108                         Options.error_if_exists: 0
2025/06/30-11:26:13.492769 10108                       Options.create_if_missing: 1
2025/06/30-11:26:13.492770 10108                         Options.paranoid_checks: 1
2025/06/30-11:26:13.492770 10108             Options.flush_verify_memtable_count: 1
2025/06/30-11:26:13.492771 10108          Options.compaction_verify_record_count: 1
2025/06/30-11:26:13.492771 10108                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-11:26:13.492772 10108        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-11:26:13.492772 10108                                     Options.env: 0xffff86231620
2025/06/30-11:26:13.492773 10108                                      Options.fs: PosixFileSystem
2025/06/30-11:26:13.492773 10108                                Options.info_log: 0xffff91b65000
2025/06/30-11:26:13.492774 10108                Options.max_file_opening_threads: 16
2025/06/30-11:26:13.492774 10108                              Options.statistics: (nil)
2025/06/30-11:26:13.492775 10108                               Options.use_fsync: 0
2025/06/30-11:26:13.492776 10108                       Options.max_log_file_size: 1048576
2025/06/30-11:26:13.492776 10108                  Options.max_manifest_file_size: 1073741824
2025/06/30-11:26:13.492777 10108                   Options.log_file_time_to_roll: 0
2025/06/30-11:26:13.492777 10108                       Options.keep_log_file_num: 1
2025/06/30-11:26:13.492778 10108                    Options.recycle_log_file_num: 0
2025/06/30-11:26:13.492778 10108                         Options.allow_fallocate: 1
2025/06/30-11:26:13.492779 10108                        Options.allow_mmap_reads: 0
2025/06/30-11:26:13.492779 10108                       Options.allow_mmap_writes: 0
2025/06/30-11:26:13.492780 10108                        Options.use_direct_reads: 0
2025/06/30-11:26:13.492815 10108                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-11:26:13.492816 10108          Options.create_missing_column_families: 1
2025/06/30-11:26:13.492816 10108                              Options.db_log_dir: 
2025/06/30-11:26:13.492817 10108                                 Options.wal_dir: 
2025/06/30-11:26:13.492817 10108                Options.table_cache_numshardbits: 6
2025/06/30-11:26:13.492818 10108                         Options.WAL_ttl_seconds: 0
2025/06/30-11:26:13.492818 10108                       Options.WAL_size_limit_MB: 0
2025/06/30-11:26:13.492819 10108                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-11:26:13.492819 10108             Options.manifest_preallocation_size: 4194304
2025/06/30-11:26:13.492820 10108                     Options.is_fd_close_on_exec: 1
2025/06/30-11:26:13.492820 10108                   Options.advise_random_on_open: 1
2025/06/30-11:26:13.492821 10108                    Options.db_write_buffer_size: 0
2025/06/30-11:26:13.492821 10108                    Options.write_buffer_manager: 0xffff91a14840
2025/06/30-11:26:13.492822 10108           Options.random_access_max_buffer_size: 1048576
2025/06/30-11:26:13.492822 10108                      Options.use_adaptive_mutex: 0
2025/06/30-11:26:13.492823 10108                            Options.rate_limiter: (nil)
2025/06/30-11:26:13.492824 10108     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-11:26:13.492825 10108                       Options.wal_recovery_mode: 0
2025/06/30-11:26:13.492825 10108                  Options.enable_thread_tracking: 0
2025/06/30-11:26:13.492826 10108                  Options.enable_pipelined_write: 0
2025/06/30-11:26:13.492827 10108                  Options.unordered_write: 0
2025/06/30-11:26:13.492827 10108         Options.allow_concurrent_memtable_write: 1
2025/06/30-11:26:13.492828 10108      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-11:26:13.492828 10108             Options.write_thread_max_yield_usec: 100
2025/06/30-11:26:13.492835 10108            Options.write_thread_slow_yield_usec: 3
2025/06/30-11:26:13.492836 10108                               Options.row_cache: None
2025/06/30-11:26:13.492836 10108                              Options.wal_filter: None
2025/06/30-11:26:13.492837 10108             Options.avoid_flush_during_recovery: 0
2025/06/30-11:26:13.492838 10108             Options.allow_ingest_behind: 0
2025/06/30-11:26:13.492838 10108             Options.two_write_queues: 0
2025/06/30-11:26:13.492839 10108             Options.manual_wal_flush: 0
2025/06/30-11:26:13.492839 10108             Options.wal_compression: 0
2025/06/30-11:26:13.492840 10108             Options.background_close_inactive_wals: 0
2025/06/30-11:26:13.492840 10108             Options.atomic_flush: 0
2025/06/30-11:26:13.492841 10108             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-11:26:13.492841 10108             Options.prefix_seek_opt_in_only: 0
2025/06/30-11:26:13.492842 10108                 Options.persist_stats_to_disk: 0
2025/06/30-11:26:13.492843 10108                 Options.write_dbid_to_manifest: 1
2025/06/30-11:26:13.492843 10108                 Options.write_identity_file: 1
2025/06/30-11:26:13.492844 10108                 Options.log_readahead_size: 0
2025/06/30-11:26:13.492844 10108                 Options.file_checksum_gen_factory: Unknown
2025/06/30-11:26:13.492845 10108                 Options.best_efforts_recovery: 0
2025/06/30-11:26:13.492845 10108                Options.max_bgerror_resume_count: 2147483647
2025/06/30-11:26:13.492846 10108            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-11:26:13.492846 10108             Options.allow_data_in_errors: 0
2025/06/30-11:26:13.492847 10108             Options.db_host_id: __hostname__
2025/06/30-11:26:13.492848 10108             Options.enforce_single_del_contracts: true
2025/06/30-11:26:13.492848 10108             Options.metadata_write_temperature: kUnknown
2025/06/30-11:26:13.492849 10108             Options.wal_write_temperature: kUnknown
2025/06/30-11:26:13.492849 10108             Options.max_background_jobs: 2
2025/06/30-11:26:13.492850 10108             Options.max_background_compactions: -1
2025/06/30-11:26:13.492850 10108             Options.max_subcompactions: 1
2025/06/30-11:26:13.492851 10108             Options.avoid_flush_during_shutdown: 0
2025/06/30-11:26:13.492851 10108           Options.writable_file_max_buffer_size: 1048576
2025/06/30-11:26:13.492852 10108             Options.delayed_write_rate : 16777216
2025/06/30-11:26:13.492852 10108             Options.max_total_wal_size: 0
2025/06/30-11:26:13.492853 10108             Options.delete_obsolete_files_period_micros: 180000000
2025/06/30-11:26:13.492854 10108                   Options.stats_dump_period_sec: 600
2025/06/30-11:26:13.492854 10108                 Options.stats_persist_period_sec: 600
2025/06/30-11:26:13.492855 10108                 Options.stats_history_buffer_size: 1048576
2025/06/30-11:26:13.492855 10108                          Options.max_open_files: 256
2025/06/30-11:26:13.492856 10108                          Options.bytes_per_sync: 0
2025/06/30-11:26:13.492856 10108                      Options.wal_bytes_per_sync: 0
2025/06/30-11:26:13.492857 10108                   Options.strict_bytes_per_sync: 0
2025/06/30-11:26:13.492857 10108       Options.compaction_readahead_size: 2097152
2025/06/30-11:26:13.492858 10108                  Options.max_background_flushes: -1
2025/06/30-11:26:13.492859 10108 Options.daily_offpeak_time_utc: 
2025/06/30-11:26:13.492860 10108 Compression algorithms supported:
2025/06/30-11:26:13.492860 10108 	kZSTD supported: 0
2025/06/30-11:26:13.492861 10108 	kXpressCompression supported: 0
2025/06/30-11:26:13.492861 10108 	kBZip2Compression supported: 0
2025/06/30-11:26:13.492862 10108 	kZSTDNotFinalCompression supported: 0
2025/06/30-11:26:13.492862 10108 	kLZ4Compression supported: 1
2025/06/30-11:26:13.492863 10108 	kZlibCompression supported: 0
2025/06/30-11:26:13.492864 10108 	kLZ4HCCompression supported: 1
2025/06/30-11:26:13.492864 10108 	kSnappyCompression supported: 1
2025/06/30-11:26:13.492865 10108 Fast CRC32 supported: Not supported on x86
2025/06/30-11:26:13.492865 10108 DMutex implementation: pthread_mutex_t
2025/06/30-11:26:13.492866 10108 Jemalloc supported: 0
2025/06/30-11:26:13.494164 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494195 10108           Options.merge_operator: None
2025/06/30-11:26:13.494196 10108        Options.compaction_filter: None
2025/06/30-11:26:13.494197 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494197 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494198 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494198 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494209 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494320 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494321 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494322 10108          Options.compression: LZ4
2025/06/30-11:26:13.494323 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494323 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494324 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494325 10108             Options.num_levels: 7
2025/06/30-11:26:13.494325 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494326 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494327 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494327 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494328 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494329 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494330 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494330 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494331 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494333 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494333 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494334 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494335 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494335 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494336 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494336 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494337 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494338 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494338 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494339 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494339 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494340 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494340 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494341 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494342 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494342 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494343 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494343 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494344 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494345 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494345 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494346 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494347 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494347 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494348 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494349 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494349 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494350 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494351 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494351 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494352 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494353 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494353 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494354 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494354 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494355 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494355 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494356 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494357 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494358 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494358 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494359 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494360 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494361 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494361 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494363 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494363 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494364 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494364 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494365 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.494365 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494366 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494366 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494367 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494368 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494368 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494369 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.494369 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494370 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494370 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494371 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494372 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.494372 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.494373 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494374 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494374 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494375 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494376 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494394 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494395 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494395 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494396 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494541 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494542 10108           Options.merge_operator: None
2025/06/30-11:26:13.494543 10108        Options.compaction_filter: None
2025/06/30-11:26:13.494544 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494544 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494545 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494545 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494552 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494554 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494554 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494555 10108          Options.compression: LZ4
2025/06/30-11:26:13.494556 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494556 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494557 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494557 10108             Options.num_levels: 7
2025/06/30-11:26:13.494558 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494558 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494559 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494559 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494560 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494561 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494562 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494563 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494563 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494564 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494564 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494565 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494565 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494566 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494567 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494567 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494568 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494569 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494569 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494570 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494571 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494571 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494572 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494573 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494573 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494574 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494574 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494575 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494575 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494576 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494577 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494577 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494578 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494578 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494579 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494580 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494580 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494581 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494581 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494583 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494583 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494584 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494584 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494585 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494586 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494586 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494587 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494587 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494588 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494589 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494589 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494590 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494591 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494592 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494592 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494593 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494594 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494594 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494595 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494595 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.494596 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494596 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494597 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494598 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494598 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494599 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494599 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.494600 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494601 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494602 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494602 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494603 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.494604 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.494605 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494605 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494606 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494606 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494607 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494617 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494618 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494619 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494620 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494817 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494818 10108           Options.merge_operator: None
2025/06/30-11:26:13.494818 10108        Options.compaction_filter: None
2025/06/30-11:26:13.494819 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494820 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494821 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494821 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494827 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494828 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494829 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494830 10108          Options.compression: LZ4
2025/06/30-11:26:13.494830 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494831 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494832 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494832 10108             Options.num_levels: 7
2025/06/30-11:26:13.494833 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494833 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494834 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494835 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494835 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494836 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494836 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494837 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494838 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494839 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494839 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494840 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494840 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494841 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494842 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494842 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494843 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494843 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494844 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494844 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494845 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494846 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494846 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494847 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494848 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494848 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494849 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494849 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494850 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494850 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494851 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494852 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494852 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494856 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494857 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494857 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494858 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494859 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494859 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494860 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494860 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494861 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494861 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494862 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494863 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494863 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494864 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494864 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494865 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494865 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494866 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494866 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494867 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494868 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494868 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494869 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494869 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494870 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494871 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494871 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.494872 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494872 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494873 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494873 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494874 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494874 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494875 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.494875 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494876 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494877 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494878 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494878 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.494879 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.494879 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494880 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494880 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494881 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494881 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494882 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494883 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494883 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494884 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494914 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494915 10108           Options.merge_operator: None
2025/06/30-11:26:13.494916 10108        Options.compaction_filter: None
2025/06/30-11:26:13.494916 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494917 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494917 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494918 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494923 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494925 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494925 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494926 10108          Options.compression: LZ4
2025/06/30-11:26:13.494926 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494927 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494927 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494928 10108             Options.num_levels: 7
2025/06/30-11:26:13.494928 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494929 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494929 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494930 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494931 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494931 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494932 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494933 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494933 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494934 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494934 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494935 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494935 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494936 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494937 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494937 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494938 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494938 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494939 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494939 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494940 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494940 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494941 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494941 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494942 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494942 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494943 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494943 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494944 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494944 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494945 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494945 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494946 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494947 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494947 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494948 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494948 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494949 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494949 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494950 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494950 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494951 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494951 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494952 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494953 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494953 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494954 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494954 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494955 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494956 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494956 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494957 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494958 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494958 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494959 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494959 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494960 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494961 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494961 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494962 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.494962 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494963 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494963 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494964 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494964 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494965 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494965 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.494966 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494966 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494967 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494967 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494968 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.494968 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.494969 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494969 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494970 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494971 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494971 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494972 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494973 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494973 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494974 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.495028 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.495029 10108           Options.merge_operator: None
2025/06/30-11:26:13.495030 10108        Options.compaction_filter: None
2025/06/30-11:26:13.495030 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.495031 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.495031 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.495032 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.495037 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.495038 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.495039 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.495040 10108          Options.compression: LZ4
2025/06/30-11:26:13.495040 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.495041 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.495041 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.495042 10108             Options.num_levels: 7
2025/06/30-11:26:13.495042 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.495043 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.495043 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.495044 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.495044 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.495045 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.495045 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.495046 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.495047 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.495047 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.495048 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.495048 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.495049 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.495049 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.495050 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.495050 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.495051 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.495051 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.495052 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.495052 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.495053 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.495053 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.495054 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.495054 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.495055 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.495055 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.495056 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.495056 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.495057 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.495058 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.495058 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.495059 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.495059 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.495060 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.495060 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.495061 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.495061 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.495062 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.495063 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.495064 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.495065 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.495065 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.495066 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.495066 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.495067 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.495067 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.495068 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.495068 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.495069 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.495070 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.495070 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.495071 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.495071 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.495076 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.495077 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.495078 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.495079 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.495080 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.495081 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.495082 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.495082 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.495083 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.495083 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.495084 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.495084 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.495085 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.495086 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.495086 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.495087 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.495087 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.495088 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.495088 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.495089 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.495089 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.495090 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.495090 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.495091 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.495092 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.495092 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.495093 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.495093 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.495094 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.495139 10108               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.495144 10108           Options.merge_operator: None
2025/06/30-11:26:13.495144 10108        Options.compaction_filter: None
2025/06/30-11:26:13.495145 10108        Options.compaction_filter_factory: None
2025/06/30-11:26:13.495145 10108  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.495146 10108         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.495146 10108            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.495152 10108            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff91a08120)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff91a14250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.495154 10108        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.495154 10108  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.495155 10108          Options.compression: LZ4
2025/06/30-11:26:13.495155 10108                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.495156 10108       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.495156 10108   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.495157 10108             Options.num_levels: 7
2025/06/30-11:26:13.495157 10108        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.495158 10108     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.495158 10108     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.495159 10108            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.495159 10108                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.495160 10108               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.495160 10108         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.495161 10108         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.495162 10108         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.495162 10108                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.495163 10108         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.495163 10108         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.495164 10108            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.495164 10108                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.495165 10108               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.495165 10108         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.495166 10108         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.495166 10108         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.495167 10108         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.495167 10108                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.495168 10108         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.495169 10108      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.495169 10108          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.495170 10108              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.495171 10108                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.495171 10108             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.495172 10108                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.495172 10108 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.495173 10108          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.495173 10108 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.495174 10108 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.495174 10108 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.495175 10108 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.495175 10108 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.495176 10108 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.495177 10108 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.495177 10108       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.495178 10108                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.495178 10108                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.495179 10108   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.495179 10108   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.495180 10108                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.495181 10108                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.495181 10108                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.495182 10108 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.495182 10108 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.495183 10108 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.495222 10108 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.495226 10108 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.495228 10108 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.495229 10108 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.495231 10108 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.495232 10108 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.495237 10108                   Options.table_properties_collectors: 
2025/06/30-11:26:13.495238 10108                   Options.inplace_update_support: 0
2025/06/30-11:26:13.495239 10108                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.495243 10108               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.495244 10108               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.495245 10108   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.495246 10108                           Options.bloom_locality: 0
2025/06/30-11:26:13.495247 10108                    Options.max_successive_merges: 0
2025/06/30-11:26:13.495248 10108             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.495249 10108                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.495250 10108                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.495251 10108                Options.force_consistency_checks: 1
2025/06/30-11:26:13.495252 10108                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.495253 10108                               Options.ttl: 2592000
2025/06/30-11:26:13.495281 10108          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.495282 10108                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.495283 10108  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.495283 10108    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.495284 10108                       Options.enable_blob_files: false
2025/06/30-11:26:13.495284 10108                           Options.min_blob_size: 0
2025/06/30-11:26:13.495285 10108                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.495285 10108                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.495286 10108          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.495287 10108      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.495287 10108 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.495288 10108          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.495288 10108                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.495289 10108         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.495289 10108            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.516198 10108 DB pointer 0xfffd66c46800
