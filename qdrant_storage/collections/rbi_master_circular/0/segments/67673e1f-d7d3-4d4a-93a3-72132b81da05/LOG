2025/06/30-11:26:13.492027 10107 RocksDB version: 9.9.3
2025/06/30-11:26:13.492109 10107 Compile date 2024-12-05 01:25:31
2025/06/30-11:26:13.492110 10107 DB SUMMARY
2025/06/30-11:26:13.492112 10107 Host name (Env):  3ed34e58cde5
2025/06/30-11:26:13.492112 10107 DB Session ID:  HLH6JCEFCE6BMGI16FO7
2025/06/30-11:26:13.492604 10107 CURRENT file:  CURRENT
2025/06/30-11:26:13.492608 10107 MANIFEST file:  MANIFEST-000073 size: 2652 Bytes
2025/06/30-11:26:13.492610 10107 SST files in ./storage/collections/rbi_master_circular/0/segments/67673e1f-d7d3-4d4a-93a3-72132b81da05 dir, Total Num: 24, files: 000016.sst 000018.sst 000020.sst 000022.sst 000024.sst 000026.sst 000028.sst 000030.sst 000032.sst 
2025/06/30-11:26:13.492610 10107 Write Ahead Log file in ./storage/collections/rbi_master_circular/0/segments/67673e1f-d7d3-4d4a-93a3-72132b81da05: 
2025/06/30-11:26:13.492612 10107                         Options.error_if_exists: 0
2025/06/30-11:26:13.492612 10107                       Options.create_if_missing: 1
2025/06/30-11:26:13.492613 10107                         Options.paranoid_checks: 1
2025/06/30-11:26:13.492614 10107             Options.flush_verify_memtable_count: 1
2025/06/30-11:26:13.492614 10107          Options.compaction_verify_record_count: 1
2025/06/30-11:26:13.492615 10107                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-11:26:13.492616 10107        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-11:26:13.492617 10107                                     Options.env: 0xffff86231620
2025/06/30-11:26:13.492618 10107                                      Options.fs: PosixFileSystem
2025/06/30-11:26:13.492618 10107                                Options.info_log: 0xffff2dd47000
2025/06/30-11:26:13.492620 10107                Options.max_file_opening_threads: 16
2025/06/30-11:26:13.492621 10107                              Options.statistics: (nil)
2025/06/30-11:26:13.492621 10107                               Options.use_fsync: 0
2025/06/30-11:26:13.492622 10107                       Options.max_log_file_size: 1048576
2025/06/30-11:26:13.492623 10107                  Options.max_manifest_file_size: 1073741824
2025/06/30-11:26:13.492623 10107                   Options.log_file_time_to_roll: 0
2025/06/30-11:26:13.492624 10107                       Options.keep_log_file_num: 1
2025/06/30-11:26:13.492625 10107                    Options.recycle_log_file_num: 0
2025/06/30-11:26:13.492625 10107                         Options.allow_fallocate: 1
2025/06/30-11:26:13.492626 10107                        Options.allow_mmap_reads: 0
2025/06/30-11:26:13.492626 10107                       Options.allow_mmap_writes: 0
2025/06/30-11:26:13.492627 10107                        Options.use_direct_reads: 0
2025/06/30-11:26:13.492627 10107                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-11:26:13.492628 10107          Options.create_missing_column_families: 1
2025/06/30-11:26:13.492629 10107                              Options.db_log_dir: 
2025/06/30-11:26:13.492629 10107                                 Options.wal_dir: 
2025/06/30-11:26:13.492630 10107                Options.table_cache_numshardbits: 6
2025/06/30-11:26:13.492631 10107                         Options.WAL_ttl_seconds: 0
2025/06/30-11:26:13.492631 10107                       Options.WAL_size_limit_MB: 0
2025/06/30-11:26:13.492633 10107                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-11:26:13.492634 10107             Options.manifest_preallocation_size: 4194304
2025/06/30-11:26:13.492634 10107                     Options.is_fd_close_on_exec: 1
2025/06/30-11:26:13.492635 10107                   Options.advise_random_on_open: 1
2025/06/30-11:26:13.492636 10107                    Options.db_write_buffer_size: 0
2025/06/30-11:26:13.492636 10107                    Options.write_buffer_manager: 0xffff9a21c900
2025/06/30-11:26:13.492637 10107           Options.random_access_max_buffer_size: 1048576
2025/06/30-11:26:13.492638 10107                      Options.use_adaptive_mutex: 0
2025/06/30-11:26:13.492638 10107                            Options.rate_limiter: (nil)
2025/06/30-11:26:13.492640 10107     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-11:26:13.492640 10107                       Options.wal_recovery_mode: 0
2025/06/30-11:26:13.492641 10107                  Options.enable_thread_tracking: 0
2025/06/30-11:26:13.492642 10107                  Options.enable_pipelined_write: 0
2025/06/30-11:26:13.492642 10107                  Options.unordered_write: 0
2025/06/30-11:26:13.492643 10107         Options.allow_concurrent_memtable_write: 1
2025/06/30-11:26:13.492644 10107      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-11:26:13.492644 10107             Options.write_thread_max_yield_usec: 100
2025/06/30-11:26:13.492645 10107            Options.write_thread_slow_yield_usec: 3
2025/06/30-11:26:13.492645 10107                               Options.row_cache: None
2025/06/30-11:26:13.492646 10107                              Options.wal_filter: None
2025/06/30-11:26:13.492647 10107             Options.avoid_flush_during_recovery: 0
2025/06/30-11:26:13.492647 10107             Options.allow_ingest_behind: 0
2025/06/30-11:26:13.492649 10107             Options.two_write_queues: 0
2025/06/30-11:26:13.492649 10107             Options.manual_wal_flush: 0
2025/06/30-11:26:13.492650 10107             Options.wal_compression: 0
2025/06/30-11:26:13.492650 10107             Options.background_close_inactive_wals: 0
2025/06/30-11:26:13.492651 10107             Options.atomic_flush: 0
2025/06/30-11:26:13.492651 10107             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-11:26:13.492652 10107             Options.prefix_seek_opt_in_only: 0
2025/06/30-11:26:13.492653 10107                 Options.persist_stats_to_disk: 0
2025/06/30-11:26:13.492654 10107                 Options.write_dbid_to_manifest: 1
2025/06/30-11:26:13.492654 10107                 Options.write_identity_file: 1
2025/06/30-11:26:13.492655 10107                 Options.log_readahead_size: 0
2025/06/30-11:26:13.492656 10107                 Options.file_checksum_gen_factory: Unknown
2025/06/30-11:26:13.492656 10107                 Options.best_efforts_recovery: 0
2025/06/30-11:26:13.492657 10107                Options.max_bgerror_resume_count: 2147483647
2025/06/30-11:26:13.492658 10107            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-11:26:13.492658 10107             Options.allow_data_in_errors: 0
2025/06/30-11:26:13.492659 10107             Options.db_host_id: __hostname__
2025/06/30-11:26:13.492660 10107             Options.enforce_single_del_contracts: true
2025/06/30-11:26:13.492661 10107             Options.metadata_write_temperature: kUnknown
2025/06/30-11:26:13.492661 10107             Options.wal_write_temperature: kUnknown
2025/06/30-11:26:13.492662 10107             Options.max_background_jobs: 2
2025/06/30-11:26:13.492663 10107             Options.max_background_compactions: -1
2025/06/30-11:26:13.492663 10107             Options.max_subcompactions: 1
2025/06/30-11:26:13.492664 10107             Options.avoid_flush_during_shutdown: 0
2025/06/30-11:26:13.492665 10107           Options.writable_file_max_buffer_size: 1048576
2025/06/30-11:26:13.492665 10107             Options.delayed_write_rate : 16777216
2025/06/30-11:26:13.492666 10107             Options.max_total_wal_size: 0
2025/06/30-11:26:13.492667 10107             Options.delete_obsolete_files_period_micros: 180000000
2025/06/30-11:26:13.492668 10107                   Options.stats_dump_period_sec: 600
2025/06/30-11:26:13.492669 10107                 Options.stats_persist_period_sec: 600
2025/06/30-11:26:13.492669 10107                 Options.stats_history_buffer_size: 1048576
2025/06/30-11:26:13.492670 10107                          Options.max_open_files: 256
2025/06/30-11:26:13.492671 10107                          Options.bytes_per_sync: 0
2025/06/30-11:26:13.492671 10107                      Options.wal_bytes_per_sync: 0
2025/06/30-11:26:13.492672 10107                   Options.strict_bytes_per_sync: 0
2025/06/30-11:26:13.492672 10107       Options.compaction_readahead_size: 2097152
2025/06/30-11:26:13.492673 10107                  Options.max_background_flushes: -1
2025/06/30-11:26:13.492674 10107 Options.daily_offpeak_time_utc: 
2025/06/30-11:26:13.492674 10107 Compression algorithms supported:
2025/06/30-11:26:13.492675 10107 	kZSTD supported: 0
2025/06/30-11:26:13.492676 10107 	kXpressCompression supported: 0
2025/06/30-11:26:13.492676 10107 	kBZip2Compression supported: 0
2025/06/30-11:26:13.492677 10107 	kZSTDNotFinalCompression supported: 0
2025/06/30-11:26:13.492678 10107 	kLZ4Compression supported: 1
2025/06/30-11:26:13.492678 10107 	kZlibCompression supported: 0
2025/06/30-11:26:13.492679 10107 	kLZ4HCCompression supported: 1
2025/06/30-11:26:13.492680 10107 	kSnappyCompression supported: 1
2025/06/30-11:26:13.492681 10107 Fast CRC32 supported: Not supported on x86
2025/06/30-11:26:13.492681 10107 DMutex implementation: pthread_mutex_t
2025/06/30-11:26:13.492682 10107 Jemalloc supported: 0
2025/06/30-11:26:13.493568 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493571 10107           Options.merge_operator: None
2025/06/30-11:26:13.493572 10107        Options.compaction_filter: None
2025/06/30-11:26:13.493573 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493574 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493574 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493575 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493591 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493595 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493596 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493598 10107          Options.compression: LZ4
2025/06/30-11:26:13.493600 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493601 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493601 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493602 10107             Options.num_levels: 7
2025/06/30-11:26:13.493602 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493603 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493604 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493604 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493606 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493606 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493607 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493608 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493608 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493610 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493611 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493611 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493612 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493613 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493613 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493614 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493615 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493615 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493616 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493616 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493617 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493617 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493618 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493619 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493619 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493620 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493621 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493621 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493623 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493624 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493625 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493625 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493626 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493626 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493627 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493628 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493629 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493629 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493630 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493630 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493631 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493632 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493633 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493634 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493635 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493635 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493636 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493636 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493637 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493638 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493638 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493639 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493639 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493644 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493645 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493646 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493647 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493647 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493648 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493649 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.493649 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493650 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493650 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493651 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493651 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493652 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493653 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.493653 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493654 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493655 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493655 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493656 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.493656 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.493657 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493657 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493658 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493659 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493660 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493660 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493662 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493662 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493663 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.493757 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493758 10107           Options.merge_operator: None
2025/06/30-11:26:13.493759 10107        Options.compaction_filter: None
2025/06/30-11:26:13.493759 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493760 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493760 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493761 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493769 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493771 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493772 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493772 10107          Options.compression: LZ4
2025/06/30-11:26:13.493773 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493774 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493774 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493775 10107             Options.num_levels: 7
2025/06/30-11:26:13.493775 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493776 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493776 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493777 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493778 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493778 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493779 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493779 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493780 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493780 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493781 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493781 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493782 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493783 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493783 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493784 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493784 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493785 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493786 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493786 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493787 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493787 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493788 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493788 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493789 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493790 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493790 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493791 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493791 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493792 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493793 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493793 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493794 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493794 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493795 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493795 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493796 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493796 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493797 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493798 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493798 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493799 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493800 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493800 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493801 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493801 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493802 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493802 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493803 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493804 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493804 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493805 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493805 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493807 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493807 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493808 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493808 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493809 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493810 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493810 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.493811 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493811 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493812 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493812 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493813 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493813 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493814 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.493814 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493815 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493816 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493816 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493817 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.493817 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.493818 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493818 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493819 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493819 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493820 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493821 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493821 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493822 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493822 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.493869 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493870 10107           Options.merge_operator: None
2025/06/30-11:26:13.493871 10107        Options.compaction_filter: None
2025/06/30-11:26:13.493871 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493873 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493874 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493874 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493881 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493882 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493883 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493883 10107          Options.compression: LZ4
2025/06/30-11:26:13.493884 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493885 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493886 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493886 10107             Options.num_levels: 7
2025/06/30-11:26:13.493887 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493887 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493888 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493889 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493889 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493890 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493890 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493891 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493891 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493892 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493893 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493893 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493894 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493896 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493897 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493898 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493898 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493899 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493899 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493900 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493900 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493901 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493901 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493903 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493903 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493904 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493904 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493905 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493906 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493907 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493907 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493908 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493908 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493909 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493909 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493910 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493910 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493911 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493912 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493912 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493913 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493914 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493914 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493915 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493915 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493916 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493917 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493917 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493918 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493918 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493919 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493919 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493920 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493921 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493922 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493922 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493923 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493925 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493926 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493927 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.493927 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493928 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493928 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493929 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493929 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493930 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493931 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.493931 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493932 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493933 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493933 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493934 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.493935 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.493935 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493936 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493937 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493938 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493938 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493939 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493939 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493940 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493941 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.493970 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493971 10107           Options.merge_operator: None
2025/06/30-11:26:13.493971 10107        Options.compaction_filter: None
2025/06/30-11:26:13.493972 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493973 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493973 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493974 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493979 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493981 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493981 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493982 10107          Options.compression: LZ4
2025/06/30-11:26:13.493982 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493983 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493983 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493984 10107             Options.num_levels: 7
2025/06/30-11:26:13.493984 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493985 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493985 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493986 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493986 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493987 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493987 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493988 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493989 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493989 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493990 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493991 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493991 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493992 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493992 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493993 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493993 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493994 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493994 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493995 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493995 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493996 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493996 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493997 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493997 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493998 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493998 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493999 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494000 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494000 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494001 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494001 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494002 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494002 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494003 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494003 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494004 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494004 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494005 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494005 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494006 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494007 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494007 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494008 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494008 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494009 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494010 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494010 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494011 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494012 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494012 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494013 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494015 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494015 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494016 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494017 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494017 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494018 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494019 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494019 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.494020 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494022 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494023 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494023 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494025 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494025 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494026 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.494026 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494027 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494028 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494028 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494029 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.494029 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.494030 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494030 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494031 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494032 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494032 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494033 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494034 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494034 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494035 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494068 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494069 10107           Options.merge_operator: None
2025/06/30-11:26:13.494069 10107        Options.compaction_filter: None
2025/06/30-11:26:13.494070 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494071 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494071 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494072 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494078 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494080 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494080 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494081 10107          Options.compression: LZ4
2025/06/30-11:26:13.494081 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494082 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494082 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494083 10107             Options.num_levels: 7
2025/06/30-11:26:13.494084 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494085 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494085 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494086 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494086 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494087 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494088 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494088 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494089 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494090 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494090 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494091 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494092 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494092 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494093 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494094 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494094 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494095 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494095 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494096 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494097 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494097 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494098 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494099 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494099 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494100 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494101 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494101 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494102 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494103 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494103 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494104 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494105 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494105 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494106 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494107 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494107 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494110 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494111 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494113 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494114 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494114 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494115 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494115 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494116 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494117 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494117 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494118 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494118 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494119 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494119 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494120 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494120 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494121 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494122 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494122 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494123 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494124 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494124 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494125 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.494125 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494126 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494126 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494127 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494127 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494128 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494128 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.494129 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494129 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494130 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494130 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494131 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.494131 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.494132 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494132 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494133 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494134 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494134 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494135 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494135 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494136 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494136 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494200 10107               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494201 10107           Options.merge_operator: None
2025/06/30-11:26:13.494202 10107        Options.compaction_filter: None
2025/06/30-11:26:13.494203 10107        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494203 10107  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494204 10107         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494204 10107            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494265 10107            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff9a200d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff9a21c190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494269 10107        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494270 10107  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494271 10107          Options.compression: LZ4
2025/06/30-11:26:13.494271 10107                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494272 10107       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494272 10107   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494273 10107             Options.num_levels: 7
2025/06/30-11:26:13.494274 10107        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494275 10107     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494275 10107     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494276 10107            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494277 10107                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494277 10107               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494278 10107         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494279 10107         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494280 10107         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494281 10107                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494282 10107         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494282 10107         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494283 10107            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494284 10107                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494284 10107               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494285 10107         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494285 10107         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494286 10107         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494287 10107         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494287 10107                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494288 10107         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494289 10107      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494289 10107          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494290 10107              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494291 10107                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494291 10107             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494292 10107                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494293 10107 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494294 10107          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494294 10107 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494295 10107 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494295 10107 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494296 10107 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494296 10107 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494297 10107 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494297 10107 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494298 10107       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494299 10107                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494299 10107                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494300 10107   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494300 10107   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494301 10107                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494301 10107                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494302 10107                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494303 10107 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494304 10107 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494304 10107 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494305 10107 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494305 10107 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494306 10107 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494306 10107 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494307 10107 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494307 10107 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494308 10107                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494309 10107                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494309 10107                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494310 10107               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494311 10107               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494311 10107   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494312 10107                           Options.bloom_locality: 0
2025/06/30-11:26:13.494312 10107                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494313 10107             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494313 10107                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494314 10107                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494314 10107                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494315 10107                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494315 10107                               Options.ttl: 2592000
2025/06/30-11:26:13.494326 10107          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494327 10107                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494328 10107  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494328 10107    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494329 10107                       Options.enable_blob_files: false
2025/06/30-11:26:13.494329 10107                           Options.min_blob_size: 0
2025/06/30-11:26:13.494330 10107                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494331 10107                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494331 10107          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494332 10107      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494332 10107 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494333 10107          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494334 10107                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494334 10107         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494335 10107            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.515872 10107 DB pointer 0xffff2d904000
