#!/usr/bin/env python3
"""
Configuration test script to verify all settings are correct
"""

import sys
from pathlib import Path

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))

def test_configuration():
    """Test configuration loading and validation"""
    print("🔧 Testing Configuration...")
    
    try:
        from utils.config import config
        print("✅ Configuration module loaded successfully")
        
        # Test OpenAI config
        print(f"📝 OpenAI API keys configured: {len(config.openai.api_keys)} keys")
        print(f"📝 OpenAI embedding model: {config.openai.embedding_model}")
        
        # Test Qdrant config
        print(f"🔍 Qdrant host: {config.qdrant.host}")
        print(f"🔍 Qdrant URL: {config.qdrant.full_url}")
        print(f"🔍 Qdrant embedding size: {config.qdrant.embedding_size}")
        
        # Test database config
        print(f"💾 Database URI: {config.database.uri[:50]}...")
        
        # Test S3 config
        print(f"☁️ S3 bucket: {config.s3.bucket_name}")
        
        # Test validation
        is_valid = config.validate()
        if is_valid:
            print("✅ Configuration validation passed")
        else:
            print("❌ Configuration validation failed")
            
        return is_valid
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_qdrant_connection():
    """Test Qdrant connection"""
    print("\n🔍 Testing Qdrant Connection...")
    
    try:
        from utils.qdrant_utils import qdrant_manager
        
        # Test collection creation
        test_collection = "test_connection"
        success = qdrant_manager.ensure_collection(test_collection)
        
        if success:
            print("✅ Qdrant connection and collection creation successful")
            
            # Test point upsert
            test_success = qdrant_manager.upsert_point(
                collection_name=test_collection,
                point_id="test_point",
                payload={"test": "data"},
                text_for_embedding="This is a test document"
            )
            
            if test_success:
                print("✅ Qdrant point upsert successful")
            else:
                print("❌ Qdrant point upsert failed")
                
            return test_success
        else:
            print("❌ Qdrant connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Qdrant test failed: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    print("\n💾 Testing Database Connection...")
    
    try:
        from utils.database_utils import db_manager
        
        # Test document upsert
        test_doc = {
            "document_number": "TEST-001",
            "title": "Test Document",
            "test": True
        }
        
        success = db_manager.upsert_document(
            test_doc,
            db_name="test_db",
            collection_name="test_collection"
        )
        
        if success:
            print("✅ Database connection and upsert successful")
            
            # Test document retrieval
            found_doc = db_manager.find_document(
                {"document_number": "TEST-001"},
                db_name="test_db",
                collection_name="test_collection"
            )
            
            if found_doc:
                print("✅ Database document retrieval successful")
            else:
                print("⚠️ Database document retrieval returned None (might be expected)")
                
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_openai_connection():
    """Test OpenAI connection"""
    print("\n🤖 Testing OpenAI Connection...")
    
    try:
        from utils.openai_utils import en_embeddings
        
        # Test embedding generation
        test_text = "This is a test document for embedding generation."
        embedding = en_embeddings.embed_query(test_text)
        
        if embedding and len(embedding) > 0:
            print(f"✅ OpenAI embedding generation successful (dimension: {len(embedding)})")
            return True
        else:
            print("❌ OpenAI embedding generation failed")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")
        return False

def test_splade_encoder():
    """Test SPLADE encoder"""
    print("\n🧠 Testing SPLADE Encoder...")
    
    try:
        from utils.qdrant_utils import splade_encoder
        
        # Test SPLADE encoding
        test_text = "This is a test document for SPLADE encoding."
        splade_vector = splade_encoder.encode(test_text)
        
        if splade_vector and "indices" in splade_vector and "values" in splade_vector:
            print(f"✅ SPLADE encoding successful (indices: {len(splade_vector['indices'])})")
            return True
        else:
            print("❌ SPLADE encoding failed")
            return False
            
    except Exception as e:
        print(f"❌ SPLADE test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Configuration and Connection Tests\n")
    
    tests = [
        ("Configuration", test_configuration),
        ("Qdrant Connection", test_qdrant_connection),
        ("Database Connection", test_database_connection),
        ("OpenAI Connection", test_openai_connection),
        ("SPLADE Encoder", test_splade_encoder),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your configuration is ready.")
    else:
        print("⚠️ Some tests failed. Please check the configuration and try again.")
        
        # Provide specific guidance
        if not results.get("Configuration"):
            print("\n💡 Configuration issues:")
            print("   - Check Airflow Variables are set correctly")
            print("   - Ensure no carriage returns in variable values")
            
        if not results.get("Qdrant Connection"):
            print("\n💡 Qdrant issues:")
            print("   - Check vector_db_host variable")
            print("   - Ensure Qdrant is running and accessible")
            
        if not results.get("Database Connection"):
            print("\n💡 Database issues:")
            print("   - Check documentdb_uri variable")
            print("   - Ensure database is accessible")
            
        if not results.get("OpenAI Connection"):
            print("\n💡 OpenAI issues:")
            print("   - Check openai_api_keys variable")
            print("   - Ensure API keys are valid and have quota")

if __name__ == "__main__":
    main()
