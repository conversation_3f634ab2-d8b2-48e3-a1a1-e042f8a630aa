from typing import List, Dict, Any
# Generated by Copilot
from pydantic import BaseModel, Field

import json

# Define Pydantic models for structured data
class PlanStep(BaseModel):
    agent: str = Field(description="Name of the agent to execute")
    description: str = Field( description="Description of what this step accomplishes")
    dependencies: List[str] = Field(
        default_factory=list,
        description="Steps that must complete before this one"
    )
    parameters: Dict[str, str] = Field(
        default_factory=dict,
        description="Parameters for the agent",
        additionalProperties=False  # Ensure schema validation
    )


class ExecutionPlan(BaseModel):
    steps: List[PlanStep] = Field( description="Ordered list of steps to execute")
    context: Dict[str, Any] = Field(default_factory=dict, description="Execution context and metadata")
    validation_rules: List[str] = Field(default_factory=list, description="Rules to validate the execution")
    telemetry: Dict[str, Any] = Field(
        default_factory=lambda: {
            "timestamps": {},
            "llm_calls": [],
            "context_versions": []
        },
        description="Execution tracing data"
    )

    def details(self) -> Dict[str, Any]:
        steps = [{"agent": step.agent, "description": step.description} for step in self.steps]
        return {
            "steps": steps,
        }


class ValidationResult(BaseModel):
    is_valid: bool = Field(description="Whether the execution was valid")
    issues: List[str] = Field(default_factory=list, description="List of identified issues")
    suggestions: List[str] = Field(default_factory=list, description="Suggestions for improvement")
    validation_evidence: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Specific text segments from context used in validation"
    )
