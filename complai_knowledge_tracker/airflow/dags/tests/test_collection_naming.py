#!/usr/bin/env python3
"""
Test script to verify collection naming consistency across all components
"""

import sys
from pathlib import Path

# Add the parent directory to path to import utils
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

# Mock airflow imports to avoid dependency issues
class MockVariable:
    @staticmethod
    def get(key, default_var=None):
        return default_var

sys.modules['airflow'] = type('MockAirflow', (), {})()
sys.modules['airflow.models'] = type('MockModels', (), {'Variable': MockVariable})()

def test_document_type_mapper():
    """Test DocumentTypeMapper functionality"""
    print("🧪 Testing DocumentTypeMapper...")
    
    try:
        from utils.document_pipeline import DocumentTypeMapper, DocumentType
        
        # Test RSS category to collection name mapping
        test_cases = [
            ("circulars", "rbi_circular"),
            ("master_circulars", "rbi_master_circular"),
            ("master_directions", "rbi_master_direction"),
            ("notifications", "rbi_notification"),
            ("press_releases", "rbi_press_release"),
            ("speeches", "rbi_speech"),
            ("publications", "rbi_publication"),
        ]
        
        print("  Testing RSS category to collection name mapping:")
        for rss_category, expected_collection in test_cases:
            actual_collection = DocumentTypeMapper.rss_category_to_collection_name(rss_category)
            if actual_collection == expected_collection:
                print(f"    ✅ {rss_category} -> {actual_collection}")
            else:
                print(f"    ❌ {rss_category} -> {actual_collection} (expected {expected_collection})")
        
        # Test document type to collection name mapping
        print("\n  Testing DocumentType to collection name mapping:")
        for doc_type in DocumentType:
            collection_name = DocumentTypeMapper.document_type_to_collection_name(doc_type)
            print(f"    ✅ {doc_type.value} -> {collection_name}")
        
        # Test title categorization
        print("\n  Testing title categorization:")
        title_test_cases = [
            ("RBI Master Direction on Banking Regulations", "rbi_master_direction"),
            ("RBI Master Circular on Credit Risk", "rbi_master_circular"),
            ("RBI Circular on Payment Systems", "rbi_circular"),
            ("RBI Notification on New Guidelines", "rbi_notification"),
            ("RBI Press Release on Policy Changes", "rbi_press_release"),
            ("Governor's Speech at Banking Conference", "rbi_speech"),
            ("Annual Report Publication", "rbi_publication"),
            ("Some Other Document", "rbi_other"),
        ]
        
        for title, expected_collection in title_test_cases:
            actual_collection = DocumentTypeMapper.categorize_document_title(title)
            if actual_collection == expected_collection:
                print(f"    ✅ '{title}' -> {actual_collection}")
            else:
                print(f"    ❌ '{title}' -> {actual_collection} (expected {expected_collection})")
        
        # Test get all collection names
        print("\n  All possible collection names:")
        all_collections = DocumentTypeMapper.get_all_collection_names()
        for collection in sorted(all_collections):
            print(f"    - {collection}")
        
        print(f"\n✅ DocumentTypeMapper tests completed. Found {len(all_collections)} collections.")
        return True
        
    except Exception as e:
        print(f"❌ DocumentTypeMapper test failed: {e}")
        return False

def test_semantic_retriever_compatibility():
    """Test that semantic retriever expects the same collection names"""
    print("\n🧪 Testing semantic retriever compatibility...")
    
    try:
        from utils.document_pipeline import DocumentTypeMapper
        
        # Expected collection names from semantic retriever
        expected_semantic_collections = {
            "rbi_master_direction",
            "rbi_master_circular", 
            "rbi_circular",
            "rbi_notification",
            "rbi_press_release",
            "rbi_speech",
            "rbi_publication",
            "rbi_other",
        }
        
        # Get collection names from DocumentTypeMapper
        mapper_collections = set(DocumentTypeMapper.get_all_collection_names())
        
        print("  Comparing collection names:")
        print(f"    Semantic retriever expects: {sorted(expected_semantic_collections)}")
        print(f"    DocumentTypeMapper provides: {sorted(mapper_collections)}")
        
        if mapper_collections == expected_semantic_collections:
            print("  ✅ Collection names match perfectly!")
            return True
        else:
            missing_in_mapper = expected_semantic_collections - mapper_collections
            extra_in_mapper = mapper_collections - expected_semantic_collections
            
            if missing_in_mapper:
                print(f"  ❌ Missing in DocumentTypeMapper: {missing_in_mapper}")
            if extra_in_mapper:
                print(f"  ❌ Extra in DocumentTypeMapper: {extra_in_mapper}")
            return False
            
    except Exception as e:
        print(f"❌ Semantic retriever compatibility test failed: {e}")
        return False

def test_rss_feed_categorization():
    """Test RSS feed categorization function"""
    print("\n🧪 Testing RSS feed categorization...")
    
    try:
        # Import the categorize_document function from RSS feed ETL
        sys.path.append(str(Path(__file__).parent.parent))
        from inuse_rss_feed_etl_dag import categorize_document
        
        test_titles = [
            "RBI Master Direction - Banking Supervision",
            "RBI Master Circular on Credit Risk Management", 
            "RBI Circular on Payment and Settlement Systems",
            "RBI Notification - New Banking Guidelines",
            "RBI Press Release - Monetary Policy Decision",
            "Governor's Speech at Annual Banking Conference",
            "RBI Annual Report 2023-24",
            "Some Random Document Title",
        ]
        
        print("  Testing RSS feed categorization:")
        for title in test_titles:
            collection_name = categorize_document(title)
            print(f"    '{title}' -> {collection_name}")
        
        print("  ✅ RSS feed categorization test completed.")
        return True
        
    except Exception as e:
        print(f"❌ RSS feed categorization test failed: {e}")
        return False

def main():
    """Run all collection naming tests"""
    print("🚀 Starting Collection Naming Consistency Tests")
    print("=" * 60)
    
    tests = [
        test_document_type_mapper,
        test_semantic_retriever_compatibility,
        test_rss_feed_categorization,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Collection naming is consistent.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the collection naming implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
