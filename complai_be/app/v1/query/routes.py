from fastapi import APIRouter, Depends, HTTPException, Request
from app.v1.query.service import process_user_query
from app.v1.session.service import check_session_limits
from app.db.models import QueryRequest
from app.db.database import get_db

query_router = APIRouter()

@query_router.post("/query")
async def handle_user_query(request: QueryRequest, db=Depends(lambda : get_db())):
    """
    Endpoint to handle user queries.
    Expects a query string and returns the processed response.
    """
    try:
        # Check session limits based on request origin
        session_limits = check_session_limits(request.username, request.session_id, request, db)
        print("session_limits", session_limits)
        
        # Process the query if limits are not exceeded
        result = process_user_query(request.message, request.session_id, request.username, db)
        
        # Add remaining query info to response
        result['limits'] = session_limits
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
