import yaml
import json
from app.v2.query.utils.completion_client import completion_client
from langfuse.decorators import observe
from app.v2.query.utils.completion_client import completion_client
 
from app.v2.query.response.models import answer_models
 
class AnswerGenerator:
    def __init__(self, model: str, prompt_file: str):
        self.model = model
        self.prompt_cfg = self._load_prompt(prompt_file)

    def _load_prompt(self, prompt_file: str) -> dict:
        with open(prompt_file, "r") as f:
            return yaml.safe_load(f)
    
    def _consolidate_retrieved_knowledge(self, retrieved_points):

        import re
        from datetime import datetime
        from typing import List, Dict, Any, Optional

        # 1. Define priority
        DOCUMENT_TYPE_PRIORITY = {
            "master_direction": 0,
            "master_circular": 1,
            "notification": 2,
            "press_release": 3,
            "speech": 4,
            "tender": 5,
            "publication": 6,
            "other": 7,
        }
        # 2. Sort by type priority then date_of_issue
        def sort_key(obj):
            
            md = obj.get("metadata", {})
            dtype = md.get("document_type", "other")
            prio = DOCUMENT_TYPE_PRIORITY.get(dtype, DOCUMENT_TYPE_PRIORITY["other"])
            # parse date; fallback to epoch if missing
            try:
                date_of_issue = md.get("date_of_issue", "2010-1-1")
                print("date_of_issue :" , date_of_issue)
                if date_of_issue:
                    date = datetime.strptime(date_of_issue, "%Y-%m-%d")
                else:
                    date = datetime(2010,1,1)
            except ValueError:
                date = datetime(2010,1,1)
            print(prio, date)
            return (prio, date)
        
        sorted_retrieved_points = sorted(retrieved_points, key=sort_key)
        print(sorted_retrieved_points)
        # 3. Group by document_id
        groups: Dict[str, List[Dict[str, Any]]] = {}
        for point in sorted_retrieved_points:
            document_id = point['metadata']['document_id']
            current_doc = groups.get('document_id', {
                "document_title" : point['metadata']['document_title'],
                "document_type" : point['metadata']['document_type'],
                "summary" : point['metadata']['summary'],
                "topics" : point['metadata']['topics'],
                "date_of_issue" : point['metadata']['date_of_issue'],
                "content": ""
            })
            current_doc["content"] = current_doc["content"]+"\n"+point['page_content'].split("Content:")[1]
            groups[document_id] = current_doc
        print(groups)
        combined_content = ""
        for document_id, document_data in groups.items():
            topics = "\n *".join(document_data['topics'])
            formatted_content = f"""
                Document Name :  {document_data['document_title']}
                Document ID : {document_id}
                Document Type : {document_data['document_type']}
                Document Summary : {document_data['summary']}
                Document Topics : {topics}
                Document Issue Date: {document_data['date_of_issue']}
                Document Content: {document_data['content']}
                """
            combined_content += formatted_content
        return combined_content


    def run(self, query, context, retrieved_points, definitions) -> answer_models.AnswerGeneratorOutput:
        try:
            knowledge_context = self._consolidate_retrieved_knowledge(retrieved_points)
            sample_answer_json = """{
                    "comprehensive_response": [
                        {
                        "answer_part": "The KYC process requires regulated entities to verify customer identity using officially valid documents such as passport, driver’s license, or Aadhaar.",
                        "answer_part_references": [
                            {
                            "document_id": "RBI/DBR/2015-16/18",
                            "page_number": 5,
                            "bboxes": [[108.0, 66.54, 526.72, 79.92]
                            }
                        ]
                        },
                        {
                        "answer_part": "Enhanced due diligence must be performed for high-risk categories, including Politically Exposed Persons (PEPs) and non-resident customers.",
                        "answer_part_references": [
                            {
                            "document_id": "RBI/DBR/2015-16/18  Master Direction DBR.AML.BC.No.81/14.01.001/2015-16",
                            "page_number": 7,
                            "bboxes": [[94.32, 127.56, 526.69, 141.32],[94.32, 147.56, 526.69, 161.32],[94.32, 167.56, 526.69, 181.32]]
                            }
                        ]
                        },
                        {
                        "answer_part": "Ongoing monitoring of transactions and periodic KYC updates are required based on the customer’s risk profile.",
                        "answer_part_references": []
                        }
                    ],
                    "clarification_requests": [
                        "Please specify whether video-based customer identification (V-CIP) is in scope for your institution."
                    ],
                    "information_gaps": [
                        "No details provided on the bank’s internal risk categorization thresholds."
                    ]
                    }"""

            final_prompt = f"""
             agent: "AI Compliance Officer Agent for Banks but NOT NBFC and Co-operative banks"
                    specialization: "Understanding RBI regulations and guidelines"
                    description: |
                        An agent that analyzes compliance queries using only RBI regulatory documents.
            INSTRUCTIONS:
                - "Prioritize Master Directions type Knowledge before others."
                - "Master Directions -> Master Circulars -> Circulars -> Notifications -> Any Other Document."
                - "Use only the provided Knowledge Context"
                - "**STRICT RULE** : It is mandatory and always Track and Cite the All Content utilized in generating AnswerPart from Knowledge Context using data-page and data-bbox as AnswerReference Positions"
                - "**STRICT RULE** : Always use the relevant content from Knowledge Context to generate response to Query"
                - "**STRICT RULE**: The AnswerReference values must be taken from the Knowledge Context only. Dont make any assumptions."
                - "**STRICT RULE**: Always Keep answer_part_reference bboxes list to cover a valid complete paragraph or a sentence based which alls part of Knowledge Context are considered in the generating answer_part text"
                - "**STRICT RULE**: Make sure to Keep answer_part_reference bboxes relevant to the generating answer_part text"
                - "**STRICT RULE** : Always match the exact and complete value of Document ID from Knowledge Context when generating AnswerPart document_id"
                - "**STRICT RULE**: When using Knowledge Context make sure to not use any Documents that are only for NBFC or Co-Operative Banks"
                - "**STRICT RULE**: Do not put position information like document, bbox, page in answer_part "
            QUERY: {query}
            Conversation Context: {context}
            Knowledge Context: {knowledge_context}
            You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.
                    **Important Instructions:**
                    - context contains what and how to answer the query.
                    Your role is to:
                    1. Analyze the provided query and context use ONLY the given Knowledge Context.
                    2. Only use the relevant content from Knowledge Context to generate response to user query QUERY.
                    2. Generate comprehensive answers based strictly on RBI regulatory documents.
                    3. Structure responses for clarity and ease of understanding.
                    4. Maintain a expert complaince officer tone and terminology while ensuring accessibility.
                    5. Plan complete response format and feel free to use Markdown formatting for answer parts.
                    5. Track and cite content used from page and bbox attributes of content while generating response.
                    6. Consider both long-term and short-term conversation context.
                    7. Handle missing information appropriately.

                    Response Structure:
                    1. Comprehensive Response:
                        - Detailed RBI-regulated answer using only retrieved Knowledge Context
                        - Write a summary or directed response if based on the context of the question
                        - Point-wise response if that makes sense based on the user query.
                        - Format and structure the response summary, points and conclusion format, no need to mention these names "summary, points and conclusion" in response, Make sure Summary should address the majority or key concern of the user's query with strong evidence
                        - Format and structure in IBC - Intro, Body, Conclusion,  NEVER MENTION these names "Intro, Body, Conclusion" as section heading in response instead use headings that make sense to the planned response
                        - Plan a markdown structure for the generating answer and keep it consistent across answer pieces
                        - Use any of the above formats and generate answer parts that render properly as markdown
                        - Clear explanation with supporting evidence from Knowledge Context
                        - Direct citations i.e., AnswerReference to relevant regulations
                        - Plan the structure of complete response in beautiful markdown presentation and generate AnswerParts aligning to the Planned Structure
                    2. Clarification Requests:
                        - List any unclear aspects requiring user input
                    3. Information Gaps:
                        - Note any missing information needed for complete response

                    Important Notes:
                    - Document Type Priority order for referencing information in Knowledge Context:
                        1. master_direction 
                        2. master_circular
                        3. notification
                    - AFter Document Type Priority, consider the latest information first from Document Issue Date
                    - The Knowledge Context contains a document summary and html content
                    - Focus on practical, implementable guidance if any required

                    Answer Format:
                        {sample_answer_json}

                    Respond with ONLY the structured answer following the above format, each answer part can be a markdown formatted text which completes the complete markdown formatted answer
                    **Phrase your response as an AI Compliance Officer DO NOT USE internals of the prompt like "Knowledge Context, Conversation Context".**
                    Respond only as JSON.
                    Ensure the JSON is valid and well-structured.
            """
 
            messages = [{"role": "system", "content": final_prompt}]
            response_model = completion_client.completion_structured_as_answer_model(
                required_model=answer_models.AnswerGeneratorOutput,
                messages=messages,
                model=self.model,
                tools=[{"name": "answer_generator", "description": "Generates a structured answer."}],
            )
 
            # Parse the JSON structured response
            return response_model

        except Exception as e:
            print(f"Error in AnswerGenerator: {e}")
            raise
