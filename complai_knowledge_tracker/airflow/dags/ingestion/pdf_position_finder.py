# pdf_position_finder.py
import fitz  # PyMuPDF

def get_text_positions(pdf_path: str, chunk_text: str, pages_sorted: list) -> list:
    """
    For a given text chunk and known OCR pages, search the PDF for pixel positions where the text appears.
    
    Args:
        pdf_path (str): Path to the PDF file.
        chunk_text (str): Markdown chunk text.
        pages_sorted (list): Sorted OCR pages.
    
    Returns:
        list: List of dicts with page index and found rectangles.
    """
    doc = fitz.open(pdf_path)
    results = []
    # Iterate over pages provided by OCR.
    for page in pages_sorted:
        page_index = page["index"]
        pdf_page = doc.load_page(page_index)
        # Attempt to search for the chunk text.
        rects = pdf_page.search_for(chunk_text)
        if rects:
            results.append({
                "page_index": page_index,
                "rectangles": [ (r.x0, r.y0, r.x1, r.y1) for r in rects ]
            })
    return results
