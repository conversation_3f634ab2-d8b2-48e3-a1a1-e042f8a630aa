"""
dag_rbi_regulations_processing.py
---------------------------------
Refactored Airflow DAG to process RBI regulation PDFs using PyMuPDF and HTML AST.
"""

from datetime import datetime, timedelta
import json
import os
import re
import sys
import uuid
import time
import random
import logging
from typing import List, Dict, Any
from io import BytesIO
from enum import Enum
import itertools
from pydantic import BaseModel, Field
import csv

import requests
import fitz  # PyMuPDF
import html2text
import boto3
from bs4 import BeautifulSoup
from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
from openai import OpenAI
from openai import RateLimitError
from pymongo import MongoClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode, FastEmbedSparse
from qdrant_client import QdrantClient, models

from utils.date_utils import normalize_date

# Initialize logger
logger = logging.getLogger("airflow.task")

# Import OpenAI utilities from common utils
from utils.openai_utils import with_key_rotation, client_openai, strip_data_attributes

class Position(BaseModel):
    page_number: int = Field(
        ...,
        description="The page number from which this item was extracted referred to data-page"
    )
    bboxes: List[List[float]] = Field(
        ..., 
        description="List of Bounding box [[x1, y1, x2, y2],[x1, y1, x2, y2]] of all content of RBI regulation HTML Fragement(data-bbox list) considered as the guideline ")

class Obligation(BaseModel):
    guideline: str = Field(
        ...,
        description="Exact paragraph or sentence considered as guideline from RBI regulation HTML Fragement, this will be used to extract the obligation"
    )
    positions: List[Position] = Field(
        ...,
        description="Positional information of the guideline page and bounding boxes of the guideline text"
    )
    obligation: str = Field(
        ...,
        description="As Complaiance office, The extracted or paraphrased guideline as obligation "
    )
    action_item: str = Field(
        ...,
        description="A concise, actionable item derived from the obligation."
    )

class ObligationList(BaseModel):
    obligations_list: List[Obligation] = Field(
        ...,
        description="A list of all extracted obligations."
    )

def extract_obligations(html_chunks: List[str]) -> List[dict]:
    """
    For each HTML fragment, call OpenAI to pull out
    obligations (para, obligation, action_item) + positions.
    """
    all_obls = []
    for chunk in html_chunks:
        prompt = (
            "Role: You are an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items\n"
            "Your task is to extract all the guidelines and obligations from guidelines, action_items from obligations from the provided RBI regulation HTML fragment.\n\n"
            "Goal: Extract a JSON array where each object contains exactly these fields:\n"
            "  - 'guideline' - identified sentence or paragraph as a instruction to the bank\n "
            "  - 'positions' - poistion of the guildeline, extracted from data-page and data-bbox of the exact/complete sentence or paragraph considered as guideline\n"
            "  - 'obligation'- rephrased guideline with the context of complaince officer, usually start with 'Bank Shall\n"
            "  - 'action_item' - rephrased obligations as a list of action items to be taken by the bank'\n\n"
            """
            Below are the sample Guidelines from a RBI Regulation, and corresponding generated/rephrased Obligation
                Guideline	: Notes determined as counterfeit shall be stamped as "COUNTERFEIT NOTE"
                Obligation	: Bank shall ensure that the Notes determined as counterfeit shall be stamped as "COUNTERFEIT NOTE" and impounded in the prescribed format (Annex I).

                Guideline	: Such impounded note shall be recorded under authentication, in a separate register.
                Obligation	: Bank shall ensure that the impounded notes shall be recorded under authentication, in a separate register.

                Guideline	: "When a banknote tendered at the counter of a bank branch / back office and currency chest or treasury is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer."
                Obligation	 : Bank shall ensure that when a banknote tendered at the counter of a bank branch is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.

                Guideline	: Detection of Counterfeit Notes - Reporting to Police and other bodies
                Obligation	: "Bank shall ensure to report to Police and other bodies on Detection of Counterfeit Notes.
                                The following procedure shall be followed while reporting incidence of detection of Counterfeit Note to the Police: 
                                1.  For cases of detection of Counterfeit Notes up to four (04) pieces in a single transaction, a consolidated report in the prescribed format (Annex III) shall be sent by the Nodal Bank Officer to the police authorities or the Nodal Police Station, along with the suspect Counterfeit Notes, at the end of the month.  
                                2.  For cases of detection of Counterfeit Notes of five (05) or more pieces in a single transaction, the Counterfeit Notes shall be forwarded immediately by the Nodal Bank Officer to the local police authorities or the Nodal Police Station for investigation by filing FIR in the prescribed format (Annex IV).  
                                3.  A copy of the monthly consolidated report / FIR shall be sent to the Forged Note Vigilance Cell constituted at the Head Office of the bank.  "
                Guideline	: Acknowledgement receipt - Notice to this effect should be displayed prominently at the offices / branches for information of the public
                Obligation	: Bank shall display a notice on availability of the Acknowledgement receipt to customers prominently at the offices / branches for information of the public.
            """

            "Instructions:\n"
            "1. Locate each paragraph/section containing guidelines (keywords: 'shall', 'must', 'required to') which can be percieved as instructions to banks\n"
            "3. Output only a JSON array of objects with those three keys, no extras.\n\n"
            "4. RBI Regulation as HTML contains position information as data-page_number and data-bbox atrributes for each guideline"
            "4. Make sure the Position information is accurately extracted for the each Guideline observed using  data-page_number and data-bbox atrributes"
            "5. Make sure understand the guideline and generate obligation similar to rephrasing pattern of the samples shared in the same context"
            "6. Use obligation to generate action items for the bank"
            "7. Always make sure to track and generate Position infromation for each Guideline using data-page_number and data-bbox atrributes "
            f"RBI Regulation as HTML:\n{chunk}"
        )
        completion = with_key_rotation(
        client_openai.beta.chat.completions.parse,
            model="gpt-4.1-mini-2025-04-14",
            messages=[
                {"role": "system", "content": "Extract structured obligations from RBI HTML docs as an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items"},
                {"role": "user",   "content": prompt},
            ],
            response_format=ObligationList,
        )
        obls = completion.choices[0].message.parsed.obligations_list
        # convert Pydantic models → dicts
        for o in obls:
            all_obls.append(o.dict())
    return all_obls

# ----------------------------------------------------------------------------
# HTML Utility: strip data- attributes and extract positions
# ----------------------------------------------------------------------------

def strip_data_attributes(html: str) -> str:
    """
    Remove all data- attributes (e.g., data-page, data-bbox) from HTML content,
    then convert the cleaned HTML to Markdown.
    """
    # Remove data-* attributes
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all():
        attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
        for attr in attrs_to_remove:
            del tag.attrs[attr]
    cleaned_html = str(soup)

    # Convert HTML to Markdown
    markdown_text = html2text.html2text(cleaned_html)
    return markdown_text


def extract_positions_from_html(html: str) -> List[Dict]:
    """
    Parse the chunk HTML and return a list of positions for each data-page/bbox tag.
    """
    soup = BeautifulSoup(html, "html.parser")
    positions: List[Dict] = []
    for tag in soup.find_all(attrs={"data-page": True}):
        try:
            page_num = int(tag["data-page"])
        except (ValueError, TypeError):
            page_num = None
        bbox = json.loads(tag.get("data-bbox", "[]"))
        positions.append({"page": page_num, "bbox": bbox})
    return positions

# Import embeddings from common utils
from utils.openai_utils import en_embeddings, get_embedding_size

# Get embedding size from utils
embedding_size = get_embedding_size()

# Qdrant host
tvector_host = Variable.get("vector_db_host")

# DocumentDB
documentdb_uri = Variable.get("documentdb_uri")


# ----------------------------------------------------------------------------
# Check if Withdrawn
# ----------------------------------------------------------------------------

def is_diagonal(bbox: fitz.Rect, tol: float = 0.3) -> bool:
    """
    Returns True if the bbox is roughly square (i.e. width ≈ height),
    which is a good heuristic for text rotated ~45° across the page.
    """
    w = bbox.x1 - bbox.x0
    h = bbox.y1 - bbox.y0
    if h == 0:
        return False
    ratio = w / h
    return abs(ratio - 1.0) < tol

def has_diagonal_forbidden_watermark(
    pdf_path: str,
    size_threshold: float = 20.0,
    ratio_tolerance: float = 0.3
) -> bool:
    """
    Open the PDF at pdf_path and scan every text‐span in rawdict mode.
    Return True only if:
      1) the span's text, when stripped of whitespace, contains "withdrawn"
      2) the span's font size is >= size_threshold
      3) the span's bbox is roughly square (diagonal orientation)
      4) the span does NOT contain "withdrawn" or "superseded"
    """
    doc = fitz.open(pdf_path)
    for pno, page in enumerate(doc, start=1):
        raw = page.get_text("rawdict")
        for block in raw["blocks"]:
            if block["type"] != 0:
                continue
            for line in block["lines"]:
                for span in line["spans"]:
                    text = "".join(ch["c"] for ch in span["chars"])
                    norm = text.lower().replace(" ", "").replace("\n", "")

                    if "withdrawn" in norm or "superseded" in norm:
                        continue  # skip any span with either term

                    size = span["size"]
                    if size < size_threshold:
                        continue

                    if not is_diagonal(fitz.Rect(span["bbox"]), tol=ratio_tolerance):
                        continue

                    # For debugging or logging
                    print(f"→ Skipped span on page {pno} due to forbidden keywords.")
    return False


# ----------------------------------------------------------------------------
# PDF Parsing and HTML AST Construction
# ----------------------------------------------------------------------------

def parse_pdf_to_html_ast(pdf_path: str) -> BeautifulSoup:
    """
    Parse PDF and build HTML AST (<section>, <h1>-<h6>, <p>, <ul>/<ol>, <li>)
    using font-size, boldness, spacing. Attach data-page/bbox attributes.
    """
    doc = fitz.open(pdf_path)
    # Collect font-size frequencies
    size_counts: Dict[float,int] = {}
    for page in doc:
        blocks = page.get_text("dict")["blocks"]
        for blk in blocks:
            if blk.get("type") != 0:
                continue
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    size = round(span.get("size",0),1)
                    size_counts[size] = size_counts.get(size,0) + 1
    # Determine body text size (most common)
    if not size_counts:
        body_size = 0
    else:
        body_size = max(size_counts, key=size_counts.get)
    # Map larger sizes to heading levels
    heading_sizes = sorted([s for s in size_counts.keys() if s > body_size], reverse=True)
    size_to_level: Dict[float,int] = {}
    for idx,size in enumerate(heading_sizes[:6]):
        size_to_level[size] = idx+1
    max_heading = 6

    soup = BeautifulSoup("", "html.parser")
    root = soup.new_tag("div")
    soup.append(root)

    # Stack: (section_tag, level)
    section_stack: List[tuple] = [(root, 0)]

    for page_num, page in enumerate(doc, start=1):
        for blk in page.get_text("dict")["blocks"]:
            if blk.get("type") != 0:
                continue
            spans = [span for line in blk.get("lines",[]) for span in line.get("spans",[])]
            if not spans:
                continue
            block_max = max(round(span.get("size",0),1) for span in spans)
            lines_text = []
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    lines_text.append(span.get("text",""))
                lines_text.append("\n")
            text = "".join(lines_text).strip()
            if not text:
                continue

            # Heading detection
            if block_max > body_size:
                level = size_to_level.get(round(block_max,1), max_heading)
                tag = soup.new_tag(f"h{level}")
                tag.string = text
                tag["data-page"] = str(page_num)
                tag["data-bbox"] = json.dumps(blk.get("bbox"))
                # Pop stack until parent level < this level
                while section_stack and section_stack[-1][1] >= level:
                    section_stack.pop()
                parent = section_stack[-1][0]
                sec = soup.new_tag("section")
                sec["data-page"] = str(page_num)
                sec["data-bbox"] = json.dumps(blk.get("bbox"))
                parent.append(sec)
                sec.append(tag)
                section_stack.append((sec, level))
            else:
                parent = section_stack[-1][0]
                flat = text.replace("\n"," ").strip()
                # List detection
                if re.match(r"^(\u2022|\*|-|\d+[\.\)])\s+", flat):
                    if re.match(r"^\d+[\.\)]\s+", flat):
                        lst_name = "ol"
                    else:
                        lst_name = "ul"
                    last = parent.contents[-1] if parent.contents else None
                    if not (hasattr(last,"name") and last.name==lst_name):
                        lst = soup.new_tag(lst_name)
                        lst["data-page"] = str(page_num)
                        lst["data-bbox"] = json.dumps(blk.get("bbox"))
                        parent.append(lst)
                    else:
                        lst = last
                    li = soup.new_tag("li")
                    li.string = flat
                    li["data-page"] = str(page_num)
                    li["data-bbox"] = json.dumps(blk.get("bbox"))
                    lst.append(li)
                else:
                    p = soup.new_tag("p")
                    p.string = flat
                    p["data-page"] = str(page_num)
                    p["data-bbox"] = json.dumps(blk.get("bbox"))
                    parent.append(p)
    return soup


def chunk_html_ast(soup: BeautifulSoup, max_chars: int=3000) -> List[Dict]:
    """
    Split HTML AST into ~max_chars chunks, preserving boundaries.
    Returns list of {content: html_str, positions: [...]}
    """
    chunks: List[Dict] = []
    curr_html = ""
    curr_pos: List[Dict] = []

    def flush():
        nonlocal curr_html, curr_pos
        if curr_html:
            chunks.append({"content": curr_html, "positions": curr_pos.copy()})
            curr_html = ""
            curr_pos.clear()

    # Determine root container for top-level elements
    root = soup.find("div") or soup

    # Iterate direct children of the root
    for elem in root.find_all(recursive=False):
        if not hasattr(elem, 'name'):
            continue
        e_html = str(elem)
        # Collect positions metadata
        pos_list: List[Dict] = []
        for tag in elem.find_all(attrs={"data-page": True}):
            try:
                page_num = int(tag["data-page"])
            except ValueError:
                page_num = None
            bbox = json.loads(tag.get("data-bbox", "[]"))
            pos_list.append({"page": page_num, "bbox": bbox})

        # Flush if adding this element exceeds max_chars
        if curr_html and len(curr_html) + len(e_html) > max_chars:
            flush()
        # Append element HTML and positions
        curr_html += e_html
        curr_pos.extend(pos_list)

    # Flush any remaining content
    flush()
    return chunks


# ----------------------------------------------------------------------------
# In-line summarizer + topic extractor with JSON parse fallback
# ----------------------------------------------------------------------------
def recursive_split(text: str, max_len: int = 50000) -> List[str]:
    if len(text) <= max_len:
        return [text]
    mid = len(text) // 2
    split_point = text.rfind(". ", 0, mid)
    if split_point == -1:
        split_point = mid
    return recursive_split(text[:split_point], max_len) + recursive_split(text[split_point:], max_len)


def summarize_and_extract(chunk: str, max_words: int = 150, max_topics: int = 5) -> Dict[str, Any]:

    class SummaryAndTopics(BaseModel):
        summary: str
        topics: List[str]

    prompt = f"""
You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.

QUERY: Provide a JSON object with two fields:
  - "summary": a concise overview in strictly {max_words} maximum words
  - "topics": up to {max_topics} key topics from the content

CONTEXT:
{chunk}
"""
    completion = with_key_rotation(
        client_openai.beta.chat.completions.parse,
        model="gpt-4.1-mini-2025-04-14",
        messages=[
            {"role":"system","content":"You are a regulatory compliance summarizer and topic extractor for RBI guidelines."},
            {"role":"user","content":prompt}
        ],
        response_format=SummaryAndTopics,
        )
    data: SummaryAndTopics = completion.choices[0].message.parsed
    return data.dict()

# ------------------------------------------------------------------------------

# ------------------------------------------------------------------------------
# OpenAI Structured Extraction for RBI Metadata
class DocumentType(str, Enum):
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    CIRCULAR = "circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    TENDER = "tender"
    PUBLICATION = "publication"
    OTHER = "other"

class RBIMetadataExtraction(BaseModel):
    document_title: str
    document_type: DocumentType
    document_number: str
    date_of_issue: str
    addressee: str
    is_applicable_to_banks: bool
    is_exclusive_to_nbfc: bool
    is_exclusive_to_co_operative_banks: bool
    addressee_entities: list[str]
    addressee_person: list[str]
    applicable_departments: list[str]
    is_withdrawn: bool
    keywords: list[str]
    effective_date: str
    supersedes: list[str]
    summary:str
    short_summary:str
    topics:List[str]

def extract_rbi_metadata(document_text: str) -> RBIMetadataExtraction:
    slice_ = document_text[:6000]
    metadata_prompt = (
        "As an expert Complaince officer working in a Bank, Analyze the following RBI regulation provided in Document Content as HTML and generate a structured JSON output with the following fields:\n"
        "1. 'document_title' (string) - The full title of the document.\n"
        "2. 'document_type' (string) - The RBI regulation/document type (value can be master_direction, master_circular, notification, press_release, speech, tender, publication or other).\n"
        """3. 'document_number' (string) - Similar patterns like RBI/YYYY-YY/NNN, DEPT.DIV.NN/NN.NN.NNN/YYYY-YY or similar reference numbers. Can be found in the heading section of the document
                                          And if document_type is press_release the document_number will be found in the footer or towards the end of page """
        "4. 'date_of_issue' (string in DD/MM/YYYY format) - The official issue date.\n"
        "5. 'addressee' (list of strings) - The financial institutions to which the document is addressed.\n"
        "6. 'is_applicable_to_banks' (boolean) - Whether the Regulation is applicable to All Kinds of Banks except NBFC and Co-operative(true/false).\n"
        "6. 'is_exclusive_to_nbfc' (boolean) - Whether the Regulation is exclusive for NBFCs.\n"
        "6. 'is_exclusive_to_co_operative_banks' (boolean) - Whether the Regulation is exclusive for Co-operative banks (true/false).\n"
        "7. 'addressee_entities' (list of strings) - Entities such as Scheduled Banks, NBFCs, etc.\n"
        "8. 'addressee_person' (list of strings) - If addressed to individuals within the institution.\n"
        "9. 'applicable_departments' (array of department codes) - Departments this applies to.\n"
        "10. 'is_withdrawn' (boolean) - If the document is mentioned to be withdrawn.\n"
        "11. 'keywords' (array of strings) - Important keywords for search indexing.\n"
        "12. 'effective_date' (string in DD/MM/YYYY format) - When the circular takes effect.\n"
        "13. 'supersedes' (array of strings) - Any previous circulars this replaces.\n\n"
        "Instructions:\n"
        "- Think like an Experience and Expert Complaince officer "
        "- Look for department codes like DOR, DEPR, DSIM, DBR, DBS, DCM in the circular number, title, authority, or signature.\n"
        "- Identify the type of regulation Master Direction, Master Circular, Circular, Notification, Press Relase or Other\n"
        "- Extract key points, updates, repeals, deadlines, penalties, and technology recommendations.\n"
        "Regulation Content:\n"
        f"{slice_}\n"
    )

    completion = with_key_rotation(
        client_openai.beta.chat.completions.parse,
        model="gpt-4.1-mini-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert at extracting structured data from RBI regulatory documents."},
            {"role": "user", "content": metadata_prompt}
        ],
        response_format=RBIMetadataExtraction,
        )
    metadata: RBIMetadataExtraction = completion.choices[0].message.parsed

    # normalize dates
    metadata.date_of_issue  = normalize_date(metadata.date_of_issue)
    metadata.effective_date = normalize_date(metadata.effective_date)
    return metadata


def extract_rbi_summaries(document_text: str, max_chunk_words: int = 100, final_max_words: int = 170, final_max_topics: int = 10) -> dict:
    """
    Break the document into chunks, generate mini-summaries + topics for each,
    then aggregate into one concise summary and a deduplicated topic list.
    Returns a dict with keys: 'summary' and 'topics'.
    """
    pieces = recursive_split(document_text)
    mini_results = [
        summarize_and_extract(strip_data_attributes(piece), max_words=max_chunk_words)
        for piece in pieces
    ]

    # build up a long raw summary
    full_summary = "".join(m.get("summary", "") for m in mini_results if m.get("summary"))

    # dedupe topics in order
    seen = set()
    aggregated_topics = []
    for m in mini_results:
        for topic in m.get("topics", []):
            if topic not in seen:
                seen.add(topic)
                aggregated_topics.append(topic)

    # final concise summary + pruned topic list
    final_block = summarize_and_extract(
        full_summary + "\nTopics:\n" + "\n".join(aggregated_topics),
        max_words=final_max_words,
        max_topics=final_max_topics
    )
    return (final_block.get("summary", ""),final_block.get("topics", []))


# ----------------------------------------------------------------------------
# DocumentDB Storage
# ----------------------------------------------------------------------------

def download_tls_ca_file():
    try:
        resp = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        resp.raise_for_status()
        with open("/tmp/global-bundle.pem","wb") as f:
            f.write(resp.content)
        logger.info("TLS CA file downloaded.")
    except Exception as e:
        logger.error(f"Failed to download TLS CA: {e}")
        raise


def check_if_exists(document_number):
    if not documentdb_uri.startswith(("mongodb://","mongodb+srv://")):
        raise ValueError("Invalid DocumentDB URI")
    client = MongoClient(
        documentdb_uri,
        tls=True,
        tlsCAFile="/tmp/global-bundle.pem",
        replicaSet="rs0",
        readPreference="secondaryPreferred",
        retryWrites=False
    )
    db = client["rbi"]
    coll_name = "rbi_regulations"
    if coll_name not in db.list_collection_names():
        db.create_collection(coll_name)
    coll = db[coll_name]

    # Use the correct PyMongo method:
    document = coll.find_one({"document_number": document_number})
    logger.info("looked up document: %s", document)

    return document



def store_metadata_in_documentdb(metadata:dict):
    if not documentdb_uri.startswith(("mongodb://","mongodb+srv://")):
        raise ValueError("Invalid DocumentDB URI")
    download_tls_ca_file()
    client = MongoClient(
        documentdb_uri,
        tls=True,
        tlsCAFile="/tmp/global-bundle.pem",
        replicaSet="rs0",
        readPreference="secondaryPreferred",
        retryWrites=False
    )
    try:
        db = client["rbi"]
        coll_name = "rbi_regulations"
        if coll_name not in db.list_collection_names():
            db.create_collection(coll_name)
        coll = db[coll_name]
        unique_id = metadata.get("document_number")
        coll.update_one({"_id": unique_id}, {"$set": metadata}, upsert=True)
        logger.info("Stored metadata in DocumentDB.")
    finally:
        client.close()

# ----------------------------------------------------------------------------
# Extract Document Offline
# ----------------------------------------------------------------------------

def extract_document_code(pdf_path):
    """Extracts Document Code, Dept, and Status from the extracted text."""
    
    def extract_text_from_pdf(pdf_path):
        """Extracts text from each page in the PDF and returns it."""
        doc = fitz.open(pdf_path)
        try:
            full_text = ""
            # Determine how many pages we actually have (max 2)
            pages_to_extract = min(2, doc.page_count)
            for page_number in range(pages_to_extract):
                page = doc.load_page(page_number)
                full_text += page.get_text()
            return full_text
        finally:
            doc.close()

    text = extract_text_from_pdf(pdf_path)
    # Extract Document Code (assumed to be at the top left)
    document_code_match = re.search(r"^\s*(RBI/\d{4}-\d{2}/\d+.*?Circular No\. \d+)", text, re.MULTILINE)
    document_code = document_code_match.group(1).strip() if document_code_match else "Not Found"
  
    return document_code
   


# ----------------------------------------------------------------------------
# Qdrant Ingestion
# ----------------------------------------------------------------------------

def ingest_chunks_to_qdrant(collection:str, chunks_payload:List[Dict], document_id:str,
                            document_summary:str, topics:List[str], document_metadata:dict):
    client = QdrantClient(host=tvector_host, port=6333)
    sparse = FastEmbedSparse(model_name="Qdrant/bm25",model_kwargs={"device":"cpu"})
    client.set_sparse_model("Qdrant/bm25")
    # ensure collection
    if not client.collection_exists(collection):
        sparse_params = client.get_fastembed_sparse_vector_params()
        try:
            client.create_collection(
                collection_name=collection,
                vectors_config={"size":embedding_size,"distance":"Cosine"},
                sparse_vectors_config=sparse_params,
                hnsw_config = models.HnswConfigDiff(m=32, ef_construct=150, full_scan_threshold=10000)
            )
        except:
            pass
    qstore = QdrantVectorStore(
        client=client,
        collection_name=collection,
        embedding=en_embeddings,
        sparse_embedding=sparse,
        retrieval_mode=RetrievalMode.HYBRID,
        sparse_vector_name="fast-sparse-bm25"
    )
    texts, metas = [], []
    for idx,payload in enumerate(chunks_payload):
        # html = strip_data_attributes(payload["content"])
        html = payload["content"]
        chunk_text = (
            f"Document Summary:\n{document_summary}\n\n"
            f"Topics:\n{', '.join(topics)}\n\n"
            f"Content:\n{html}"
        )
        texts.append(chunk_text)
        meta = {
            "document_id": document_id,
            "chunk_index": idx,
            "positions": payload["positions"],
            **document_metadata
        }
        metas.append(meta)
    qstore.add_texts(texts, metas)
    logger.info(f"Ingested {len(texts)} chunks of '{document_id}' into Qdrant.")

# ----------------------------------------------------------------------------
# Airflow DAG Definition
# ----------------------------------------------------------------------------

def _fetch_files_from_s3() -> List[str]:
    """Read S3 URLs from CSV file instead of directly querying S3"""
    dag_folder = os.path.dirname(__file__)
    csv_file_path = os.path.join(dag_folder, "all_missing_s3_urls_combined.csv")
    
    # Download CSV from S3 if it's stored there
    # try:
    #     bucket = Variable.get("s3_bucket_name")
    #     client = boto3.client(
    #         "s3",
    #         aws_access_key_id=Variable.get("aws_access_key_id"),
    #         aws_secret_access_key=Variable.get("aws_secret_access_key")
    #     )
        
    #     # Try to download the CSV file from S3 root
    #     try:
    #         client.download_file(bucket, "all_missing_s3_urls_combined.csv", csv_file_path)
    #         logger.info(f"Downloaded CSV from S3 to {csv_file_path}")
    #     except:
    #         logger.warning("CSV file not found in S3 root, expecting it to be available locally")
    # except Exception as e:
    #     logger.warning(f"Could not download CSV from S3: {e}. Will try to read from local path.")
    
    # Read the CSV file
    urls = []
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                url = row.get('url', '').strip()
                if url and url.lower().endswith('.pdf'):
                    urls.append(url)
        
        logger.info(f"Found {len(urls)} PDF URLs in CSV file.")
        return urls
        
    except FileNotFoundError:
        logger.error(f"CSV file not found at {csv_file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading CSV file: {e}")
        raise

with DAG(
    "missing_regulations_processor",
    default_args={"owner":"airflow","start_date":datetime(2025,1,1),"retries":0},
    catchup=False,
    max_active_runs=8,
    # concurrency=8,
    description="Process RBI regs via PDF->HTML->chunks->Qdrant",
) as dag:

    @task
    def process_file(file_key:str) -> Dict:
        # Extract bucket and key from S3 URL if file_key is a full URL
        import urllib.parse
        
        if file_key.startswith('http'):
            # It's a full S3 URL, parse it
            parsed_url = urllib.parse.urlparse(file_key)
            if '.s3.amazonaws.com' in parsed_url.netloc:
                # Format: https://bucket-name.s3.amazonaws.com/path/to/file.pdf
                bucket_name = parsed_url.netloc.split('.s3.amazonaws.com')[0]
                actual_key = parsed_url.path.lstrip('/')
            elif 's3.amazonaws.com' in parsed_url.netloc:
                # Format: https://s3.amazonaws.com/bucket-name/path/to/file.pdf
                path_parts = parsed_url.path.lstrip('/').split('/', 1)
                bucket_name = path_parts[0]
                actual_key = path_parts[1] if len(path_parts) > 1 else ''
            else:
                raise ValueError(f"Invalid S3 URL format: {file_key}")
        else:
            # It's just a key, use the configured bucket
            bucket_name = Variable.get("s3_bucket_name")
            actual_key = file_key
        
        s3 = boto3.client(
            "s3",
            aws_access_key_id=Variable.get("aws_access_key_id"),
            aws_secret_access_key=Variable.get("aws_secret_access_key")
        )
        local_path = f"/tmp/{os.path.basename(actual_key)}"
        s3.download_file(bucket_name, actual_key, local_path)
        logger.info(f"Downloaded {file_key} -> {local_path}")
        
        extracted_document_code = extract_document_code(local_path)
        logger.info(f"extracted_document_code -> {extracted_document_code}")
        if (extracted_document_code!="Not Found") and check_if_exists(extracted_document_code):
            logger.info("Processed already - Local check")
            return {"metadata": m_dict}

        # Parse PDF -> HTML AST
        soup = parse_pdf_to_html_ast(local_path)
        full_html = str(soup)

        # Metadata extraction
        text_plain = soup.get_text(separator="\n")
        metadata = extract_rbi_metadata(full_html)
        metadata.is_withdrawn = has_diagonal_forbidden_watermark(local_path)
        m_dict = metadata.dict()
        m_dict.update({
            "s3_url": file_key if file_key.startswith('http') else f"https://{bucket_name}.s3.amazonaws.com/{actual_key}"
        })

        # Ingesting duplicates also
        # if check_if_exists(metadata.document_number):
        #     logger.info("Processed already - Post extraction check")
        #     return {"metadata": m_dict}

        if metadata.is_exclusive_to_nbfc or metadata.is_exclusive_to_co_operative_banks:
            logger.info(f"{file_key} is not applicable to Banks")
            return {"reason": f"{file_key} is not applicable to Banks"}

        if metadata.document_type == DocumentType.MASTER_DIRECTION :
            # ── NEW: extract obligations from the raw HTML chunks ───────────────────
            #   - use a larger chunk size so we don't split obligations in half
            raw_chunks = [c["content"] for c in chunk_html_ast(soup, max_chars=30000)]
            #   - strip any lingering data-* tags so the model sees clean HTML
            clean_chunks = [strip_data_attributes(c) for c in raw_chunks]
            obligations = extract_obligations(clean_chunks)
            m_dict["obligations"] = obligations

        summary, topics = extract_rbi_summaries(strip_data_attributes(full_html))
        metadata.summary = summary
        metadata.topics = topics

        store_metadata_in_documentdb(m_dict)

        if metadata.document_type == DocumentType.MASTER_DIRECTION :
            del m_dict["obligations"]

         # Chunk HTML AST
        chunks = chunk_html_ast(soup, max_chars=6000)

        # 1) Merge too-small initial chunks (<500 chars) into combined_chunks, with 100-char overlap
        combined_chunks: List[Dict] = []
        overlap_chars = 800
        buffer_content = ""
        # We'll recalc positions after merging
        for ch in chunks:
            ch_content = ch["content"]
            if len(buffer_content) + len(ch_content) <= 6000:
                buffer_content += ch_content
            else:
                if buffer_content:
                    # recalc positions for merged piece
                    combined_chunks.append({
                        "content": buffer_content,
                        "positions": extract_positions_from_html(buffer_content)
                    })
                overlap_text = buffer_content[-overlap_chars:] if len(buffer_content) > overlap_chars else buffer_content
                buffer_content = overlap_text + ch_content
        if buffer_content:
            combined_chunks.append({
                "content": buffer_content,
                "positions": extract_positions_from_html(buffer_content)
            })

        # 2) Subdivide only the large combined_chunks (>1000 chars) into overlapping smaller chunks
        small_chunks: List[Dict] = []
        for ch in combined_chunks:
            content = ch["content"]
            if len(content) > 8000:
                words = content.split()
                overlap = 800
                max_words = 6000  # desired words per sub-chunk
                start = 0
                while start < len(words):
                    segment = words[start:start+max_words]
                    seg_text = " ".join(segment)
                    # recalc positions for this sub-chunk
                    small_chunks.append({
                        "content": seg_text,
                        "positions": extract_positions_from_html(seg_text)
                    })
                    if start + max_words >= len(words):
                        break
                    start += max_words - overlap
            else:
                small_chunks.append({
                    "content": content,
                    "positions": extract_positions_from_html(content)
                })

        logger.info(f"Created {len(chunks)} original chunks.")
        logger.info(f"Created {len(small_chunks)} small chunks.")
        logger.info(f"Position is missing in {len([position_missing_chunk for position_missing_chunk in (combined_chunks+small_chunks) if position_missing_chunk['positions'] == []])} chunks")
        
        doc_id = metadata.document_number or metadata.document_title or file_key
        # ingest_chunks_to_qdrant(f"rbi_bank_{metadata.document_type.value}", combined_chunks, doc_id, metadata.summary, metadata.topics, m_dict)
        ingest_chunks_to_qdrant(f"rbi_{metadata.document_type.value}", small_chunks, doc_id, metadata.short_summary, metadata.topics, m_dict)

        return {"metadata": metadata.dict(), "chunks": len(chunks)}

    
    files = _fetch_files_from_s3()
    logger.info(f"Files {len(files)}.")
    # define your batch size
    batch_size = 1024

    # build list of batches
    batches = [
        files[i : i + batch_size]
        for i in range(0, len(files), batch_size)
    ]

    for batch in batches:
        results = process_file.expand(file_key=batch)