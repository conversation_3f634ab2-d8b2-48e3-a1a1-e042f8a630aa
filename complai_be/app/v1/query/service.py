import requests
from datetime import datetime
from app.summarization import generate_summary

def process_user_query(query: str, session_id: str, username: str, db, kb_id: str = "rbi_9"):
    """
    Processes the user's query by constructing a contextual query based on
    the session's long-term (summary) and short-term (last few messages) memory.
    """
    url = "http://localhost:8001/knowledge/query"
    headers = {
        "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Content-Type": "application/json",
    }
    
    # Retrieve the session document from the "sessions" collection.
    session = db["session"].find_one({"session_id": session_id, "username": username})
    if not session:
        session = {
            "session_id": session_id,
            "username": username,
            "messages": [],
            "summary": ""
        }
        db["session"].insert_one(session)
    
    # Use the stored summary (long-term memory) or default to an empty string.
    long_term_memory = session.get("summary", "") or ""
    messages = session.get("messages", [])
    
    # Use the last 5 messages for short-term memory.
    last_messages = messages[-5:]
    
    if len(messages) < 5:
        # If fewer than 5 messages, use all for short-term context.
        context = "\n".join([f"User: {msg.get('query')}\nBot: {msg.get('content')}" for msg in messages])
        contextual_query = f"query: {query}\n short_term_context:\n{context}"
    else:
        # Every 5 messages, update the long-term summary.
        if len(messages) % 5 == 0:
            block_summary = generate_summary(
                [{"query": msg.get("query"), "content": msg.get("content")} for msg in last_messages]
            )
            long_term_memory += f"\n\n{block_summary}"
        short_term_context = "\n".join([f"User: {msg.get('query')}\nBot: {msg.get('content')}" for msg in last_messages])
        contextual_query = (
            f"long_term_memory:\n{long_term_memory}\n\n"
            f"short_term_context:\n{short_term_context}\n\n"
            f"query: {query}"
        )

    # Prepare the payload for the API call.
    payload = {
        "kb_id": kb_id,
        "query": contextual_query,
    }

    try:
        # Call the external knowledge API.
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        answer = data['data']['answer']
        metadata = data['data']['metadata']
        
        # Construct the new message to append.
        new_message = {
            "query": query,
            "content": answer,
            "metadata": metadata,
            "timestamp": datetime.utcnow()
        }
        # Append the new message to the session's messages.
        messages.append(new_message)
        
        # Update the session document with the new messages and updated summary.
        db["session"].update_one(
            {"session_id": session_id, "username": username},
            {"$set": {"messages": messages, "summary": long_term_memory}}
        )
        
        # Construct and return the final response.
        return {
            "query": query,
            "response": answer,
            "metadata": metadata,
        }
    except requests.exceptions.RequestException as e:
        raise Exception(f"Error making API call: {str(e)}")
    except KeyError as e:
        raise Exception(f"Unexpected response format: {str(e)}")
    except Exception as e:
        raise Exception(f"An unexpected error occurred: {str(e)}")
