# ocr_extractor.py
from pathlib import Path
import json
from mistralai import DocumentURLChunk  # Also available: ImageURLChunk, TextChunk
import time
import random
from typing import Any
import logging

from mistralai.models.sdkerror import SDKError

logger = logging.getLogger(__name__)


def extract_ocr_from_pdf(pdf_path: str, client, max_retries: int = 5) -> dict:
    """
    Uploads the PDF to Mistral's OCR service, processes it, and returns the OCR response as a dict.
    Retries the OCR-processing step on HTTP 429 errors.

    Args:
        pdf_path (str): Path to the PDF file.
        client: An instance of the Mistralai client.
        max_retries (int): Number of times to retry on 429 errors.

    Returns:
        dict: OCR response parsed from JSON.
    """
    pdf_file = Path(pdf_path)
    if not pdf_file.is_file():
        raise FileNotFoundError(f"{pdf_path} does not exist")

    # 1) Upload PDF file
    uploaded_file = client.files.upload(
        file={
            "file_name": pdf_file.stem,
            "content": pdf_file.read_bytes(),
        },
        purpose="ocr",
    )

    # 2) Get signed URL
    signed_url = client.files.get_signed_url(file_id=uploaded_file.id, expiry=1)

    # 3) OCR with retry on 429
    for attempt in range(1, max_retries + 1):
        try:
            pdf_response = client.ocr.process(
                document=DocumentURLChunk(document_url=signed_url.url),
                model="mistral-ocr-latest",
                include_image_base64=True
            )
            break  # success, exit retry loop

        except SDKError as e:
            msg = str(e)
            # check if it's a rate-limit error
            if "Status 429" in msg and attempt < max_retries:
                wait_secs = random.uniform(3, 6)
                logger.warning(
                    "Rate limited on OCR (429). Retry %d/%d after %.1f seconds.",
                    attempt, max_retries, wait_secs
                )
                time.sleep(wait_secs)
                continue
            # non-429 or out of retries: re-raise
            logger.error("OCR failed on attempt %d: %s", attempt, msg)
            raise

    else:
        # never broke out of the loop
        raise RuntimeError(
            f"OCR processing failed after {max_retries} retries due to rate limiting"
        )

    # 4) Parse JSON
    response_dict = json.loads(pdf_response.model_dump_json())

    # 5) Persist for debugging
    with open("ocr_response.json", "w", encoding="utf-8") as f:
        json.dump(response_dict, f, indent=4)

    return response_dict