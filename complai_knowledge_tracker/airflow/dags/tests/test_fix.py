#!/usr/bin/env python3
"""
Simple test to verify the PointStruct fix works
"""

import sys
from pathlib import Path

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))

def test_basic_import():
    """Test that we can import the qdrant_utils without errors"""
    print("🧪 Testing basic import...")
    
    try:
        from utils.qdrant_utils import qdrant_manager, generate_valid_point_id
        print("  ✅ Successfully imported qdrant_utils")
        return True
    except Exception as e:
        print(f"  ❌ Failed to import qdrant_utils: {e}")
        return False

def test_point_id_generation():
    """Test point ID generation"""
    print("\n🧪 Testing point ID generation...")
    
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        test_ids = ["test_doc_1", "article_123", "chunk_456"]
        for test_id in test_ids:
            point_id = generate_valid_point_id(test_id)
            print(f"  ✅ {test_id} -> {point_id}")
        
        return True
    except Exception as e:
        print(f"  ❌ Point ID generation failed: {e}")
        return False

def test_collection_format_detection():
    """Test collection format detection without actually connecting"""
    print("\n🧪 Testing collection format detection logic...")
    
    try:
        from utils.qdrant_utils import qdrant_manager
        
        # Test the method exists
        if hasattr(qdrant_manager, '_check_collection_format'):
            print("  ✅ Collection format detection method exists")
            return True
        else:
            print("  ❌ Collection format detection method missing")
            return False
            
    except Exception as e:
        print(f"  ❌ Collection format detection test failed: {e}")
        return False

def test_splade_availability():
    """Test SPLADE availability detection"""
    print("\n🧪 Testing SPLADE availability detection...")
    
    try:
        from utils.qdrant_utils import SPLADE_AVAILABLE, splade_encoder
        
        print(f"  📊 SPLADE_AVAILABLE: {SPLADE_AVAILABLE}")
        
        if SPLADE_AVAILABLE:
            print("  ✅ SPLADE dependencies are available")
        else:
            print("  ⚠️ SPLADE dependencies not available (expected in this environment)")
        
        # Test that splade_encoder can be created without errors
        if splade_encoder:
            print("  ✅ SPLADE encoder instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ SPLADE availability test failed: {e}")
        return False

def main():
    """Run basic tests to verify the fix"""
    print("🚀 Starting Basic Fix Verification Tests\n")
    
    tests = [
        ("Basic Import", test_basic_import),
        ("Point ID Generation", test_point_id_generation),
        ("Collection Format Detection", test_collection_format_detection),
        ("SPLADE Availability", test_splade_availability),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 BASIC FIX VERIFICATION SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        print("\n💡 Key Fixes Applied:")
        print("   ✅ Optional SPLADE dependencies (no crash if missing)")
        print("   ✅ Collection format detection logic")
        print("   ✅ PointStruct format compatibility")
        print("\n🔧 The main fix:")
        print("   - upsert_point() now detects collection format")
        print("   - Uses 'vector' for single-vector collections")
        print("   - Uses 'vectors' for named-vector collections")
        print("   - This should resolve the PointStruct validation errors")
    else:
        print("⚠️ Some basic tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
