#!/usr/bin/env python3
"""
Test the updated prompts to ensure they handle URLs correctly
"""

import sys
import os
sys.path.append('/Users/<USER>/selkea/complai_knowledge_tracker/airflow/dags')

def test_prompt_content():
    """Test that the prompts have the correct URL handling instructions"""
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT, UPDATE_ACTION_DETERMINER_PROMPT
        
        print("🧪 Testing Updated Prompt Content")
        print("=" * 80)
        
        # Test AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        print("\n📋 Testing AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:")
        
        # Check for critical URL handling rules
        critical_rules = [
            "ONLY use URLs from EXTRACTED_PDF_LINKS",
            "NEVER generate or invent URLs",
            "Empty URL fields if no links provided",
            "No fallback URLs",
            "NEVER use placeholder URLs like \"NOTI134.PDF\"",
            "leave new_document_url empty"
        ]
        
        for rule in critical_rules:
            if rule in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                print(f"   ✅ Found: {rule}")
            else:
                print(f"   ❌ Missing: {rule}")
        
        # Check that NOTI134 is not used as a positive example
        if "NOTI134.PDF" in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
            # Check if it's used as a forbidden example
            if "NEVER use placeholder URLs like" in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                print(f"   ✅ NOTI134.PDF mentioned only as forbidden example")
            else:
                print(f"   ❌ NOTI134.PDF found as positive example - this is wrong!")
        else:
            print(f"   ✅ No NOTI134.PDF references found")
        
        # Test UPDATE_ACTION_DETERMINER_PROMPT
        print("\n📋 Testing UPDATE_ACTION_DETERMINER_PROMPT:")
        
        update_rules = [
            "NEVER generate or invent URLs",
            "NEVER use placeholder URLs",
            "Leave URL fields empty",
            "do NOT create placeholder URLs"
        ]
        
        for rule in update_rules:
            if rule in UPDATE_ACTION_DETERMINER_PROMPT:
                print(f"   ✅ Found: {rule}")
            else:
                print(f"   ❌ Missing: {rule}")
        
        print("\n🎉 Prompt content validation complete!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sample_llm_input():
    """Test what the LLM would see with sample input"""
    
    print("\n🤖 Testing Sample LLM Input")
    print("=" * 80)
    
    # Sample enhanced content that would be passed to LLM
    sample_enhanced_content = """
    <p>FEMA 23(R)/(6)/2025-RB</p>
    <p>June 24, 2025</p>
    <p>All Authorised Dealers</p>
    <p class='head'>Foreign Exchange Management (Export of Goods & Services) (Amendment) Regulations, 2025</p>
    
    EXTRACTED_REFERENCE_NUMBERS: FEMA 23(R)/(6)/2025-RB
    EXTRACTED_PDF_LINKS: https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF
    EXTRACTED_RBI_PAGE_URLS: https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345
    """
    
    print("📄 Sample enhanced content:")
    print(sample_enhanced_content)
    
    print("\n✅ Key observations:")
    print("   📎 EXTRACTED_PDF_LINKS contains real PDF URL")
    print("   🔗 EXTRACTED_RBI_PAGE_URLS contains RBI page URL")
    print("   📋 EXTRACTED_REFERENCE_NUMBERS contains reference")
    
    print("\n🎯 Expected LLM behavior with updated prompts:")
    print("   ✅ Should use the EXTRACTED_PDF_LINKS URL exactly as provided")
    print("   ✅ Should NOT generate any placeholder URLs")
    print("   ✅ Should NOT use NOTI134.PDF or similar fallbacks")
    print("   ✅ Should leave URL fields empty if EXTRACTED_PDF_LINKS is empty")
    
    return True

def test_empty_links_scenario():
    """Test scenario where no PDF links are extracted"""
    
    print("\n🔍 Testing Empty Links Scenario")
    print("=" * 80)
    
    # Sample content with no PDF links
    sample_no_links = """
    <p>RBI/2025-26/64</p>
    <p>July 2, 2025</p>
    <p>All Banks</p>
    <p class='head'>Some Informational Circular</p>
    
    EXTRACTED_REFERENCE_NUMBERS: RBI/2025-26/64
    EXTRACTED_PDF_LINKS: 
    EXTRACTED_RBI_PAGE_URLS: 
    """
    
    print("📄 Sample content with no links:")
    print(sample_no_links)
    
    print("\n🎯 Expected LLM behavior:")
    print("   ✅ Should leave new_document_url empty ('')")
    print("   ✅ Should leave notification_pdf_url empty ('')")
    print("   ✅ Should NOT generate any placeholder URLs")
    print("   ✅ Should NOT use fallback URLs")
    
    return True

def main():
    """Main test function"""
    
    print("🚀 Testing Prompt URL Handling Updates")
    print("=" * 80)
    
    success = True
    
    # Test prompt content
    if not test_prompt_content():
        success = False
    
    # Test sample scenarios
    if not test_sample_llm_input():
        success = False
    
    if not test_empty_links_scenario():
        success = False
    
    if success:
        print("\n🎉 All prompt tests passed!")
        print("✅ Prompts should now prevent NOTI134.PDF fallbacks")
        print("✅ LLM will only use URLs from EXTRACTED_PDF_LINKS")
        print("✅ No more placeholder or generated URLs")
    else:
        print("\n❌ Some tests failed - check prompt content")
    
    return success

if __name__ == "__main__":
    main()
