import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from app.v2.query.retrieval.mongo_client import get_mongo_collection

from app.v2.query.utils.completion_client import completion_client


from app.v2.query.models import DocumentChunk
import yaml


class ContextExpanderWithFilter:
    """
    Combines MongoDB filter generation and context expansion into a single class.
    Generated by Copilot.
    """

    def __init__(self, model: str, prompt_file: str = "app/v2/query/prompts/metadata_filter_prompt.yaml"):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.mongo_coll = get_mongo_collection()
        self.model = model
        self.prompt_data = self._load_prompt(prompt_file)

    def _load_prompt(self, prompt_file: str) -> dict:
        try:
            with open(prompt_file, "r") as f:
                data = yaml.safe_load(f)
            return {
                "system_template": data.get("prompt_template_mongo", ""),
                "today": datetime.now().strftime("%d %m %Y")
            }
        except Exception as e:
            self.logger.error(f"Failed to load prompt file: {e}")
            return {"system_template": "", "today": ""}

    def generate_mongo_filter(self, query: str) -> Dict[str, Any]:
        """
        Generates a MongoDB filter based on the user query.
        Generated by Copilot.
        """
        self.logger.info(f"Generating MongoDB filter for query: {query}")
        try:
            # Load the prompt template and format it with the current date
            prompt = self.prompt_data["system_template"].format(
                today=self.prompt_data["today"])
            messages = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": f"Parse this query into a MongoDB filter: {query}"}
            ]
            # Send the messages to the model for generating the filter
            response = completion_client.completion(
                model=self.model,
                messages=messages,
            )
            self.logger.info(f"Generated MongoDB filter response: {response}")

            # Extract the filter from the response
            if isinstance(response, tuple) and len(response) > 0:
                mongo_filter = response[0]
            else:
                raise ValueError(f"Unexpected response format: {response}")

            # Parse the generated filter from JSON
            mongo_filter = json.loads(mongo_filter)

            # Ensure the filter is a dictionary
            if not isinstance(mongo_filter, dict):
                raise ValueError(
                    f"Generated filter is not a dictionary: {mongo_filter}")
        except Exception as e:
            self.logger.error(f"Error generating MongoDB filter: {e}")
            # Fallback to an empty filter if generation fails
            mongo_filter = {}
        return mongo_filter

    def expand_context(self, retrieved_points: List[Dict[str, Any]], mongo_filter: Dict[str, Any] = None) -> list:
        """
        Expands context by fetching documents and paragraphs from MongoDB.
        """
        if mongo_filter is None:
            mongo_filter = {}
        self.logger.info(
            f"Expanding context for {len(retrieved_points)} retrieved points")
        self.logger.info(f"Using MongoDB filter: {mongo_filter}")

        contexts = []

        if mongo_filter:
            # findall documents matching the filter
            docs = self.mongo_coll.find(mongo_filter)
            for doc in docs:
                doc_id = doc.get("_id")
                if not doc_id:
                    self.logger.warning(
                        f"Document ID not found in MongoDB document: {doc}")
                    continue
                # Assuming the document has a 'content' field that contains paragraphs
                paragraphs = doc.get("content", "").split("\n\n")
                for para_index, paragraph in enumerate(paragraphs):
                    context = {
                        "document_id": doc_id,
                        "document_title": doc.get("document_title", ""),
                        "paragraph_index": para_index,
                        "text": paragraph,
                        "s3_url": doc.get("s3_url", ""),
                        "metadata": doc.get("metadata", {})
                    }
                    contexts.append(context)

        else:
            for pt in retrieved_points:
                try:
                    if not isinstance(pt, DocumentChunk):
                        doc_chunk = DocumentChunk.model_validate({
                            "metadata": pt.get("metadata", {}),
                            "page_content": pt.get("page_content", ""),
                            **pt
                        })
                    else:
                        doc_chunk = pt

                    doc_id = doc_chunk.metadata.document_id
                    para_index = getattr(
                        doc_chunk.metadata, "paragraph_index", None) or 0

                    combined_filter = {"_id": doc_id}
                    combined_filter.update(mongo_filter)
                    # Fetch the document from MongoDB using the combined filter
                    doc = self.mongo_coll.find_one(combined_filter)

                    if not doc:
                        self.logger.warning(
                            f"Document not found in MongoDB: {doc_id} with filter {combined_filter}")
                        continue

                    paragraphs = doc.get("content", "").split("\n\n")
                    text = paragraphs[para_index] if para_index < len(
                        paragraphs) else ""

                    doc_metadata = doc.get("metadata", {})
                    context = {
                        "document_id": doc_id,
                        "document_title": doc.get("document_title", doc_chunk.metadata.document_title or doc_id),
                        "paragraph_index": para_index,
                        "text": text,
                        "s3_url": doc.get("s3_url", ""),
                        "metadata": {
                            "document_type": doc_metadata.get("document_type", doc_chunk.metadata.document_type),
                            "is_applicable_to_banks": doc_metadata.get("is_applicable_to_banks", doc_chunk.metadata.is_applicable_to_banks),
                            "date_of_issue": doc_metadata.get("date_of_issue", doc_chunk.metadata.date_of_issue)
                        }
                    }
                    contexts.append(context)
                except Exception as e:
                    self.logger.error(
                        f"Error expanding context for point: {pt} - {e}")

        self.logger.info(
            f"Context expansion complete. Total contexts: {len(contexts)}")
        return contexts

    def run(self, query: str, retrieved_points: List[Dict[str, Any]]) -> list:
        """
        Generates a MongoDB filter and expands context in a single workflow.
        """
        mongo_filter = self.generate_mongo_filter(query)
        return self.expand_context(retrieved_points, mongo_filter)
