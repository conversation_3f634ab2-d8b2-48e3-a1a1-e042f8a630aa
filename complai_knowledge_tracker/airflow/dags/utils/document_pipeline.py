"""
Document Processing Pipeline
Handles PDF parsing, metadata extraction, obligation extraction, and multi-vector embedding generation
"""

import logging
import tempfile
import os
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .config import config
from .pdf_utils import (
    parse_pdf_to_html_ast, chunk_html_ast, has_diagonal_withdrawn_watermark,
    extract_obligations, strip_data_attributes, extract_document_code
)
from .openai_utils import openai_manager, client_openai
from .qdrant_utils import ingest_chunks_to_qdrant
from .database_utils import store_metadata_in_documentdb, check_document_exists

logger = logging.getLogger(__name__)

class DocumentType(str, Enum):
    """Document type enumeration"""
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    CIRCULAR = "circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    TENDER = "tender"
    PUBLICATION = "publication"
    OTHER = "other"


class DocumentTypeMapper:
    """Utility class to map between different document type naming conventions"""

    # Mapping from RSS feed categories to DocumentType enum values
    RSS_TO_DOCUMENT_TYPE = {
        "circulars": DocumentType.CIRCULAR,
        "directions": DocumentType.CIRCULAR,  # Directions are treated as circulars
        "master_circulars": DocumentType.MASTER_CIRCULAR,
        "master_directions": DocumentType.MASTER_DIRECTION,
        "notifications": DocumentType.NOTIFICATION,
        "press_releases": DocumentType.PRESS_RELEASE,
        "speeches": DocumentType.SPEECHES,
        "tenders": DocumentType.TENDER,
        "publications": DocumentType.PUBLICATION,
    }

    # Mapping from DocumentType to collection names (with rbi_ prefix)
    DOCUMENT_TYPE_TO_COLLECTION = {
        DocumentType.MASTER_DIRECTION: "rbi_master_direction",
        DocumentType.MASTER_CIRCULAR: "rbi_master_circular",
        DocumentType.CIRCULAR: "rbi_circular",
        DocumentType.NOTIFICATION: "rbi_notification",
        DocumentType.PRESS_RELEASE: "rbi_press_release",
        DocumentType.SPEECHES: "rbi_speech",
        DocumentType.TENDER: "rbi_tender",
        DocumentType.PUBLICATION: "rbi_publication",
        DocumentType.OTHER: "rbi_other",
    }

    @classmethod
    def rss_category_to_collection_name(cls, rss_category: str) -> str:
        """Convert RSS feed category to Qdrant collection name"""
        document_type = cls.RSS_TO_DOCUMENT_TYPE.get(rss_category, DocumentType.OTHER)
        return cls.DOCUMENT_TYPE_TO_COLLECTION[document_type]

    @classmethod
    def document_type_to_collection_name(cls, document_type: DocumentType) -> str:
        """Convert DocumentType to Qdrant collection name"""
        return cls.DOCUMENT_TYPE_TO_COLLECTION[document_type]

    @classmethod
    def get_all_collection_names(cls) -> List[str]:
        """Get all possible collection names"""
        return list(cls.DOCUMENT_TYPE_TO_COLLECTION.values())

    @classmethod
    def categorize_document_title(cls, title: str) -> str:
        """Categorize document based on title and return collection name"""
        title_lower = title.lower()

        if "circular" in title_lower and "master" not in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.CIRCULAR]
        elif "direction" in title_lower and "master" not in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.CIRCULAR]  # Directions treated as circulars
        elif "master circular" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.MASTER_CIRCULAR]
        elif "master direction" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.MASTER_DIRECTION]
        elif "notification" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.NOTIFICATION]
        elif "press release" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.PRESS_RELEASE]
        elif "speech" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.SPEECHES]
        elif "tender" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.TENDER]
        elif "publication" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.PUBLICATION]
        else:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.OTHER]

@dataclass
class ProcessingResult:
    """Result of document processing"""
    success: bool
    document_id: str
    metadata: Optional[Dict[str, Any]] = None
    chunks_count: int = 0
    error_message: Optional[str] = None
    obligations_count: int = 0

class DocumentProcessor:
    """Main document processing pipeline"""
    
    def __init__(self):
        self.config = config
    
    def process_pdf_from_path(
        self,
        pdf_path: str,
        collection_name: Optional[str] = None,
        source_info: Optional[Dict[str, Any]] = None
    ) -> ProcessingResult:
        """
        Process a PDF file from local path
        """
        try:
            # Extract document code for duplicate checking
            document_code = extract_document_code(pdf_path)
            logger.info(f"Extracted document code: {document_code}")
            
            # Check if document already exists
            if document_code != "Not Found" and check_document_exists(document_code):
                logger.info(f"Document {document_code} already processed")
                return ProcessingResult(
                    success=True,
                    document_id=document_code,
                    error_message="Document already processed"
                )
            
            # Parse PDF to HTML AST
            soup = parse_pdf_to_html_ast(pdf_path)
            full_html = str(soup)
            
            # Extract metadata
            metadata = self._extract_metadata(full_html, pdf_path)

            # Check if document is withdrawn
            metadata["is_withdrawn"] = has_diagonal_withdrawn_watermark(pdf_path)

            # Add source information
            if source_info:
                metadata.update(source_info)

            # Determine collection name based on document type if not provided
            if collection_name is None:
                document_type = metadata.get("document_type", DocumentType.OTHER)
                if isinstance(document_type, str):
                    # Convert string to DocumentType enum
                    try:
                        document_type = DocumentType(document_type)
                    except ValueError:
                        document_type = DocumentType.OTHER
                collection_name = DocumentTypeMapper.document_type_to_collection_name(document_type)
                logger.info(f"Determined collection name: {collection_name} for document type: {document_type}")
            
            # Check if document is applicable to banks
            if metadata.get("is_exclusive_to_nbfc") or metadata.get("is_exclusive_to_co_operative_banks"):
                logger.info(f"Document {metadata.get('document_number')} not applicable to banks")
                return ProcessingResult(
                    success=True,
                    document_id=metadata.get("document_number", "unknown"),
                    error_message="Document not applicable to banks"
                )
            
            # Extract obligations for Master Directions
            obligations = []
            if metadata.get("document_type") == DocumentType.MASTER_DIRECTION:
                obligations = self._extract_obligations(soup)
                metadata["obligations"] = obligations
            
            # Generate summary and topics
            summary, topics = self._generate_summary_and_topics(full_html)
            metadata["summary"] = summary
            metadata["topics"] = topics
            
            # Store metadata in database
            store_metadata_in_documentdb(metadata)
            
            # Remove obligations from metadata for chunking (too large)
            if "obligations" in metadata:
                del metadata["obligations"]
            
            # Chunk the document
            chunks = self._create_chunks(soup)
            
            # Ingest chunks to Qdrant
            success = ingest_chunks_to_qdrant(
                collection=collection_name,
                chunks_payload=chunks,
                document_id=metadata.get("document_number", "unknown"),
                document_summary=summary,
                topics=topics,
                document_metadata=metadata
            )
            
            if not success:
                raise Exception("Failed to ingest chunks to Qdrant")
            
            return ProcessingResult(
                success=True,
                document_id=metadata.get("document_number", "unknown"),
                metadata=metadata,
                chunks_count=len(chunks),
                obligations_count=len(obligations)
            )
            
        except Exception as e:
            logger.error(f"Failed to process PDF {pdf_path}: {e}")
            return ProcessingResult(
                success=False,
                document_id="unknown",
                error_message=str(e)
            )
    
    def process_pdf_from_url(
        self,
        pdf_url: str,
        collection_name: Optional[str] = None,
        source_info: Optional[Dict[str, Any]] = None
    ) -> ProcessingResult:
        """
        Process a PDF file from URL by downloading it first
        """
        import requests
        
        try:
            # Download PDF to temporary file
            response = requests.get(pdf_url, timeout=30)
            response.raise_for_status()
            
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_path = tmp_file.name
            
            try:
                # Add URL to source info
                if source_info is None:
                    source_info = {}
                source_info["source_url"] = pdf_url
                
                # Process the downloaded file
                result = self.process_pdf_from_path(tmp_path, collection_name, source_info)
                return result
                
            finally:
                # Clean up temporary file
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
                    
        except Exception as e:
            logger.error(f"Failed to process PDF from URL {pdf_url}: {e}")
            return ProcessingResult(
                success=False,
                document_id="unknown",
                error_message=str(e)
            )
    
    def _extract_metadata(self, html_content: str, pdf_path: str) -> Dict[str, Any]:
        """Extract metadata from document content"""
        from .pdf_utils import extract_rbi_metadata
        
        try:
            metadata = extract_rbi_metadata(html_content)
            return metadata.dict()
        except Exception as e:
            logger.error(f"Failed to extract metadata: {e}")
            # Return basic metadata
            return {
                "document_title": os.path.basename(pdf_path),
                "document_type": DocumentType.OTHER,
                "document_number": "unknown",
                "date_of_issue": "",
                "is_applicable_to_banks": True,
                "is_exclusive_to_nbfc": False,
                "is_exclusive_to_co_operative_banks": False,
                "is_withdrawn": False
            }
    
    def _extract_obligations(self, soup) -> List[Dict[str, Any]]:
        """Extract obligations from HTML content"""
        try:
            # Create larger chunks for obligation extraction
            raw_chunks = [c["content"] for c in chunk_html_ast(soup, max_chars=30000)]
            clean_chunks = [strip_data_attributes(c) for c in raw_chunks]
            obligations = extract_obligations(clean_chunks)
            logger.info(f"Extracted {len(obligations)} obligations")
            return obligations
        except Exception as e:
            logger.error(f"Failed to extract obligations: {e}")
            return []
    
    def _generate_summary_and_topics(self, html_content: str) -> Tuple[str, List[str]]:
        """Generate summary and topics for the document"""
        from .pdf_utils import extract_rbi_summaries
        
        try:
            summary, topics = extract_rbi_summaries(strip_data_attributes(html_content))
            return summary, topics
        except Exception as e:
            logger.error(f"Failed to generate summary and topics: {e}")
            return "Summary not available", []
    
    def _create_chunks(self, soup) -> List[Dict[str, Any]]:
        """Create optimized chunks from HTML AST"""
        try:
            # Initial chunking
            chunks = chunk_html_ast(soup, max_chars=config.processing.max_chunk_chars)
            
            # Optimize chunks with overlap and merging
            optimized_chunks = self._optimize_chunks(chunks)
            
            logger.info(f"Created {len(optimized_chunks)} optimized chunks")
            return optimized_chunks
            
        except Exception as e:
            logger.error(f"Failed to create chunks: {e}")
            return []
    
    def _optimize_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize chunks by merging small ones and adding overlap"""
        if not chunks:
            return []
        
        optimized = []
        overlap_chars = config.processing.overlap_chars
        max_chars = config.processing.max_chunk_chars
        
        buffer_content = ""
        
        for chunk in chunks:
            content = chunk["content"]
            
            # If buffer + current chunk fits, add to buffer
            if len(buffer_content) + len(content) <= max_chars:
                buffer_content += content
            else:
                # Flush buffer if it has content
                if buffer_content:
                    optimized.append({
                        "content": buffer_content,
                        "positions": self._extract_positions_from_content(buffer_content)
                    })
                
                # Start new buffer with overlap
                if buffer_content and len(buffer_content) > overlap_chars:
                    overlap_text = buffer_content[-overlap_chars:]
                    buffer_content = overlap_text + content
                else:
                    buffer_content = content
        
        # Flush final buffer
        if buffer_content:
            optimized.append({
                "content": buffer_content,
                "positions": self._extract_positions_from_content(buffer_content)
            })
        
        return optimized
    
    def _extract_positions_from_content(self, content: str) -> List[Dict[str, Any]]:
        """Extract position information from content"""
        from .pdf_utils import extract_positions_from_html
        return extract_positions_from_html(content)

def ingest_single_document(
    collection: Optional[str],
    document_id: str,
    content: str,
    metadata: Dict[str, Any]
) -> bool:
    """
    Ingest a single document with multi-vector support using valid point IDs.
    If collection is None, determines collection name from document metadata.
    """
    try:
        from .qdrant_utils import qdrant_manager, generate_valid_point_id
        from .openai_utils import strip_data_attributes

        # Determine collection name if not provided
        if collection is None:
            document_type = metadata.get("document_type", DocumentType.OTHER)
            if isinstance(document_type, str):
                try:
                    document_type = DocumentType(document_type)
                except ValueError:
                    document_type = DocumentType.OTHER
            collection = DocumentTypeMapper.document_type_to_collection_name(document_type)
            logger.info(f"Determined collection name: {collection} for document type: {document_type}")

        # Clean content for embedding
        cleaned_content = strip_data_attributes(content)

        # Generate valid point ID
        valid_point_id = generate_valid_point_id(document_id)

        # Generate all vector types
        success = qdrant_manager.upsert_point(
            collection_name=collection,
            point_id=valid_point_id,
            payload={
                "content": content,
                "cleaned_content": cleaned_content,
                "original_document_id": document_id,  # Keep original ID in payload
                "metadata": metadata
            },
            text_for_embedding=cleaned_content
        )

        if success:
            logger.info(f"Successfully ingested document {document_id} into collection {collection}")

        return success

    except Exception as e:
        logger.error(f"Failed to ingest document {document_id}: {e}")
        return False

# Global document processor instance
document_processor = DocumentProcessor()
