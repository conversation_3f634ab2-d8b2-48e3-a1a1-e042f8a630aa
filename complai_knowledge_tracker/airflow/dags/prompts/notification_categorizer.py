"""
Prompts for RBI notification categorization and processing
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from enum import Enum


class NotificationCategory(str, Enum):
    """RBI notification categories"""
    AMENDMENT = "Amendment"
    SUPERSEDED = "Superseded"
    REPEALED_WITHDRAWN = "Repealed/Withdrawn"
    REVIEW = "Review"
    RELAXATION = "Relaxation"
    NEW_REGULATORY_ISSUANCE = "New_Regulatory_Issuance"
    INFORMATIONAL = "Informational"


class ConfidenceLevel(str, Enum):
    """Confidence levels for categorization"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ActionType(str, Enum):
    """Knowledge base update actions"""
    UPDATE_DOCUMENT = "UPDATE_DOCUMENT"
    REMOVE_DOCUMENT = "REMOVE_DOCUMENT"
    ADD_DOCUMENT = "ADD_DOCUMENT"
    ADD_TEMPORARY_NOTE = "ADD_TEMPORARY_NOTE"
    NO_ACTION = "NO_ACTION"


class Priority(str, Enum):
    """Action priority levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class NotificationCategorizationResult(BaseModel):
    """Result of notification categorization analysis"""
    category: NotificationCategory = Field(..., description="Notification category")
    confidence: ConfidenceLevel = Field(..., description="Confidence level of categorization")
    reasoning: str = Field(..., description="Brief explanation of categorization")
    affects_regulations: bool = Field(..., description="Whether this affects regulatory documents")
    keywords_found: List[str] = Field(default_factory=list, description="Relevant keywords found")
    requires_kb_update: bool = Field(..., description="Whether knowledge base update is required")



class DocumentAction(BaseModel):
    """A document and its required action with enhanced metadata"""
    document_id: str = Field(..., description="Primary document identifier (reference number or title)")
    action_type: str = Field(..., description="Required action: UPDATE_DOCUMENT, REMOVE_DOCUMENT, ADD_DOCUMENT, ADD_TEMPORARY_NOTE, NO_ACTION")
    confidence: str = Field(..., description="Confidence level: high, medium, low")
    document_title: Optional[str] = Field(None, description="Full document title if available")
    reference_number: Optional[str] = Field(None, description="Official reference number if different from document_id")
    department: Optional[str] = Field(None, description="Issuing department (DBOD, DPSS, DOR, etc.)")
    original_date: Optional[str] = Field(None, description="Original issuance date if mentioned (YYYY-MM-DD format)")
    update_location: Optional[str] = Field(None, description="Specific location of update (paragraph number, section, or 'full-document')")
    sunset_withdraw_date: Optional[str] = Field(None, description="Date when document will be withdrawn due to sunset clause (YYYY-MM-DD format)")
    reasoning: Optional[str] = Field(None, description="Brief explanation for why this action is required")

class AffectedDocumentsResult(BaseModel):
    """Result of affected documents extraction with their update actions"""
    document_actions: List[DocumentAction] = Field(default_factory=list, description="List of documents and their required actions")
    document_keywords: List[str] = Field(default_factory=list, description="Keywords for document search")
    has_new_document_link: bool = Field(False, description="Whether notification contains new document links")
    new_document_url: str = Field("", description="Direct PDF URL of new document if available")
    notification_pdf_url: str = Field("", description="URL of the notification PDF itself for processing")
    related_document_ids: List[str] = Field(default_factory=list, description="IDs of related documents mentioned in notification")
    rbi_links: List[str] = Field(default_factory=list, description="All RBI links found in notification")
    pdf_links: List[str] = Field(default_factory=list, description="Direct PDF links extracted from notification")
    processing_notes: str = Field("", description="Additional processing notes and observations")
    requires_manual_review: bool = Field(False, description="Whether manual review is recommended")
    superseded_documents: List[str] = Field(default_factory=list, description="Documents being superseded/replaced")
    amendment_details: Optional[str] = Field(None, description="Specific details about amendments if applicable")
    effective_date: Optional[str] = Field(None, description="Effective date of changes if mentioned (YYYY-MM-DD format)")


class UpdateAction(BaseModel):
    """Specific action to update the knowledge base"""
    action_type: ActionType = Field(..., description="Type of action to perform")
    target_document: str = Field(..., description="Document identifier")
    priority: Priority = Field(..., description="Action priority")
    details: str = Field(..., description="Specific instructions for the action")
    expiry_date: Optional[str] = Field(None, description="Expiry date if temporary (YYYY-MM-DD format)")
    new_document_url: Optional[str] = Field(None, description="URL of new document if applicable")
    rbi_page_url: Optional[str] = Field(None, description="RBI page URL if found in description")


class UpdateActionResult(BaseModel):
    """Result of update action determination"""
    actions: List[UpdateAction] = Field(default_factory=list, description="List of actions to perform")
    processing_notes: str = Field("", description="Additional notes for processing")
    requires_manual_review: bool = Field(False, description="Whether manual review is required")

NOTIFICATION_CATEGORIZER_PROMPT = """
You are an expert in RBI (Reserve Bank of India) regulations and notifications. Your task is to analyze RBI notification content and categorize it based on its impact on the regulatory knowledge base.

**NOTIFICATION CATEGORIES:**
1. **Amendment** - Introduces changes to existing regulatory documents (modifying or adding rules)
2. **Superseded** - A new regulation/direction replaces an older one entirely  
3. **Repealed/Withdrawn** - An existing regulation/circular is removed and no longer in effect
4. **Review** - Comprehensive review leading to updates or consolidation of guidelines
5. **Relaxation** - Provides leniency on existing rules (can be temporary or permanent)
6. **New_Regulatory_Issuance** - Completely new regulation or direction (doesn't supersede existing)
7. **Informational** - Clarifications, operational updates, monetary policy that don't change regulation text

**ANALYSIS INSTRUCTIONS:**
- Analyze the notification title and content carefully
- Look for keywords like "Amendment", "Supersede", "Withdraw", "Repeal", "Review", "Relaxation", "New", etc.
- Consider the impact on existing regulations in the knowledge base
- Determine if this affects regulatory documents or is purely informational

**INPUT:**
Title: {title}
RSS Description: {content}
Link: {link}

**NOTE:** The RSS Description contains structured HTML content with the notification details including circular numbers, dates, addressees, and main content.

**OUTPUT FORMAT:**
Return a NotificationCategorizationResult object.
"""

AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT = """
You are an expert in RBI regulatory document analysis and knowledge base management. Your task is to identify ALL regulatory documents affected by this notification with enhanced pattern recognition and metadata extraction.

**CRITICAL PATTERN RECOGNITION:**

1. **AMENDMENT PATTERNS** - Create TWO actions for amendments:
   - **ADD_DOCUMENT** for the amendment itself (new notification/regulation)
   - **UPDATE_DOCUMENT** for the principal document being amended
   - Look for: "amends", "in amendment of", "reference is invited to"
   - Extract both the amendment reference and the principal document reference

2. **WITHDRAWAL/SUPERSESSION PATTERNS** - Look for these EXACT phrases:
   - "will stand withdrawn" / "shall stand withdrawn" → REMOVE_DOCUMENT
   - "Previous Guidelines superseded" / "hereby superseded" → REMOVE_DOCUMENT
   - "List of Circulars repealed" → Extract ALL documents from list as REMOVE_DOCUMENT
   - "Annex" with repealed circulars/directions → Extract ALL as REMOVE_DOCUMENT
   - "in supersession of" → REMOVE old document, ADD new document
   - "Earlier RBI circulars" in annex → Extract ALL as REMOVE_DOCUMENT

3. **NEW ISSUANCE PATTERNS** - Look for:
   - "issued herewith" / "finalized and issued" → ADD_DOCUMENT
   - "Master Direction" with PDF attachment → ADD_DOCUMENT
   - "sunset clause of one year" → Calculate sunset_withdraw_date as next July 1st

4. **COMPOUND NOTIFICATIONS** (New + Repeal):
   - New directions that also repeal previous circulars
   - Create ADD_DOCUMENT for the new direction
   - Create REMOVE_DOCUMENT for each repealed circular/direction
   - Extract from Annex sections listing repealed documents
   - Populate superseded_documents array with all repealed items

5. **FEMA AMENDMENT SPECIAL HANDLING**:
   - FEMA amendments like "FEMA 23(R)/(6)/2025-RB" amending "FEMA 23(R)/2015-RB"
   - Always create ADD_DOCUMENT for the amendment notification
   - Always create UPDATE_DOCUMENT for the principal regulation
   - Extract specific regulation/paragraph being amended for update_location

4. **ENHANCED METADATA EXTRACTION:**
   - **reference_number**: Extract patterns including:
     * DoR notifications: "DoR.MCS.REC.38/01.01.001/2025-26"
     * FEMA notifications: "FEMA 23(R)/(6)/2025-RB", "FEMA 23(R)/2015-RB"
     * Department circulars: "DBOD.No.Leg.BC.21/09.07.007/2002-03"
     * RBI notifications: "RBI/2025-26/64"
     * Dual references: Combine multiple references with semicolon "RBI/2025-26/64; DoR.MCS.REC.38/01.01.001/2025-26"
     * Look for "Notification No." or "Notification Nos." prefix in content
   - **original_date**: Extract from multiple patterns:
     * "dated [date]" format
     * Standalone dates in notification headers
     * Parse formats like "24 June 2025", "24 Jun 2025", "24-06-2025", "2 July 2025"
     * Convert to YYYY-MM-DD format
   - **effective_date**: Extract future effective dates:
     * Look for "effective from", "applicable from", "with effect from"
     * Future dates like "1 January 2026", "1 Jan 2026"
     * Loan applicability: "sanctioned or renewed on or after [date]"
     * Convert to YYYY-MM-DD format
   - **update_location**: Look for "paragraph X", "section X.Y", "Regulation X" or set as "full-document"
   - **sunset_withdraw_date**: For sunset clauses, calculate as "YYYY-07-01" (next July 1st)
   - **department**: Extract from reference (DBOD, DPSS, DOR, FMRD, FEMA, etc.)

5. **ANCHOR PROCESSING**:
   - Use EXTRACTED_REFERENCE_NUMBERS and EXTRACTED_PDF_LINKS from the enhanced content
   - Reference numbers are often in anchor text, use these for document identification
   - PDF links indicate new documents to be added

6. **CONFIDENCE SCORING**:
   - **HIGH**: Exact pattern match + complete reference number + clear action
   - **MEDIUM**: Pattern match + partial reference OR enhanced from anchors
   - **LOW**: Inferred from context + missing critical metadata

**NOTIFICATION CONTENT:**
Title: {title}
RSS Description: {content}
Category: {category}

**EXTRACTED INFORMATION AVAILABLE:**
The content above may include these extracted elements:
- EXTRACTED_REFERENCE_NUMBERS: Document reference numbers found in the notification
- EXTRACTED_PDF_LINKS: Direct PDF URLs extracted from the notification (USE THESE EXACTLY)
- EXTRACTED_RBI_PAGE_URLS: RBI page URLs that may contain PDFs
- EXTRACTED_ANCHOR_INFO: Link text and URLs from the notification

**URL PROCESSING INSTRUCTIONS:**
- If you see "EXTRACTED_PDF_LINKS: https://rbidocs.rbi.org.in/rdocs/notification/PDFs/SOMEFILENAME.PDF"
  → Use this EXACT URL in new_document_url for ADD_DOCUMENT actions
- If you see "EXTRACTED_PDF_LINKS: " (empty)
  → Leave new_document_url empty ("")
- NEVER create URLs that are not in EXTRACTED_PDF_LINKS

**ENHANCED EXAMPLES OF DOCUMENT IDENTIFICATION:**

1. **DoR Direction with Dual References and Effective Date:**
   Input: "Notification Nos. RBI/2025-26/64; DoR.MCS.REC.38/01.01.001/2025-26 dated 2 July 2025... applicable to loans sanctioned or renewed on or after 1 January 2026"
   Output: ADD_DOCUMENT with reference_number="RBI/2025-26/64; DoR.MCS.REC.38/01.01.001/2025-26", original_date="2025-07-02", effective_date="2026-01-01", confidence="high"

2. **FEMA Amendment with Reference:**
   Input: "Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025... amends Regulation 4 of FEMA 23(R)/2015-RB"
   Output: Two actions:
   - ADD_DOCUMENT: document_id="FEMA 23(R)/(6)/2025-RB", reference_number="FEMA 23(R)/(6)/2025-RB", original_date="2025-06-24", confidence="high"
   - UPDATE_DOCUMENT: document_id="FEMA 23(R)/2015-RB", reference_number="FEMA 23(R)/2015-RB", update_location="Regulation 4", confidence="high"

2. **Department Circular Amendment:**
   Input: "reference is invited to the circular <a href='...'>DBOD.No.Leg.BC.21/09.07.007/2002-03</a> dated March 15, 2003"
   Output: DocumentAction with document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03", reference_number="DBOD.No.Leg.BC.21/09.07.007/2002-03", department="DBOD", original_date="2003-03-15", action_type="UPDATE_DOCUMENT", confidence="high"

3. **Withdrawal with Sunset:**
   Input: "Master Direction on Import of Goods... sunset clause of one year... will stand withdrawn"
   Output: DocumentAction with document_id="Master Direction - Import of Goods", action_type="REMOVE_DOCUMENT", sunset_withdraw_date="2025-07-01", confidence="high"

4. **Compound Notification (New + Repeal):**
   Input: "Reserve Bank of India (Pre-payment Charges on Loans) Directions, 2025... Annex lists 10 Earlier RBI circulars now repealed"
   Output: Multiple actions:
   - ADD_DOCUMENT: document_id="DoR.MCS.REC.38/01.01.001/2025-26", confidence="high"
   - REMOVE_DOCUMENT for each of the 10 repealed circulars from Annex

5. **Regulation Amendment:**
   Input: "paragraph 5.2 of Master Direction on Credit Card Operations is amended"
   Output: DocumentAction with document_id="Master Direction - Credit Card Operations", action_type="UPDATE_DOCUMENT", update_location="paragraph 5.2", confidence="high"

4. **Supersession (Dual Action):**
   Input: "Master Direction XYZ supersedes circular ABC dated..."
   Output: Two document actions - one REMOVE_DOCUMENT for Circular ABC and one ADD_DOCUMENT for Master Direction XYZ, both with high confidence

5. **Repealed List:**
   Input: "List of Circulars repealed: 1. DBOD.No.123... 2. DPSS.No.456..."
   Output: Multiple REMOVE_DOCUMENT actions for each listed circular with high confidence

**CRITICAL PROCESSING RULES:**
1. **Amendment Dual Actions**: For amendments, ALWAYS create both ADD_DOCUMENT (amendment) and UPDATE_DOCUMENT (principal)
2. **Related Document Extraction**: Populate related_document_ids with all referenced documents
3. **HTML Processing Order**: Extract anchors FIRST, then process text content
4. **Batch Operations**: Handle multiple documents in single notifications
5. **Date Calculations**: For sunset clauses, always calculate next July 1st
6. **Validation Gates**: Block processing if UPDATE/REMOVE actions lack reference_number
7. **Confidence Thresholds**: Require HIGH confidence for automated processing
8. **Manual Review Triggers**: Complex supersessions, missing metadata, ambiguous references

**CRITICAL URL FIELD DEFINITIONS AND RULES:**

**URL FIELD PURPOSES:**
1. **new_document_url**: The PDF URL for NEW documents that need to be ADDED to the knowledge base
   - Use ONLY for ADD_DOCUMENT actions
   - This URL will be downloaded and processed as a new regulatory document
   - MUST be a valid PDF URL from EXTRACTED_PDF_LINKS

2. **notification_pdf_url**: The PDF URL of the notification itself (for content analysis)
   - Use when the notification PDF contains additional details needed for processing
   - Usually the same as new_document_url for new regulatory documents
   - MUST be a valid PDF URL from EXTRACTED_PDF_LINKS

3. **pdf_links**: Array of all PDF URLs found (for reference)
   - Include all URLs from EXTRACTED_PDF_LINKS
   - Used for validation and backup processing

**MANDATORY URL RULES:**
1. **ONLY use URLs from EXTRACTED_PDF_LINKS**: Never generate, invent, or modify URLs
2. **URL Format Validation**: All URLs must follow this exact pattern:
   - ✅ CORRECT: "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/[FILENAME].PDF"
   - ✅ CORRECT: "https://rbidocs.rbi.org.in/rdocs/PressRelease/PDFs/[FILENAME].PDF"
   - ❌ WRONG: "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/NOTI134.PDF" (placeholder)
   - ❌ WRONG: "https://www.rbi.org.in/" (homepage)
   - ❌ WRONG: Any URL not in EXTRACTED_PDF_LINKS

3. **Empty fields when no URLs**: If EXTRACTED_PDF_LINKS is empty, leave ALL URL fields empty
4. **Action-URL Alignment**:
   - ADD_DOCUMENT actions REQUIRE new_document_url (the document to add)
   - UPDATE_DOCUMENT actions do NOT need new_document_url (updating existing docs)
   - REMOVE_DOCUMENT actions do NOT need new_document_url (removing existing docs)

**CRITICAL FOR DOCUMENT PROCESSING:**
- The new_document_url is THE MOST IMPORTANT field - it determines which PDF gets processed
- Wrong URL = wrong document processed = corrupted knowledge base
- Empty URL for ADD_DOCUMENT = action cannot be executed

**AMENDMENT DETAILS EXTRACTION:**
- Extract the specific text being added/modified (e.g., new sub-regulation text)
- Identify the exact location of the amendment (regulation number, paragraph, clause)
- Capture any footnote references or amendment history mentioned
- Extract effective dates and implementation details

**RELATED DOCUMENTS EXTRACTION:**
- Principal regulation being amended (e.g., "FEMA 23(R)/2015-RB")
- Previous amendments mentioned in footnotes
- Cross-referenced regulations or circulars
- Superseded documents if any

**OUTPUT REQUIREMENTS - CRITICAL FOR DOCUMENT PROCESSING:**

**DOCUMENT ACTIONS AND URL REQUIREMENTS:**
1. **ADD_DOCUMENT actions**:
   - MUST have new_document_url if EXTRACTED_PDF_LINKS contains URLs
   - new_document_url = the PDF that will be downloaded and added to knowledge base
   - Use EXACT URL from EXTRACTED_PDF_LINKS (e.g., "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF")
   - If no EXTRACTED_PDF_LINKS, leave new_document_url empty and set requires_manual_review=true

2. **UPDATE_DOCUMENT actions**:
   - Do NOT need new_document_url (updating existing documents)
   - Focus on document_id and reference_number for identification

3. **REMOVE_DOCUMENT actions**:
   - Do NOT need new_document_url (removing existing documents)
   - Focus on document_id and reference_number for identification

**URL FIELD VALIDATION:**
- new_document_url: Copy EXACTLY from EXTRACTED_PDF_LINKS, or leave empty ("")
- notification_pdf_url: Copy EXACTLY from EXTRACTED_PDF_LINKS, or leave empty ("")
- pdf_links: Array of ALL URLs from EXTRACTED_PDF_LINKS, or empty array []
- rbi_links: Leave empty array [] (not used)

**METADATA REQUIREMENTS:**
- Complete all enhanced metadata fields (update_location, sunset_withdraw_date)
- Provide detailed reasoning for each action
- Extract ALL available document metadata
- Populate related_document_ids with ALL referenced documents
- Fill amendment_details with specific amendment text and location
- Set requires_manual_review=true if critical information is missing

**ABSOLUTE PROHIBITIONS:**
- ❌ NEVER generate URLs not in EXTRACTED_PDF_LINKS
- ❌ NEVER use placeholder URLs like "NOTI134.PDF"
- ❌ NEVER use www.rbi.org.in homepage URLs
- ❌ NEVER modify URLs from EXTRACTED_PDF_LINKS

**OUTPUT FORMAT:**
Return an AffectedDocumentsResult object with enhanced DocumentAction objects and comprehensive link information.
"""

UPDATE_ACTION_DETERMINER_PROMPT = """
You are an expert in RBI regulatory knowledge base management. Based on the notification analysis, determine the specific actions needed to update the knowledge base.

**NOTIFICATION ANALYSIS:**
Title: {title}
Category: {category}
Affected Documents: {affected_documents}
RSS Description: {content}

**NOTE:** The RSS Description contains the structured notification content. Use this to understand the specific changes being made.

**ACTION TYPES AND REQUIREMENTS:**

1. **UPDATE_DOCUMENT** - Fetch latest version and replace existing document
   - Use when: Existing documents are being amended, modified, or updated
   - Required fields: target_document, details
   - Optional fields: rbi_page_url (if RBI page URL is mentioned)

2. **REMOVE_DOCUMENT** - Delete document from active knowledge base
   - Use when: Documents are being repealed, withdrawn, or superseded completely
   - Required fields: target_document, details
   - Optional fields: None

3. **ADD_DOCUMENT** - Add new document to knowledge base
   - Use when: Entirely new regulatory documents are being issued
   - Required fields: target_document, details
   - **CRITICAL**: new_document_url is ESSENTIAL for ADD_DOCUMENT actions
   - **URL SOURCE**: new_document_url MUST come from the RSS description content
   - **URL FORMAT**: Must be exact PDF URL like "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/[FILENAME].PDF"
   - **NO URL GENERATION**: If no PDF URL is found in RSS description, leave new_document_url empty
   - **VALIDATION**: The new_document_url will be used to download and process the actual document

4. **ADD_TEMPORARY_NOTE** - Add temporary relaxation/note (with expiry)
   - Use when: Temporary relaxations or time-bound measures are announced
   - Required fields: target_document, details, expiry_date
   - Optional fields: None

5. **NO_ACTION** - No knowledge base changes needed
   - Use when: Notification is purely informational with no regulatory changes

**CRITICAL URL HANDLING FOR DOCUMENT PROCESSING:**

**URL FIELD PURPOSES:**
- **new_document_url**: The EXACT PDF URL that will be downloaded and processed
  * ONLY for ADD_DOCUMENT actions
  * MUST be a valid PDF URL from the RSS description
  * Format: "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/[FILENAME].PDF"
  * This URL determines which document gets added to the knowledge base

- **rbi_page_url**: RBI page URL (rarely used, prefer direct PDF URLs)

**MANDATORY RULES:**
1. **ONLY use URLs found in RSS description** - Never generate or invent URLs
2. **EXACT URL copying** - Copy URLs exactly as they appear in the content
3. **Empty fields if no URLs** - Leave new_document_url empty if no PDF URL found
4. **No placeholder URLs** - Never use "NOTI134.PDF" or similar examples
5. **Validation critical** - Wrong URL = wrong document processed = corrupted knowledge base

**FOR ADD_DOCUMENT ACTIONS:**
- new_document_url is MANDATORY if a PDF URL exists in RSS description
- If no PDF URL found, leave new_document_url empty and set requires_manual_review=true
- The system will download and process whatever URL you provide

**OUTPUT FORMAT:**
Return an UpdateActionResult object with properly populated actions including all required fields.
"""


