from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import date # Use date instead of datetime if only date is expected

# Define models based on the provided schema

class Position(BaseModel):
    page_index: int
    rectangles: List[List[float]]

class Metadata(BaseModel):
    document_id: str
    chunk_index: int
    document_summary: Optional[str] = None # Made optional as it might not always be present
    document_number: Optional[str] = None # Made optional
    document_title: Optional[str] = None # Made optional
    document_type: Optional[str] = None # Made optional
    date_of_issue: Optional[str] = None # Kept as string, consider parsing to date if needed
    addressee: Optional[str] = None # Made optional
    is_applicable_to_banks: Optional[bool] = None # Made optional
    addressee_entities: Optional[List[str]] = Field(default_factory=list) # Default to empty list
    addressee_person: Optional[List[str]] = Field(default_factory=list) # Default to empty list
    applicable_departments: Optional[List[str]] = Field(default_factory=list) # Default to empty list
    is_withdrawn: Optional[bool] = None # Made optional
    keywords: Optional[List[str]] = Field(default_factory=list) # Default to empty list
    effective_date: Optional[str] = None # Kept as string, consider parsing to date if needed
    supersedes: Optional[List[str]] = Field(default_factory=list) # Default to empty list
    positions: Optional[List[Position]] = Field(default_factory=list) # Default to empty list
    # Add other potential fields from Qdrant point payload if necessary
    url: Optional[str] = None
    source_url: Optional[str] = None
    paragraph_index: Optional[int] = None # Added based on usage in ContextExtenderNode

class DocumentChunk(BaseModel):
    id: Optional[str] = None
    embedding: Optional[List[float]] = None
    metadata: Metadata  # Changed from 'payload' to 'metadata'
    score: Optional[float] = None
    page_content: Optional[str] = None