import re
from app.v2.query.retrieval.qdrant_client import get_qdrant_client
import os
import json
from typing import Dict, Any, List, Optional
import re
from difflib import SequenceMatcher
from collections import Counter
import string

from config import settings

class AnnotationGenerator:
    def __init__(self):
        self.client = get_qdrant_client()
        self.collection_name = settings.QDRANT_COLLECTION


    def run(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Safely generate annotations from retrieved_points and return formatted_answer,
        even if retrieved_points is missing or invalid.
        """
        metadata_list = []

        try:
            annotations = state.get("retrieved_points", [])
        
            # Check if annotations is a list or a dictionary
            if isinstance(annotations, list):
                for annotation in annotations:
                    if isinstance(annotation, dict) and 'metadata' in annotation:
                        doc_id = annotation['metadata'].get('document_id')
                        annotation['metadata']['positions'] = state["retrieved_points_positions"].get(doc_id,[])
                        metadata_list.append(annotation['metadata'])
            elif isinstance(annotations, dict) and 'metadata' in annotations:
                metadata_list.append(annotations['metadata'])
        except Exception as e:
            print(f"Error extracting metadata: {e}")

        try:
            final_answer = state["formatted_answer"]
        except KeyError:
            final_answer = {}
        final_answer["metadata"] = metadata_list
        # print(f"final_answer metadata: {final_answer['metadata']}")
        # print(f"final_answer: {final_answer}")
        return final_answer