# bounding_box_calculator.py
def compute_chunk_bounding_boxes(elements: list) -> list:
    """
    Compute combined bounding boxes (per page) from individual element bounding boxes.
    
    Args:
        elements (list): List of extracted elements with bounding boxes.
        
    Returns:
        list: List of dicts with page index and overall bounding box.
    """
    page_boxes = {}
    for el in elements:
        for bbox in el.get("bounding_boxes", []):
            page_index = bbox["page_index"]
            tl = bbox["top_left"]
            br = bbox["bottom_right"]
            if page_index not in page_boxes:
                page_boxes[page_index] = {
                    "top_left_x": tl[0],
                    "top_left_y": tl[1],
                    "bottom_right_x": br[0],
                    "bottom_right_y": br[1]
                }
            else:
                box = page_boxes[page_index]
                box["top_left_x"] = min(box["top_left_x"], tl[0])
                box["top_left_y"] = min(box["top_left_y"], tl[1])
                box["bottom_right_x"] = max(box["bottom_right_x"], br[0])
                box["bottom_right_y"] = max(box["bottom_right_y"], br[1])
    combined = []
    for page_index, box in page_boxes.items():
        combined.append({
            "page_index": page_index,
            "top_left": (box["top_left_x"], box["top_left_y"]),
            "bottom_right": (box["bottom_right_x"], box["bottom_right_y"])
        })
    return combined
