import re
import csv
import logging
from qdrant_client import QdrantClient

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

QDRANT_URL = "http://localhost:6333"
COLLECTION_NAME = "rbi_master_direction"
BATCH_SIZE = 100
OUTPUT_CSV = "document_ids_cleaned.csv"

DOCUMENT_TYPES = [
    "Master Direction", "Master Circular", "Notification", "Circular", "Directions"
]

DOC_TYPE_REGEX = re.compile(
    r"(" + "|".join(re.escape(dt) for dt in DOCUMENT_TYPES) + r")",
    flags=re.IGNORECASE
)

FALLBACK_SPLIT_REGEX = re.compile(
    r"^(.*?)\s+([A-Z]{2,}[\.\w/\-]+.*)$"
)

def clean_id(raw_id: str) -> str:
    cleaned = raw_id.strip()
    cleaned = re.sub(r"\s+", " ", cleaned)
    cleaned = re.sub(r"\s*/\s*", "/", cleaned)
    cleaned = re.sub(r"\s*\.\s*", ".", cleaned)
    return cleaned


def parse_document_id(cleaned_id: str):
    """
    Parse RBI document_id into short_id, document_type, long_code.
    Handles cases where department code is repeated before doc_type.
    """
    short_id = ""
    doc_type = ""
    long_code = ""

    match = DOC_TYPE_REGEX.search(cleaned_id)

    if match:
        doc_type = match.group(1).title()
        before = cleaned_id[:match.start()].strip(" .")
        after = cleaned_id[match.end():].strip(" .")

        short_id = before
        long_code = after

        # remove repeated dept code from long_code if present
        dept_code_match = re.search(r"RBI/([A-Z]+)/", short_id)
        if dept_code_match:
            dept_code = dept_code_match.group(1)
            if long_code.upper().startswith(dept_code):
                long_code = re.sub(rf"^{dept_code}\s*", "", long_code, flags=re.IGNORECASE)
    else:
        fallback = FALLBACK_SPLIT_REGEX.match(cleaned_id)
        if fallback:
            short_id = fallback.group(1).strip(" .")
            long_code = fallback.group(2).strip(" .")
        else:
            short_id = cleaned_id

    return short_id, doc_type, long_code


def extract_document_ids():
    client = QdrantClient(url=QDRANT_URL)
    scroll_offset = None
    documents = []

    while True:
        points_batch, next_page = client.scroll(
            collection_name=COLLECTION_NAME,
            offset=scroll_offset,
            limit=BATCH_SIZE,
            with_payload=True,
            with_vectors=False
        )

        for point in points_batch:
            payload = point.payload or {}
            metadata = payload.get("metadata", {})
            raw_id = metadata.get("document_id") or payload.get("document_id")
            if not raw_id:
                logging.warning("Missing document_id in point.")
                continue

            cleaned_id = clean_id(str(raw_id))
            short_id, doc_type, long_code = parse_document_id(cleaned_id)

            documents.append({
                "raw_document_id": raw_id,
                "cleaned_document_id": cleaned_id,
                "short_id": short_id,
                "document_type": doc_type,
                "long_code": long_code
            })

        if next_page is None:
            break
        scroll_offset = next_page

    logging.info(f"Extracted {len(documents)} document IDs.")
    return documents


def save_to_csv(documents, filename):
    with open(filename, mode="w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(
            csvfile,
            fieldnames=["raw_document_id", "cleaned_document_id", "short_id", "document_type", "long_code"]
        )
        writer.writeheader()
        for doc in documents:
            writer.writerow(doc)
    logging.info(f"Results saved to {filename}")


if __name__ == "__main__":
    docs = extract_document_ids()
    for d in docs:
        logging.info(
            f"[RAW] {d['raw_document_id']} → [SHORT] {d['short_id']} | [TYPE] {d['document_type']} | [LONG] {d['long_code']}"
        )
    save_to_csv(docs, OUTPUT_CSV)


# import pandas as pd

# doc_ids_df = pd.read_csv("./document_ids_cleaned.csv")
# doc_ids_df = doc_ids_df.drop_duplicates()
# print(len(doc_ids_df))
# uniqe_shorts = doc_ids_df.short_id.unique()
# len(uniqe_shorts)
