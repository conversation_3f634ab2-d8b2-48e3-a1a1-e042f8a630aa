import json
import yaml
import logging
from config import settings

from .utils.memory_manager import memory_manager
from .retrieval.qdrant_client import get_qdrant_client
from .retrieval.mongo_client import get_mongo_collection
from langfuse.decorators import observe
from .utils.completion_client import completion_client

from typing import List

# Import agent classes
from .agents.query_rewriter import QueryRewriter
from .agents.ambiguity_resolver import AmbiguityResolver
from .agents.multi_query_generator import MultiQueryGenerator
from .agents.semantic_retriever import SemanticRetriever
from .agents.planner import PlannerAgent
from .agents.metadata_filter_generator import QdrantFilterGenerator, MongoFilterBuilder
from .agents.context_expander import ContextExpander
from .agents.dictionary_agent import DefinitionRetriever

from .response.recursive_summarizer import RecursiveSummarizer
from .response.answer_generator import AnswerGenerator
from .response.critic_agent import CriticAgent
from .response.answer_formatter import AnswerFormatter
from .response.annotation_generator import AnnotationGenerator
from .response.proactive_suggestor import ProactiveSuggestor
from .response.context_expander_with_filter import ContextExpanderWithFilter


# Import the new models
from app.v2.query.models import DocumentChunk, Metadata # Assuming models.py is in the same directory

from langchain_openai import ChatOpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BaseNode:

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    @observe()
    def run(self, state: dict) -> dict:
        raise NotImplementedError

    @observe()
    def post_run(self, state: dict) -> dict:
        return state

# Node implementations.
class QueryRewriterNode(BaseNode):

    def __init__(self, model, prompt_file, extra_context=None, callback=None):
        super().__init__()
        self.agent = QueryRewriter(model, prompt_file)
        self.extra_context = extra_context
        self.callback = callback
       
    @observe(name="query_rewriter")
    def run(self, state: dict) -> dict:
        print("Running QueryRewriterNode: ")
        query = state.get("query", "")
        memory_text = state.get("chat_history", "")
        condensed_context = state.get("condensed_context", "")

        self.logger.info(f"Input query: {query}")
        self.logger.info(f"Chat history length: {len(memory_text)}")
        self.logger.info(f"Condensed context length: {len(condensed_context)}")

        prompt_filled = self.agent.prompt.format(
            regulatory_domain="banking",
            regulatory_body="RBI",
            user_role="compliance officer",
            organization_type="bank",
            query_purpose="audit",
            chat_history=memory_text,
            session_summary=condensed_context,
            original_query=query
        )

        messages = [{"role": "system", "content": prompt_filled}]
        response, _, _ = completion_client.completion(messages=messages, model=self.agent.model)
        rewritten = response.strip()

        self.logger.info(f"Original query: {query}")
        self.logger.info(f"Rewritten query: {rewritten}")

        if hasattr(self, "callback"):
            self.callback({
                "event": "node_output",
                "node": "QueryRewriterNode",
                "status": "success",
                "state": [f"Original query: {query}", f"Rewritten query: {rewritten}"]
            })

        state["rewritten_query"] = rewritten
        return state

# class MultiQueryGeneratorNode(BaseNode):

#     def __init__(self, model, prompt_file):
#         super().__init__()
#         self.agent = MultiQueryGenerator(model, prompt_file)

#     @observe()
#     def run(self, state: dict) -> dict:
#         print("Running MultiQueryGeneratorNode: ")
#         query = state.get("rewritten_query", state["query"])
#         self.logger.info(f"Input query for generation: {query}")

#         queries = self.agent.run(query)
#         self.logger.info(f"Generated {len(queries)} search queries:")
#         for i, q in enumerate(queries, 1):
#             self.logger.info(f"Query {i}: {q}")

#         state["query"] = queries
#         return state

# class QdrantFilterGeneratorNode(BaseNode):

#     def __init__(self, model: str):
#         super().__init__()
#         self.agent = QdrantFilterGenerator(model)

#     @observe()
#     def run(self, state: dict) -> dict:
#         print("Running QdrantFilterGeneratorNode: ")

#         if "rewritten_query" not in state:
#             query = state["query"]
#             if isinstance(query, list):
#                 query = query[0]
#             self.logger.info("Processing query: %s", query)
#         else:
#             query = state["rewritten_query"]

#         try:
#             metadata_filter = self.agent.run(query, state.get("mongo_filter", {}))
#             self.logger.info(f"Generated Qdrant filter: {metadata_filter}")
#             state["metadata_filter"] = metadata_filter
#         except Exception as e:
#             self.logger.error(f"Filter generation failed: {str(e)}")
#             state["metadata_filter"] = None

#         return state

class SemanticRetrieverNode(BaseNode):

    def __init__(self, q_client, collection, embed_model, chat_model=None, callback=None):
        super().__init__()
        self.agent = SemanticRetriever(q_client, collection, embed_model, chat_model)
       
    @observe(name="semantic_retriever")
    def run(self, state: dict) -> dict:
        print("Running SemanticRetrieverNode: ")
        queries = state["query"]
        if isinstance(queries, str):
            queries = [queries]
        metadata_filter = state.get("metadata_filter")

        self.logger.info(f"Running semantic retrieval for {len(queries)} queries")
        self.logger.info(f"Using metadata filter: {metadata_filter}")

        retrieved, position = self.agent.run(queries)
        #, metadata_filter=metadata_filter)
        self.logger.info(f"Retrieved {len(retrieved)} documents")

        if hasattr(self, "callback"):
            self.callback({
                "event": "node_output",
                "node": "SemanticRetrieverNode",
                "status": "success",
                "state": [f"Retrieved {len(retrieved)} documents"]
            })

        state["retrieved_points"] = retrieved
        state["retrieved_points_positions"] = position
        print("count of Retrived Points: ", len(retrieved))
        return state

class MongoFilterBuilderNode(BaseNode):

    def __init__(self, model: str, prompt_file: str):
        super().__init__()
        self.agent = MongoFilterBuilder(model, prompt_file)
       
    @observe(name="mongo_filter")
    def run(self, state: dict) -> dict:
        print("Running MongoFilterBuilderNode: ")
        query = state.get("rewritten_query", state["query"])
        if isinstance(query, list):
            query = query[0]
        try:
            mongo_filter = self.agent.run(query)
            self.logger.info(f"Generated MongoDB filter: {mongo_filter}")
            state["mongo_filter"] = mongo_filter
        except Exception as e:
            self.logger.error(f"MongoDB filter generation failed: {str(e)}")
            state["mongo_filter"] = {}

        return state


class SummarizerNode(BaseNode):

    def __init__(self, model, prompt_file,session_id, callback=None):
        super().__init__()
        self.agent = RecursiveSummarizer(model, prompt_file)
        self.callback = callback
        self.session_id = session_id
       
    @observe(name="summarizer")
    def run(self, state: dict) -> dict:
        print("Running SummarizerNode: ")
        query = state.get("rewritten_query", state["query"])
        if isinstance(query, list):
            query = query[0]


        previous_condensed = state.get("condensed_context", "")
        # Changed from state["contexts"] to use retrieved documents
        new_documents = state.get("retrieved_points", [])
        self.logger.info(f"Summarizing for query: {query}")
        self.logger.info(f"Number of retrieved documents: {len(new_documents)}")
        self.logger.info(f"Previous context length: {len(previous_condensed)}")

        if self.callback:
            self.callback({
                "event": "node_output",
                "node": "SemanticRetrieverNode",
                "status": "success",
                "state": [f"Summarizing for query: {query}", f"Number of retrieved documents: {len(new_documents)}"]
            })


        if not previous_condensed:
            previous_condensed = memory_manager.get_condensed_context(self.session_id)

        # Updated document processing with error handling
        context_docs = []
        for doc in new_documents:
            try:
                doc_meta = doc.get("metadata", {})
                doc_content = doc.get("document_summary") or doc.get("page_content", "")
                doc_number = doc_meta.get("document_number", "UNKNOWN")
                doc_type = doc_meta.get("document_type", "Regulatory Document")

                context_docs.append(
                    f"{doc_type} {doc_number}: {doc_content[:500]}..."
                )
            except Exception as e:
                self.logger.error(f"Error processing document: {str(e)}")
                continue

        condensed = self.agent.run(
            query=query,
            contexts=context_docs + ([previous_condensed] if previous_condensed else [])
        )

        self.logger.info(f"Generated condensed context length: {len(condensed)}")
        state["condensed_context"] = condensed
        return state
   
    @observe(name="semantic_retriever_post_run")
    def post_run(self, state: dict) -> dict:
        if "condensed_context" in state:
            memory_manager.update_condensed_context(
                self.session_id,
                state["condensed_context"]
            )
        return state

class AnswerGeneratorNode(BaseNode):

    def __init__(self, model, prompt_file):
        super().__init__()
        self.agent = AnswerGenerator(model, prompt_file)


    @observe(name="answer_generator")
    def run(self, state: dict) -> dict:
        print("Running AnswerGeneratorNode: ")
        # select the query
        if "rewritten_query" in state:
            query = state["rewritten_query"]
        elif isinstance(state["query"], list):
            query = state["query"][0]
        else:
            query = state["query"]

        condensed = state.get("condensed_context", "").strip()
        self.logger.info(f"Generating answer for query: {query}")
        self.logger.info(f"Using condensed context length: {len(condensed)}")

        if not condensed:
            self.logger.warning("No condensed context available, using empty string")
            condensed = ""

        try:
            # run and get structured response

            answer = self.agent.run(query, condensed, state.get("retrieved_points", []), state.get("definitions", []))
            # self.logger.info(f"Received structured answer keys: {answer_struct.model_dump()}")

            # Reflect full structure in state
            state["comprehensive_response"]   = answer.dict()["comprehensive_response"]
            state["clarification_requests"]   = answer.clarification_requests
            state["information_gaps"]         = answer.information_gaps
            
            points = state.get("retrieved_points", [])
            extract_keys = [
                    "document_id", 
                    "document_title", 
                    "document_type", 
                    "date_of_issue", 
                    "applicable_departments",
                    "s3_url"
                    ]
            metadata = []
            for point in points:
                metadata.append({key:value for key, value in point['metadata'].items() if key in extract_keys})
            
            state["final_answer"] = {
                "query": query.strip(),
                "response": state["comprehensive_response"],
                "metadata": metadata
            }
            
        except Exception as e:
            self.logger.error(f"Answer generation failed: {e}")
            raise

        return state

    def post_run(self, state)-> dict:
        return state

class CriticAgentNode(BaseNode):

    def __init__(self, model, prompt_file=None):
        super().__init__()
        self.agent = CriticAgent(model)
        if prompt_file:
            with open(prompt_file, "r") as f:
                data = yaml.safe_load(f)
            self.agent.prompt = data.get("prompt", self.agent.__doc__)
           
    @observe(name="critic_agent")
    def run(self, state: dict) -> dict:
        print("Running CriticAgentNode: ")
        if "rewritten_query" in state:
            query = state["rewritten_query"]
        elif isinstance(state["query"], list):
            query = state["query"][0]
        else:
            query = state["query"]

        context = state.get("condensed_context", "")
        draft = state.get("draft_answer","")
       
        self.logger.info(f"Critiquing answer for query: {query}")
        self.logger.info(f"Draft answer length: {len(draft)}")

        critique = self.agent.run(query, context, draft)
        self.logger.info(f"Critique result: {critique}")
        print("Draft from critic agent1: ", draft)
        if critique.strip().lower() != "ok":
            self.logger.info("Revising answer based on critique")
            # draft = self.agent.run(query, critique, draft)
            anwer_generator = AnswerGenerator(settings.OPENAI_CHAT_MODEL, prompt_file="app/v2/query/prompts/answer_generator.yaml")
            draft = anwer_generator.run(query, critique, state.get("retrieved_points",[]), None)
            response = draft.comprehensive_response
            self.logger.info(f"Revised answer length: {len(response)}")
            state["draft_answer"] = response
        return state


class ProactiveSuggestorNode(BaseNode):

    def __init__(self, model, prompt_file=None):
        super().__init__()
        self.agent = ProactiveSuggestor(model, prompt_file)
       
    @observe(name="proactive_suggestor")
    def run(self, state: dict) -> dict:
        print("Running ProactiveSuggestorNode: ")
        try:
            query = state["rewritten_query"]
        except KeyError:
            query = state["query"]
        answer = state["final_answer"]
        contexts = state.get("contexts", [])

        self.logger.info(f"Generating suggestions for query: {query}")
        self.logger.info(f"Using {len(contexts)} contexts")

        try:
            suggestions = self.agent.run(query, answer, contexts)
            self.logger.info(f"Generated {len(suggestions)} suggestions")
            state["suggestions"] = suggestions
        except Exception as e:
            self.logger.error(f"Failed to generate suggestions: {str(e)}")

        return state

class MemoryUpdateNode(BaseNode):

    def __init__(self, role: str, message_key: str, session_id: str = None, username: str = None):
        super().__init__()
        self.role = role
        # depricated now only final_answer is used
        # self.message_key = message_key
        self.message_key = "final_answer"
        self.session_id = session_id
        self.username = username
       
    @observe(name="memory_update")
    def run(self, state: dict) -> dict:

        print("Running MemoryUpdateNode: ", self.session_id)

        if self.session_id and self.message_key in state:
            self.logger.info(f"Updating memory for session {self.session_id}")
            self.logger.info(f"Role: {self.role}, Message key: {self.message_key}")

            memory_manager.update_session_memory_chat(self.session_id, state["query"], state[self.message_key], self.username)

            if "condensed_context" in state:
                self.logger.info("Updating condensed context")
                memory_manager.update_condensed_context(self.session_id, state["condensed_context"])

        return state

class AmbiguityResolverNode(BaseNode):

    def __init__(self, model, prompt_file):
        super().__init__()
        self.agent = AmbiguityResolver(model, prompt_file)

    @observe()
    def run(self, state: dict) -> dict:
        rewritten = state.get("rewritten_query", state["query"])
        self.logger.info(f"Checking ambiguity for query: {rewritten}")

        result = self.agent.run(rewritten)
        self.logger.info(f"Ambiguity check result: {result}")

        state.update(result)
        return state
       
    @observe(name="ambiguity_resolver")
    def post_run(self, state: dict) -> dict:
        result = state.get("ambiguous", False)
        if isinstance(result, dict):
            if result.get("clarification_question"):
                self.logger.info(f"Clarification needed: {result['clarification_question']}")
                state["clarification_question"] = result["clarification_question"]
                state["requires_clarification"] = True
                state["clarification_source"] = self.__class__.__name__
        return state

class PlannerNode(BaseNode):

    def __init__(self, model=settings.OPENAI_CHAT_MODEL):
        super().__init__()
        self.planner = PlannerAgent(model)
   
    @observe(name="planner")
    def run(self, state: dict) -> dict:
        query = state.get("query", "")
        self.logger.info(f"Creating execution plan for query: {query}")

        context = {
            "query": query,
            "chat_history": state.get("chat_history", ""),
            "session_id": state.get("session_id", ""),
            # "extra_context": state.get("extra_context", {})
        }

        plan =  self.planner.create_plan(
            f"Process compliance query: {context['query']}",
            context
        )

        self.logger.info(f"Generated execution plan with {len(plan.steps)} steps")
        state["execution_plan"] = plan
        return state

class ContextExpanderNode(BaseNode):

    def __init__(self):
        super().__init__()
        self.agent = ContextExpander()
    @observe(name="context_expander")
    def run(self, state: dict) -> dict:
        # Test: Log if there is anything in the collection
        try:
            if self.agent.mongo_coll.find_one():
                self.logger.info("MongoDB collection is connected")
                self.logger.warning("MongoDB collection is connected but empty.")
        except Exception as e:
            self.logger.error(f"Error accessing MongoDB collection: {e}")

        # Use empty filter to retrieve all documents
        mongo_filter = {}
        mongo_points = list(self.agent.mongo_coll.find(mongo_filter))
        self.logger.info(f"Retrieved {len(mongo_points)} points from MongoDB using filter")

        for point in mongo_points:
            if "positions" in point["metadata"]:
                del point["metadata"]["positions"]

        state["retrieved_points"].extend(mongo_points)
        retrieved_points = state.get("retrieved_points", [])


        mongo_filter = state.get("mongo_filter", {})
        contexts = self.agent.run(retrieved_points, mongo_filter)
        self.logger.info(f"ContextExpanderNode: Expanded {len(contexts)} contexts")
        state["contexts"] = contexts
        return state


class ContextExpanderWithFilterNode(BaseNode):
    def __init__(self, model, prompt):
        super().__init__()
        self.agent = ContextExpanderWithFilter(model, prompt)

    @observe(name="context_expander_with_filter")
    def run(self, state: dict) -> dict:
        print("Running ContextExpanderWithFilterNode")
        query = state.get("rewritten_query", state["query"])
        retrieved_points = state.get("retrieved_points", [])

        self.logger.info(f"Expanding context for query: {query}")
        self.logger.info(f"Number of retrieved points: {len(retrieved_points)}")

        # Generate the filter only once
        if "mongo_filter" not in state:
            try:
                mongo_filter = self.agent.generate_mongo_filter(query)
                state["mongo_filter"] = mongo_filter
                self.logger.info(f"Generated MongoDB filter: {mongo_filter}")
            except Exception as e:
                self.logger.error(f"Failed to generate MongoDB filter: {e}")
                state["mongo_filter"] = {}

        mongo_filter = state.get("mongo_filter", {})
        self.logger.info(f"Using MongoDB filter: {mongo_filter}")

        # Expand context only if there are retrieved points
        if state.get("retrieved_points", None) and not mongo_filter:
            try:
                contexts = self.agent.expand_context(state["retrieved_points"], mongo_filter)
                self.logger.info(f"Expanded {len(contexts)} contexts")
                state["contexts"] = contexts
            except Exception as e:
                self.logger.error(f"Context expansion failed: {e}")
                state["contexts"] = []
            return state

        # Always run MongoDB retrieval
        try:
            mongo_points = list(self.agent.mongo_coll.find(mongo_filter))
            self.logger.info(f"Retrieved {len(mongo_points)} points from MongoDB using filter")

            for point in mongo_points:
                if "positions" in point.get("metadata", {}):
                    del point["metadata"]["positions"]

            if "retrieved_points" not in state:
                state["retrieved_points"] = mongo_points
            else:
                state["retrieved_points"].extend(mongo_points)
        except Exception as e:
            self.logger.error(f"MongoDB retrieval failed: {e}")


        return state


class DictionaryAgentNode(BaseNode):

    def __init__(self, model, dict_collection):
        super().__init__()
        qdrant_client = get_qdrant_client()
        self.agent = DefinitionRetriever(client=qdrant_client,
                                         collection=dict_collection,
                                         embed_model=model)
       
    @observe(name="dictionary")
    def run(self, state: dict) -> dict:
        print("Running DictionaryAgentNode: ")
        query = state.get("rewritten_query", state["query"])
        self.logger.info(f"Retrieving definitions for query: {query}")

        try:
            definitions = self.agent.run(query)
            self.logger.info(f"Retrieved {len(definitions)} definitions")
            state["definitions"] = definitions
        except Exception as e:
            self.logger.error(f"Definition retrieval failed: {e}")
            state["definitions"] = []

        return state

class MeetAndGreetAgentNode(BaseNode):
    """
    A node that handles greetings, pleasantries, and non-compliance queries.
    This node checks if the input query is a greeting or small talk and 
    responds appropriately without engaging the full compliance pipeline.
    
    Generated by Copilot
    """
    def __init__(self, model: str, prompt_file: str = None):
        super().__init__()
        # Import here to avoid circular imports
        from .agents.meet_and_greet_agent import MeetAndGreetAgent
        self.agent = MeetAndGreetAgent(model, prompt_file)
        self.formatter = AnswerFormatter()

    @observe()
    def run(self, state: dict) -> dict:
        print("Running MeetAndGreetAgentNode: ")
        # Get the query from state
        query = state.get("query", "")
        if isinstance(query, list):
            query = query[0]
        
        self.logger.info(f"Processing query with MeetAndGreetAgentNode: {query}")
        
        # Generate response using the agent
        response = self.agent.run(query)
        
        # Add response to state
        state["greeting_response"] = response
        
        # Set a flag to indicate this was handled by the greeting agent
        state["handled_by_greeter"] = True
        
        # Add to final_answer for compatibility with the pipeline
        state["final_answer"] = response
        
        self.logger.info("MeetAndGreetAgentNode completed processing")
        return state

    def post_run(self, state: dict) -> dict:
        state["formatted_answer"] = self.formatter.run(state)
        return state