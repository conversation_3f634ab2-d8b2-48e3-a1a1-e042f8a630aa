// export interface ChatMessage {
//   id: string
//   content: string
//   sender: "user" | "assistant"
//   timestamp: Date
//   regulation?: RegulationInfo
// }

// export interface RegulationInfo {
//   id: string
//   title: string
//   authority: string
//   issueDate: string
//   topic: string
//   sections: RegulationSection[]
// }

export interface RegulationSection {
  id: string
  title: string
  content: string
}

export interface RecentChat {
  id: string
  title: string
  date: "Today" | "Yesterday" | "Last Week"
}

export interface LatestUpdate {
  id: string
  title: string
  date: string
  category: "Today" | "Yesterday"
}
