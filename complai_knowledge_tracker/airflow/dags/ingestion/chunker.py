# chunker.py
import re
from typing import List, Dict
from ingestion.markdown_parser import MarkdownNode, build_markdown_tree

# Assume these are available from your utils or LLM module
from .summarizer import summarize_text, extract_topics  # You need to implement these functions

def split_text_by_sentences(text: str, max_chars: int) -> List[str]:
    sentences = re.split(r'(?<=[.!?])\s+', text)
    chunks = []
    current_chunk = ""
    for sentence in sentences:
        if len(current_chunk) + len(sentence) + 1 <= max_chars:
            current_chunk += sentence + " "
        else:
            if current_chunk.strip():
                chunks.append(current_chunk.strip())
            current_chunk = sentence + " "
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    return chunks

def chunk_markdown_node(node: MarkdownNode, max_chars: int = 3000, section_summaries: Dict[str, str] = None) -> List[Dict]:
    rendered = node.render().strip()
    node_title = node.title if hasattr(node, 'title') and node.title else 'root'

    if section_summaries is None:
        section_summaries = {}

    if len(rendered) <= max_chars:
        # summary = summarize_text(rendered, max_words=100)
        # section_summaries[node_title] = summary
        return [{
            "content": rendered,
            # "section_summary": summary,
            "section_title": node_title
        }]
    else:
        chunks = []
        header_and_content = ""
        from mistletoe.markdown_renderer import MarkdownRenderer
        with MarkdownRenderer() as renderer:
            if node.token and isinstance(node.token, type(node.token)):
                header_and_content += renderer.render(node.token).strip() + "\n\n"
            for token in node.content_tokens:
                header_and_content += renderer.render(token).strip() + "\n\n"

        if header_and_content.strip():
            split_chunks = split_text_by_sentences(header_and_content, max_chars)
            # summary = summarize_text(header_and_content, max_words=100)
            # section_summaries[node_title] = summary
            for chunk in split_chunks:
                chunks.append({
                    "content": chunk,
                    # "section_summary": summary,
                    "section_title": node_title
                })

        for child in node.children:
            child_chunks = chunk_markdown_node(child, max_chars, section_summaries)
            chunks.extend(child_chunks)

        return chunks

def get_chunks_and_summary(markdown_text: str, max_chars: int = 5000) -> Dict:
    tree = build_markdown_tree(markdown_text)
    topics = extract_topics(markdown_text)  # Reintroducing the extraction of topics
    document_summary = summarize_text(markdown_text, max_words=150)
    section_summaries = {}
    chunks = chunk_markdown_node(tree, max_chars=max_chars, section_summaries=section_summaries)
    return (document_summary,topics, chunks )

