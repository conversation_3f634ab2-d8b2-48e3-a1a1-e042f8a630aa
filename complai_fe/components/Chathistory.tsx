// //ChatHistory.tsx
// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { ChatHistoryProps } from "@/types/chat";
// import { useChat } from "@/contexts/ChatContext";
// import { useEffect} from "react";

// interface ExtendedChatHistoryProps extends ChatHistoryProps {
//   setActiveView: (view: "home" | "chat") => void;
// }

// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView }: ExtendedChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();
  
//   // console.log("iscollapsed is passed here : ", isCollapsed);

//   // Add this useEffect right after your existing hooks in the ChatHistory component
// useEffect(() => {
//   // If currentSessionId is null and there are chats available, select the most recent one
//   if (currentSessionId === null && chats.length > 0) {
//     const sortedChats = [...chats].sort((a, b) => {
//       // Sort by timestamp (newest first)
//       return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//     });
    
//     // Get the most recent chat
//     const mostRecentChat = sortedChats[0];
    
//     // Load the most recent chat session
//     if (mostRecentChat) {
//       console.log("Auto-selecting most recent chat:", mostRecentChat.id);
//       loadSession(mostRecentChat.id);
      
//       // Ensure we switch to chat view
//       setActiveView("chat");
//     }
//   }
// }, [currentSessionId, chats, loadSession, setActiveView]);

//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       // console.log("New Chat clicked"); // Debug log
//       // Call API to create a new session and get session_id
//       await createNewChat();
      
//       // Navigate to home view to start the new chat
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//       // Could add error handling UI here if needed
//     }
//   };
  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     // console.log("checking inside toggle:",isCollapsed);
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session
//   const handleChatSelect = async (sessionId: string) => {
//     try {
//       // Load the selected chat session
//       await loadSession(sessionId);
      
//       // Ensure we switch to chat view when selecting a session
//       setActiveView("chat");
      
//       // Optional: Ensure we're on the home page to view the chat
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to load chat session:", err);
//     }
//   };

//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => chats.some(chat => chat.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   return (
//     <div className={`h-full relative transition-all duration-300 ease-in-out ${!isCollapsed ? "w-[212px] border-r border-[#D1C3BE]" : "w-[60px]"}`}>
//       {/* New Chat button at top - only when expanded */}
//       {!isCollapsed ? (
//         <div 
//           className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//           onClick={handleNewChat}
//         >
//           <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="Chat" 
//             className="w-8 h-8 overflow-hidden pointer-events-none" 
//           />
//         </div>
//       ) : (
//         // New Chat icon when collapsed - positioned below the sidebar icon
//         <div 
//           className="absolute left-[14px] top-[66px] z-10 cursor-pointer"
//           onClick={handleNewChat}
//         >
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="New Chat" 
//             className="w-8 h-8 overflow-hidden" 
//           />
//         </div>
//       )}
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className={`w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 ${!isCollapsed ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
//           {/* Apply CSS to hide the scrollbar */}
//           <style jsx>{`
//             .scrollbar-hide::-webkit-scrollbar {
//               display: none;
//             }
//           `}</style>
          
//           {isLoading ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : error ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error}
//             </div>
//           ) : chats.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No chats found
//             </div>
//           ) : (
//             // Map through each date group that has chats
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {chats
//                   .filter(chat => chat.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((chat) => (
//                     <div 
//                       key={chat.id} 
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 mb-2 ${
//                        currentSessionId === chat.id ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleChatSelect(chat.id)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium" style={{ fontFamily: 'Figtree' }}>
//                         {chat.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { ChatHistoryProps } from "@/types/chat";
// import { useChat } from "@/contexts/ChatContext";

// interface ExtendedChatHistoryProps extends ChatHistoryProps {
//   setActiveView: (view: "home" | "chat") => void;
// }

// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView }: ExtendedChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();

//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       await createNewChat();
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//     }
//   };
  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session
//   const handleChatSelect = async (sessionId: string) => {
//     try {
//       await loadSession(sessionId);
//       setActiveView("chat");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to load chat session:", err);
//     }
//   };

//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => chats.some(chat => chat.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   if (isCollapsed) {
//     // When collapsed, only render the top bar with toggle and new chat button
//     return (
//       <div className="h-[80px]">
//         <div className="flex items-center h-full">
//           {/* Sidebar toggle icon */}
//           <div 
//             onClick={toggleSidebar} 
//             className="w-8 h-8 ml-3 cursor-pointer"
//           >
//             <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//           </div>
          
//           {/* New Chat button */}
//           <div 
//             className="flex items-center ml-3 cursor-pointer"
//             onClick={handleNewChat}
//           >
//             <span className="text-[#FF6B1C] text-sm font-medium mr-1">New Chat</span>
//             <img 
//               src={NewChatIcon.src || NewChatIcon} 
//               alt="New Chat" 
//               className="w-6 h-6" 
//             />
//           </div>
//         </div>
//       </div>
//     );
//   }

//   // Full sidebar when not collapsed
//   return (
//     <div className="h-full relative w-[212px] border-r border-[#D1C3BE]">
//       {/* New Chat button at top */}
//       <div 
//         className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//         onClick={handleNewChat}
//       >
//         <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//         <img 
//           src={NewChatIcon.src || NewChatIcon} 
//           alt="Chat" 
//           className="w-8 h-8 overflow-hidden pointer-events-none" 
//         />
//       </div>
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className="w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 overflow-hidden">
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
//           {/* Apply CSS to hide the scrollbar */}
//           <style jsx>{`
//             .scrollbar-hide::-webkit-scrollbar {
//               display: none;
//             }
//           `}</style>
          
//           {isLoading ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : error ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error}
//             </div>
//           ) : chats.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No chats found
//             </div>
//           ) : (
//             // Map through each date group that has chats
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {chats
//                   .filter(chat => chat.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((chat) => (
//                     <div 
//                       key={chat.id} 
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 ${
//                         currentSessionId === chat.id ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleChatSelect(chat.id)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap" style={{ fontFamily: 'Figtree' }}>
//                         {chat.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }


// import React, { useState, useEffect } from "react";
// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { ChatHistoryProps } from "@/types/chat";
// import { useChat } from "@/contexts/ChatContext";

// interface ExtendedChatHistoryProps extends ChatHistoryProps {
//   setActiveView: (view: "home" | "chat") => void;
// }

// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView }: ExtendedChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();
  
//   // Animation state
//   const [isAnimating, setIsAnimating] = useState(false);
//   const [showFullSidebar, setShowFullSidebar] = useState(!isCollapsed);

//   // Handle animation when isCollapsed prop changes
//   useEffect(() => {
//     if (isCollapsed && showFullSidebar) {
//       // Start collapse animation
//       setIsAnimating(true);
//       // After animation duration, actually hide the full sidebar
//       const timer = setTimeout(() => {
//         setShowFullSidebar(false);
//         setIsAnimating(false);
//       }, 300); // Match this to the animation duration in CSS
//       return () => clearTimeout(timer);
//     } else if (!isCollapsed && !showFullSidebar) {
//       // Show the full sidebar immediately and then animate it in
//       setShowFullSidebar(true);
//       setIsAnimating(true);
//       // After animation is complete, clear the animating flag
//       const timer = setTimeout(() => {
//         setIsAnimating(false);
//       }, 300); // Match this to the animation duration in CSS
//       return () => clearTimeout(timer);
//     }
//   }, [isCollapsed, showFullSidebar]);

//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       await createNewChat();
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//     }
//   };
  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session
//   const handleChatSelect = async (sessionId: string) => {
//     try {
//       await loadSession(sessionId);
//       setActiveView("chat");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to load chat session:", err);
//     }
//   };

//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => chats.some(chat => chat.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   if (!showFullSidebar) {
//     // When collapsed, only render the top bar with toggle and new chat button
//     return (
//       <div className="h-[80px]">
//         <div className="flex items-center h-full">
//           {/* Sidebar toggle icon */}
//           <div 
//             onClick={toggleSidebar} 
//             className="w-8 h-8 ml-3 cursor-pointer"
//           >
//             <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//           </div>
          
//           {/* New Chat button */}
//           <div 
//             className="flex items-center ml-3 cursor-pointer"
//             onClick={handleNewChat}
//           >
//             <span className="text-[#FF6B1C] text-xs font-medium mr-1">New Chat</span>
//             <img 
//               src={NewChatIcon.src || NewChatIcon} 
//               alt="New Chat" 
//               className="w-6 h-6" 
//             />
//           </div>
//         </div>
//       </div>
//     );
//   }

//   // Full sidebar with animation
//   return (
//     <div className={`h-full relative w-[212px] border-r border-[#D1C3BE] ${isAnimating && isCollapsed ? 'animate-slide-out' : isAnimating && !isCollapsed ? 'animate-slide-in' : ''}`}>
//       {/* Combined styles for animation and scrollbar */}
//       <style jsx global>{`
//         @keyframes slideOutLeft {
//           from {
//             opacity: 1;
//             transform: translateX(0);
//           }
//           to {
//             opacity: 0;
//             transform: translateX(-30px);
//           }
//         }
        
//         @keyframes slideInLeft {
//           from {
//             opacity: 0;
//             transform: translateX(-30px);
//           }
//           to {
//             opacity: 1;
//             transform: translateX(0);
//           }
//         }
        
//         .animate-slide-out {
//           animation: slideOutLeft 500ms ease-in-out forwards;
//         }
        
//         .animate-slide-in {
//           animation: slideInLeft 500ms ease-in-out forwards;
//         }
        
//         .scrollbar-hide::-webkit-scrollbar {
//           display: none;
//         }
//       `}</style>
      
//       {/* New Chat button at top */}
//       <div 
//         className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//         onClick={handleNewChat}
//       >
//         <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//         <img 
//           src={NewChatIcon.src || NewChatIcon} 
//           alt="Chat" 
//           className="w-8 h-8 overflow-hidden pointer-events-none" 
//         />
//       </div>
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className="w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 overflow-hidden">
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
          
//           {isLoading ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : error ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error}
//             </div>
//           ) : chats.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No chats found
//             </div>
//           ) : (
//             // Map through each date group that has chats
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {chats
//                   .filter(chat => chat.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((chat) => (
//                     <div 
//                       key={chat.id} 
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 ${
//                         currentSessionId === chat.id ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleChatSelect(chat.id)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap" style={{ fontFamily: 'Figtree' }}>
//                         {chat.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }


// //ChatHistory.tsx
// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { ChatHistoryProps } from "@/types/chat";
// import { useChat } from "@/contexts/ChatContext";
// import { useEffect, useState } from "react";
// import { useAuth } from "@/contexts/AuthContext";
// import {ACTION_ITEMS_ACTION_ENDPOINT} from "@/app/utils/Api"

// interface ExtendedChatHistoryProps extends ChatHistoryProps {
//   setActiveView: (view: "home" | "chat") => void;
// }

// interface ActionItem {
//   _id: string;
//   document_number: string;
//   document_title: string;
//   s3_url: string;
//   username: string;
//   document_id?: string;
// }

// interface CombinedHistoryItem {
//   id: string;
//   title: string;
//   date: string;
//   created_at: string;
//   type: 'session' | 'action_item';
//   original_data?: any;
// }

// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView, setSelectedActionItem }: ExtendedChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();
//   const [actionItems, setActionItems] = useState<ActionItem[]>([]);
//   const [actionItemsLoading, setActionItemsLoading] = useState(false);
//   const [actionItemsError, setActionItemsError] = useState<string | null>(null);
//   const {user}=useAuth();
//   const username= user?.username;

//   // Fetch action items
//   useEffect(() => {
//     const fetchActionItems = async () => {
//       setActionItemsLoading(true);
//       setActionItemsError(null);
      
//       try {
//         const response = await fetch(ACTION_ITEMS_ACTION_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           username:username
//         }),
//       });
//         if (!response.ok) {
//           throw new Error('Failed to fetch action items');
//         }
//         const data = await response.json();
//         setActionItems(data);
//       } catch (err) {
//         console.error('Error fetching action items:', err);
//         setActionItemsError('Failed to load action items');
//       } finally {
//         setActionItemsLoading(false);
//       }
//     };

//     fetchActionItems();
//   }, []);

//   // Combine chats and action items
//   const getCombinedHistory = (): CombinedHistoryItem[] => {
//     const combinedItems: CombinedHistoryItem[] = [];
    
//     // Add chat sessions
//     chats.forEach(chat => {
//       combinedItems.push({
//         id: chat.id,
//         title: chat.title,
//         date: chat.date,
//         created_at: chat.created_at,
//         type: 'session',
//         original_data: chat
//       });
//     });
    
//     // Add action items with today's date
//     const today = new Date();
//     const todayString = today.toISOString();
    
//     actionItems.forEach(item => {
//       combinedItems.push({
//         id: item._id,
//         title: `AI: ${item.document_title}`,
//         date: "Today", // Using "Today" for all action items as requested
//         created_at: todayString,
//         type: 'action_item',
//         original_data: item
//       });
//     });
    
//     return combinedItems;
//   };

//   const combinedHistory = getCombinedHistory();
  
//   // Add this useEffect right after your existing hooks in the ChatHistory component
//   useEffect(() => {
//     // If currentSessionId is null and there are chats available, select the most recent one
//     if (currentSessionId === null && chats.length > 0) {
//       const sortedChats = [...chats].sort((a, b) => {
//         // Sort by timestamp (newest first)
//         return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//       });
      
//       // Get the most recent chat
//       const mostRecentChat = sortedChats[0];
      
//       // Load the most recent chat session
//       if (mostRecentChat) {
//         console.log("Auto-selecting most recent chat:", mostRecentChat.id);
//         loadSession(mostRecentChat.id);
        
//         // Ensure we switch to chat view
//         setActiveView("chat");
//       }
//     }
//   }, [currentSessionId, chats, loadSession, setActiveView]);

//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       // console.log("New Chat clicked"); // Debug log
//       // Call API to create a new session and get session_id
//       await createNewChat();
      
//       // Navigate to home view to start the new chat
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//       // Could add error handling UI here if needed
//     }
//   };
  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     // console.log("checking inside toggle:",isCollapsed);
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session or action item
//   const handleItemSelect = async (item: CombinedHistoryItem) => {
//     try {
//       if (item.type === 'session') {
//         // Load the selected chat session
//         await loadSession(item.id);
        
//         // Ensure we switch to chat view when selecting a session
//         setActiveView("chat");
        
//         // Optional: Ensure we're on the home page to view the chat
//         router.push('/');
//       } else if (item.type === 'action_item') {
//         // Handle action item selection - navigate to ActionItemsDetail
//         console.log("Action item selected:", item.original_data);
        
//         const actionItemData = item.original_data;
        
//         // Set the selected action item if the function is provided
//         if (setSelectedActionItem) {
//           setSelectedActionItem(actionItemData);
//         }
        
//         // Switch to action-items view to show the ActionItemsDetail
//         setActiveView("action-items");
        
//         // The action items view will handle loading the specific document
//         router.push('/');
//       }
//     } catch (err) {
//       console.error("Failed to load item:", err);
//     }
//   };

//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the combined data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => combinedHistory.some(item => item.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   const isLoadingAny = isLoading || actionItemsLoading;
//   const hasError = error || actionItemsError;

//   return (
//     <div className={`h-full relative transition-all duration-300 ease-in-out ${!isCollapsed ? "w-[212px] border-r border-[#D1C3BE]" : "w-[60px]"}`}>
//       {/* New Chat button at top - only when expanded */}
//       {!isCollapsed ? (
//         <div 
//           className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//           onClick={handleNewChat}
//         >
//           <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="Chat" 
//             className="w-8 h-8 overflow-hidden pointer-events-none" 
//           />
//         </div>
//       ) : (
//         // New Chat icon when collapsed - positioned below the sidebar icon
//         <div 
//           className="absolute left-[14px] top-[66px] z-10 cursor-pointer"
//           onClick={handleNewChat}
//         >
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="New Chat" 
//             className="w-8 h-8 overflow-hidden" 
//           />
//         </div>
//       )}
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className={`w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 ${!isCollapsed ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
//           {/* Apply CSS to hide the scrollbar */}
//           <style jsx>{`
//             .scrollbar-hide::-webkit-scrollbar {
//               display: none;
//             }
//           `}</style>
          
//           {isLoadingAny ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : hasError ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error || actionItemsError}
//             </div>
//           ) : combinedHistory.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No items found
//             </div>
//           ) : (
//             // Map through each date group that has items
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {combinedHistory
//                   .filter(item => item.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((item) => (
//                     <div 
//                       key={`${item.type}-${item.id}`}
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 mb-2 ${
//                        currentSessionId === item.id && item.type === 'session' ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleItemSelect(item)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium" style={{ fontFamily: 'Figtree' }}>
//                         {item.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

//ChatHistory.tsx
// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { useChat } from "@/contexts/ChatContext";
// import { useEffect, useState } from "react";
// import { useAuth } from "@/contexts/AuthContext";
// import {ACTION_ITEMS_ACTION_ENDPOINT} from "@/app/utils/Api"
// import type {ChatHistoryProps, ActionItem, CombinedHistoryItem} from "@/types/main_types"


// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView, setSelectedActionItem, setShowDetailView }: ChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();
//   const [actionItems, setActionItems] = useState<ActionItem[]>([]);
//   const [actionItemsLoading, setActionItemsLoading] = useState(false);
//   const [actionItemsError, setActionItemsError] = useState<string | null>(null);
//   const [selectedActionItemId, setSelectedActionItemId] = useState<string | null>(null);
  
//   const {user}=useAuth();
//   const username= user?.username;

//   // Fetch action items
//   useEffect(() => {
//     const fetchActionItems = async () => {
//       setActionItemsLoading(true);
//       setActionItemsError(null);
      
//       try {
//         const response = await fetch(ACTION_ITEMS_ACTION_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           username:username
//         }),
//       });
//         if (!response.ok) {
//           throw new Error('Failed to fetch action items');
//         }
//         const data = await response.json();
//         setActionItems(data);
//       } catch (err) {
//         console.error('Error fetching action items:', err);
//         setActionItemsError('Failed to load action items');
//       } finally {
//         setActionItemsLoading(false);
//       }
//     };

//     fetchActionItems();
//   }, []);

//   // Combine chats and action items
//   const getCombinedHistory = (): CombinedHistoryItem[] => {
//     const combinedItems: CombinedHistoryItem[] = [];
    
//     // Add chat sessions
//     chats.forEach(chat => {
//       combinedItems.push({
//         id: chat.id,
//         title: chat.title,
//         date: chat.date,
//         created_at: chat.created_at,
//         type: 'session',
//         original_data: chat
//       });
//     });
    
//     // Add action items with today's date
//     const today = new Date();
//     const todayString = today.toISOString();
    
//     actionItems.forEach(item => {
//       combinedItems.push({
//         id: item._id,
//         title: `AI: ${item.document_title}`,
//         date: "Today", // Using "Today" for all action items as requested
//         created_at: todayString,
//         type: 'action_item',
//         original_data: item
//       });
//     });
    
//     return combinedItems;
//   };

//   const combinedHistory = getCombinedHistory();
  
//   // Add this useEffect right after your existing hooks in the ChatHistory component
//   useEffect(() => {
//     // If currentSessionId is null and there are chats available, select the most recent one
//     if (currentSessionId === null && chats.length > 0) {
//       const sortedChats = [...chats].sort((a, b) => {
//         // Sort by timestamp (newest first)
//         return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//       });
      
//       // Get the most recent chat
//       const mostRecentChat = sortedChats[0];
      
//       // Load the most recent chat session
//       if (mostRecentChat) {
//         console.log("Auto-selecting most recent chat:", mostRecentChat.id);
//         loadSession(mostRecentChat.id);
        
//         // Ensure we switch to chat view
//         setActiveView("chat");
//       }
//     }
//   }, [currentSessionId, chats, loadSession, setActiveView]);

//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       // console.log("New Chat clicked"); // Debug log
//       // Call API to create a new session and get session_id
//       await createNewChat();
      
//       // Clear selections when creating new chat
//       setSelectedActionItemId(null);
      
//       // Navigate to home view to start the new chat
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//       // Could add error handling UI here if needed
//     }
//   };
  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     // console.log("checking inside toggle:",isCollapsed);
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session or action item
//   const handleItemSelect = async (item: CombinedHistoryItem) => {
//     try {
//       if (item.type === 'session') {
//         // Load the selected chat session
//         await loadSession(item.id);
        
//         // Clear action item selection when selecting a session
//         setSelectedActionItemId(null);
        
//         // Ensure we switch to chat view when selecting a session
//         setActiveView("chat");
        
//         // Optional: Ensure we're on the home page to view the chat
//         router.push('/');
//       } else if (item.type === 'action_item') {
//         // Handle action item selection - navigate to ActionItemsDetail
//         console.log("Action item selected:", item.original_data);
//         setShowDetailView(true);
//         const actionItemData = item.original_data;
        
//         // Set the selected action item ID for highlighting
//         setSelectedActionItemId(item.id);
        
//         // Set the selected action item if the function is provided
//         if (setSelectedActionItem) {
//           setSelectedActionItem(actionItemData);
//         }
        
//         // Switch to action-items view to show the ActionItemsDetail
//         setActiveView("action-items");
        
//         // The action items view will handle loading the specific document
//         router.push('/');
//       }
//     } catch (err) {
//       console.error("Failed to load item:", err);
//     }
//   };

//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the combined data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => combinedHistory.some(item => item.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   // Helper function to determine if an item should be highlighted
//   const isItemSelected = (item: CombinedHistoryItem): boolean => {
//     if (item.type === 'session') {
//       return currentSessionId === item.id && selectedActionItemId === null;
//     } else if (item.type === 'action_item') {
//       return selectedActionItemId === item.id;
//     }
//     return false;
//   };

//   const isLoadingAny = isLoading || actionItemsLoading;
//   const hasError = error || actionItemsError;

//   return (
//     <div className={`h-full relative transition-all duration-300 ease-in-out ${!isCollapsed ? "w-[212px] border-r border-[#D1C3BE]" : "w-[60px]"}`}>
//       {/* New Chat button at top - only when expanded */}
//       {!isCollapsed ? (
//         <div 
//           className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//           onClick={handleNewChat}
//         >
//           <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="Chat" 
//             className="w-8 h-8 overflow-hidden pointer-events-none" 
//           />
//         </div>
//       ) : (
//         // New Chat icon when collapsed - positioned below the sidebar icon
//         <div 
//           className="absolute left-[14px] top-[66px] z-10 cursor-pointer"
//           onClick={handleNewChat}
//         >
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="New Chat" 
//             className="w-8 h-8 overflow-hidden" 
//           />
//         </div>
//       )}
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className={`w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 ${!isCollapsed ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
//           {/* Apply CSS to hide the scrollbar */}
//           <style jsx>{`
//             .scrollbar-hide::-webkit-scrollbar {
//               display: none;
//             }
//           `}</style>
          
//           {isLoadingAny ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : hasError ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error || actionItemsError}
//             </div>
//           ) : combinedHistory.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No items found
//             </div>
//           ) : (
//             // Map through each date group that has items
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {combinedHistory
//                   .filter(item => item.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((item) => (
//                     <div 
//                       key={`${item.type}-${item.id}`}
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 mb-2 ${
//                         isItemSelected(item) ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleItemSelect(item)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium" style={{ fontFamily: 'Figtree' }}>
//                         {item.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }


// //ChatHistory.tsx
// import { useRouter } from "next/navigation";
// import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
// import SideBarIcon from '@/components/assets/icons/side-bar.svg';
// import { useChat } from "@/contexts/ChatContext";
// import { useEffect, useState } from "react";
// import { useAuth } from "@/contexts/AuthContext";
// import {ACTION_ITEMS_ACTION_ENDPOINT} from "@/app/utils/Api"
// import type {ChatHistoryProps, ActionItem, CombinedHistoryItem} from "@/types/main_types"


// export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView, setSelectedActionItem, setShowDetailView }: ChatHistoryProps) {
//   const router = useRouter();
//   const { chats, isLoading, error, createNewChat, loadSession, currentSessionId } = useChat();
//   const [actionItems, setActionItems] = useState<ActionItem[]>([]);
//   const [actionItemsLoading, setActionItemsLoading] = useState(false);
//   const [actionItemsError, setActionItemsError] = useState<string | null>(null);
//   const [selectedActionItemId, setSelectedActionItemId] = useState<string | null>(null);
//   const {user}=useAuth();
//   const username= user?.username;

//   // Fetch action items
//   useEffect(() => {
//     const fetchActionItems = async () => {
//       setActionItemsLoading(true);
//       setActionItemsError(null);
      
//       try {
//         const response = await fetch(ACTION_ITEMS_ACTION_ENDPOINT, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           username:username
//         }),
//       });
//         if (!response.ok) {
//           throw new Error('Failed to fetch action items');
//         }
//         const data = await response.json();
//         setActionItems(data);
//       } catch (err) {
//         console.error('Error fetching action items:', err);
//         setActionItemsError('Failed to load action items');
//       } finally {
//         setActionItemsLoading(false);
//       }
//     };

//     fetchActionItems();
//   }, []);



//   // Helper function to categorize dates
//   const categorizeDate = (dateString: string): string => {
//     const date = new Date(dateString);
//     const now = new Date();
//     const diffInMs = now.getTime() - date.getTime();
//     const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

//     if (diffInDays === 0) {
//       return "Today";
//     } else if (diffInDays === 1) {
//       return "Yesterday";
//     } else if (diffInDays <= 7) {
//       return "Last Week";
//     } else {
//       return "Older";
//     }
//   };




//   // Combine chats and action items
//   const getCombinedHistory = (): CombinedHistoryItem[] => {
//     const combinedItems: CombinedHistoryItem[] = [];
    
//     // Add chat sessions
//     chats.forEach(chat => {
//       combinedItems.push({
//         id: chat.id,
//         title: chat.title,
//         date: chat.date,
//         created_at: chat.created_at,
//         type: 'session',
//         original_data: chat
//       });
//     });
    
//     // Add action items using their changed_date
//     actionItems.forEach(item => {
//       const actionItemDate = categorizeDate(item.changed_date);
      
//       combinedItems.push({
//         id: item._id,
//         title: `AI: ${item.document_title}`,
//         date: actionItemDate,
//         created_at: item.changed_date, // Use changed_date for sorting
//         type: 'action_item',
//         original_data: item
//       });
//     });
    
//     return combinedItems;
//   };



//   const combinedHistory = getCombinedHistory();
  

//   useEffect(() => {
//     // If currentSessionId is null and there are chats available, select the most recent one
//     if (currentSessionId === null && chats.length > 0) {
//       const sortedChats = [...chats].sort((a, b) => {
//         // Sort by timestamp (newest first)
//         return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//       });
      
//       // Get the most recent chat
//       const mostRecentChat = sortedChats[0];
      
//       // Load the most recent chat session
//       if (mostRecentChat) {
//         // console.log("Auto-selecting most recent chat:", mostRecentChat.id);
//         loadSession(mostRecentChat.id);
        
//         // Ensure we switch to chat view
//         setActiveView("chat");
//       }
//     }
//   }, [currentSessionId, chats, loadSession, setActiveView]);



//   // Handle creating a new chat
//   const handleNewChat = async () => {
//     try {
//       // console.log("New Chat clicked"); // Debug log
//       // Call API to create a new session and get session_id
//       await createNewChat();
      
//       // Clear selections when creating new chat
//       setSelectedActionItemId(null);
      
//       // Navigate to home view to start the new chat
//       setActiveView("home");
//       router.push('/');
//     } catch (err) {
//       console.error("Failed to create new chat:", err);
//       // Could add error handling UI here if needed
//     }
//   };



  
//   // Handle toggling through sidebar icon
//   const toggleSidebar = () => {
//     // console.log("checking inside toggle:",isCollapsed);
//     onToggleCollapse(!isCollapsed);
//   };

//   // Handle selecting a previous chat session or action item
//   const handleItemSelect = async (item: CombinedHistoryItem) => {
//     try {
//       if (item.type === 'session') {
//         // Load the selected chat session
//         setShowDetailView(false);
//         await loadSession(item.id);
        
//         // Clear action item selection when selecting a session
//         setSelectedActionItemId(null);
        
//         // Ensure we switch to chat view when selecting a session
//         setActiveView("chat");
        
//         // Optional: Ensure we're on the home page to view the chat
//         router.push('/');
//       } else if (item.type === 'action_item') {
//         // Handle action item selection - navigate to ActionItemsDetail
//         // console.log("Action item selected:", item.original_data);
//         setShowDetailView(true);
//         const actionItemData = item.original_data;
        
//         // Set the selected action item ID for highlighting
//         setSelectedActionItemId(item.id);
        
//         // Set the selected action item if the function is provided
//         if (setSelectedActionItem) {
//           setSelectedActionItem(actionItemData);
//         }
        
//         // Switch to action-items view to show the ActionItemsDetail
//         setActiveView("action-items");
        
//         // The action items view will handle loading the specific document
//         router.push('/');
//       }
//     } catch (err) {
//       console.error("Failed to load item:", err);
//     }
//   };



//   // Define date group order for sorting
//   const dateGroupOrder = {
//     "Today": 0,
//     "Yesterday": 1,
//     "Last Week": 2,
//     "Older": 3
//   };

//   // Get unique date groups that exist in the combined data, sorted by recency
//   const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
//     .filter(group => combinedHistory.some(item => item.date === group))
//     .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

//   // Helper function to determine if an item should be highlighted
//   const isItemSelected = (item: CombinedHistoryItem): boolean => {
//     if (item.type === 'session') {
//       return currentSessionId === item.id && selectedActionItemId === null;
//     } else if (item.type === 'action_item') {
//       return selectedActionItemId === item.id;
//     }
//     return false;
//   };

//   const isLoadingAny = isLoading || actionItemsLoading;
//   const hasError = error || actionItemsError;



//   return (
//     <div className={`h-full relative transition-all duration-300 ease-in-out ${!isCollapsed ? "w-[212px] border-r border-[#D1C3BE]" : "w-[60px]"}`}>
//       {/* New Chat button at top - only when expanded */}
//       {!isCollapsed ? (
//         <div 
//           className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
//           onClick={handleNewChat}
//         >
//           <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="Chat" 
//             className="w-8 h-8 overflow-hidden pointer-events-none" 
//           />
//         </div>
//       ) : (
//         // New Chat icon when collapsed - positioned below the sidebar icon
//         <div 
//           className="absolute left-[14px] top-[66px] z-10 cursor-pointer"
//           onClick={handleNewChat}
//         >
//           <img 
//             src={NewChatIcon.src || NewChatIcon} 
//             alt="New Chat" 
//             className="w-8 h-8 overflow-hidden" 
//           />
//         </div>
//       )}
     
//       {/* Sidebar toggle icon */}
//       <div 
//         onClick={toggleSidebar} 
//         className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
//       >
//         <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
//       </div>
      
//       {/* Chat list container */}
//       <div className={`w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 ${!isCollapsed ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
//         {/* RECENT header - Keep this outside the scrollable area */}
//         <div className="self-stretch px-5 rounded-xl flex items-center gap-2 flex-shrink-0">
//           <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
//         </div>
        
//         {/* Scrollable area for chat list with hidden scrollbar */}
//         <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
//              style={{ 
//                scrollbarWidth: 'none', /* For Firefox */
//                msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
//              }}>
//           {/* Apply CSS to hide the scrollbar */}
//           <style jsx>{`
//             .scrollbar-hide::-webkit-scrollbar {
//               display: none;
//             }
//           `}</style>
          
//           {isLoadingAny ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               Loading...
//             </div>
//           ) : hasError ? (
//             <div className="self-stretch px-4 py-2 text-center text-red-500">
//               {error || actionItemsError}
//             </div>
//           ) : combinedHistory.length === 0 ? (
//             <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
//               No items found
//             </div>
//           ) : (
//             // Map through each date group that has items
//             dateGroups.map((group) => (
//               <div key={group} className="self-stretch px-1 flex flex-col">
//                 <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
//                   <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
//                 </div>
//                 {combinedHistory
//                   .filter(item => item.date === group)
//                   .sort((a, b) => {
//                     // Sort by timestamp (newest first) within each date group
//                     return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
//                   })
//                   .map((item) => (
//                     <div 
//                       key={`${item.type}-${item.id}`}
//                       className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 mb-2 ${
//                         isItemSelected(item) ? 'bg-[#F1E9E6]' : ''
//                       }`}
//                       onClick={() => handleItemSelect(item)}
//                     >
//                       <div className="text-[#494949] text-sm font-medium" style={{ fontFamily: 'Figtree' }}>
//                         {item.title}
//                       </div>
//                     </div>
//                   ))
//                 }
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

//ChatHistory.tsx
import { useRouter } from "next/navigation";
import NewChatIcon from '@/components/assets/icons/new-chat-icon.svg';
import SideBarIcon from '@/components/assets/icons/side-bar.svg';
import { useChat } from "@/contexts/ChatContext";
import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {ACTION_ITEMS_ACTION_ENDPOINT} from "@/app/utils/Api"
import type {ChatHistoryProps, ActionItem, CombinedHistoryItem} from "@/types/main_types"


export function ChatHistory({ isCollapsed, onToggleCollapse, setActiveView, setSelectedActionItem, setShowDetailView }: ChatHistoryProps) {
  const router = useRouter();
  const { chats, isLoading, error, createNewChat, loadSession, currentSessionId, refreshChats } = useChat();
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [actionItemsLoading, setActionItemsLoading] = useState(false);
  const [actionItemsError, setActionItemsError] = useState<string | null>(null);
  const [selectedActionItemId, setSelectedActionItemId] = useState<string | null>(null);
  const {user}=useAuth();
  const username= user?.username;

  // Fetch action items function
  const fetchActionItems = async () => {
    if (!username) return;
    
    setActionItemsLoading(true);
    setActionItemsError(null);
    
    try {
      const response = await fetch(ACTION_ITEMS_ACTION_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username:username
      }),
    });
      if (!response.ok) {
        throw new Error('Failed to fetch action items');
      }
      const data = await response.json();
      setActionItems(data);
    } catch (err) {
      console.error('Error fetching action items:', err);
      setActionItemsError('Failed to load action items');
    } finally {
      setActionItemsLoading(false);
    }
  };

  // Initial fetch of action items
  useEffect(() => {
    fetchActionItems();
  }, [username]);

  // Refresh both chats and action items when sidebar is opened
  const refreshChatHistory = async () => {
    try {
      // Refresh chats (assuming your ChatContext has a refresh function)
      if (refreshChats) {
        await refreshChats();
      }
      
      // Refresh action items
      await fetchActionItems();
    } catch (error) {
      console.error('Error refreshing chat history:', error);
    }
  };

  // Helper function to categorize dates
  const categorizeDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return "Today";
    } else if (diffInDays === 1) {
      return "Yesterday";
    } else if (diffInDays <= 7) {
      return "Last Week";
    } else {
      return "Older";
    }
  };

  // Combine chats and action items
  const getCombinedHistory = (): CombinedHistoryItem[] => {
    const combinedItems: CombinedHistoryItem[] = [];
    
    // Add chat sessions
    chats.forEach(chat => {
      combinedItems.push({
        id: chat.id,
        title: chat.title,
        date: chat.date,
        created_at: chat.created_at,
        type: 'session',
        original_data: chat
      });
    });
    
    // Add action items using their changed_date
    actionItems.forEach(item => {
      const actionItemDate = categorizeDate(item.changed_date);
      
      combinedItems.push({
        id: item._id,
        title: `AI: ${item.document_title}`,
        date: actionItemDate,
        created_at: item.changed_date, // Use changed_date for sorting
        type: 'action_item',
        original_data: item
      });
    });
    
    return combinedItems;
  };

  const combinedHistory = getCombinedHistory();
  
  useEffect(() => {
    // If currentSessionId is null and there are chats available, select the most recent one
    if (currentSessionId === null && chats.length > 0) {
      const sortedChats = [...chats].sort((a, b) => {
        // Sort by timestamp (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
      
      // Get the most recent chat
      const mostRecentChat = sortedChats[0];
      
      // Load the most recent chat session
      if (mostRecentChat) {
        // console.log("Auto-selecting most recent chat:", mostRecentChat.id);
        loadSession(mostRecentChat.id);
        
        // Ensure we switch to chat view
        setActiveView("chat");
      }
    }
  }, [currentSessionId, chats, loadSession, setActiveView]);

  // Handle creating a new chat
  const handleNewChat = async () => {
    try {
      // console.log("New Chat clicked"); // Debug log
      // Call API to create a new session and get session_id
      await createNewChat();
      
      // Clear selections when creating new chat
      setSelectedActionItemId(null);
      
      // Navigate to home view to start the new chat
      setActiveView("home");
      router.push('/');
    } catch (err) {
      console.error("Failed to create new chat:", err);
      // Could add error handling UI here if needed
    }
  };

  // Handle toggling through sidebar icon with refresh
  const toggleSidebar = async () => {
    // If sidebar is currently collapsed and we're about to open it, refresh the data
    if (isCollapsed) {
      await refreshChatHistory();
    }
    
    onToggleCollapse(!isCollapsed);
  };

  // Handle selecting a previous chat session or action item
  const handleItemSelect = async (item: CombinedHistoryItem) => {
    try {
      if (item.type === 'session') {
        // Load the selected chat session
        setShowDetailView(false);
        await loadSession(item.id);
        
        // Clear action item selection when selecting a session
        setSelectedActionItemId(null);
        
        // Ensure we switch to chat view when selecting a session
        setActiveView("chat");
        
        // Optional: Ensure we're on the home page to view the chat
        router.push('/');
      } else if (item.type === 'action_item') {
        // Handle action item selection - navigate to ActionItemsDetail
        // console.log("Action item selected:", item.original_data);
        setShowDetailView(true);
        const actionItemData = item.original_data;
        
        // Set the selected action item ID for highlighting
        setSelectedActionItemId(item.id);
        
        // Set the selected action item if the function is provided
        if (setSelectedActionItem) {
          setSelectedActionItem(actionItemData);
        }
        
        // Switch to action-items view to show the ActionItemsDetail
        setActiveView("action-items");
        
        // The action items view will handle loading the specific document
        router.push('/');
      }
    } catch (err) {
      console.error("Failed to load item:", err);
    }
  };

  // Define date group order for sorting
  const dateGroupOrder = {
    "Today": 0,
    "Yesterday": 1,
    "Last Week": 2,
    "Older": 3
  };

  // Get unique date groups that exist in the combined data, sorted by recency
  const dateGroups = ["Today", "Yesterday", "Last Week", "Older"]
    .filter(group => combinedHistory.some(item => item.date === group))
    .sort((a, b) => dateGroupOrder[a as keyof typeof dateGroupOrder] - dateGroupOrder[b as keyof typeof dateGroupOrder]);

  // Helper function to determine if an item should be highlighted
  const isItemSelected = (item: CombinedHistoryItem): boolean => {
    if (item.type === 'session') {
      return currentSessionId === item.id && selectedActionItemId === null;
    } else if (item.type === 'action_item') {
      return selectedActionItemId === item.id;
    }
    return false;
  };

  const isLoadingAny = isLoading || actionItemsLoading;
  const hasError = error || actionItemsError;

  return (
    <div className={`h-full relative transition-all duration-300 ease-in-out ${!isCollapsed ? "w-[212px] border-r border-[#D1C3BE]" : "w-[60px]"}`}>
      {/* New Chat button at top - only when expanded */}
      {!isCollapsed ? (
        <div 
          className="absolute left-[100px] top-[18px] flex items-center gap-1 cursor-pointer z-10"
          onClick={handleNewChat}
        >
          <div className="text-[#FF6B1C] text-sm font-medium pointer-events-none" style={{ fontFamily: 'Figtree' }}>New Chat</div>
          <img 
            src={NewChatIcon.src || NewChatIcon} 
            alt="Chat" 
            className="w-8 h-8 overflow-hidden pointer-events-none" 
          />
        </div>
      ) : (
        // New Chat icon when collapsed - positioned below the sidebar icon
        <div 
          className="absolute left-[14px] top-[66px] z-10 cursor-pointer"
          onClick={handleNewChat}
        >
          <img 
            src={NewChatIcon.src || NewChatIcon} 
            alt="New Chat" 
            className="w-8 h-8 overflow-hidden" 
          />
        </div>
      )}
     
      {/* Sidebar toggle icon */}
      <div 
        onClick={toggleSidebar} 
        className="w-8 h-8 left-[14px] top-[18px] absolute overflow-hidden rounded-full cursor-pointer z-10"
      >
        <img src={SideBarIcon.src || SideBarIcon} alt="Toggle Sidebar" className="w-8 h-8" />
      </div>
      
      {/* Chat list container */}
      <div className={`w-full pt-[92px] pb-[16px] absolute left-0 top-0 bottom-0 flex flex-col gap-2 ${!isCollapsed ? "opacity-100" : "opacity-0"} transition-opacity duration-300 overflow-hidden`}>
        {/* RECENT header with refresh button */}
        <div className="self-stretch px-5 rounded-xl flex items-center justify-between gap-2 flex-shrink-0">
          <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>RECENT</div>
          {!isCollapsed && (
            <button
              onClick={refreshChatHistory}
              className="text-[rgba(73,73,73,0.6)] hover:text-[rgba(73,73,73,0.8)] transition-colors duration-200 p-1 rounded"
              title="Refresh"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M21 3v5h-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 16l-5 5v-5h5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          )}
        </div>
        
        {/* Scrollable area for chat list with hidden scrollbar */}
        <div className="flex-1 flex flex-col gap-2 h-full pb-4 overflow-y-auto scrollbar-hide" 
             style={{ 
               scrollbarWidth: 'none', /* For Firefox */
               msOverflowStyle: 'none',  /* For Internet Explorer and Edge */
             }}>
          {/* Apply CSS to hide the scrollbar */}
          <style jsx>{`
            .scrollbar-hide::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          
          {isLoadingAny ? (
            <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
              Loading...
            </div>
          ) : hasError ? (
            <div className="self-stretch px-4 py-2 text-center text-red-500">
              {error || actionItemsError}
            </div>
          ) : combinedHistory.length === 0 ? (
            <div className="self-stretch px-4 py-2 text-center text-[#7B7B7B]">
              No items found
            </div>
          ) : (
            // Map through each date group that has items
            dateGroups.map((group) => (
              <div key={group} className="self-stretch px-1 flex flex-col">
                <div className="self-stretch px-4 py-2 rounded-xl flex items-center gap-2">
                  <div className="text-[#7B7B7B] text-xs font-bold" style={{ fontFamily: 'Figtree' }}>{group}</div>
                </div>
                {combinedHistory
                  .filter(item => item.date === group)
                  .sort((a, b) => {
                    // Sort by timestamp (newest first) within each date group
                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
                  })
                  .map((item) => (
                    <div 
                      key={`${item.type}-${item.id}`}
                      className={`self-stretch px-4 py-2 rounded-xl hover:bg-[#F1E9E6] cursor-pointer flex items-center gap-2 mb-2 ${
                        isItemSelected(item) ? 'bg-[#F1E9E6]' : ''
                      }`}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="text-[#494949] text-sm font-medium" style={{ fontFamily: 'Figtree' }}>
                        {item.title}
                      </div>
                    </div>
                  ))
                }
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}