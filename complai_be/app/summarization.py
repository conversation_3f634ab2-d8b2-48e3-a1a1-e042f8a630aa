from app.ai import ai_client
from app.summarization_prompt import generate_summarization_prompt

def generate_summary(conversations):
    """
    Generates a summary of the last 5 query-response pairs using ChatGPT.

    Args:
        conversations (list): List of the last 5 query-response pairs.

    Returns:
        str: The summary generated by the ChatGPT API.
    """
    try:
        # Generate the summarization prompt
        prompt = generate_summarization_prompt(conversations)

        # Call ChatGPT API to generate summary
        response = ai_client.completion([{
            'role' : 'user',
            'content' : prompt
        }])
        return response

    except Exception as e:
        raise Exception(f"Error during summarization: {str(e)}")
