import asyncio
from fastapi import <PERSON><PERSON>outer, HTTPException
from pydantic import BaseModel
from .pipelines import run_langgraph_pipeline, generate_sse_events
from fastapi.responses import StreamingResponse

query_router = APIRouter()

class QueryRequest(BaseModel):
    query: str
    session_id: str
    username: str

@query_router.post("/query")
async def query_endpoint(request: QueryRequest):
    extra_context = {
        "regulatory_domain": "banking",
        "regulatory_body": "RBI",
        "user_role": "compliance officer",
        "organization_type": "bank",
        "query_purpose": "audit",
        "session_summary": "Recent discussion on customer due diligence."
    }

    state = run_langgraph_pipeline(request.query, request.session_id, extra_context, request.username)

    if state.get("llm_follow_up"):
        return {
            "status": "completed",
            "answer": {
                "query": state.get("query"),
                "response": state.get("llm_follow_up"),
                "metadata": []
            },
            "sources": {},
            # "session_id": request.session_id,
            "suggestions": state.get("suggestions")
        }

    sources = []  # Optionally, extract document titles from contexts.
    return {
        "status": "completed",
        "answer": state.get("final_answer"),
        "sources": sources,
        "suggestions": state.get("suggestions")
    }


@query_router.post("/query-sse")
async def query_endpoint_sse(request: QueryRequest):
    extra_context = {
        "regulatory_domain": "banking",
        "regulatory_body": "RBI",
        "user_role": "compliance officer",
        "organization_type": "bank",
        "query_purpose": "audit",
        "session_summary": "Recent discussion on customer due diligence."
    }

    # Create server-sent events generator
    event_generator = generate_sse_events(
        request.query,
        request.session_id,
        extra_context,
        request.username
    )

    return StreamingResponse(
        event_generator,
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # Disable buffering in Nginx
        }
    )
