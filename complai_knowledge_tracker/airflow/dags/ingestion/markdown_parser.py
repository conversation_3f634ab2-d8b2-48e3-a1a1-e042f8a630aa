# markdown_parser.py
import re
from mistletoe import Document, block_token
from mistletoe.markdown_renderer import MarkdownRenderer

class MarkdownNode:
    """
    Represents a section (node) in the markdown document.
    Each node holds:
      - token: (optionally) a Heading token
      - content_tokens: list of tokens that belong to this node (paragraphs, code blocks, etc.)
      - children: sub‑sections (as MarkdownNode)
    """
    def __init__(self, token: block_token.BlockToken = None):
        self.token = token
        self.content_tokens = []
        self.children = []

    def render(self) -> str:
        """Render this node (header, its own content, and its children) back to markdown."""
        md = ""
        with Markdown<PERSON>enderer() as renderer:
            if self.token and isinstance(self.token, block_token.Heading):
                md += renderer.render(self.token).strip() + "\n\n"
            for token in self.content_tokens:
                md += renderer.render(token).strip() + "\n\n"
        for child in self.children:
            md += child.render()
        return md

def build_markdown_tree(markdown_text: str) -> MarkdownNode:
    """
    Build a hierarchical tree from the given markdown.
    The returned root is a dummy node whose children are the top‑level sections.
    """
    doc = Document(markdown_text)
    root = MarkdownNode()  # dummy root with no token
    # Stack entries: (node, heading level) with dummy root at level 0.
    stack = [(root, 0)]
    for token in doc.children:
        if isinstance(token, block_token.Heading):
            level = token.level
            new_node = MarkdownNode(token)
            # Pop until the last node has a level lower than current heading.
            while stack and stack[-1][1] >= level:
                stack.pop()
            stack[-1][0].children.append(new_node)
            stack.append((new_node, level))
        else:
            stack[-1][0].content_tokens.append(token)
    return root


