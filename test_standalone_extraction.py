#!/usr/bin/env python3
"""
Standalone test for RBI PDF extraction with the fixed logic
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def _process_relative_url(href):
    """Process relative URLs to absolute URLs"""
    if href.startswith('http'):
        return href
    
    # Relative URL with leading slash
    if href.startswith('/'):
        if 'rdocs' in href or '.pdf' in href.lower():
            return f"https://rbidocs.rbi.org.in{href}"
        else:
            return f"https://www.rbi.org.in{href}"
    
    # Relative URL without leading slash
    if not href.startswith('http'):
        if 'rdocs' in href or '.pdf' in href.lower():
            return f"https://rbidocs.rbi.org.in/{href}"
        else:
            return f"https://www.rbi.org.in/{href}"
    
    return href

def extract_pdf_from_rbi_page_fixed(rbi_page_url):
    """
    Fixed version of PDF extraction from RBI page
    """
    try:
        logger.info(f"🔍 Fetching PDF link from RBI page: {rbi_page_url}")

        # Add headers to mimic browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(rbi_page_url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Enhanced PDF extraction patterns for RBI pages
        pdf_link = None
        notification_id = None

        # Extract notification ID from URL for validation
        id_match = re.search(r'Id=(\d+)', rbi_page_url)
        if id_match:
            notification_id = id_match.group(1)
            logger.info(f"   🔍 Notification ID: {notification_id}")

        # Pattern 1: Table header with PDF icon and link (most common RBI pattern)
        tableheader = soup.find('td', class_='tableheader')
        if tableheader:
            # Look for anchor tag within tableheader
            pdf_anchor = tableheader.find('a', href=True)
            if pdf_anchor and pdf_anchor.get('href'):
                href = pdf_anchor['href']
                if '.pdf' in href.lower():
                    # Simplified validation - avoid only known bad patterns
                    if 'NOTI134' in href.upper():
                        logger.warning(f"   ❌ Skipping known incorrect NOTI134 link: {href}")
                    elif 'Utkarsh30122022.pdf' in href:
                        logger.warning(f"   ❌ Skipping generic RBI vision document: {href}")
                    else:
                        pdf_link = href
                        logger.info(f"   ✅ Found PDF in tableheader: {pdf_link}")

            # Alternative: Look for href in any element within tableheader
            if not pdf_link:
                for element in tableheader.find_all(href=True):
                    href = element.get('href', '')
                    if '.pdf' in href.lower():
                        # Apply same simplified validation
                        if 'NOTI134' in href.upper():
                            logger.warning(f"   ❌ Skipping NOTI134 in tableheader element: {href}")
                        elif 'Utkarsh30122022.pdf' in href:
                            logger.warning(f"   ❌ Skipping generic vision document: {href}")
                        else:
                            pdf_link = href
                            logger.info(f"   ✅ Found PDF in tableheader element: {pdf_link}")
                            break

        # Pattern 2: Direct PDF links anywhere on page (with filtering)
        if not pdf_link:
            pdf_anchors = soup.find_all('a', href=re.compile(r'.*\.pdf$', re.IGNORECASE))
            for anchor in pdf_anchors:
                href = anchor['href']
                # Skip known generic documents
                if 'NOTI134' in href.upper() or 'Utkarsh30122022.pdf' in href:
                    continue
                # Prefer notification/PressRelease PDFs over content PDFs
                if 'notification/PDFs/' in href or 'PressRelease/PDFs/' in href:
                    pdf_link = href
                    logger.info(f"   ✅ Found priority PDF link: {pdf_link}")
                    break
                elif not pdf_link:  # Use as fallback if no priority link found
                    pdf_link = href
                    logger.info(f"   ✅ Found fallback PDF link: {pdf_link}")

        if pdf_link:
            # Process relative URL if needed
            processed_pdf_link = _process_relative_url(pdf_link)
            logger.info(f"   📄 Final PDF URL: {processed_pdf_link}")
            return processed_pdf_link
        else:
            logger.warning(f"   ⚠️ No PDF link found on page: {rbi_page_url}")
            return None

    except requests.RequestException as e:
        logger.error(f"   ❌ Network error fetching RBI page {rbi_page_url}: {e}")
        return None
    except Exception as e:
        logger.error(f"   ❌ Error extracting PDF from RBI page {rbi_page_url}: {e}")
        return None

def test_fixed_extraction():
    """Test the fixed extraction function"""
    
    test_urls = [
        "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0",
        "https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566",
        "https://www.rbi.org.in/Scripts/BS_PressReleaseDisplay.aspx?prid=54321"
    ]
    
    print("🧪 Testing Fixed RBI PDF Extraction")
    print("=" * 80)
    
    results = []
    for url in test_urls:
        print(f"\n🔍 Testing: {url}")
        print("-" * 60)
        
        try:
            pdf_url = extract_pdf_from_rbi_page_fixed(url)
            if pdf_url:
                print(f"✅ SUCCESS: {pdf_url}")
                results.append(pdf_url)
            else:
                print(f"❌ FAILED: No PDF found")
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print(f"\n📊 Summary: Found {len(results)} PDF links")
    for i, url in enumerate(results, 1):
        print(f"   {i}. {url}")

if __name__ == "__main__":
    test_fixed_extraction()
