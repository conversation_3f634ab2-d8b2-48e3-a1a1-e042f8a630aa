"""
dag_rbi_regulations_processing.py
---------------------------------
An Airflow DAG to process RBI regulation PDFs from S3, extract metadata and chunks,
store metadata in DocumentDB and ingest chunks into Qdrant.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
import json
import os
import re
import uuid
import requests
import logging
from typing import List
from io import BytesIO

from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
import boto3

import fitz  # PyMuPDF

import markdown
import strip_markdown

# Generated by Copilot


from bs4 import BeautifulSoup
from openpyxl import Workbook
from pydantic import BaseModel
from openai import OpenAI
from mistralai import Mistral
from pymongo import MongoClient

# ------------------------------------------------------------------------------
# Assume these modules are implemented and available.
from ingestion.combine_pages import combine_pages
from ingestion.element_extractor import extract_markdown_elements
from ingestion.ocr_extractor import extract_ocr_from_pdf
from ingestion.chunker import get_chunks_and_summary
from langchain_openai import OpenAIEmbeddings


from utils.date_utils import normalize_date

# ------------------------------------------------------------------------------
# Global Clients/Keys
openai_api_key = Variable.get("openai_api_key")
client_openai = OpenAI(api_key=openai_api_key)

mistral_api_key = Variable.get("mistral_api_key")
client_mistral = Mistral(api_key=mistral_api_key)

aws_access_key_id = Variable.get("aws_access_key_id")
aws_secret_access_key = Variable.get("aws_secret_access_key")
s3_bucket_name = Variable.get("s3_bucket_name")   # e.g., "rbi-actionables"
# Initialize the OpenAI embeddings using your API key.
embeddings = OpenAIEmbeddings(api_key=openai_api_key,
                                model="text-embedding-3-large",
                                max_retries = 5, 
                                retry_min_seconds=2, 
                                retry_max_seconds= 5)
embedding_size = len(embeddings.embed_query('a'))

vector_db_host = Variable.get("vector_db_host")

# Logger
logger = logging.getLogger("airflow.task")

documentdb_uri = Variable.get("documentdb_uri")
try:
    response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
    response.raise_for_status()
    with open("/tmp/global-bundle.pem", "wb") as file:
        file.write(response.content)
    logger.info("Downloaded TLS CA file successfully.")
except Exception as e:
    logger.error(f"Error downloading TLS CA file: {e}")
    raise

def download_pdf(url: str) -> BytesIO:
    response = requests.get(url)
    response.raise_for_status()
    return BytesIO(response.content)
# ------------------------------------------------------------------------------

# ------------------------------------------------------------------------------
# OpenAI Structured Extraction for RBI Metadata
class DocumentType(str, Enum):
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    TENDER = "tender"
    PUBLICATION = "publication"
    OTHER = "other"

class RBIMetadataExtraction(BaseModel):
    document_number: str = None
    document_title: str = None
    document_type: DocumentType
    date_of_issue: str = None
    addressee: str = None
    is_applicable_to_banks: bool = None
    addressee_entities: list[str] 
    addressee_person: list[str] 
    applicable_departments: list[str] 
    is_withdrawn: bool = None
    keywords: list[str] 
    effective_date: str = None
    supersedes: list[str]

def extract_rbi_metadata(document_text: str) -> RBIMetadataExtraction:
    metadata_prompt = (
        "Analyze the following RBI regulation document and generate a structured JSON output with the following fields:\n"
        "1. 'document_number' (string) - Similar patterns like RBI/YYYY-YY/NNN, DEPT.DIV.NN/NN.NN.NNN/YYYY-YY or similar reference numbers.\n"
        "2. 'document_title' (string) - The full title of the document.\n"
        "3. 'document_type' (string) - The RBI regulation/document type (value can be master_direction, master_circular, notification, press_release, speech, tender, publication or other).\n"
        "4. 'date_of_issue' (string in DD/MM/YYYY format) - The official issue date.\n"
        "5. 'addressee' (list of strings) - The financial institutions to which the document is addressed.\n"
        "6. 'is_applicable_to_banks' (boolean) - Whether it is applicable to banks (true/false).\n"
        "7. 'addressee_entities' (list of strings) - Entities such as Scheduled Banks, NBFCs, etc.\n"
        "8. 'addressee_person' (list of strings) - If addressed to individuals within the institution.\n"
        "9. 'applicable_departments' (array of department codes) - Departments this applies to.\n"
        "10. 'is_withdrawn' (boolean) - If the document is mentioned to be withdrawn.\n"
        "11. 'keywords' (array of strings) - Important keywords for search indexing.\n"
        "12. 'effective_date' (string in DD/MM/YYYY format) - When the circular takes effect.\n"
        "13. 'supersedes' (array of strings) - Any previous circulars this replaces.\n\n"
        "Instructions:\n"
        "- Look for department codes like DOR, DEPR, DSIM, DBR, DBS, DCM in the circular number, title, authority, or signature.\n"
        "- Identify Master Directions and Notifications.\n"
        "- Extract deadlines, penalties, and technology recommendations.\n"
        "Return ONLY the structured JSON, no explanations.\n\n"
        "Document Content:\n"
        f"{document_text}\n"
    )

    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert at extracting structured data from RBI regulatory documents."},
            {"role": "user", "content": metadata_prompt}
        ],
        response_format=RBIMetadataExtraction,
    )
    metadata: RBIMetadataExtraction = completion.choices[0].message.parsed

    # convert date to DD/MM/YYYY format
    metadata.date_of_issue = normalize_date(metadata.date_of_issue)
    return metadata

# ------------------------------------------------------------------------------
# DocumentDB Storage (using pymongo)
def download_tls_ca_file():
    try:
        response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        response.raise_for_status()
        with open("/tmp/global-bundle.pem", "wb") as file:
            file.write(response.content)
        logger.info("TLS CA file downloaded successfully")
    except requests.RequestException as e:
        logger.error(f"Failed to download TLS CA file: {e}")
        raise

def store_metadata_in_documentdb(metadata: dict):
    # Ensure the URI starts with 'mongodb://' or 'mongodb+srv://'
    if not documentdb_uri.startswith(("mongodb://", "mongodb+srv://")):
        raise ValueError("Invalid DocumentDB URI: Must start with 'mongodb://' or 'mongodb+srv://'")

    # Download TLS CA and create MongoDB client
    download_tls_ca_file()
    client = MongoClient(
        # "mongodb://host.docker.internal:27017",
        # tls= False
        documentdb_uri,
        tls=True,
        tlsCAFile="/tmp/global-bundle.pem",
        replicaSet="rs0",
        readPreference="secondaryPreferred",
        retryWrites=False
    )
    import sys
    import json

    def log_large_fields(metadata):
        total_size = sys.getsizeof(json.dumps(metadata))
        print(f"📦 Total document size: {total_size / (1024*1024):.2f} MB")

        # Check size of each key individually
        field_sizes = [(k, sys.getsizeof(json.dumps(v))) for k, v in metadata.items()]
        field_sizes.sort(key=lambda x: x[1], reverse=True)

        print("\n🧱 Field size breakdown (Top 10):")
        for k, size in field_sizes[:10]:
            print(f"- {k}: {size / 1024:.2f} KB")

    (metadata)

    try:
        db = client["rbi_regulations_db"]
        collection_name = "rbi_regulations_new"
        if collection_name not in db.list_collection_names():
            db.create_collection(collection_name)
            logger.info(f"Created collection: {collection_name}")
        collection = db[collection_name]
        print("Collection name: ", collection.name)

        unique_id = metadata.get("document_number") or metadata.get("document_title") or "unknown"
        print("Unique ID: ", unique_id)
        log_large_fields(metadata)
        collection.update_one({"_id": unique_id}, {"$set": metadata}, upsert=True)
        
        logger.info("Stored metadata in DocumentDB.")
    except Exception as e:
        logger.error(f"Failed to store metadata in DocumentDB: {e}")
        raise
    finally:
        client.close()
# Qdrant Ingestion


def ingest_chunks_to_qdrant(chunks_payload: list, document_id: str, document_summary: str, topics: list, document_metadata: dict):
    """
    Ingests all chunk payloads into Qdrant using QdrantVectorStore.
    Each chunk’s payload is enriched with:
      - Document ID
      - Chunk index
      - The complete document summary
      - Base metadata (extracted from the document)
      - Positional info (bounding boxes, page numbers, etc.)
    """
    from langchain_qdrant import QdrantVectorStore, RetrievalMode, FastEmbedSparse
    from qdrant_client import QdrantClient

    
    # Configure sparse embeddings. Adjust model_kwargs as per your environment.
    model_kwargs = {"device": "cpu"}
    sparse_embeddings = FastEmbedSparse(
        model_name="Qdrant/bm25",
        model_kwargs=model_kwargs,
    )
    # Initialize Qdrant client and specify the collection name.
    #qdrant_client = QdrantClient(host="host.docker.internal", port=6333)
    qdrant_client = QdrantClient(host=vector_db_host, port=6333)
    qdrant_client.set_sparse_model('Qdrant/bm25')
    index_name = "rbi_chunks_new"
    # Check if the collection exists, and create it if it doesn't
    def check_for_collection(index_name: str):
        if not qdrant_client.collection_exists(index_name):
            # The collection does not exist, so proceed to create it
            try:
                sparse_params = qdrant_client.get_fastembed_sparse_vector_params()  # Adjust based on your setup
                
                qdrant_client.create_collection(
                    collection_name=index_name, 
                    vectors_config={
                        'size': embedding_size, 
                        'distance': 'Cosine'
                    },
                    sparse_vectors_config=sparse_params
                )
                print("Collection created successfully.")
            except Exception as e:
                print("Failed to create the collection:", e.__class__.__name__, str(e))
        else:
            # The collection already exists
            print("Collection already exists, skipping creation.")

    check_for_collection(index_name)
    # Initialize QdrantVectorStore with the provided configurations.
    qdrant_vector_search = QdrantVectorStore(
        client=qdrant_client,
        collection_name=index_name,
        embedding=embeddings,
        sparse_embedding=sparse_embeddings,
        retrieval_mode=RetrievalMode.HYBRID,
        sparse_vector_name='fast-sparse-bm25'
    )

    # Prepare the lists of chunk texts and their associated metadata.
    chunks = []
    metadata_list = []
    for idx, payload in enumerate(chunks_payload):
        
        # strip markdown
        content = strip_markdown.strip_markdown(payload["content"])
        # section_summary = payload["section_summary"]
        section_title = payload["section_title"]
        # add summary to chunk to have better embeddings
        chunk_text = (
            f"Document Summary:\n{document_summary}\n\n"
            f"Topics:\n{', '.join(topics)}\n\n"
            f"Section Title [{section_title}]\n\n"
            f"Content:\n{content}"
        )
        chunks.append(chunk_text)
        # Merge the base metadata with additional information specific to this chunk.
        chunk_metadata = {
            "document_id": document_id,
            "chunk_index": idx,
            "document_summary": document_summary,
            **document_metadata,  # Base metadata extracted from the document
            "positions": payload["positions"]
        }
        metadata_list.append(chunk_metadata)

    # Add the prepared chunks and metadata to Qdrant.
    qdrant_vector_search.add_texts(chunks, metadata_list)
    logger.info(f"File '{document_metadata.get('document_title', 'Unnamed')}' is indexed successfully with bounding boxes and page numbers.")



class Obligation(BaseModel):
    para: str
    obligation: str
    action_item: str

class ObligationList(BaseModel):
   obligations_list: List[Obligation]

def extract_obligations_from_markdown(markdown_text: str) -> List[Obligation]:
    """
    Sends the entire markdown text to OpenAI structured output API call and extracts a JSON array of obligations.
    Each obligation has the fields:
      - 'Circular Number'
      - 'Circular Title'
      - 'Para'
      - 'Obligation'
      - 'Action Item'
    """
    prompt = (
    "Role: You are an expert compliance analyst with extensive experience in RBI regulatory documents. "
    "Your task is to extract all obligations from the provided RBI regulation markdown document.\n\n"
    
    "Goal: Extract a JSON array where each object contains exactly the following fields:\n"
    "  - 'Para': the paragraph number or section identifier (string)\n"
    "  - 'Obligation': the text of the obligation as it is from the markdown document(string)\n\n"
    "  - 'Action Item': a concise action item for the compliance office derived from the obligation (string)\n\n"
    
    "Instructions:\n"
    "1. Read and understand the entire markdown document provided below.\n"
    "2. Identify the Circular Number and Circular Title from the document header.\n"
    "3. Process the document to locate each paragraph or section that contains a regulatory obligation. "
    "An obligation is typically indicated by language such as 'shall', 'must', 'required to', 'obligated', or 'need to'.\n"
    "4. For each obligation, capture:\n"
    "   - 'Para': the paragraph or section identifier (e.g., '2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3')\n"
    "   - 'Obligation': the complete text of the obligation statement.\n"
    "5. Ensure that the output JSON array contains objects with exactly the following keys: 'Circular Number', "
    "'Circular Title', 'Para', and 'Obligation'. Do not include any additional fields.\n"
    "6. If a paragraph contains multiple obligations, list each obligation as a separate object while repeating the "
    "Circular Number and Circular Title.\n\n"
     "7. If a paragraph contains multiple obligations, list each obligation as a separate object while repeating the "
        "Circular Number and Circular Title.\n\n"
      
    "Example:\n\n"
    "---------------------------------------------------------\n"
    "Circular Number: RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\n"
    "Circular Title: Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\n"
    "Para: 2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3\n"
    "Obligation: Banknotes tendered over the counter shall be examined for authenticity through machines. "
    "No credit to customer's account is to be given for Counterfeit Notes, if any, detected in the tender received over the counter or at the back-office / currency chest. "
    "In no case, the Counterfeit Notes should be returned to the tenderer or destroyed by the bank branches / treasuries. "
    "Failure of the banks to impound Counterfeit Notes detected at their end will be construed as wilful involvement of the bank concerned in circulating Counterfeit Notes and penalty will be imposed.\n"
    "Action Item: Ensure that all banknotes tendered over the counter are verified for authenticity using designated machines, and implement immediate measures if counterfeit notes are detected.\n"
    "---------------------------------------------------------\n\n"
    
    "Another sample entry:\n\n"
    "---------------------------------------------------------\n"
    "Circular Number: RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\n"
    "Circular Title: Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\n"
    "Para: 3. Impounding of Counterfeit Notes\n"
    "Obligation: Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE'.\n"
    "Action Item: Ensure that notes identified as counterfeit are promptly stamped and impounded as per guidelines.\n"
    "---------------------------------------------------------\n\n"
    "Based on the above, your output should be a JSON array like the following:\n\n"
    
    "[\n"
    "  {\n"
    "    \"Circular Number\": \"RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\",\n"
    "    \"Circular Title\": \"Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\",\n"
    "    \"Para\": \"2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3\",\n"
    "    \"Obligation\": \"Banknotes tendered over the counter shall be examined for authenticity through machines. No credit to customer's account is to be given for Counterfeit Notes, if any, detected in the tender received over the counter or at the back-office / currency chest. In no case, the Counterfeit Notes should be returned to the tenderer or destroyed by the bank branches / treasuries. Failure of the banks to impound Counterfeit Notes detected at their end will be construed as wilful involvement of the bank concerned in circulating Counterfeit Notes and penalty will be imposed.\"\n"
    "    \"Action Item\": \"Bank shall ensure that all banknotes tendered over the counter are verified for authenticity using designated machines, and that any detected counterfeit notes are immediately impounded in accordance with regulatory guidelines.\","
    "  },\n"
    "  {\n"
    "    \"Circular Number\": \"RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\",\n"
    "    \"Circular Title\": \"Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\",\n"
    "    \"Para\": \"3. Impounding of Counterfeit Notes\",\n"
    "    \"Obligation\": \"Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE'.\"\n"
    "    \"Action Item\": \"Bank shall ensure that all notes identified as counterfeit are promptly stamped as 'COUNTERFEIT NOTE' and secured in accordance with the prescribed procedures.\","
    "  }\n"
    "]\n\n"
    
    "Now, carefully analyze the following markdown document and produce the JSON array accordingly.\n\n"
    "Markdown Document:\n"
    f"{markdown_text}"
    )

    
    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert at extracting structured information from regulatory documents."},
            {"role": "user", "content": prompt}
        ],
        response_format=ObligationList,
    )
    obligations = completion.choices[0].message.parsed
    print("obligations : ", obligations)
    return obligations.obligations_list

def generate_action_item(obligation_text: str) -> str:
    """
    Generates an action item for a given obligation using an OpenAI API call.
    """
    prompt = (
        f"Based on the following regulatory obligation, generate a concise action item for the compliance office to verify and implement:\n\n"
        f"Obligation: {obligation_text}\n\n"
        "Return only the action item as plain text."
    )
    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert compliance advisor."},
            {"role": "user", "content": prompt}
        ],
        response_format=str
    )
    action_item = completion.choices[0].message.content.strip()
    return action_item

def compile_excel(actionables: List[dict]) -> str:
    """
    Compiles the processed obligations and action items into an Excel (.xlsx) file.
    Returns the local file path of the generated Excel file.
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "Obligations & Action Items"
    # Header row
    headers = ["Circular Number", "Circular Title", "Para", "Obligation", "Action Item"]
    ws.append(headers)
    for action in actionables:
            row = [
                action.get("circular_number"),
                action.get("circular_title"),
                action.get("para"),
                action.get("obligation"),
                action.get("action_item")
            ]
            ws.append(row)
    file_path = f"/tmp/rbi_obligations_{uuid.uuid4()}.xlsx"
    wb.save(file_path)
    logger.info(f"Excel file saved to {file_path}")
    return file_path

def upload_excel_to_s3(file_path: str) -> str:
    """
    Uploads the generated Excel file to S3 and returns its S3 URL.
    """
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    file_name = os.path.basename(file_path)
    s3_key = f"actionables/{file_name}"
    s3_client.upload_file(file_path, s3_bucket_name, s3_key)
    s3_url = f"https://{s3_bucket_name}.s3.amazonaws.com/{s3_key}"
    logger.info(f"Uploaded Excel file to {s3_url}")
    return s3_url

def add_position_info_to_chunks(chunks, local_path, pages_sorted):
    """
    Identify and annotate each chunk's text with its pixel positions in the source PDF.
    Each chunk in `chunks` will get a "positions" key added, which is a list of 
    {"page": page_number, "bbox": [x0, y0, x1, y1]} entries.
    """
    import re
    import fitz  # PyMuPDF for PDF text search and coordinate extraction
    import strip_markdown  # to convert markdown to plain text

    # Open the PDF document
    doc = fitz.open(local_path)

    # Keep track of the last seen page and y-coordinate of the previous chunk’s end
    prev_end_page = 0
    prev_end_y = 0.0

    for idx, chunk in enumerate(chunks):
        # 0. Convert chunk markdown to plain text
        raw_markdown = chunk.get("content", "")
        content = strip_markdown.strip_markdown(raw_markdown)
        if not content.strip():
            chunk["positions"] = []
            continue  # skip if no content

        # 1. Determine pointer lines: first line, last line, and up to 3 evenly spaced lines in between
        lines = [ln for ln in content.splitlines() if ln.strip()]
        pointer_lines = []
        n_lines = len(lines)
        if n_lines >= 1:
            pointer_lines.append(lines[0].strip())
        if n_lines > 1:
            pointer_lines.append(lines[-1].strip())
        if n_lines > 2:
            # select up to 3 interior points at 25%, 50%, 75%
            quarter = max(1, n_lines // 4)
            half = max(1, n_lines // 2)
            three_quarter = max(1, (3 * n_lines) // 4)
            for i in {quarter, half, three_quarter}:
                if 0 < i < n_lines - 1:
                    pointer_lines.append(lines[i].strip())
        # dedupe while preserving order
        seen = set()
        pointer_lines = [ln for ln in pointer_lines if ln not in seen and not seen.add(ln)]
        split_pointer_lines = []
        for ln in pointer_lines:
            if len(ln) > 100:
                split_pointer_lines.append(ln.split(".")[0])
            else:
                 split_pointer_lines.append(ln)
        pointer_lines = split_pointer_lines
        print("Got pointer lines : ", pointer_lines)
        # 2. Search each pointer line in the PDF to find its position
        pointer_positions = []
        start_page = prev_end_page
        for pline in pointer_lines:
            search_text = re.sub(r"\s+", " ", pline.strip())
            if not search_text:
                continue
            found = False
            # exact match search
            for pnum in range(start_page, len(doc)):
                page = doc[pnum]
                rects = page.search_for(search_text)
                if rects:
                    # filter out matches before previous chunk end on same page
                    if pnum == prev_end_page and prev_end_y:
                        rects = [r for r in rects if r.y0 >= prev_end_y - 1]
                        if not rects:
                            continue
                    match = rects[0]
                    pointer_positions.append({
                        "page": pnum,
                        "bbox": [int(match.x0), int(match.y0), int(match.x1), int(match.y1)]
                    })
                    found = True
                    break
            if found:
                continue
            # fuzzy fallback: normalize and remove punctuation
            norm = re.sub(r"[^\w\s]", "", search_text.lower())
            for pnum in range(start_page, len(doc)):
                page = doc[pnum]
                text_norm = re.sub(r"[^\w\s]", "", page.get_text().lower())
                if norm in text_norm:
                    rects = page.search_for(search_text)
                    # if still empty, try splitting pointer
                    if not rects:
                        parts = search_text.split()
                        if len(parts) >= 2:
                            m = len(parts) // 2
                            r1 = page.search_for(" ".join(parts[:m]))
                            r2 = page.search_for(" ".join(parts[m:]))
                            if r1 and r2:
                                a, b = r1[0], r2[-1]
                                rects = [fitz.Rect(min(a.x0, b.x0), min(a.y0, b.y0), max(a.x1, b.x1), max(a.y1, b.y1))]
                    if rects:
                        if pnum == prev_end_page and prev_end_y:
                            rects = [r for r in rects if r.y0 >= prev_end_y - 1]
                            if not rects:
                                continue
                        mrect = rects[0]
                        pointer_positions.append({
                            "page": pnum,
                            "bbox": [int(mrect.x0), int(mrect.y0), int(mrect.x1), int(mrect.y1)]
                        })
                        break
        print("Got pointer positions : ", pointer_positions)
        # 3. Combine positions by page into unified bboxes
        combined = []
        if pointer_positions:
            pointer_positions.sort(key=lambda x: (x["page"], x["bbox"][1]))
            cur_page = pointer_positions[0]["page"]
            x0, y0, x1, y1 = pointer_positions[0]["bbox"]
            for pos in pointer_positions[1:]:
                pg, (bx0, by0, bx1, by1) = pos["page"], pos["bbox"]
                if pg == cur_page:
                    x0, y0, x1, y1 = min(x0, bx0), min(y0, by0), max(x1, bx1), max(y1, by1)
                else:
                    combined.append({"page": cur_page, "bbox": [x0, y0, x1, y1]})
                    cur_page, x0, y0, x1, y1 = pg, bx0, by0, bx1, by1
            combined.append({"page": cur_page, "bbox": [x0, y0, x1, y1]})
        chunk["positions"] = combined

        # update previous chunk end info
        if combined:
            last = combined[-1]
            prev_end_page = last["page"]
            prev_end_y = last["bbox"][3]
        else:
            prev_end_y = 0.0

    doc.close()
    return chunks
# ------------------------------------------------------------------------------
# Airflow DAG Definition

default_args = {
    "owner": "airflow",
    "start_date": datetime(2025, 1, 1),
    "retries": 0,
    # "retry_delay": timedelta(minutes=5)
}

with DAG(
    "rbi_regulations_processing",
    default_args=default_args,
    schedule_interval="@daily",
    catchup=False,
    description="Process RBI regulations from S3, extract metadata and chunks, store metadata in DocumentDB and chunks into Qdrant",
) as dag:

    @task
    def fetch_files_from_s3() -> list:
        """List all PDF files in the designated S3 bucket."""
        S3_BUCKET_NAME = "rbi-docs-storage"
        aws_access_key_id = Variable.get("aws_access_key_id")
        aws_secret_access_key = Variable.get("aws_secret_access_key")
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )

        paginator = s3_client.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=S3_BUCKET_NAME)
        files = []
        for page in pages:
            for obj in page.get("Contents", []):
                key = obj["Key"]
                if key.startswith("Directions/") and key.lower().endswith(".pdf"):
                    files.append(key)
        logger.info(f"Found {len(files)} PDF files in S3.")
        return files[:3]

    @task
    def process_file(file_key: str) -> dict:
        """
        Download a PDF file from S3, process it:
         - Extract OCR.
         - Combine OCR markdown.
         - Chunk the markdown.
         - Process chunks to get text positions.
         - Extract document metadata.
         - Store metadata in DocumentDB.
         - Ingest chunks into Qdrant.
        """
        S3_BUCKET_NAME = "rbi-docs-storage"
        aws_access_key_id = Variable.get("aws_access_key_id")
        aws_secret_access_key = Variable.get("aws_secret_access_key")
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        local_path = f"/tmp/{os.path.basename(file_key)}"
        s3_client.download_file(S3_BUCKET_NAME, file_key, local_path)
        logger.info(f"Downloaded {file_key} to {local_path}.")

        # STEP 0: Extract OCR from the PDF (assumes extract_ocr_from_pdf is implemented)
        ocr_response = extract_ocr_from_pdf(local_path, client=client_mistral)  # Pass client if needed
        pages = ocr_response.get("pages", [])
        if not pages:
            logger.error("No OCR pages extracted, skipping file.")
            return {}

        # STEP 1: Combine markdown from OCR pages
        combined_markdown, pages_sorted = combine_pages(pages)
        
        logger.info("Combined markdown from OCR pages.")

        # STEP 2: Split combined markdown into chunks (using chunk_full_markdown)
        (document_summary, topics, chunks )= get_chunks_and_summary(combined_markdown, max_chars=3000)
        logger.info(f"Created {len(chunks)} chunks from markdown.")

        # STEP 3-6: Process chunks to get text positions
        chunks_with_position_info = add_position_info_to_chunks(chunks, local_path, pages_sorted)
        logger.info("Processed chunks to obtain text positions.")

        # Extract metadata from the first page's markdown
        document_metadata = extract_rbi_metadata(pages[0]['markdown'])
        logger.info("Extracted metadata from document.")

        # obligations = extract_obligations_from_markdown(combined_markdown)
        # processed_actionables = []
        # for obligation_item in obligations:
        #     processed_actionables.append({
        #         "circular_number": obligation_item.circular_number,
        #         "circular_title": obligation_item.circular_title,
        #         "para": obligation_item.para,
        #         "obligation": obligation_item.obligation,
        #         "action_item": obligation_item.action_item
        # })

        # file_path = compile_excel(processed_actionables)
        # s3_url = upload_excel_to_s3(file_path)

        # Store metadata in DocumentDB
        document_metadata_dict = document_metadata.dict()
        document_metadata_dict['s3_url'] = f"https://rbi-docs-storage.s3.us-east-2.amazonaws.com/{file_key}"
        import copy
        document_metadata_point = copy.deepcopy(document_metadata_dict)
        document_metadata_dict['markdown'] = combined_markdown
        # document_metadata_dict['actionables'] = processed_actionables
        # document_metadata_dict['actionables_excel_url'] = s3_url
        document_metadata_dict['summary'] = document_summary
        document_metadata_dict['topics'] = topics
        store_metadata_in_documentdb(document_metadata_dict)
        logger.info("Stored metadata in DocumentDB.")

        # Ingest chunks into Qdrant
        if (document_metadata.is_applicable_to_banks and (not document_metadata.is_withdrawn)):
            document_id = document_metadata.document_number or document_metadata.document_title or file_key
            ingest_chunks_to_qdrant(chunks_with_position_info, document_id, document_summary, topics, document_metadata_point)
            logger.info("Ingested chunks into Qdrant.")



        return {"metadata": document_metadata.dict(), "chunks_processed": len(chunks_with_position_info)}

    # Use dynamic task mapping to process each file.
    files = fetch_files_from_s3()
    process_results = process_file.expand(file_key=files)

# End of DAG
