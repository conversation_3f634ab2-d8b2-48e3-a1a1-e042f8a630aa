#!/usr/bin/env python3
"""
Test script to verify that metadata is properly nested in Qdrant points
"""

import sys
import os
import tempfile
import uuid
from pathlib import Path

# Add the parent directory to the path to import utils
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "utils"))

from utils.qdrant_utils import qdrant_manager, ingest_chunks_to_qdrant, ingest_single_document
from utils.document_pipeline import document_processor

def test_qdrant_manager_metadata_structure():
    """Test that qdrant_manager.upsert_point creates properly nested metadata"""
    print("\n🧪 Testing QdrantManager metadata structure...")
    
    try:
        test_collection = "test_metadata_structure"
        test_point_id = str(uuid.uuid4())
        
        # Test metadata that should be nested
        test_metadata = {
            "document_title": "Test Document",
            "document_type": "test",
            "published_date": "2024-01-01",
            "category": "test_category"
        }
        
        # Create payload with proper nesting
        test_payload = {
            "content": "This is test content",
            "metadata": test_metadata
        }
        
        # Upsert the point
        success = qdrant_manager.upsert_point(
            collection_name=test_collection,
            point_id=test_point_id,
            payload=test_payload,
            text_for_embedding="This is test content for embedding"
        )
        
        if success:
            print("✅ Point upserted successfully")
            
            # Retrieve the point to verify structure
            try:
                points = qdrant_manager.client.retrieve(
                    collection_name=test_collection,
                    ids=[test_point_id]
                )
                
                if points and len(points) > 0:
                    point = points[0]
                    payload = point.payload
                    
                    print(f"📋 Retrieved payload structure: {list(payload.keys())}")
                    
                    # Check if metadata is properly nested
                    if "metadata" in payload:
                        nested_metadata = payload["metadata"]
                        print(f"✅ Metadata is properly nested with keys: {list(nested_metadata.keys())}")
                        
                        # Verify specific fields are in nested metadata
                        expected_fields = ["document_title", "document_type", "published_date", "category"]
                        missing_fields = [field for field in expected_fields if field not in nested_metadata]
                        
                        if not missing_fields:
                            print("✅ All expected metadata fields are properly nested")
                            return True
                        else:
                            print(f"❌ Missing fields in nested metadata: {missing_fields}")
                            return False
                    else:
                        print("❌ Metadata is not properly nested - 'metadata' key not found")
                        print(f"   Available keys: {list(payload.keys())}")
                        return False
                else:
                    print("❌ Could not retrieve the test point")
                    return False
                    
            except Exception as e:
                print(f"❌ Error retrieving point: {e}")
                return False
        else:
            print("❌ Failed to upsert test point")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_ingest_chunks_metadata_structure():
    """Test that ingest_chunks_to_qdrant creates properly nested metadata"""
    print("\n🧪 Testing ingest_chunks_to_qdrant metadata structure...")
    
    try:
        test_collection = "test_chunks_metadata"
        test_document_id = str(uuid.uuid4())
        
        # Test document metadata
        document_metadata = {
            "document_title": "Test Chunks Document",
            "document_type": "test",
            "published_date": "2024-01-01",
            "source": "test"
        }
        
        # Test chunks
        test_chunks = [
            {
                "content": "<p>This is chunk 1 content</p>",
                "positions": [{"page": 1, "bbox": [0, 0, 100, 100]}]
            },
            {
                "content": "<p>This is chunk 2 content</p>",
                "positions": [{"page": 1, "bbox": [0, 100, 100, 200]}]
            }
        ]
        
        # Ingest chunks
        success = ingest_chunks_to_qdrant(
            collection=test_collection,
            chunks_payload=test_chunks,
            document_id=test_document_id,
            document_summary="Test document summary",
            topics=["test", "metadata"],
            document_metadata=document_metadata
        )
        
        if success:
            print("✅ Chunks ingested successfully")
            
            # Retrieve points to verify structure
            try:
                # Get all points in the collection
                points, _ = qdrant_manager.client.scroll(
                    collection_name=test_collection,
                    limit=10
                )
                
                if points and len(points) > 0:
                    # Check the first point
                    point = points[0]
                    payload = point.payload
                    
                    print(f"📋 Chunk payload structure: {list(payload.keys())}")
                    
                    # Check if metadata is properly nested
                    if "metadata" in payload:
                        nested_metadata = payload["metadata"]
                        print(f"✅ Chunk metadata is properly nested with keys: {list(nested_metadata.keys())}")
                        
                        # Verify specific fields are in nested metadata
                        expected_fields = ["document_title", "document_type", "published_date", "source"]
                        missing_fields = [field for field in expected_fields if field not in nested_metadata]
                        
                        if not missing_fields:
                            print("✅ All expected metadata fields are properly nested in chunks")
                            return True
                        else:
                            print(f"❌ Missing fields in nested chunk metadata: {missing_fields}")
                            return False
                    else:
                        print("❌ Chunk metadata is not properly nested - 'metadata' key not found")
                        print(f"   Available keys: {list(payload.keys())}")
                        return False
                else:
                    print("❌ Could not retrieve any chunk points")
                    return False
                    
            except Exception as e:
                print(f"❌ Error retrieving chunk points: {e}")
                return False
        else:
            print("❌ Failed to ingest test chunks")
            return False
            
    except Exception as e:
        print(f"❌ Chunks test failed with error: {e}")
        return False

def test_single_document_metadata_structure():
    """Test that ingest_single_document creates properly nested metadata"""
    print("\n🧪 Testing ingest_single_document metadata structure...")
    
    try:
        test_collection = "test_single_doc_metadata"
        test_document_id = str(uuid.uuid4())
        
        # Test document metadata
        document_metadata = {
            "document_title": "Test Single Document",
            "document_type": "test",
            "published_date": "2024-01-01",
            "source": "test"
        }
        
        # Test content
        test_content = "<p>This is test document content for single document ingestion</p>"
        
        # Ingest single document
        success = ingest_single_document(
            collection=test_collection,
            document_id=test_document_id,
            content=test_content,
            metadata=document_metadata
        )
        
        if success:
            print("✅ Single document ingested successfully")
            
            # Retrieve the point to verify structure
            try:
                points = qdrant_manager.client.retrieve(
                    collection_name=test_collection,
                    ids=[test_document_id]
                )
                
                if points and len(points) > 0:
                    point = points[0]
                    payload = point.payload
                    
                    print(f"📋 Single document payload structure: {list(payload.keys())}")
                    
                    # Check if metadata is properly nested
                    if "metadata" in payload:
                        nested_metadata = payload["metadata"]
                        print(f"✅ Single document metadata is properly nested with keys: {list(nested_metadata.keys())}")
                        
                        # Verify specific fields are in nested metadata
                        expected_fields = ["document_title", "document_type", "published_date", "source"]
                        missing_fields = [field for field in expected_fields if field not in nested_metadata]
                        
                        if not missing_fields:
                            print("✅ All expected metadata fields are properly nested in single document")
                            return True
                        else:
                            print(f"❌ Missing fields in nested single document metadata: {missing_fields}")
                            return False
                    else:
                        print("❌ Single document metadata is not properly nested - 'metadata' key not found")
                        print(f"   Available keys: {list(payload.keys())}")
                        return False
                else:
                    print("❌ Could not retrieve the single document point")
                    return False
                    
            except Exception as e:
                print(f"❌ Error retrieving single document point: {e}")
                return False
        else:
            print("❌ Failed to ingest test single document")
            return False
            
    except Exception as e:
        print(f"❌ Single document test failed with error: {e}")
        return False

def main():
    """Run all metadata structure tests"""
    print("🚀 Starting Metadata Structure Tests")
    print("=" * 50)
    
    tests = [
        ("QdrantManager Metadata Structure", test_qdrant_manager_metadata_structure),
        ("Ingest Chunks Metadata Structure", test_ingest_chunks_metadata_structure),
        ("Single Document Metadata Structure", test_single_document_metadata_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All metadata structure tests passed!")
        return True
    else:
        print("⚠️ Some metadata structure tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
