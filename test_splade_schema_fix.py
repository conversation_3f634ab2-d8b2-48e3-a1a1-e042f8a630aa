#!/usr/bin/env python3
"""
Test adding SPLADE vector schema to existing collection
"""

from qdrant_client import QdrantClient, models

def test_add_splade_schema():
    """Test adding SPLADE vector schema to collection"""
    
    client = QdrantClient(url="http://localhost:6333")
    collection_name = "rbi_master_direction"
    
    try:
        # Check current sparse vectors
        info = client.get_collection(collection_name)
        sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}
        
        print(f"Current sparse vectors: {list(sparse_vectors.keys())}")
        
        # Check if SPLADE vector exists
        splade_vector_name = "fast-sparse-bm25-splade"
        has_splade = splade_vector_name in sparse_vectors
        
        print(f"Has SPLADE vector ({splade_vector_name}): {has_splade}")
        
        if not has_splade:
            print(f"\nAdding SPLADE vector schema...")
            
            # Add SPLADE vector configuration
            missing_vectors = {
                splade_vector_name: models.SparseVectorParams()
            }
            
            client.update_collection(
                collection_name=collection_name,
                sparse_vectors_config=missing_vectors
            )
            
            print(f"✅ Successfully added SPLADE vector schema")
            
            # Verify it was added
            info = client.get_collection(collection_name)
            sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}
            print(f"Updated sparse vectors: {list(sparse_vectors.keys())}")
            
        else:
            print("✅ SPLADE vector schema already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_add_splade_schema()
