"""
S3-Qdrant Sync Checker

This script compares files in S3 buckets with documents stored in Qdrant collections
to identify files that exist in S3 but are missing from Qdrant.

Usage:
    1. Set environment variables:
       export AWS_ACCESS_KEY_ID="your_access_key"
       export AWS_SECRET_ACCESS_KEY="your_secret_key"
       export QDRANT_HOST="your_qdrant_host"  # Optional, defaults to *************
       export QDRANT_PORT="6333"              # Optional, defaults to 6333

    2. Run the script:
       python s3_qdrant_sync.py

The script will:
- Connect to S3 and Qdrant
- Compare files in each configured folder/collection pair
- Generate a detailed log file (missing_files.log)
- Create an Excel report if missing files are found
"""


import boto3
import logging
from typing import Dict, Set, List
import os
from tqdm import tqdm
import pandas as pd
from datetime import datetime
from qdrant_client import QdrantClient

# Configuration
S3_BUCKET_NAME = "rbi-docs-full-2025-08-05"
# Use environment variables for AWS credentials for security
aws_access_key_id = "********************"
aws_secret_access_key = "2FfZsDlPIYjAz8EEMlxOrX54hYMN90qB1Kk0pDJ+"

# Qdrant Configuration
QDRANT_HOST = os.getenv("QDRANT_HOST", "*************")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

# Define folder to collection mapping
FOLDER_COLLECTION_MAPPING = {
    "master_directions": {
        "s3_folder": "Master Directions/",
        "qdrant_collection": "rbi_master_direction"
    },
    "master_circulars": {
        "s3_folder": "Master Circulars/",
        "qdrant_collection": "rbi_master_circular"
    },
    "notifications": {
        "s3_folder": "Notifications/",
        "qdrant_collection": "rbi_notification"
    }
}

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[logging.FileHandler("missing_files.log"), logging.StreamHandler()]
)

def get_s3_files(bucket: str, folder: str) -> Set[str]:
    """
    Get all files from a specific S3 folder
    Returns set of file URLs
    """
    try:
        s3 = boto3.client('s3',
                          aws_access_key_id=aws_access_key_id,
                          aws_secret_access_key=aws_secret_access_key)

        files = set()
        paginator = s3.get_paginator('list_objects_v2')

        # Show progress bar while fetching files
        with tqdm(desc=f"Fetching S3 {folder}") as pbar:
            for page in paginator.paginate(Bucket=bucket, Prefix=folder):
                if 'Contents' in page:
                    for obj in page['Contents']:
                        # Only include actual files, not folders
                        if not obj['Key'].endswith('/'):
                            url = f"https://{bucket}.s3.amazonaws.com/{obj['Key']}"
                            files.add(url)
                            pbar.update(1)

        logging.info(f"Found {len(files)} files in S3 folder '{folder}'")
        return files

    except Exception as e:
        logging.error(f"Failed to fetch S3 files from '{folder}': {e}")
        return set()

def get_qdrant_files(collection_name: str) -> Set[str]:
    """
    Get files that exist in Qdrant for a specific collection
    Returns set of file URLs from s3_url metadata
    """
    try:
        # Initialize Qdrant client
        client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

        files = set()
        scroll_offset = None
        batch_size = 100

        # Scroll through all points in the collection
        while True:
            try:
                points_batch, scroll_offset = client.scroll(
                    collection_name=collection_name,
                    limit=batch_size,
                    with_payload=True,
                    offset=scroll_offset
                )

                if not points_batch:
                    break

                # Extract s3_url from metadata
                for point in points_batch:
                    metadata = point.payload.get("metadata", {})
                    s3_url = metadata.get("s3_url")

                    if s3_url:
                        files.add(s3_url)

                if scroll_offset is None:
                    break

            except Exception as e:
                logging.warning(f"Error scrolling collection {collection_name}: {e}")
                break

        logging.info(f"Found {len(files)} files in Qdrant collection '{collection_name}'")
        return files

    except Exception as e:
        logging.error(f"Failed to connect to Qdrant or query collection '{collection_name}': {e}")
        return set()

def create_excel_report(missing_files: Dict[str, List[str]]) -> None:
    """
    Create an Excel report with missing files information
    """
    # Prepare data for Excel
    report_data = []
    for folder_name, files in missing_files.items():
        for file in files:
            report_data.append({
                'Folder': folder_name,
                'Missing File URL': file
            })
    
    # Create DataFrame
    df = pd.DataFrame(report_data)
    
    # Generate timestamp for filename
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'missing_files_report_{timestamp}.xlsx'
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Missing Files', index=False)
        
        # Get workbook and worksheet objects
        workbook = writer.book
        worksheet = writer.sheets['Missing Files']
        
        # Add formatting
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',
            'font_color': 'white',
            'border': 1
        })
        
        # Format headers
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # Adjust column widths
        worksheet.set_column('A:A', 20)  # Folder column
        worksheet.set_column('B:B', 60)  # URL column
        
    logging.info(f"Excel report generated: {filename}")

def find_missing_files() -> Dict[str, List[str]]:
    """
    Compare Qdrant and S3 files to find files missing in Qdrant
    Returns dict with folder names and lists of missing files
    """
    missing_files = {}

    for folder_name, config in FOLDER_COLLECTION_MAPPING.items():
        s3_folder = config["s3_folder"]
        qdrant_collection = config["qdrant_collection"]

        logging.info(f"\nProcessing {folder_name}...")

        # Get files from both sources
        s3_files = get_s3_files(S3_BUCKET_NAME, s3_folder)
        qdrant_files = get_qdrant_files(qdrant_collection)

        # Find files that are in S3 but not in Qdrant (FIXED LOGIC)
        missing = s3_files - qdrant_files

        logging.info(f"Total files in S3: {len(s3_files)}")
        logging.info(f"Total files in Qdrant: {len(qdrant_files)}")
        logging.info(f"Files missing in Qdrant: {len(missing)}")

        if missing:
            missing_files[folder_name] = list(missing)

            # Log each missing file
            for file in missing:
                logging.info(f"Missing in Qdrant: {file}")
        else:
            logging.info(f"✅ All S3 files are present in Qdrant for {folder_name}")

    return missing_files

def validate_environment():
    """Validate that required environment variables are set"""
    missing_vars = []

    # if not AWS_ACCESS_KEY:
    #     missing_vars.append("AWS_ACCESS_KEY_ID")
    # if not AWS_SECRET_KEY:
    #     missing_vars.append("AWS_SECRET_ACCESS_KEY")

    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

if __name__ == "__main__":
    try:
        # Validate environment
        validate_environment()

        logging.info("Starting missing files check...")
        logging.info(f"S3 Bucket: {S3_BUCKET_NAME}")
        logging.info(f"Qdrant Host: {QDRANT_HOST}:{QDRANT_PORT}")

        missing_files = find_missing_files()

        # Create Excel report only if there are missing files
        if any(missing_files.values()):
            create_excel_report(missing_files)
            total_missing = sum(len(files) for files in missing_files.values())
            logging.info(f"Found {total_missing} missing files across all collections")
        else:
            logging.info("✅ No missing files found! All S3 files are present in Qdrant.")

        logging.info("Check complete. See missing_files.log for details.")

    except Exception as e:
        logging.error(f"Script failed: {e}")
        raise
