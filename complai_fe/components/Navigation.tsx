//Navigation.tsx
"use client";

import { useEffect, useState } from "react";
import { MainMenu } from "./MainMenu";
import { ChatHistory } from "./Chathistory";
import type { NavigationProps } from "@/types/main_types";



export function Navigation({
  isCollapsed,
  onToggleCollapse,
  setActiveView,
  setSelectedActionItem,
  setShowDetailView,
}: NavigationProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Prevent mismatched SSR vs. client rendering
  if (!hasMounted) return null;

  return (
    <div className="flex h-full">
      <MainMenu />
      <ChatHistory
        isCollapsed={isCollapsed}
        onToggleCollapse={onToggleCollapse}
        setActiveView={setActiveView}
        setSelectedActionItem={setSelectedActionItem}
        setShowDetailView={setShowDetailView}
      />
    </div>
  );
}
