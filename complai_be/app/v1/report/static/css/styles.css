:root {
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --success: #10b981;
    --error: #ef4444;
    --background: #f3f4f6;
    --card: #ffffff;
    --text: #1f2937;
    --text-light: #6b7280;
    --border: #e5e7eb;
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
    background: var(--background);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--text);
}

.container {
    background: var(--card);
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 700px;
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary), var(--primary-light));
}

h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
    color: var(--text);
    position: relative;
}

h1::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: var(--primary);
    margin: 0.5rem auto 0;
    border-radius: 2px;
}

.filter-section {
    background: var(--background);
    padding: 2rem;
    border-radius: 0.75rem;
    margin-bottom: 2rem;
}

.filter-type {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: var(--transition);
}

.radio-label:hover {
    background: var(--card);
}

.radio-label input[type="radio"] {
    margin-right: 0.5rem;
    cursor: pointer;
}

.date-inputs {
    display: none;
    animation: fadeIn 0.3s ease;
}

.date-inputs.active {
    display: block;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text);
}

input[type="date"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition);
}

input[type="date"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

button {
    background: var(--primary);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

#errorMessage {
    background: #fef2f2;
    color: var(--error);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    display: none;
    animation: fadeIn 0.3s ease;
}

#successMessage {
    background: #ecfdf5;
    color: var(--success);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 640px) {
    body {
        padding: 1rem;
    }

    .container {
        padding: 1.5rem;
    }

    .filter-section {
        padding: 1.5rem;
    }

    h1 {
        font-size: 1.5rem;
    }
}
