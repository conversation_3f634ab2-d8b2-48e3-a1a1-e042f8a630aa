import yaml
from app.v2.query.utils.completion_client import completion_client

class RecursiveSummarizer:
    def __init__(self, model: str, prompt_file: str, max_length: int = 1500):
        self.model = model
        self.prompt = self._load_prompt(prompt_file)
        self.max_length = max_length

    def _load_prompt(self, prompt_file: str) -> str:
        with open(prompt_file, "r") as f:
            data = yaml.safe_load(f)
        return data.get("prompt", "")
    
    def run(self, query: str, contexts: list) -> str:
        combined = "\n\n".join([
            ctx if isinstance(ctx, str) else ctx["text"] 
            for ctx in contexts
        ])
        if len(combined) <= self.max_length:
            return combined
        final_prompt = self.prompt.format(query=query, context=combined)
        messages = [{"role": "system", "content": final_prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)
        return response.strip()
