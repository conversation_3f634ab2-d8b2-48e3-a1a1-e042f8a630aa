FROM python:3.10-slim

# Update apt and install system packages
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    python3-dev \
    libpq-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Copy the requirements file first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the code
COPY . .

# Set environment variable for MongoDB URI
ENV MONGO_URI=default

# Make sure this matches your startup file (if your file is named start.p, update this command)
CMD ["python", "start.py"]
