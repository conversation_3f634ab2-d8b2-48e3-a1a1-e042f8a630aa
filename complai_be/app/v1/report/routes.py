import os
import hmac
import jwt
from datetime import datetime, timedelta
from fastapi import APIRouter, Request, Depends, HTTPException, Response, Form
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from pathlib import Path
from app.db.database import get_db
import logging

router = APIRouter()

# Set up the templates directory relative to this file's location
templates = Jinja2Templates(directory=str(Path(__file__).parent / "templates"))

# Configuration: Retrieve secrets from environment variables if available
PASSWORD = os.environ.get("REPORT_ACCESS_PASSWORD", "selkea$$")
SECRET_KEY = os.environ.get("SECRET_KEY", "your_secret_key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

def verify_password(provided_password: str):
    # Use constant-time comparison to prevent timing attacks
    if not hmac.compare_digest(provided_password, PASSWORD):
        raise HTTPException(status_code=401, detail="Unauthorized: Incorrect password")

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def truncate_text(text, max_length=32767):
    return text[:max_length] if isinstance(text, str) else text

@router.get("/", response_class=HTMLResponse)
async def report_index(request: Request):
    """
    Render the index page that includes a login prompt.
    """
    return templates.TemplateResponse("index.html", {"request": request})

@router.post("/login")
async def login(response: Response, password: str = Form(...)):
    """
    Login endpoint that verifies the password.
    On success, it sets an HTTP-only cookie with a JWT token and returns a JSON success message.
    """
    verify_password(password)
    token = create_access_token({"sub": "report_access"})
    # Set the token as an HTTP-only cookie
    response.set_cookie(key="access_token", value=token, httponly=True, max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60)
    return JSONResponse({"message": "Login successful"})

@router.get("/get-data")
async def get_data(request: Request, db=Depends(get_db)):
    """
    Fetch queries and messages based on timestamps.
    Instead of filtering entire sessions, this retrieves only the relevant messages.
    """
    token = request.cookies.get("access_token")
    # if not token:
    #     raise HTTPException(status_code=401, detail="Authentication token required")
    # verify_token(token)

    filter_type = request.query_params.get("filterType")
    start_date_str = request.query_params.get("startDate")
    end_date_str = request.query_params.get("endDate")

    if not filter_type:
        return JSONResponse(status_code=400, content={"error": "Filter type is required"})

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None

        # Create message-level filter
        message_filter = {}
        if filter_type == 'single' and start_date:
            next_day = start_date + timedelta(days=1)
            message_filter = {"messages.timestamp": {"$gte": start_date, "$lt": next_day}}
        elif filter_type == 'range' and start_date and end_date:
            next_day = end_date + timedelta(days=1)
            message_filter = {"messages.timestamp": {"$gte": start_date, "$lt": next_day}}
        elif filter_type == 'greater' and start_date:
            message_filter = {"messages.timestamp": {"$gte": start_date}}
        elif filter_type == 'less' and end_date:
            message_filter = {"messages.timestamp": {"$lt": end_date}}
        else:
            return JSONResponse(status_code=400, content={"error": "Invalid date parameters"})

        logging.info(f"Using message filter: {message_filter}")

        # Fetch user details
        users_cursor = db["user"].find({}, {"username": 1, "email": 1})
        users = {user["username"]: user.get("email", "Unknown Email") for user in users_cursor}
        logging.info(f"Found {len(users)} users")

        # Fetch feedback data
        feedback_cursor = db["feedback"].find({})
        feedbacks = {}
        for f in feedback_cursor:
            msg_content = f.get("message_content")
            if msg_content:
                feedbacks[msg_content] = {
                    "upvote": "Yes" if f.get("vote") == "up" else "",
                    "downvote": "Yes" if f.get("vote") == "down" else "",
                    "feedback_text": f.get("feedback_text", ""),
                    "copied": f.get("copied", False),
                    "copy_count": f.get("copy_count", 0)
                }
        logging.info(f"Found {len(feedbacks)} feedback entries")

        # Retrieve relevant messages from sessions
        data = []
        session_cursor = db["session"].find(message_filter, {"username": 1, "messages": 1, "session_id": 1})

        for session in session_cursor:
            username = session.get("username", "Unknown User")
            email = users.get(username, "Unknown Email")
            session_id = session.get("session_id", "N/A")

            for message in session.get("messages", []):
                timestamp = message.get("timestamp")
                
                # Apply filtering again to extract only required messages
                if not timestamp:
                    continue
                if filter_type == 'single' and start_date and not (start_date <= timestamp < next_day):
                    continue
                if filter_type == 'range' and start_date and end_date and not (start_date <= timestamp < next_day):
                    continue
                if filter_type == 'greater' and start_date and not (timestamp >= start_date):
                    continue
                if filter_type == 'less' and end_date and not (timestamp < end_date):
                    continue
                  
                question = truncate_text(message.get("query", "N/A"))
                response_text = truncate_text(message.get("content", "N/A"))
                formatted_timestamp = timestamp.strftime("%Y-%m-%d %H:%M:%S") if isinstance(timestamp, datetime) else "N/A"
                feedback_entry = feedbacks.get(response_text, {})
                upvote = feedback_entry.get("upvote", "")
                downvote = feedback_entry.get("downvote", "")
                feedback_text = truncate_text(feedback_entry.get("feedback_text", ""))
                
                metadata_list = message.get("metadata", [])
                reference_sources = [
                    " | ".join(filter(None, [meta.get("title", ""), meta.get("pdf_link", ""), meta.get("document_code", "")]))
                    for meta in metadata_list if isinstance(meta, dict)
                ]
                reference_sources_str = "\n".join(reference_sources) if reference_sources else "No Sources"
                reference_sources_str = truncate_text(reference_sources_str)

                data.append([
                    session_id,username, email, question, response_text, upvote, downvote,
                    feedback_text, reference_sources_str, formatted_timestamp,
                    'Yes' if feedback_entry.get('copied', False) else 'No',
                    feedback_entry.get('copy_count', 0)
                ])

        logging.info(f"Processed {len(data)} relevant messages")
        if not data:
            return JSONResponse(status_code=404, content={"error": "No data found for the selected criteria"})

        return JSONResponse(content=data)
    except Exception as e:
        logging.exception("Error in get_data:")
        return JSONResponse(status_code=500, content={"error": str(e)})
