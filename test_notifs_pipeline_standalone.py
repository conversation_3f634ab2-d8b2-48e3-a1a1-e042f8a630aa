#!/usr/bin/env python3
"""
Standalone notification pipeline tester that works without Airflow dependencies.
Uses the JSON results from the notification processing test.
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StandaloneNotificationProcessor:
    """Standalone processor for testing notification pipeline logic"""
    
    def __init__(self, results_file: str = None):
        """Initialize with results from the notification processing test"""
        if results_file is None:
            # Default to the results file we generated
            results_file = "complai_knowledge_tracker/airflow/dags/tests/notification_pipeline_results.json"
        
        self.results_file = Path(results_file)
        self.results_data = None
        self.load_results()
    
    def load_results(self):
        """Load the notification processing results"""
        try:
            if not self.results_file.exists():
                logger.error(f"Results file not found: {self.results_file}")
                return False
            
            with open(self.results_file, 'r', encoding='utf-8') as f:
                self.results_data = json.load(f)
            
            logger.info(f"📁 Loaded {self.results_data['metadata']['total_notifications']} notification results")
            return True
            
        except Exception as e:
            logger.error(f"Error loading results: {e}")
            return False
    
    def get_notifications_by_action(self, action_type: str) -> List[Dict]:
        """Get all notifications that require a specific action"""
        if not self.results_data:
            return []
        
        matching_notifications = []
        for notification in self.results_data['notifications']:
            if notification['predicted_analysis']['action_type'] == action_type:
                matching_notifications.append(notification)
        
        return matching_notifications
    
    def get_high_confidence_notifications(self) -> List[Dict]:
        """Get all high-confidence predictions"""
        if not self.results_data:
            return []
        
        high_conf_notifications = []
        for notification in self.results_data['notifications']:
            if notification['predicted_analysis']['confidence'] == 'HIGH':
                high_conf_notifications.append(notification)
        
        return high_conf_notifications
    
    def simulate_pipeline_execution(self, notification: Dict) -> Dict:
        """Simulate what the pipeline would do for a notification"""
        result = {
            'notification_id': notification['notification_id'],
            'title': notification['title'][:60] + '...',
            'predicted_action': notification['predicted_analysis']['action_type'],
            'confidence': notification['predicted_analysis']['confidence'],
            'simulation_steps': [],
            'success': True,
            'errors': []
        }
        
        # Simulate each downstream action
        for action in notification['downstream_actions']:
            step_result = self.simulate_action(action, notification)
            result['simulation_steps'].append(step_result)
            
            if not step_result['success']:
                result['success'] = False
                result['errors'].append(step_result['error'])
        
        return result
    
    def simulate_action(self, action: Dict, notification: Dict) -> Dict:
        """Simulate a specific action"""
        action_type = action['action']
        
        if action_type == 'search_existing_document':
            return self.simulate_search_document(action, notification)
        elif action_type == 'download_latest_version' or action_type == 'download_new_document':
            return self.simulate_download_document(action, notification)
        elif action_type == 'replace_in_knowledge_base' or action_type == 'add_to_knowledge_base':
            return self.simulate_kb_operation(action, notification)
        elif action_type == 'remove_from_knowledge_base':
            return self.simulate_kb_removal(action, notification)
        else:
            return {
                'action': action_type,
                'success': True,
                'message': f"Simulated {action_type}",
                'details': action.get('description', '')
            }
    
    def simulate_search_document(self, action: Dict, notification: Dict) -> Dict:
        """Simulate searching for existing documents"""
        keywords = action.get('keywords', [])
        
        # Mock search logic
        found_docs = len(keywords) * 2  # Mock: more keywords = more potential matches
        
        return {
            'action': 'search_existing_document',
            'success': True,
            'message': f"Found {found_docs} potential matching documents",
            'details': f"Searched with keywords: {', '.join(keywords[:3])}"
        }
    
    def simulate_download_document(self, action: Dict, notification: Dict) -> Dict:
        """Simulate downloading a document"""
        pdf_url = action.get('source_url', notification.get('pdf_link', ''))
        
        if not pdf_url:
            return {
                'action': 'download_document',
                'success': False,
                'error': 'No PDF URL available',
                'details': 'Cannot download without valid URL'
            }
        
        # Check if URL is accessible (basic check)
        try:
            # Just check if URL format is valid, don't actually download
            if pdf_url.startswith('https://rbidocs.rbi.org.in/'):
                return {
                    'action': 'download_document',
                    'success': True,
                    'message': 'Document download simulated successfully',
                    'details': f"Would download from: {pdf_url[:50]}..."
                }
            else:
                return {
                    'action': 'download_document',
                    'success': False,
                    'error': 'Invalid RBI URL format',
                    'details': f"URL: {pdf_url[:50]}..."
                }
        except Exception as e:
            return {
                'action': 'download_document',
                'success': False,
                'error': f'URL validation failed: {str(e)}',
                'details': pdf_url[:50] + '...'
            }
    
    def simulate_kb_operation(self, action: Dict, notification: Dict) -> Dict:
        """Simulate knowledge base operations"""
        action_type = action['action']
        
        # Mock KB operation
        return {
            'action': action_type,
            'success': True,
            'message': f"Knowledge base {action_type} simulated successfully",
            'details': action.get('description', '')
        }
    
    def simulate_kb_removal(self, action: Dict, notification: Dict) -> Dict:
        """Simulate knowledge base document removal"""
        return {
            'action': 'remove_from_knowledge_base',
            'success': True,
            'message': 'Document removal simulated successfully',
            'details': 'Would remove document from active knowledge base'
        }
    
    def test_pipeline_for_action_type(self, action_type: str, max_tests: int = 5):
        """Test pipeline simulation for a specific action type"""
        logger.info(f"\n🧪 Testing Pipeline for Action Type: {action_type}")
        logger.info("=" * 60)
        
        notifications = self.get_notifications_by_action(action_type)
        
        if not notifications:
            logger.warning(f"No notifications found for action type: {action_type}")
            return
        
        logger.info(f"Found {len(notifications)} notifications with action type: {action_type}")
        
        # Test up to max_tests notifications
        test_notifications = notifications[:max_tests]
        
        results = []
        for i, notification in enumerate(test_notifications, 1):
            logger.info(f"\n📋 Test {i}/{len(test_notifications)}: {notification['title'][:50]}...")
            
            result = self.simulate_pipeline_execution(notification)
            results.append(result)
            
            if result['success']:
                logger.info(f"   ✅ Pipeline simulation successful")
                for step in result['simulation_steps']:
                    if step['success']:
                        logger.info(f"      ✓ {step['action']}: {step['message']}")
                    else:
                        logger.warning(f"      ✗ {step['action']}: {step.get('error', 'Unknown error')}")
            else:
                logger.error(f"   ❌ Pipeline simulation failed")
                for error in result['errors']:
                    logger.error(f"      Error: {error}")
        
        # Summary
        successful = sum(1 for r in results if r['success'])
        logger.info(f"\n📊 Summary for {action_type}:")
        logger.info(f"   Successful simulations: {successful}/{len(results)}")
        logger.info(f"   Success rate: {(successful/len(results)*100):.1f}%")
        
        return results


def main():
    """Main test function"""
    logger.info("🚀 Starting Standalone Notification Pipeline Test")
    logger.info("=" * 80)
    
    # Initialize processor
    processor = StandaloneNotificationProcessor()
    
    if not processor.results_data:
        logger.error("Failed to load notification results. Exiting.")
        return
    
    # Test different action types
    action_types = ['UPDATE_DOCUMENT', 'ADD_DOCUMENT', 'REMOVE_DOCUMENT', 'NO_ACTION']
    
    all_results = {}
    
    for action_type in action_types:
        results = processor.test_pipeline_for_action_type(action_type, max_tests=3)
        all_results[action_type] = results
    
    # Test high-confidence predictions
    logger.info(f"\n🎯 Testing High-Confidence Predictions")
    logger.info("=" * 60)
    
    high_conf_notifications = processor.get_high_confidence_notifications()
    logger.info(f"Found {len(high_conf_notifications)} high-confidence predictions")
    
    high_conf_results = []
    for i, notification in enumerate(high_conf_notifications[:5], 1):
        logger.info(f"\n📋 High-Conf Test {i}: {notification['title'][:50]}...")
        result = processor.simulate_pipeline_execution(notification)
        high_conf_results.append(result)
        
        if result['success']:
            logger.info(f"   ✅ High-confidence prediction validated")
        else:
            logger.warning(f"   ⚠️ High-confidence prediction had issues")
    
    # Final summary
    logger.info(f"\n" + "=" * 80)
    logger.info(f"🎉 PIPELINE TESTING COMPLETE")
    logger.info(f"📊 Summary:")
    
    total_tests = sum(len(results) if results else 0 for results in all_results.values())
    total_successful = sum(
        sum(1 for r in results if r and r['success']) if results else 0 
        for results in all_results.values()
    )
    
    logger.info(f"   Total simulations run: {total_tests}")
    logger.info(f"   Successful simulations: {total_successful}")
    logger.info(f"   Overall success rate: {(total_successful/total_tests*100):.1f}%" if total_tests > 0 else "   No tests run")
    logger.info(f"   High-confidence tests: {len(high_conf_results)}")
    
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
