#!/usr/bin/env python3
"""
Test graceful handling of missing SPLADE schema
"""

import sys
import os
sys.path.append('complai_knowledge_tracker/airflow/dags')

# Mock Airflow Variable for testing
class MockVariable:
    @staticmethod
    def get(key, default_var=None):
        if key == "vector_db_host":
            return "http://localhost:6333"
        return default_var

# Patch the Variable import
import sys
sys.modules['airflow'] = type(sys)('airflow')
sys.modules['airflow.models'] = type(sys)('airflow.models')
sys.modules['airflow.models'].Variable = MockVariable

def test_graceful_splade_handling():
    """Test that the system gracefully handles missing SPLADE schema"""
    
    try:
        from utils.qdrant_utils import qdrant_manager
        
        collection_name = "rbi_master_direction"
        
        print("Testing graceful SPLADE handling...")
        
        # Test format detection
        format_type = qdrant_manager._check_collection_format(collection_name)
        print(f"Collection format: {format_type}")
        
        # Test sparse vector checking
        has_all_sparse = qdrant_manager._ensure_sparse_vectors_in_collection(collection_name)
        print(f"Has all sparse vectors: {has_all_sparse}")
        
        # Test upsert with SPLADE vector (should skip gracefully)
        test_payload = {
            'content': 'Test content for graceful SPLADE handling',
            'test': True
        }
        
        print(f"\nTesting upsert with SPLADE vector...")
        success = qdrant_manager.upsert_point(
            collection_name=collection_name,
            point_id='test_graceful_splade',
            payload=test_payload,
            text_for_embedding='Test content for graceful SPLADE handling'
        )
        
        print(f"Upsert success: {success}")
        
        if success:
            print("✅ System gracefully handled missing SPLADE schema")
        else:
            print("❌ System failed to handle missing SPLADE schema")
        
        return success
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_graceful_splade_handling()
