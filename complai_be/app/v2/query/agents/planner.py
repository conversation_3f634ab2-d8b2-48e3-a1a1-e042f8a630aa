import os
from app.v2.query.agents.response_models.planner_models import ExecutionP<PERSON>, ValidationResult

# Imports for LLM and messaging
from langchain_openai import AzureChatOpenAI
from langchain.schema.messages import SystemMessage, HumanMessage

# Standard library imports
import json
from typing import Dict, Any

# Decorators for observability
from langfuse.decorators import observe

# Import settings for Azure config
from config import settings


class PlannerAgent:
    def __init__(self, model_name: str = "gpt-4"):
        """Initialize the PlannerAgent with a specified LLM model."""
        # Use structured output model for better performance
        self.llm = AzureChatOpenAI(
            azure_deployment="gpt-4.1",
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            openai_api_version=settings.AZURE_OPENAI_API_VERSION,
            api_key=settings.AZURE_OPENAI_API_KEY,
            temperature=1.0,
        )

        self.available_agents = {
            "QueryRewriter": "Rewrites queries with context of the users chat history",
            "SemanticRetriever": "Retrieves documents from Qdrant, using semantic search and vector filters",
            "ContextExpanderWithFilter": "Combines metadata-based MongoDB filter generation and context expansion into a single agent, enabling efficient retrieval of documents based on date/ title/ type etc",
            "AnswerGenerator": "Generates draft answers with mandatory sections: referenced section and condensed knowledge",
            # "CriticAgent": "Checks if there are any overlaps and contradictions between retrieved text",
            "Summarizer": "Condenses Content",

            # "AmbiguityResolver": "Resolves regulatory ambiguity of the user query by fixing dates, common acronyms and jargon",
            # "ContextExpander": "Executes MongoDB queries and metadata searches to expand execution context",
            # "MongoFilterBuilder": "Generates MongoDB metadata filters with date ranges for temporal queries and document validation",
            # "QdrantFilterGenerator": "Creates Qdrant filters with date range support for temporal queries and keyword matching ",
            # "MultiQueryGenerator": "Generates multiple queries based on original query to increase search space",
            # "ProactiveSuggestor": "Recommends RBI's Regulatory Update Subscription Service links",
            # "CompletenessValidator": "Validates context completeness and detects conflicts",
            # TODO: we dont really have this yet.. "RecursiveSummarizer": "Condenses long documents through iterative summarization",
            # "AnswerValidator": "Final compliance check before formatting",  # Stronger than CriticAgent
            # "DefinitionRetriever": "Retrieves regulatory definitions, terms, and abbreviations"
            # "AnnotationGenerator": "Annotates answers with document references and compliance markers",  # Updated description

        }

        self.available_agents["ConversationalAgent"] = "Handles greetings, pleasantries, and non-compliance queries with polite, contextually appropriate responses while maintaining RBI official tone"

    @observe()
    def create_plan(self, task_description: str, context: Dict[str, Any]) -> ExecutionPlan:
        """Creates an execution plan based on the task description and context.

        Args:
            task_description (str): Description of the task to plan.
            context (Dict[str, Any]): Additional context for planning.

        Returns:
            ExecutionPlan: The generated execution plan.

        Raises:
            Exception: If the LLM fails to generate a valid plan.
        """
        agents_desc = "\n".join(
            [f"- {k}: {v}" for k, v in self.available_agents.items()])

        # Create enhanced prompt without explicit format instructions
        prompt = f"""Given the following available agents:
            {agents_desc}

            **Task**: {task_description}
            **Context**: {json.dumps(context, indent=2)}

            **Strict Planning Protocol**:
            1. **Non-Compliance Query Detection**:
               - For greetings, pleasantries, and non-compliance queries:
                 - Check if query is a simple greeting (e.g., "hello", "good morning")
                 - Check if query is small talk unrelated to banking compliance (e.g., "how are you")
                 - Check if query is about topics outside RBI compliance scope (e.g., "tell me about sports")
               - If ANY of these conditions are met, ONLY use ConversationalAgent
               - Example: "Hello, how are you today?"
                 - Correct: [ ConversationalAgent ]
                 - Incorrect: [ QueryRewriter, SemanticRetriever ]

             3. **Query Type Analysis & Agent Selection Rules**:
               - For temporal listing queries (e.g., "list all documents before Jan 2024", "show circulars after 2022"):
                 - Try to use ContextExpanderWithFilter to generate efficient MongoDB filters with date conditions
                 - And also Use SemanticRetriever for broader context

               - For general listing queries (e.g., "show all master circulars", "list documents about CRR"):
                 - Use ContextExpanderWithFilter for metadata-based filters and direct MongoDB retrieval of documents
                 - And also SemanticRetriever for broader context

               - For semantic content queries (e.g., "explain NPA classification", "summarize regulations on"):
                 - Use SemanticRetriever for content-based search

               - Example: "List all documents before Jan 2024"
                 - Correct: [ ContextExpanderWithFilter ]
                 - Then: [Summarizer, AnswerGenerator]
                 - Incorrect: [SemanticRetriever]

               - Example: "What is the CRR for FY2023-24?"
                - Correct: [ SemanticRetriever ]
                - Then: [Summarizer, AnswerGenerator]

               - Example: "Summarise all changes to CRR in 2023"
                - Correct: [ SemanticRetriever ]
                - Then: [Summarizer, AnswerGenerator]


            5. **Mandatory Steps**:
                - AnswerGenerator MUST be used for generating answers

            **Strict Ordering Rules**:
            1. QueryRewriter calls SemanticRetriever, or if required call ContextExpanderWithFilter in parallel and then call AnswerGenerator
            4. Summarizer is called to update the context before the final answer generation

            Generate an execution plan based on the task, context, and rules. Ensure the output strictly adheres to the ExecutionPlan schema."""

        # Add resolved ambiguity context
        if context.get("resolved_ambiguities"):
            prompt += "\nResolved Ambiguities:\n" + "\n".join(
                f"- {k}: {v}" for k, v in context["resolved_ambiguities"].items()
            )

        messages = [
            SystemMessage(content="[Compliance Enforcement Mode] You MUST treat ALL regulatory queries as potentially ambiguous until proven otherwise. Document resolution steps even when using predefined terms. Generate output conforming to the ExecutionPlan schema."),
            HumanMessage(content=prompt)
        ]

        # Use with_structured_output for direct Pydantic object generation
        structured_llm = self.llm.with_structured_output(
            ExecutionPlan, method="function_calling")

        try:
            execution_plan = structured_llm.invoke(messages)
            # print(execution_plan)  # Keep print for debugging if needed
            return execution_plan
        except Exception as e:
            # Catch potential errors during LLM invocation or validation by Pydantic
            raise Exception(
                f"Failed to generate or validate execution plan: {e}")

    @observe()
    async def validate_execution(self, plan, execution_result: Dict[str, Any]):
        """Asynchronously validates the execution result against the original plan.

        Args:
            plan (ExecutionPlan): The original execution plan.
            execution_result (Dict[str, Any]): The result of the execution.

        Returns:
            ValidationResult: The validation outcome.

        Raises:
            Exception: If the LLM fails to generate a valid validation result.
        """
        prompt = f"""Validate this execution result against the original plan using the provided validation rules:
        Plan: {plan.json()}
        Execution Result: {str(execution_result)}
        Generate a validation result conforming to the ValidationResult schema."""

        messages = [
            SystemMessage(
                content="You are a Compliance Pipeline Validator. Validate execution results against plans. Generate output conforming to the ValidationResult schema."),
            HumanMessage(content=prompt)
        ]

        # TODO : removed that as structured output needs a smaller model only
        # Use with_structured_output for direct Pydantic object generation
        # structured_llm = self.llm.with_structured_output(ValidationResult, method="function_calling")
        # try:
        #     validation_result = await structured_llm.ainvoke(messages)
        #     return validation_result
        # except Exception as e:
        #     # Catch potential errors during LLM invocation or validation by Pydantic
        #     raise Exception(f"Failed to generate or validate validation result: {e}")
