#!/usr/bin/env python3
"""
Test the enhanced link scraping functionality with comprehensive BeautifulSoup extraction
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/selkea')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_link_scraping():
    """Test the enhanced link scraping with real RBI notification data"""
    
    print("🧪 Testing Enhanced Link Scraping with BeautifulSoup")
    print("=" * 80)
    
    # Import required libraries
    import requests
    from bs4 import BeautifulSoup
    import re

    # Define the functions locally to avoid Airflow dependency
    def _process_relative_url(href):
        """Process relative URLs to absolute URLs"""
        if href.startswith('http'):
            return href

        # Relative URL without leading slash
        if not href.startswith('http'):
            if 'rdocs' in href or '.pdf' in href.lower():
                return f"https://rbidocs.rbi.org.in/{href}"
            else:
                return f"https://www.rbi.org.in/{href}"

        return href

    def scrape_all_links_from_rbi_page(rbi_page_url):
        """
        Scrape ALL links from an RBI notification page using BeautifulSoup
        Returns comprehensive link information for LLM analysis
        """
        try:
            logger.info(f"🔍 Scraping ALL links from RBI page: {rbi_page_url}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(rbi_page_url, headers=headers, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            all_links = {
                'pdf_links': [],
                'rbi_links': [],
                'external_links': [],
                'page_text': soup.get_text()[:5000],  # First 5000 chars for context
                'page_title': soup.title.string if soup.title else '',
                'all_anchors': []
            }

            # Extract ALL anchor tags
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                text = link.get_text(strip=True)

                if not href or not text:
                    continue

                # Process relative URLs
                processed_href = _process_relative_url(href)

                anchor_data = {
                    'text': text,
                    'href': processed_href,
                    'original_href': href
                }
                all_links['all_anchors'].append(anchor_data)

                # Categorize links
                if processed_href.lower().endswith('.pdf'):
                    all_links['pdf_links'].append({
                        'url': processed_href,
                        'text': text,
                        'context': link.parent.get_text(strip=True)[:200] if link.parent else ''
                    })
                    logger.info(f"   📄 Found PDF: {text} -> {processed_href}")

                elif 'rbi.org.in' in processed_href:
                    all_links['rbi_links'].append({
                        'url': processed_href,
                        'text': text,
                        'context': link.parent.get_text(strip=True)[:200] if link.parent else ''
                    })
                else:
                    all_links['external_links'].append({
                        'url': processed_href,
                        'text': text
                    })

            logger.info(f"   📊 Scraped: {len(all_links['pdf_links'])} PDFs, "
                       f"{len(all_links['rbi_links'])} RBI links, "
                       f"{len(all_links['external_links'])} external links")

            return all_links

        except Exception as e:
            logger.error(f"   ❌ Error scraping RBI page {rbi_page_url}: {e}")
            return None

    def extract_anchor_information(html_content):
        """Extract comprehensive anchor information with enhanced PDF link detection"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            anchor_info = {
                'anchors': [],
                'reference_numbers': [],
                'rbi_links': [],
                'document_links': [],
                'pdf_links': [],
                'rbi_page_urls': [],
                'navigation_links': []
            }

            # Navigation link patterns to filter out
            navigation_patterns = [
                r'home', r'about', r'contact', r'sitemap', r'search', r'login',
                r'menu', r'header', r'footer', r'nav', r'breadcrumb'
            ]

            # Extract all anchor tags with their text and href
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                text = link.get_text(strip=True)

                # Skip empty links
                if not href or not text:
                    continue

                # Filter out navigation links
                is_navigation = any(pattern in href.lower() or pattern in text.lower()
                                  for pattern in navigation_patterns)

                if is_navigation:
                    anchor_info['navigation_links'].append({'text': text, 'href': href})
                    continue

                # Handle relative URLs
                processed_href = _process_relative_url(href)

                anchor_data = {
                    'text': text,
                    'href': processed_href,
                    'original_href': href,
                    'full_html': str(link)
                }
                anchor_info['anchors'].append(anchor_data)

                # Categorize links by type
                if processed_href.lower().endswith('.pdf'):
                    # Direct PDF link
                    anchor_info['pdf_links'].append(processed_href)
                    anchor_info['document_links'].append(processed_href)
                    logger.info(f"   📄 Found direct PDF link: {processed_href}")

                elif 'rbi.org.in' in processed_href:
                    if 'NotificationUser.aspx' in processed_href or 'Scripts/' in processed_href:
                        # RBI notification page that likely contains PDF
                        anchor_info['rbi_page_urls'].append(processed_href)
                        logger.info(f"   📋 Found RBI page URL: {processed_href}")
                    else:
                        # Other RBI links
                        anchor_info['rbi_links'].append(processed_href)

            return anchor_info

        except Exception as e:
            logger.error(f"Error extracting anchor information: {e}")
            return {
                'anchors': [], 'reference_numbers': [], 'rbi_links': [],
                'document_links': [], 'pdf_links': [], 'rbi_page_urls': [], 'navigation_links': []
            }

    def enhance_document_links(anchor_info):
        """Enhance document links by following RBI page URLs to find PDF links"""
        enhanced_info = dict(anchor_info)
        notification_pdf_url = None
        all_scraped_links = []

        # If we already have direct PDF links, prioritize those
        if enhanced_info['pdf_links']:
            logger.info(f"✅ Already have {len(enhanced_info['pdf_links'])} direct PDF links")
            # First PDF could be the notification itself
            notification_pdf_url = enhanced_info['pdf_links'][0]

        # If we have RBI page URLs, scrape ALL links from them
        if enhanced_info['rbi_page_urls']:
            logger.info(f"🔍 Scraping ALL links from {len(enhanced_info['rbi_page_urls'])} RBI pages")

            for rbi_page_url in enhanced_info['rbi_page_urls']:
                scraped_links = scrape_all_links_from_rbi_page(rbi_page_url)
                if scraped_links:
                    all_scraped_links.append(scraped_links)

                    # Add found PDFs to our enhanced info
                    for pdf_info in scraped_links['pdf_links']:
                        pdf_url = pdf_info['url']
                        if pdf_url not in enhanced_info['pdf_links']:
                            enhanced_info['pdf_links'].append(pdf_url)
                            enhanced_info['document_links'].append(pdf_url)
                            if not notification_pdf_url:
                                notification_pdf_url = pdf_url
                            logger.info(f"   ✅ Added PDF: {pdf_url}")

        # Store the notification PDF URL and scraped links for LLM processing
        enhanced_info['notification_pdf_url'] = notification_pdf_url or ''
        enhanced_info['all_scraped_links'] = all_scraped_links

        logger.info(f"📊 Enhanced links summary: {len(enhanced_info['pdf_links'])} PDFs, "
                   f"{len(enhanced_info['rbi_page_urls'])} RBI pages, "
                   f"notification PDF: {bool(notification_pdf_url)}")

        return enhanced_info
    
    # Test with a sample notification that has RBI page links
    sample_notification = {
        "title": "Notification No. RBI/2025-26/64 dated 2 July 2025 - Master Direction on Regulatory Retail Portfolio",
        "rss_description": """
        <p>Notification No. RBI/2025-26/64 dated 2 July 2025</p>
        <p>Master Direction on Regulatory Retail Portfolio</p>
        <p><a href='https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0'>View Notification</a></p>
        <p>This notification introduces new guidelines for retail portfolio management.</p>
        """,
        "link": "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0"
    }
    
    print(f"📋 Testing notification: {sample_notification['title'][:60]}...")
    print(f"🔗 RBI Link: {sample_notification['link']}")
    
    # Step 1: Extract anchor information from RSS description
    print("\n🔍 Step 1: Extracting anchor information from RSS description")
    print("-" * 60)
    
    anchor_info = extract_anchor_information(sample_notification['rss_description'])
    
    print(f"✅ Extracted anchor info:")
    print(f"   📄 PDF links: {len(anchor_info['pdf_links'])}")
    print(f"   🔗 RBI page URLs: {len(anchor_info['rbi_page_urls'])}")
    print(f"   📋 Reference numbers: {len(anchor_info['reference_numbers'])}")
    print(f"   ⚓ Total anchors: {len(anchor_info['anchors'])}")
    
    if anchor_info['rbi_page_urls']:
        print(f"   🎯 RBI page URLs found:")
        for url in anchor_info['rbi_page_urls']:
            print(f"      • {url}")
    
    # Step 2: Test direct link scraping (if we have RBI page URLs)
    if anchor_info['rbi_page_urls']:
        print(f"\n🔍 Step 2: Testing direct link scraping from RBI page")
        print("-" * 60)
        
        test_url = anchor_info['rbi_page_urls'][0]
        scraped_links = scrape_all_links_from_rbi_page(test_url)
        
        if scraped_links:
            print(f"✅ Successfully scraped links from: {test_url}")
            print(f"   📄 PDF links found: {len(scraped_links['pdf_links'])}")
            print(f"   🔗 RBI links found: {len(scraped_links['rbi_links'])}")
            print(f"   🌐 External links found: {len(scraped_links['external_links'])}")
            print(f"   📰 Page title: {scraped_links['page_title']}")
            
            if scraped_links['pdf_links']:
                print(f"   📄 PDF links details:")
                for i, pdf_info in enumerate(scraped_links['pdf_links'][:3], 1):
                    print(f"      {i}. '{pdf_info['text']}' -> {pdf_info['url']}")
                    if pdf_info['context']:
                        print(f"         Context: {pdf_info['context'][:100]}...")
        else:
            print(f"❌ Failed to scrape links from: {test_url}")
    
    # Step 3: Test enhanced document links function
    print(f"\n🔍 Step 3: Testing enhanced document links function")
    print("-" * 60)
    
    enhanced_info = enhance_document_links(anchor_info)
    
    print(f"✅ Enhanced document links:")
    print(f"   📄 Total PDF links: {len(enhanced_info['pdf_links'])}")
    print(f"   🎯 Notification PDF URL: {enhanced_info.get('notification_pdf_url', 'None')}")
    print(f"   📊 Scraped data available: {len(enhanced_info.get('all_scraped_links', []))}")
    
    if enhanced_info.get('all_scraped_links'):
        print(f"   📋 Comprehensive scraped links summary:")
        for i, scraped_data in enumerate(enhanced_info['all_scraped_links'], 1):
            print(f"      Page {i}: {len(scraped_data['pdf_links'])} PDFs, {len(scraped_data['rbi_links'])} RBI links")
    
    # Step 4: Test the enhanced content generation (simulate what the LLM would see)
    print(f"\n🔍 Step 4: Testing enhanced content for LLM")
    print("-" * 60)
    
    # Simulate the enhanced content generation from extract_affected_documents
    enhanced_content = sample_notification['rss_description'][:2000]
    enhanced_content += f"\n\n=== EXTRACTED INFORMATION FOR PROCESSING ==="
    
    if enhanced_info['pdf_links']:
        enhanced_content += f"\nEXTRACTED_PDF_LINKS: {', '.join(enhanced_info['pdf_links'][:3])}"
        enhanced_content += f"\n*** USE THESE EXACT URLs FOR new_document_url - DO NOT MODIFY ***"
    
    if enhanced_info.get('all_scraped_links'):
        enhanced_content += f"\n\n=== COMPREHENSIVE SCRAPED LINKS FROM RBI PAGES ==="
        for i, scraped_data in enumerate(enhanced_info['all_scraped_links'][:2]):
            enhanced_content += f"\n--- PAGE {i+1} SCRAPED LINKS ---"
            
            if scraped_data['pdf_links']:
                enhanced_content += f"\nPDF_LINKS_FOUND:"
                for pdf_info in scraped_data['pdf_links'][:5]:
                    enhanced_content += f"\n  • '{pdf_info['text']}' -> {pdf_info['url']}"
                    if pdf_info['context']:
                        enhanced_content += f" (Context: {pdf_info['context'][:100]}...)"
        
        enhanced_content += f"\n*** IMPORTANT: Use the exact PDF URLs from above for new_document_url ***"
    
    enhanced_content += f"\n=== END EXTRACTED INFORMATION ==="
    
    print(f"✅ Enhanced content generated ({len(enhanced_content)} characters)")
    print(f"📝 Sample enhanced content:")
    print("-" * 40)
    print(enhanced_content[-800:])  # Show last 800 characters
    print("-" * 40)
    
    # Step 5: Save results for analysis
    results = {
        "test_timestamp": datetime.now().isoformat(),
        "sample_notification": sample_notification,
        "anchor_info": {
            "pdf_links_count": len(anchor_info['pdf_links']),
            "rbi_page_urls_count": len(anchor_info['rbi_page_urls']),
            "reference_numbers_count": len(anchor_info['reference_numbers']),
            "pdf_links": anchor_info['pdf_links'],
            "rbi_page_urls": anchor_info['rbi_page_urls'],
            "reference_numbers": anchor_info['reference_numbers']
        },
        "enhanced_info": {
            "total_pdf_links": len(enhanced_info['pdf_links']),
            "notification_pdf_url": enhanced_info.get('notification_pdf_url', ''),
            "scraped_pages_count": len(enhanced_info.get('all_scraped_links', [])),
            "pdf_links": enhanced_info['pdf_links']
        },
        "enhanced_content_length": len(enhanced_content)
    }
    
    with open('enhanced_link_scraping_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Test Results Summary:")
    print(f"   ✅ Anchor extraction: {results['anchor_info']['pdf_links_count']} PDFs, {results['anchor_info']['rbi_page_urls_count']} RBI pages")
    print(f"   ✅ Enhanced links: {results['enhanced_info']['total_pdf_links']} total PDFs")
    print(f"   ✅ Scraped pages: {results['enhanced_info']['scraped_pages_count']}")
    print(f"   ✅ Enhanced content: {results['enhanced_content_length']} characters")
    print(f"   💾 Results saved to: enhanced_link_scraping_test_results.json")
    
    return results

if __name__ == "__main__":
    try:
        results = test_enhanced_link_scraping()
        print(f"\n🎉 Enhanced link scraping test completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
