#!/usr/bin/env python3
"""
Test script to verify that our Pydantic models are compatible with OpenAI's structured output format.
"""

import sys
import json
from pathlib import Path

# Add the parent directory to the path to import DAG modules
sys.path.append(str(Path(__file__).parent.parent))

def test_pydantic_schema_generation():
    """Test that our Pydantic models generate valid schemas for OpenAI"""
    print("🧪 Testing Pydantic schema generation for OpenAI compatibility...")
    
    try:
        from prompts.notification_categorizer import AffectedDocumentsResult
        
        # Generate the JSON schema
        schema = AffectedDocumentsResult.model_json_schema()
        
        print("✅ Schema generated successfully")
        print(f"   📋 Schema type: {schema.get('type')}")
        print(f"   🔑 Properties: {list(schema.get('properties', {}).keys())}")
        
        # Check if all properties are in required (OpenAI requirement)
        properties = schema.get('properties', {})
        required = schema.get('required', [])
        
        print(f"   📝 Required fields: {required}")
        print(f"   📊 Total properties: {len(properties)}")
        print(f"   ✅ Required properties: {len(required)}")
        
        # Verify that all non-optional fields are in required
        missing_required = []
        for prop_name, prop_def in properties.items():
            # Check if it's not optional (doesn't have 'null' in type or anyOf)
            is_optional = (
                prop_def.get('type') == 'null' or
                'null' in str(prop_def.get('anyOf', [])) or
                prop_name.endswith('_optional')
            )
            
            if not is_optional and prop_name not in required:
                missing_required.append(prop_name)
        
        if missing_required:
            print(f"⚠️  Fields that should be required: {missing_required}")
        else:
            print("✅ All non-optional fields are properly marked as required")
        
        # Pretty print the schema for inspection
        print("\n📄 Generated Schema:")
        print(json.dumps(schema, indent=2))
        
        return len(missing_required) == 0
        
    except Exception as e:
        print(f"❌ Error generating schema: {e}")
        return False

def test_model_instantiation():
    """Test that we can create instances with all required fields"""
    print("\n🏗️  Testing model instantiation...")
    
    try:
        from prompts.notification_categorizer import AffectedDocumentsResult
        
        # Test with minimal required data
        minimal_result = AffectedDocumentsResult(
            document_actions=[],
            document_keywords=[],
            has_new_document_link=False,
            new_document_url="",
            rbi_links=[],
            processing_notes="",
            requires_manual_review=False
        )
        
        print("✅ Minimal instance created successfully")
        
        # Test with full data
        from prompts.notification_categorizer import DocumentAction

        full_result = AffectedDocumentsResult(
            document_actions=[
                DocumentAction(
                    document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03",
                    action_type="UPDATE_DOCUMENT",
                    confidence="high"
                ),
                DocumentAction(
                    document_id="Master Direction - XYZ",
                    action_type="REMOVE_DOCUMENT",
                    confidence="medium"
                )
            ],
            document_keywords=["banking", "credit"],
            has_new_document_link=True,
            new_document_url="https://rbi.org.in/doc.pdf",
            rbi_links=["https://rbi.org.in/page1"],
            processing_notes="Successfully processed",
            requires_manual_review=False
        )

        print("✅ Full instance created successfully")
        print(f"   📄 Document actions: {len(full_result.document_actions)}")
        
        # Test serialization
        serialized = full_result.model_dump()
        print("✅ Serialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating instances: {e}")
        return False

def test_openai_compatibility_simulation():
    """Simulate what OpenAI's API would do with our schema"""
    print("\n🤖 Testing OpenAI API compatibility simulation...")
    
    try:
        from prompts.notification_categorizer import AffectedDocumentsResult
        
        # Get the schema
        schema = AffectedDocumentsResult.model_json_schema()
        
        # Simulate OpenAI's validation checks
        properties = schema.get('properties', {})
        required = schema.get('required', [])
        
        # Check 1: All properties should be in required for OpenAI structured output
        all_props_required = set(properties.keys()) == set(required)
        
        if all_props_required:
            print("✅ All properties are marked as required (OpenAI compatible)")
        else:
            missing = set(properties.keys()) - set(required)
            print(f"⚠️  Properties not in required: {missing}")
        
        # Check 2: No default values should cause issues
        has_defaults = any('default' in str(prop) for prop in properties.values())
        
        if not has_defaults:
            print("✅ No problematic default values found")
        else:
            print("⚠️  Some properties may have default values")
        
        # Check 3: Schema structure is valid
        required_schema_fields = ['type', 'properties']
        has_required_fields = all(field in schema for field in required_schema_fields)
        
        if has_required_fields:
            print("✅ Schema has required structure")
        else:
            print("❌ Schema missing required fields")
        
        return all_props_required and not has_defaults and has_required_fields
        
    except Exception as e:
        print(f"❌ Error in compatibility simulation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing OpenAI Schema Compatibility")
    print("=" * 60)
    
    tests = [
        test_pydantic_schema_generation,
        test_model_instantiation,
        test_openai_compatibility_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The schema is OpenAI compatible.")
    else:
        print("⚠️  Some tests failed. The schema may need adjustments.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
