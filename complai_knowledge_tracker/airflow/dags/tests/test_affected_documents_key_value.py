#!/usr/bin/env python3
"""
Test script to verify the new key-value structure for affected documents and their update actions.
"""

import sys
from pathlib import Path

# Add the parent directory to the path to import DAG modules
sys.path.append(str(Path(__file__).parent.parent))

def test_affected_documents_result_structure():
    """Test the new AffectedDocumentsResult structure with document actions"""
    print("🧪 Testing AffectedDocumentsResult structure...")

    try:
        from prompts.notification_categorizer import AffectedDocumentsResult, DocumentAction

        # Test creating an instance with the enhanced structure
        test_result = AffectedDocumentsResult(
            document_actions=[
                DocumentAction(
                    document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03",
                    action_type="UPDATE_DOCUMENT",
                    confidence="high",
                    document_title="Master Direction on Credit Card Operations for Banks",
                    reference_number="DBOD.No.Leg.BC.21/09.07.007/2002-03",
                    department="DBOD",
                    original_date="2003-03-15",
                    reasoning="Document needs updates due to new amendments"
                ),
                DocumentAction(
                    document_id="Master Direction - Credit Card Operations",
                    action_type="REMOVE_DOCUMENT",
                    confidence="medium",
                    document_title="Master Direction - Credit Card Operations",
                    department="DBOD",
                    reasoning="Being superseded by new direction"
                ),
                DocumentAction(
                    document_id="RBI/2024-25/123",
                    action_type="ADD_TEMPORARY_NOTE",
                    confidence="high",
                    reference_number="RBI/2024-25/123",
                    reasoning="Temporary relaxation for COVID-19 impact"
                )
            ],
            document_keywords=["credit card", "banking operations"],
            has_new_document_link=True,
            new_document_url="https://rbi.org.in/new-document.pdf",
            rbi_links=["https://rbi.org.in/page1", "https://rbi.org.in/page2"],
            processing_notes="Successfully processed notification with enhanced metadata",
            requires_manual_review=False,
            superseded_documents=["Old Credit Card Circular No. 456"],
            amendment_details="Updates to transaction limits and KYC requirements",
            effective_date="2024-01-01"
        )

        print("✅ AffectedDocumentsResult created successfully")
        print(f"   📄 Document Actions: {len(test_result.document_actions)}")
        print(f"   🔑 Document-Action pairs: {[(da.document_id, da.action_type) for da in test_result.document_actions]}")
        print(f"   📝 Processing Notes: {test_result.processing_notes}")
        print(f"   🔍 Manual Review Required: {test_result.requires_manual_review}")

        # Test model_dump to ensure it serializes correctly
        result_dict = test_result.model_dump()
        print("✅ Model serialization successful")

        return True

    except Exception as e:
        print(f"❌ Error testing AffectedDocumentsResult: {e}")
        return False

def test_backward_compatibility():
    """Test that the processing functions work with the new structure"""
    print("\n🔄 Testing backward compatibility...")

    try:
        # Mock the affected documents result in the new format
        mock_affected_docs = {
            "document_actions": [
                {"document_id": "DBOD.No.Leg.BC.21/09.07.007/2002-03", "action_type": "UPDATE_DOCUMENT", "confidence": "high"},
                {"document_id": "Master Direction - Credit Card", "action_type": "REMOVE_DOCUMENT", "confidence": "medium"}
            ],
            "document_keywords": ["credit card", "banking"],
            "has_new_document_link": False,
            "new_document_url": "",
            "rbi_links": [],
            "processing_notes": "Test processing",
            "requires_manual_review": False
        }

        # Test extracting document identifiers
        document_actions = mock_affected_docs.get('document_actions', [])
        reference_numbers = [da.get('document_id', '') for da in document_actions if da.get('document_id')]

        print("✅ Document identifier extraction successful")
        print(f"   📋 Reference Numbers: {reference_numbers}")
        print(f"   🎯 Actions: {[da.get('action_type') for da in document_actions]}")

        # Test converting to update actions format
        actions = []
        for doc_action in document_actions:
            actions.append({
                'action_type': doc_action.get('action_type', 'NO_ACTION'),
                'target_document': doc_action.get('document_id', ''),
                'priority': 'high' if doc_action.get('confidence') == 'high' else 'medium',
                'details': f"Action required for {doc_action.get('document_id', '')}: {doc_action.get('action_type', 'NO_ACTION')}"
            })

        print("✅ Update actions conversion successful")
        print(f"   📊 Generated Actions: {len(actions)}")
        for action in actions:
            print(f"      - {action['target_document']}: {action['action_type']} (priority: {action['priority']})")

        return True

    except Exception as e:
        print(f"❌ Error testing backward compatibility: {e}")
        return False

def test_prompt_structure():
    """Test that the updated prompt includes the new structure requirements"""
    print("\n📝 Testing prompt structure...")
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        
        # Check if the prompt mentions key-value pairs
        if "key-value pairs" in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
            print("✅ Prompt mentions key-value pairs")
        else:
            print("⚠️  Prompt doesn't explicitly mention key-value pairs")
        
        # Check if the prompt mentions document_actions
        if "document_actions" in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
            print("✅ Prompt mentions document_actions field")
        else:
            print("⚠️  Prompt doesn't mention document_actions field")
        
        # Check if action types are mentioned
        action_types = ["UPDATE_DOCUMENT", "REMOVE_DOCUMENT", "ADD_DOCUMENT", "ADD_TEMPORARY_NOTE", "NO_ACTION"]
        mentioned_actions = [action for action in action_types if action in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT]
        
        print(f"✅ Action types mentioned in prompt: {mentioned_actions}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing prompt structure: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Affected Documents Key-Value Structure")
    print("=" * 60)
    
    tests = [
        test_affected_documents_result_structure,
        test_backward_compatibility,
        test_prompt_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The key-value structure is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
