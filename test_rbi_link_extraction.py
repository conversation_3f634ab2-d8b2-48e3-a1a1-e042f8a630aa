#!/usr/bin/env python3
"""
Test script to debug RBI link extraction issues
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_rbi_page_extraction(url):
    """Test extracting PDF from a real RBI page"""
    print(f"\n🔍 Testing RBI page: {url}")
    print("=" * 80)
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print(f"✅ Successfully fetched page (length: {len(response.text)} chars)")
        
        # Look for all PDF links
        print("\n📄 All PDF links found:")
        pdf_links = []
        
        # Method 1: Direct PDF links
        for link in soup.find_all('a', href=True):
            href = link['href']
            if '.pdf' in href.lower():
                pdf_links.append({
                    'href': href,
                    'text': link.get_text(strip=True),
                    'method': 'direct_link'
                })
                print(f"   📎 Direct: {href} (text: '{link.get_text(strip=True)}')")
        
        # Method 2: Table header approach
        print("\n🏷️ Table header analysis:")
        tableheader = soup.find('td', class_='tableheader')
        if tableheader:
            print("   ✅ Found tableheader")
            for element in tableheader.find_all(href=True):
                href = element.get('href', '')
                if href:
                    print(f"   🔗 Link in tableheader: {href}")
                    if '.pdf' in href.lower():
                        pdf_links.append({
                            'href': href,
                            'text': element.get_text(strip=True),
                            'method': 'tableheader'
                        })
        else:
            print("   ❌ No tableheader found")
        
        # Method 3: Look for PDF icons
        print("\n🖼️ PDF icon analysis:")
        pdf_images = soup.find_all('img', src=re.compile(r'.*pdf\.gif', re.IGNORECASE))
        if pdf_images:
            print(f"   ✅ Found {len(pdf_images)} PDF icons")
            for img in pdf_images:
                parent = img.parent
                if parent:
                    anchor = parent.find('a', href=True)
                    if anchor:
                        href = anchor.get('href', '')
                        print(f"   🔗 PDF icon link: {href}")
                        if '.pdf' in href.lower():
                            pdf_links.append({
                                'href': href,
                                'text': anchor.get_text(strip=True),
                                'method': 'pdf_icon'
                            })
        else:
            print("   ❌ No PDF icons found")
        
        # Method 4: Look for size indicators
        print("\n📏 Size indicator analysis:")
        size_pattern = re.compile(r'\(\d+\s*kb\)', re.IGNORECASE)
        size_elements = soup.find_all(string=size_pattern)
        if size_elements:
            print(f"   ✅ Found {len(size_elements)} size indicators")
            for element in size_elements:
                parent = element.parent
                if parent and parent.name == 'td':
                    anchor = parent.find('a', href=True)
                    if anchor:
                        href = anchor.get('href', '')
                        print(f"   🔗 Size indicator link: {href}")
                        if '.pdf' in href.lower():
                            pdf_links.append({
                                'href': href,
                                'text': anchor.get_text(strip=True),
                                'method': 'size_indicator'
                            })
        else:
            print("   ❌ No size indicators found")
        
        # Summary
        print(f"\n📊 Summary: Found {len(pdf_links)} total PDF links")
        unique_links = list(set(link['href'] for link in pdf_links))
        print(f"📊 Unique PDF URLs: {len(unique_links)}")
        
        for i, link in enumerate(unique_links):
            print(f"   {i+1}. {link}")
        
        return unique_links
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_sample_notifications():
    """Test with sample RBI notification URLs"""
    
    # Sample URLs from recent notifications
    test_urls = [
        # FEMA export regulations
        "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0",
        # Pre-payment charges
        "https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566",
        # Generic notification page
        "https://www.rbi.org.in/Scripts/BS_PressReleaseDisplay.aspx?prid=54321"
    ]
    
    print("🧪 Testing RBI Link Extraction")
    print("=" * 80)
    
    for url in test_urls:
        try:
            pdf_links = test_rbi_page_extraction(url)
            if pdf_links:
                print(f"✅ Success: Found {len(pdf_links)} PDF links")
            else:
                print(f"❌ No PDF links found")
        except Exception as e:
            print(f"❌ Error testing {url}: {e}")
        
        print("\n" + "-" * 40 + "\n")

if __name__ == "__main__":
    test_sample_notifications()
