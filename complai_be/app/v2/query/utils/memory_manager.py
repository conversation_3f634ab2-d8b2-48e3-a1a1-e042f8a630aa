import redis
import json
from config import settings
import os
from pymongo import MongoClient
from datetime import datetime

from app.db.database import get_db,DATABASE_NAME

class MemoryManager:
    def __init__(self):
        self.client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        self.db = get_db(database_name=DATABASE_NAME)

    
    def get_session_memory(self, session_id: str) -> list:
        key = f"session:{session_id}:memory"
        data = self.client.lrange(key, 0, -1)
        return [json.loads(item) for item in data]
    

    def update_session_memory_chat(self, session_id: str, query , message, username: str = None):
        key = f"session:{session_id}:memory"
        print("username", username)
        # Ensure session exists using upsert
        self.db["session"].update_one(
            {"session_id": session_id, "username": username},
            {"$setOnInsert": {"messages": [], "summary": ""}},
            upsert=True
        )

        # Now safely fetch the session
        session = self.db["session"].find_one({"session_id": session_id, "username": username})
        print(f"Session found: {session}")

        messages = session.get("messages", [])

        # Parse the message
        if isinstance(message, dict):
            query = query
            content = message.get("response", "")
            metadata = message.get("metadata", "")
            raw_message = message
        else:
            query = query
            content = ""
            metadata = ""
            raw_message = {"text": content}

        turn = {"role": "", "message": raw_message}
        print(f"Updating session memory for {session_id}: {turn}")

        message_obj = {
            "query": query,
            "content": content,
            "metadata": metadata,
            "timestamp": datetime.utcnow()
        }
        print(f"Message object: {message_obj}")

        if message_obj["content"]:
            messages.append(message_obj)

            # Update messages and clear summary
            self.db["session"].update_one(
                {"session_id": session_id, "username": username},
                {"$set": {"messages": messages, "summary": ""}}
            )

        # Push turn to Redis
        self.client.rpush(key, json.dumps(turn))

        print(f"[Memory Updated] Session: {session_id}, Query: {query or content}")


    
    def get_conversation_context(self, session_id: str) -> str:
        turns = self.get_session_memory(session_id)
        context_lines = [f"{t['role']}: {t['message']}" for t in turns]
        return "\n".join(context_lines)
    
    def update_condensed_context(self, session_id: str, context: str):
        key = f"session:{session_id}:condensed"
        self.client.set(key, context)
        
    def get_condensed_context(self, session_id: str) -> str:
        key = f"session:{session_id}:condensed"
        context = self.client.get(key)
        return context if context else ""

memory_manager = MemoryManager()
