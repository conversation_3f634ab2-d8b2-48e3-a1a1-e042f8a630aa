# combine_pages.py
def combine_pages(pages: list):
    """
    Combine the markdown text from all pages in order.
    
    Args:
        pages (list): List of pages from the OCR response.
        
    Returns:
        tuple: Combined markdown text and sorted pages (by page index).
    """
    pages_sorted = sorted(pages, key=lambda p: p["index"])
    combined = "\n".join(page.get("markdown", "") for page in pages_sorted)
    return combined, pages_sorted
