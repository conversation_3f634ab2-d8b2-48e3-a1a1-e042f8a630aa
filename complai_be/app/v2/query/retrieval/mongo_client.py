import os
import logging
import urllib.parse
import requests
from pymongo import MongoClient
from pymongo.collection import Collection
from config import settings

# Setup logging
logging.basicConfig(level=logging.INFO)

# Constants
PEM_PATH = "global-bundle.pem"

def download_tls_ca_file():
    """Downloads the global TLS CA file if not already present."""
    if os.path.exists(PEM_PATH):
        logging.info("TLS CA file already exists.")
        return
    try:
        logging.info("Downloading TLS CA file...")
        response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        response.raise_for_status()
        with open(PEM_PATH, "wb") as file:
            file.write(response.content)
        logging.info("TLS CA file downloaded.")
    except requests.RequestException as e:
        logging.error("Failed to download TLS CA file: %s", e)
        raise

def get_mongo_client() -> MongoClient:
    """Creates a MongoDB client with optional TLS support."""
    download_tls_ca_file()

    uri = settings.MONGODB_URI
    logging.info("Connecting to MongoDB...")
    if settings.ENVIRONMENT == "local":

        client = MongoClient(uri, tls=False)
    else:
        client = MongoClient(
            uri,
            tls=False,
            # tlsCAFile=PEM_PATH
        )
    logging.info("MongoClient created.")
    return client

def get_mongo_collection() -> Collection:
    """Gets the target MongoDB collection."""
    client = get_mongo_client()
    return client[settings.MONGODB_URI][settings.MONGODB_COLLECTION]

def get_mongo_schema() -> dict:
    """Inspects a document and returns its schema (key and type)."""
    collection = get_mongo_collection()
    document = collection.find_one()
    if not document:
        logging.warning("No documents found in the collection.")
        return {}
    return {key: type(value) for key, value in document.items()}

# Optional: For dependency injection
client: MongoClient = None
