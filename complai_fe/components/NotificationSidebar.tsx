//NotificationSidebar.tsx
import { useEffect, useState, useRef } from 'react';
import { NOTIFICATION_ENDPOINT } from "@/app/utils/Api";
import { X, Download, ExternalLink } from "lucide-react";
import {
  Pdf<PERSON><PERSON><PERSON>,
  PdfHighlighter,
  Highlight,
  AreaHighlight,
  Popup,
  IHighlight,
} from "react-pdf-highlighter";
import "react-pdf-highlighter/dist/style.css";

// Add a global style to hide scrollbars and fix PDF rendering
const ScrollbarStyle = () => (
  <style jsx global>{`
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }
    
    /* Override PDF scaling */
    .pdf-container {
      max-width: 100%;
      max-height: 100%;
      overflow: hidden;
    }
    
    /* Make pdf pages fit inside container */
    .pdf-highlighter-container {
      width: 100%;
      height: 100%;
      overflow: auto;
    }
    
    /* Force the PDF to scale properly */
    .react-pdf__Document {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .react-pdf__Page {
      max-width: 100% !important;
      box-shadow: none !important;
      margin: 0 !important;
    }
    
    .react-pdf__Page__canvas {
      max-width: 100% !important;
      height: auto !important;
    }
  `}</style>
);

interface NotificationSidebarProps {
  onCollapseChat?: () => void; // Add prop to collapse chat history
  onClose: () => void;
}

// Define the type for notification items
interface Notification {
  _id: string;
  title: string;
  date_iso: string;
  category?: string; // This will be calculated based on date
  s3_url: string;
  circular_numbers?: string[];
  press_release_number?: string | null;
  addressee?: string | null;
}

// TriggerComponent to handle loading and scrolling
const TriggerComponent = ({ onLoad }: { onLoad: () => void }) => {
  useEffect(() => {
    setTimeout(() => {
      onLoad?.();
    }, 400);
  }, [onLoad]);

  return null;
};

// Helper functions for hash management
const resetHash = () => {
  document.location.hash = "";
};

const parseIdFromHash = () =>
  document.location.hash.slice("#highlight-".length);

export function NotificationSidebar(
  {
    onCollapseChat,
    onClose,
  }: NotificationSidebarProps
) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [showPdf, setShowPdf] = useState(false);
  const [pdfLoadFailed, setPdfLoadFailed] = useState(false);
  const scrollViewerToRef = useRef<(highlight: IHighlight) => void>(() => {});

  useEffect(() => {
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch(NOTIFICATION_ENDPOINT);
      
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      
      const data = await response.json();
      // console.log(data);
      // Process the data to add category and filter out items with null s3_url
      const processedData = data
        .filter((item: Notification) => item.s3_url !== null && item.s3_url !== undefined)
        .map((item: Notification) => {
          return {
            ...item,
            category: getCategoryFromDate(item.date_iso)
          };
        });
      
      setNotifications(processedData);
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setLoading(false);
    }
  };

  fetchNotifications();
}, []);

  // Reset PDF load status when selecting a new notification
  useEffect(() => {
    if (selectedNotification) {
      setPdfLoadFailed(false);
    }
  }, [selectedNotification]);

  // Function to determine category based on date
  const getCategoryFromDate = (dateIso: string) => {
    const date = new Date(dateIso);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const lastWeekStart = new Date(today);
    lastWeekStart.setDate(today.getDate() - 7);

    // Reset time part for accurate day comparison
    const dateWithoutTime = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayWithoutTime = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayWithoutTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    
    if (dateWithoutTime.getTime() === todayWithoutTime.getTime()) {
      return "Today";
    } else if (dateWithoutTime.getTime() === yesterdayWithoutTime.getTime()) {
      return "Yesterday";
    } else if (date >= lastWeekStart && date < yesterdayWithoutTime) {
      return "Last Week";
    } else {
      return "Older";
    }
  };

  // Format date for display (e.g., "Apr 21, 2025")
  const formatDate = (dateIso: string) => {
    const date = new Date(dateIso);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Handler for clicking on a notification
  const handleNotificationClick = (notification: Notification) => {
    setPdfLoadFailed(false);
    setSelectedNotification(notification);
    setShowPdf(true);
    
    if (onCollapseChat) {
      onCollapseChat();
    }
  };

  // Handler to close the PDF view
  const handleClosePdf = () => {
    setShowPdf(false);
    setSelectedNotification(null);
    setPdfLoadFailed(false);
    onClose();
  };

  // Handler to download the PDF
  const handleDownloadPdf = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (selectedNotification?.s3_url) {
      try {
        // Fetch the PDF file
        const response = await fetch(selectedNotification.s3_url)
        const blob = await response.blob()
        
        // Create a blob URL for the PDF
        const blobUrl = window.URL.createObjectURL(blob)
        
        // Create a temporary anchor element to trigger the download
        const link = document.createElement('a')
        link.href = blobUrl
        link.download = `${selectedNotification.title || 'document'}.pdf`
        
        // Append to the document, click, and clean up
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // Release the blob URL to free up memory
        setTimeout(() => {
          window.URL.revokeObjectURL(blobUrl)
        }, 100)
      } catch (error) {
        console.error("Error downloading PDF:", error)
        // Fallback method if fetch fails
        const link = document.createElement('a')
        link.href = selectedNotification.s3_url
        link.download = `${selectedNotification.title || 'document'}.pdf`
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        link.setAttribute('download', '')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    }
  }

  // Handle PDF errors
  const handlePdfError = () => {
    // console.log("PDF failed to load:", selectedNotification?.s3_url);
    setPdfLoadFailed(true);
  };

  // Function to open PDF in new tab
  const openPdfInNewTab = () => {
    if (selectedNotification?.s3_url) {
      window.open(selectedNotification.s3_url, '_blank');
    }
  };

  // Directly embed the PDF using an iframe as a fallback approach
  const EmbeddedPdfViewer = () => {
    if (!selectedNotification?.s3_url) return null;
    
    // Create a valid URL for PDF.js viewer
    const pdfViewerUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(selectedNotification.s3_url)}`;
    
    return (
      <iframe 
        src={pdfViewerUrl}
        className="w-full h-full border-0"
        title="PDF Viewer"
      />
    );
  };

  // PDF Viewer component
  const PdfViewer = () => {
    if (!selectedNotification) {
      return <div className="p-6 text-center">No document selected</div>;
    }
    
    if (!selectedNotification.s3_url) {
      return (
        <div className="flex flex-col h-full">
        <div className="pt-6 pl-6 pr-6 pb-0 flex justify-between items-center bg-[#DDD1C5] z-20">
            <div>
              <h2 className="text-[14px] font-semibold text-[#494949]">{selectedNotification.title || "Regulation Document"}</h2>
              <div className="text-[10px] text-[#7B7B7B] mt-1">
                {selectedNotification.circular_numbers && selectedNotification.circular_numbers.length > 0 
                  ? selectedNotification.circular_numbers.join(', ') 
                  : (selectedNotification.press_release_number || 'No reference number')} 
                {selectedNotification.date_iso ? ` • ${formatDate(selectedNotification.date_iso)}` : ''}
              </div>
            </div>
            <button onClick={handleClosePdf} className="text-[#7B7B7B] hover:text-[#494949] transition-colors duration-200">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="p-6 text-center flex items-center justify-center h-full">
            <div className="bg-[#FFF8F2] p-6 rounded-md border border-[#FECB66] max-w-md text-center">
              <h3 className="text-[#494949] font-medium mb-3">PDF Not Available</h3>
              <p className="text-[#7B7B7B]">This notification doesn't have an associated PDF document.</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full">
        {/* Header section - matching background with overall gradient */}
        <div className="pt-6 pl-6 pr-6 pb-0 flex justify-between items-center bg-[#DDD1C5] z-20">
          <div>
            <h2 className="text-[14px] font-semibold text-[#494949]">{selectedNotification.title || "Regulation Document"}</h2>
            <div className="text-[10px] text-[#7B7B7B] mt-1">
              {selectedNotification.circular_numbers && selectedNotification.circular_numbers.length > 0 
                ? selectedNotification.circular_numbers.join(', ') 
                : (selectedNotification.press_release_number || 'No reference number')} 
              {selectedNotification.date_iso ? ` • ${formatDate(selectedNotification.date_iso)}` : ''}
            </div>
          </div>
          <div className="flex items-center gap-6">
            <button 
              onClick={handleDownloadPdf} 
              className="text-[#7B7B7B] hover:text-[#494949] transition-colors duration-200" 
              title="Download PDF"
            >
              <Download className="h-6 w-6" />
            </button>
            <button 
              onClick={handleClosePdf} 
              className="text-[#7B7B7B] hover:text-[#494949] transition-colors duration-200"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Container for PDF with fixed height and width - preventing overflow */}
        <div className="flex-1 flex m-4">
          <div className="flex-1 rounded-xl shadow-sm border border-[#D7CBBC] bg-white overflow-hidden">
            {!pdfLoadFailed ? (
              <div className="w-full h-full pdf-container" style={{ position: 'relative' }}>
                <div className="pdf-highlighter-container">
                  <PdfLoader 
                    url={selectedNotification.s3_url} 
                    beforeLoad={
                      <div className="flex justify-center items-center h-full">
                        <div className="animate-pulse flex flex-col items-center">
                          <div className="h-12 w-12 bg-[#F8F0DF] rounded-full mb-4"></div>
                          <div className="text-[#7B7B7B]">Loading PDF...</div>
                        </div>
                      </div>
                    }
                    onError={handlePdfError}
                    errorMessage={
                      <div className="flex flex-col items-center justify-center h-full p-6">
                        <div className="bg-[#FFF8F2] p-6 rounded-md border border-[#FECB66] max-w-md text-center">
                          <h3 className="text-[#494949] font-medium mb-3">Unable to load PDF</h3>
                          <p className="text-[#7B7B7B] mb-4">The document might be unavailable or in an unsupported format.</p>
                          <div className="flex justify-center gap-3">
                            <button 
                              onClick={handleDownloadPdf}
                              className="px-3 py-2 bg-[#EDE5DC] text-[#494949] rounded-md hover:bg-opacity-90 transition-colors"
                            >
                              Download PDF
                            </button>
                          </div>
                        </div>
                      </div>
                    }
                  >
                    {(pdfDocument) => (
                      <div style={{ width: '100%', height: '100%' }}>
                        <TriggerComponent onLoad={() => {}} />
                        <PdfHighlighter
                          pdfDocument={pdfDocument}
                          enableAreaSelection={(event) => event.altKey}
                          onScrollChange={resetHash}
                          scrollRef={(scrollTo) => {
                            scrollViewerToRef.current = scrollTo;
                          }}
                          onSelectionFinished={() => <></>}
                          highlightTransform={(
                            highlight,
                            index,
                            setTip,
                            hideTip,
                            viewportToScaled,
                            screenshot,
                            isScrolledTo
                          ) => {
                            const isTextHighlight = !highlight.content?.image;
                            const component = isTextHighlight ? (
                              <Highlight
                                isScrolledTo={false}
                                position={highlight.position}
                                comment={highlight.comment}
                              />
                            ) : (
                              <AreaHighlight
                                isScrolledTo={false}
                                highlight={highlight}
                                onChange={() => {}}
                              />
                            );

                            return (
                              <Popup
                                popupContent={<></>}
                                onMouseOver={(popupContent) =>
                                  setTip(highlight, () => popupContent)
                                }
                                onMouseOut={hideTip}
                                key={index}
                              >
                                {component}
                              </Popup>
                            );
                          }}
                          highlights={[]}
                        />
                      </div>
                    )}
                  </PdfLoader>
                </div>
              </div>
            ) : (
              // Use embedded iframe as fallback
              <div className="w-full h-full">
                <EmbeddedPdfViewer />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // If showing the PDF view
  if (showPdf && selectedNotification) {
    return (
      <aside 
        style={{
          flex: "0 0 50%",
          maxWidth: "40%",
          transition: "all 0.3s ease-in-out",
          position: "relative",
          height: "100vh",
        }} 
        className="bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D7CBBC] overflow-hidden flex flex-col"
      >
        <ScrollbarStyle />
        <PdfViewer />
      </aside>
    );
  }

  if (loading) {
    return (
      <aside className="w-[244px] bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D1C3BE] pt-[92px] pb-[83px] pl-[16px] pr-[16px]">
        <ScrollbarStyle />
        <p className="text-sm text-[#7B7B7B]">Loading regulations...</p>
      </aside>
    );
  }

  if (error) {
    return (
      <aside className="w-[244px] bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D1C3BE] pt-[92px] pb-[16px] pl-[16px] pr-[16px]">
        <ScrollbarStyle />
        <p className="text-sm text-[#7B7B7B]">Error loading regulations: {error}</p>
      </aside>
    );
  }

  // Group notifications by category
  const todayNotifications = notifications.filter(item => item.category === "Today");
  const yesterdayNotifications = notifications.filter(item => item.category === "Yesterday");
  const lastWeekNotifications = notifications.filter(item => item.category === "Last Week");

  return (
    <aside className="w-[244px] bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D1C3BE] pt-[92px] pb-[16px] pl-[16px] pr-[16px] overflow-hidden">      <div className="self-stretch  pb-6 rounded-xl flex items-center gap-2 flex-shrink-0">
          <div className="text-[rgba(73,73,73,0.6)] text-sm font-semibold" style={{ fontFamily: 'Figtree' }}>LATEST REGULATIONS</div>
        </div>
      <ScrollbarStyle />
      <div className="h-full overflow-y-auto no-scrollbar">
        <div className="space-y-6">
          {todayNotifications.length > 0 && (
            <div>
              <h3 className="text-sm text-[#7B7B7B] font-medium mb-2">Today</h3>
              <div className="space-y-2">
                {todayNotifications.map((notification) => (
                  <div 
                    key={notification._id} 
                    className="px-2 py-2 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <h4 className="text-sm font-medium text-[#494949]">{notification.title}</h4>
                    <p className="text-xs text-[#7B7B7B]">{formatDate(notification.date_iso)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {yesterdayNotifications.length > 0 && (
            <div>
              <h3 className="text-sm text-[#7B7B7B] font-medium mb-2">Yesterday</h3>
              <div className="space-y-1">
                {yesterdayNotifications.map((notification) => (
                  <div 
                    key={notification._id} 
                    className="px-2 py-2 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <h4 className="text-sm font-medium text-[#494949]">{notification.title}</h4>
                    <p className="text-xs text-[#7B7B7B]">{formatDate(notification.date_iso)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {lastWeekNotifications.length > 0 && (
            <div>
              <h3 className="text-sm text-[#7B7B7B] font-medium mb-2">Last Week</h3>
              <div className="space-y-2">
                {lastWeekNotifications.map((notification) => (
                  <div 
                    key={notification._id} 
                    className="px-2 py-2 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <h4 className="text-sm font-medium text-[#494949]">{notification.title}</h4>
                    <p className="text-xs text-[#7B7B7B]">{formatDate(notification.date_iso)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {todayNotifications.length === 0 && yesterdayNotifications.length === 0 && lastWeekNotifications.length === 0 && (
            <p className="text-sm text-[#7B7B7B]">No recent regulations available.</p>
          )}
        </div>
      </div>
    </aside>
  );
}