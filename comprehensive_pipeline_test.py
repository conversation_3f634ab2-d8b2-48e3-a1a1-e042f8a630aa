#!/usr/bin/env python3
"""
Comprehensive pipeline test that shows exactly what would happen for each notification.
Combines prediction analysis with processing simulation and outputs detailed JSON results.
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ComprehensivePipelineTest:
    """Test the complete notification processing pipeline"""
    
    def __init__(self, notifications_file: str = "rbi_notifications.json"):
        self.notifications_file = Path(notifications_file)
        self.notifications = []
        self.results = []
        
    def load_notifications(self) -> bool:
        """Load notifications from JSON file"""
        try:
            if not self.notifications_file.exists():
                logger.error(f"Notifications file not found: {self.notifications_file}")
                return False
            
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                self.notifications = json.load(f)
            
            logger.info(f"📁 Loaded {len(self.notifications)} notifications")
            return True
            
        except Exception as e:
            logger.error(f"Error loading notifications: {e}")
            return False
    
    def predict_category(self, title: str) -> str:
        """Predict notification category based on title"""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['amendment', 'amend', 'modify']):
            return 'Amendment'
        elif any(word in title_lower for word in ['supersede', 'replace']):
            return 'Superseded'
        elif any(word in title_lower for word in ['withdraw', 'repeal', 'cancel']):
            return 'Repealed/Withdrawn'
        elif any(word in title_lower for word in ['review', 'revision']):
            return 'Review'
        elif any(word in title_lower for word in ['relaxation', 'relief', 'temporary']):
            return 'Relaxation'
        elif any(word in title_lower for word in ['direction', 'master direction', 'guidelines']):
            return 'New_Regulatory_Issuance'
        else:
            return 'Informational'
    
    def predict_action(self, title: str, category: str) -> str:
        """Predict required action based on title and category"""
        title_lower = title.lower()
        
        if category == 'Amendment':
            return 'UPDATE_DOCUMENT'
        elif category == 'Superseded':
            return 'UPDATE_DOCUMENT'  # Remove old + Add new
        elif category == 'Repealed/Withdrawn':
            return 'REMOVE_DOCUMENT'
        elif category == 'New_Regulatory_Issuance':
            return 'ADD_DOCUMENT'
        elif category == 'Relaxation':
            return 'ADD_TEMPORARY_NOTE'
        elif any(word in title_lower for word in ['update', 'implementation']):
            return 'UPDATE_DOCUMENT'
        else:
            return 'NO_ACTION'
    
    def get_confidence(self, title: str, category: str) -> str:
        """Get confidence level for prediction"""
        title_lower = title.lower()
        
        high_conf_keywords = ['amendment', 'amend', 'withdraw', 'repeal', 'master direction']
        medium_conf_keywords = ['review', 'update', 'implementation', 'relaxation']
        
        if any(word in title_lower for word in high_conf_keywords):
            return 'HIGH'
        elif any(word in title_lower for word in medium_conf_keywords):
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def simulate_processing_steps(self, notification: Dict, predicted_action: str) -> List[Dict]:
        """Simulate the actual processing steps for a notification"""
        steps = []
        
        if predicted_action == 'UPDATE_DOCUMENT':
            steps = [
                {
                    "step": "search_existing_document",
                    "description": "Search knowledge base for existing document",
                    "status": "success",
                    "details": "Found potential matches based on title keywords"
                },
                {
                    "step": "download_document",
                    "description": "Download latest version from RBI",
                    "status": "success" if notification.get('PDF Link', '').startswith('https://rbidocs.rbi.org.in/') else "failed",
                    "details": f"Source: {notification.get('PDF Link', '')[:50]}..."
                },
                {
                    "step": "process_pdf",
                    "description": "Extract text and create chunks",
                    "status": "success",
                    "details": "PDF processed and chunked for vectorization"
                },
                {
                    "step": "generate_vectors",
                    "description": "Create dense and sparse embeddings",
                    "status": "success",
                    "details": "Generated OpenAI, BM25, and SPLADE vectors"
                },
                {
                    "step": "update_knowledge_base",
                    "description": "Replace existing document in Qdrant",
                    "status": "success",
                    "details": "Document updated in knowledge base"
                }
            ]
        
        elif predicted_action == 'ADD_DOCUMENT':
            steps = [
                {
                    "step": "download_document",
                    "description": "Download new document from RBI",
                    "status": "success" if notification.get('PDF Link', '').startswith('https://rbidocs.rbi.org.in/') else "failed",
                    "details": f"Source: {notification.get('PDF Link', '')[:50]}..."
                },
                {
                    "step": "process_pdf",
                    "description": "Extract text and create chunks",
                    "status": "success",
                    "details": "PDF processed and chunked for vectorization"
                },
                {
                    "step": "generate_vectors",
                    "description": "Create dense and sparse embeddings",
                    "status": "success",
                    "details": "Generated OpenAI, BM25, and SPLADE vectors"
                },
                {
                    "step": "add_to_knowledge_base",
                    "description": "Add new document to Qdrant",
                    "status": "success",
                    "details": "New document added to knowledge base"
                }
            ]
        
        elif predicted_action == 'REMOVE_DOCUMENT':
            steps = [
                {
                    "step": "search_document_to_remove",
                    "description": "Find document to remove from knowledge base",
                    "status": "success",
                    "details": "Located document for removal"
                },
                {
                    "step": "remove_from_knowledge_base",
                    "description": "Remove document from Qdrant",
                    "status": "success",
                    "details": "Document removed from active knowledge base"
                }
            ]
        
        elif predicted_action == 'ADD_TEMPORARY_NOTE':
            steps = [
                {
                    "step": "create_temporary_note",
                    "description": "Create temporary regulatory note",
                    "status": "success",
                    "details": f"Temporary note: {notification.get('Title', '')[:50]}..."
                },
                {
                    "step": "set_expiry_reminder",
                    "description": "Set reminder for temporary measure",
                    "status": "success",
                    "details": "Expiry reminder configured"
                }
            ]
        
        else:  # NO_ACTION
            steps = [
                {
                    "step": "log_notification",
                    "description": "Log notification for record keeping",
                    "status": "success",
                    "details": "Notification logged without KB changes"
                }
            ]
        
        return steps
    
    def process_notification(self, notification: Dict, index: int) -> Dict:
        """Process a single notification and return detailed results"""
        title = notification.get('Title', 'Unknown Title')
        
        # Predict category and action
        predicted_category = self.predict_category(title)
        predicted_action = self.predict_action(title, predicted_category)
        confidence = self.get_confidence(title, predicted_category)
        
        # Simulate processing steps
        processing_steps = self.simulate_processing_steps(notification, predicted_action)
        
        # Calculate success rate
        successful_steps = sum(1 for step in processing_steps if step['status'] == 'success')
        total_steps = len(processing_steps)
        success_rate = (successful_steps / total_steps * 100) if total_steps > 0 else 0
        
        # Extract metadata
        rbi_references = []
        watermark = notification.get('Watermark', '')
        if 'RBI/' in watermark:
            import re
            rbi_refs = re.findall(r'RBI/\d{4}-\d{2}/\d+', watermark)
            rbi_references.extend(rbi_refs)
        
        result = {
            "notification_id": index,
            "title": title,
            "year": notification.get('Year', ''),
            "pdf_link": notification.get('PDF Link', ''),
            "watermark": watermark,
            "prediction": {
                "category": predicted_category,
                "action_type": predicted_action,
                "confidence": confidence,
                "requires_kb_update": predicted_action != 'NO_ACTION'
            },
            "processing": {
                "steps": processing_steps,
                "total_steps": total_steps,
                "successful_steps": successful_steps,
                "success_rate": round(success_rate, 1),
                "overall_status": "success" if success_rate == 100 else "partial" if success_rate > 0 else "failed"
            },
            "metadata": {
                "rbi_references": rbi_references,
                "document_type": self.get_document_type(title),
                "department": self.extract_department(watermark)
            },
            "estimated_processing_time": self.estimate_processing_time(predicted_action),
            "priority": self.get_priority(predicted_category, confidence)
        }
        
        return result
    
    def get_document_type(self, title: str) -> str:
        """Determine document type from title"""
        title_lower = title.lower()
        if 'master direction' in title_lower:
            return 'Master Direction'
        elif 'circular' in title_lower:
            return 'Circular'
        elif 'directions' in title_lower:
            return 'Directions'
        elif 'guidelines' in title_lower:
            return 'Guidelines'
        elif 'regulations' in title_lower:
            return 'Regulations'
        else:
            return 'Other'
    
    def extract_department(self, watermark: str) -> str:
        """Extract department from watermark"""
        departments = ['DBOD', 'DPSS', 'DOR', 'FMRD', 'CENTRAL OFFICE']
        for dept in departments:
            if dept in watermark:
                return dept
        return 'Unknown'
    
    def estimate_processing_time(self, action_type: str) -> str:
        """Estimate processing time for different actions"""
        time_estimates = {
            'UPDATE_DOCUMENT': '5-10 minutes',
            'ADD_DOCUMENT': '3-7 minutes',
            'REMOVE_DOCUMENT': '1-2 minutes',
            'ADD_TEMPORARY_NOTE': '1-3 minutes',
            'NO_ACTION': '< 1 minute'
        }
        return time_estimates.get(action_type, '2-5 minutes')
    
    def get_priority(self, category: str, confidence: str) -> str:
        """Determine processing priority"""
        if category in ['Amendment', 'Repealed/Withdrawn'] and confidence == 'HIGH':
            return 'HIGH'
        elif category in ['New_Regulatory_Issuance', 'Superseded']:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def run_comprehensive_test(self, max_notifications: int = None) -> bool:
        """Run comprehensive test on all notifications"""
        if not self.load_notifications():
            return False
        
        # Limit notifications if specified
        test_notifications = self.notifications
        if max_notifications:
            test_notifications = self.notifications[:max_notifications]
        
        logger.info(f"🧪 Running comprehensive test on {len(test_notifications)} notifications")
        
        self.results = []
        for i, notification in enumerate(test_notifications, 1):
            if i % 50 == 0 or i == len(test_notifications):
                logger.info(f"   ✅ Processed {i}/{len(test_notifications)} notifications")
            
            result = self.process_notification(notification, i)
            self.results.append(result)
        
        return True
    
    def generate_summary(self) -> Dict:
        """Generate summary statistics"""
        if not self.results:
            return {}
        
        total = len(self.results)
        categories = {}
        actions = {}
        confidence_levels = {}
        priorities = {}
        success_rates = []
        
        for result in self.results:
            pred = result['prediction']
            proc = result['processing']
            
            categories[pred['category']] = categories.get(pred['category'], 0) + 1
            actions[pred['action_type']] = actions.get(pred['action_type'], 0) + 1
            confidence_levels[pred['confidence']] = confidence_levels.get(pred['confidence'], 0) + 1
            priorities[result['priority']] = priorities.get(result['priority'], 0) + 1
            success_rates.append(proc['success_rate'])
        
        avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
        
        return {
            "total_notifications": total,
            "category_distribution": categories,
            "action_distribution": actions,
            "confidence_distribution": confidence_levels,
            "priority_distribution": priorities,
            "average_success_rate": round(avg_success_rate, 1),
            "kb_updates_required": sum(1 for r in self.results if r['prediction']['requires_kb_update']),
            "high_priority_notifications": priorities.get('HIGH', 0),
            "processing_time_estimate": f"{total * 3}-{total * 8} minutes total"
        }
    
    def save_results(self, output_file: str = "comprehensive_pipeline_results.json") -> bool:
        """Save results to JSON file"""
        try:
            output = {
                "metadata": {
                    "test_date": datetime.now().isoformat(),
                    "total_notifications": len(self.results),
                    "test_type": "comprehensive_pipeline_simulation",
                    "version": "2.0"
                },
                "summary": self.generate_summary(),
                "detailed_results": self.results
            }
            
            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Results saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            return False


def main():
    """Main function"""
    logger.info("🚀 Starting Comprehensive Pipeline Test")
    logger.info("=" * 80)
    
    # Initialize tester
    tester = ComprehensivePipelineTest()
    
    # Run comprehensive test (limit to 200 for full dataset)
    if not tester.run_comprehensive_test(max_notifications=200):
        logger.error("Test failed")
        return
    
    # Generate and display summary
    summary = tester.generate_summary()
    logger.info(f"\n📊 Test Summary:")
    logger.info(f"   Total Notifications: {summary['total_notifications']}")
    logger.info(f"   KB Updates Required: {summary['kb_updates_required']}")
    logger.info(f"   Average Success Rate: {summary['average_success_rate']}%")
    logger.info(f"   High Priority: {summary['high_priority_notifications']}")
    logger.info(f"   Estimated Processing Time: {summary['processing_time_estimate']}")
    
    # Save results
    if tester.save_results():
        logger.info(f"✅ Comprehensive test completed successfully!")
    else:
        logger.error(f"❌ Failed to save results")


if __name__ == "__main__":
    main()
