import requests
import time

SRC_QDRANT_URL = "http://**************:6333"
DST_QDRANT_URL = "http://localhost:6333"
BATCH_SIZE = 10

def get_collections(qdrant_url):
    resp = requests.get(f"{qdrant_url}/collections")
    resp.raise_for_status()
    return [c["name"] for c in resp.json()["result"]["collections"]]

def get_collection_info(qdrant_url, collection):
    resp = requests.get(f"{qdrant_url}/collections/{collection}")
    resp.raise_for_status()
    return resp.json()["result"]

def delete_collection(qdrant_url, collection):
    resp = requests.delete(f"{qdrant_url}/collections/{collection}")
    if resp.ok:
        print(f"Dropped existing collection {collection}.")

def create_collection(qdrant_url, collection, info):
    config = info["config"]["params"]
    payload = {
        "vectors": config["vectors"],  # named or default
        "sparse_vectors": config.get("sparse_vectors", {}),  # dict of sparse vectors
        "shard_number": config.get("shard_number", 1),
        "replication_factor": config.get("replication_factor", 1),
        "write_consistency_factor": config.get("write_consistency_factor", 1),
        "on_disk_payload": config.get("on_disk_payload", False),
    }
    # Copy over any advanced configs if present
    for key in [
        "hnsw_config", "optimizer_config",
        "wal_config", "quantization_config", "strict_mode_config"
    ]:
        v = info["config"].get(key)
        if v is not None:
            payload[key] = v

    print(f"CREATING COLLECTION {collection} with payload:\n{payload}\n")
    resp = requests.put(f"{qdrant_url}/collections/{collection}", json=payload)
    if not resp.ok and "already exists" not in resp.text:
        print(resp.text)
        resp.raise_for_status()
    print(f"Created collection {collection}.")

def get_total_points(qdrant_url, collection):
    resp = requests.get(f"{qdrant_url}/collections/{collection}")
    resp.raise_for_status()
    return resp.json()["result"]["points_count"]

def export_points(qdrant_url, collection, offset=None, limit=10):
    url = f"{qdrant_url}/collections/{collection}/points/scroll"
    body = {"limit": limit, "with_payload": True, "with_vector": True}
    if offset is not None:
        body["offset"] = offset
    resp = requests.post(url, json=body)
    resp.raise_for_status()
    out = resp.json()["result"]
    return out["points"], out.get("next_page_offset", None)

def get_vector_schema(info):
    vectors = info["config"]["params"].get("vectors", {})
    sparse = info["config"]["params"].get("sparse_vectors", {})
    # Single-vector: { "size": 1536, ... }
    if isinstance(vectors, dict) and "size" in vectors:
        return {"__single_vector__": vectors["size"]}
    else:
        names = set(vectors.keys()) if isinstance(vectors, dict) else set()
        if isinstance(sparse, dict):
            names.update(sparse.keys())
        return names

def import_point(qdrant_url, collection, point, vector_schema):
    payload = {
        "id": point["id"],
        "payload": point.get("payload", {}),
    }
    if "__single_vector__" in vector_schema:
        # Single vector collection, insert as a flat array
        if "vector" in point:
            payload["vector"] = point["vector"]
        elif "vectors" in point and isinstance(point["vectors"], dict):
            # Use the only value (should only be one)
            value = next(iter(point["vectors"].values()))
            payload["vector"] = value
        else:
            raise Exception(f"No vector found for single-vector point id={point['id']}")
        vector_info = "single"
    else:
        # Multi-vector: must be a dict of {name: vector}
        payload["vector"] = {}
        if "vectors" in point and isinstance(point["vectors"], dict):
            for name in vector_schema:
                if name in point["vectors"]:
                    payload["vector"][name] = point["vectors"][name]
        # Handle legacy: if dense vector in 'vector', assign it to the first available dense vector name
        if "vector" in point and not payload["vector"]:
            for name in vector_schema:
                payload["vector"][name] = point["vector"]
        vector_info = list(payload["vector"].keys())
    print(f"Inserting point id={point['id']}, vectors: {vector_info}")

    resp = requests.put(
        f"{qdrant_url}/collections/{collection}/points",
        params={"wait": "true"},
        json={"points": [payload]}
    )
    if not resp.ok:
        print(f"Error importing point {point['id']}: {resp.text}")

def copy_collection(collection):
    print(f"\n---\nCopying collection: {collection}")
    info = get_collection_info(SRC_QDRANT_URL, collection)
    # Always drop and recreate for schema correctness
    delete_collection(DST_QDRANT_URL, collection)
    time.sleep(0.5)
    create_collection(DST_QDRANT_URL, collection, info)

    total = get_total_points(SRC_QDRANT_URL, collection)
    print(f"Total points: {total}")
    if total == 0:
        print("Skipping empty collection.")
        return

    vector_schema = get_vector_schema(info)
    print(f"Vector schema: {vector_schema}")
    next_offset = None
    copied = 0

    while True:
        points, next_offset = export_points(SRC_QDRANT_URL, collection, offset=next_offset, limit=BATCH_SIZE)
        print(f"Exported {len(points)} points")
        if not points:
            break
        for p in points:
            import_point(DST_QDRANT_URL, collection, p, vector_schema)
            copied += 1
            time.sleep(0.02)  # Lower this if you want faster import

        if not next_offset:
            break

    print(f"Finished copying {collection}. {copied} points transferred.")

def main():
    collections = get_collections(SRC_QDRANT_URL)
    collections = ['rbi_master_direction']
    print(f"Found collections: {collections}")
    # Uncomment the following line to test a single collection
    # collections = ['your_collection_name']
    for collection in collections:
        copy_collection(collection)
    print("\nAll collections copied.")

if __name__ == "__main__":
    main()
