#!/usr/bin/env python3
"""
Test the complete notification processing pipeline with enhanced link scraping
"""

import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_complete_pipeline():
    """Test the complete notification processing pipeline"""
    
    print("🧪 Testing Complete Notification Processing Pipeline")
    print("=" * 80)
    
    # Test notifications with different scenarios
    test_notifications = [
        {
            "id": "test_1",
            "title": "Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025 - Amendment to FEMA 23(R)/2015-RB",
            "rss_description": """
            <p>Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025</p>
            <p>Amendment to Foreign Exchange Management (Remittance of Assets) Regulations, 2015</p>
            <p>In exercise of the powers conferred by clause (a) of sub-section (3) of section 46 of the Foreign Exchange Management Act, 1999 (42 of 1999), the Reserve Bank of India hereby makes the following amendment to the Foreign Exchange Management (Remittance of Assets) Regulations, 2015 (Notification No. FEMA 23(R)/2015-RB dated 1 May 2015):</p>
            <p>In Regulation 4 of the principal regulations, the following sub-regulation shall be inserted after sub-regulation (3):</p>
            <p>"(4) A person resident in India may remit up to USD 250,000 per financial year for investment in overseas securities."</p>
            <p><a href='https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF'>Download Amendment PDF</a></p>
            <p><a href='https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12567&Mode=0'>View Notification</a></p>
            """,
            "expected_outcome": {
                "category": "Amendment",
                "pdf_url": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF",
                "document_actions": [
                    {"action_type": "ADD_DOCUMENT", "document_id": "FEMA 23(R)/(6)/2025-RB"},
                    {"action_type": "UPDATE_DOCUMENT", "document_id": "FEMA 23(R)/2015-RB"}
                ],
                "reference_numbers": ["FEMA 23(R)/(6)/2025-RB", "FEMA 23(R)/2015-RB"]
            }
        },
        {
            "id": "test_2", 
            "title": "Notification No. RBI/2025-26/64 dated 2 July 2025 - Master Direction on Regulatory Retail Portfolio",
            "rss_description": """
            <p>Notification No. RBI/2025-26/64 dated 2 July 2025</p>
            <p>Master Direction on Regulatory Retail Portfolio</p>
            <p>This direction is issued in exercise of powers conferred under Section 35A of the Banking Regulation Act, 1949.</p>
            <p>The direction shall be applicable to all commercial banks operating in India.</p>
            <p><a href='/rdocs/notification/PDFs/RBI202526064.PDF'>Download Master Direction</a></p>
            <p><a href='https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566'>View Online</a></p>
            """,
            "expected_outcome": {
                "category": "New_Regulatory_Issuance",
                "pdf_url": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/RBI202526064.PDF",
                "document_actions": [
                    {"action_type": "ADD_DOCUMENT", "document_id": "RBI/2025-26/64"}
                ],
                "reference_numbers": ["RBI/2025-26/64"]
            }
        },
        {
            "id": "test_3",
            "title": "Circular No. DBOD.No.Leg.BC.21/09.07.007/2025-26 dated 15 June 2025 - Guidelines on Credit Risk Management",
            "rss_description": """
            <p>Circular No. DBOD.No.Leg.BC.21/09.07.007/2025-26 dated 15 June 2025</p>
            <p>Guidelines on Credit Risk Management for Commercial Banks</p>
            <p>In supersession of our earlier circular DBOD.No.Leg.BC.15/09.07.007/2020-21 dated 10 March 2021, the following guidelines are issued:</p>
            <p>These guidelines shall be effective from 1 October 2025.</p>
            <p><a href='https://rbidocs.rbi.org.in/rdocs/notification/PDFs/DBODLEGBC21092025.PDF'>Download Circular</a></p>
            """,
            "expected_outcome": {
                "category": "Superseded",
                "pdf_url": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/DBODLEGBC21092025.PDF",
                "document_actions": [
                    {"action_type": "ADD_DOCUMENT", "document_id": "DBOD.No.Leg.BC.21/09.07.007/2025-26"},
                    {"action_type": "REMOVE_DOCUMENT", "document_id": "DBOD.No.Leg.BC.15/09.07.007/2020-21"}
                ],
                "reference_numbers": ["DBOD.No.Leg.BC.21/09.07.007/2025-26", "DBOD.No.Leg.BC.15/09.07.007/2020-21"]
            }
        }
    ]
    
    # Import the enhanced functions
    import requests
    from bs4 import BeautifulSoup
    import re
    
    def _process_relative_url(href):
        """Process relative URLs to absolute URLs"""
        if href.startswith('http'):
            return href
        if not href.startswith('http'):
            if 'rdocs' in href or '.pdf' in href.lower():
                clean_href = href.lstrip('/')
                return f"https://rbidocs.rbi.org.in/{clean_href}"
            else:
                clean_href = href.lstrip('/')
                return f"https://www.rbi.org.in/{clean_href}"
        return href
    
    def extract_anchor_information(html_content):
        """Extract comprehensive anchor information"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            anchor_info = {
                'anchors': [],
                'reference_numbers': [],
                'rbi_links': [],
                'document_links': [],
                'pdf_links': [],
                'rbi_page_urls': [],
                'navigation_links': []
            }
            
            # Extract all anchor tags
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                text = link.get_text(strip=True)
                
                if not href or not text:
                    continue
                
                processed_href = _process_relative_url(href)
                
                anchor_data = {
                    'text': text,
                    'href': processed_href,
                    'original_href': href
                }
                anchor_info['anchors'].append(anchor_data)
                
                # Categorize links
                if processed_href.lower().endswith('.pdf'):
                    anchor_info['pdf_links'].append(processed_href)
                    anchor_info['document_links'].append(processed_href)
                
                elif 'rbi.org.in' in processed_href:
                    if 'NotificationUser.aspx' in processed_href or 'Scripts/' in processed_href:
                        anchor_info['rbi_page_urls'].append(processed_href)
                    else:
                        anchor_info['rbi_links'].append(processed_href)
            
            # Extract reference numbers from text
            text_content = soup.get_text()
            ref_patterns = [
                r'FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
                r'FEMA\s+\d+\([A-Z]\)/\d{4}-[A-Z]+',  # FEMA 23(R)/2015-RB
                r'RBI/\d{4}-\d{2}/\d+',  # RBI/2025-26/64
                r'[A-Z]+\.No\.[A-Za-z\.]+\d+/[\d\.]+/\d{4}-?\d*'  # DBOD.No.Leg.BC.21/09.07.007/2025-26
            ]
            
            for pattern in ref_patterns:
                matches = re.findall(pattern, text_content)
                for match in matches:
                    clean_match = re.sub(r'\s+', ' ', match.strip())
                    if clean_match not in anchor_info['reference_numbers']:
                        anchor_info['reference_numbers'].append(clean_match)
            
            return anchor_info
            
        except Exception as e:
            logger.error(f"Error extracting anchor information: {e}")
            return {
                'anchors': [], 'reference_numbers': [], 'rbi_links': [],
                'document_links': [], 'pdf_links': [], 'rbi_page_urls': [], 'navigation_links': []
            }
    
    def create_enhanced_content(notification, anchor_info):
        """Create enhanced content for LLM processing"""
        enhanced_content = notification['rss_description'][:2000]
        enhanced_content += f"\n\n=== EXTRACTED INFORMATION FOR PROCESSING ==="
        
        if anchor_info['reference_numbers']:
            enhanced_content += f"\nEXTRACTED_REFERENCE_NUMBERS: {', '.join(anchor_info['reference_numbers'])}"
        
        if anchor_info['pdf_links']:
            enhanced_content += f"\nEXTRACTED_PDF_LINKS: {', '.join(anchor_info['pdf_links'][:3])}"
            enhanced_content += f"\n*** USE THESE EXACT URLs FOR new_document_url - DO NOT MODIFY ***"
        else:
            enhanced_content += f"\nEXTRACTED_PDF_LINKS: (none found)"
            enhanced_content += f"\n*** NO PDF LINKS AVAILABLE - LEAVE new_document_url EMPTY ***"
        
        enhanced_content += f"\n=== END EXTRACTED INFORMATION ==="
        
        return enhanced_content
    
    # Process each test notification
    results = []
    
    for notification in test_notifications:
        print(f"\n🔍 Processing: {notification['id']} - {notification['title'][:50]}...")
        print("-" * 80)
        
        # Step 1: Extract anchor information
        anchor_info = extract_anchor_information(notification['rss_description'])
        
        print(f"✅ Anchor extraction:")
        print(f"   📄 PDF links: {len(anchor_info['pdf_links'])}")
        print(f"   📋 Reference numbers: {len(anchor_info['reference_numbers'])}")
        
        # Step 2: Create enhanced content
        enhanced_content = create_enhanced_content(notification, anchor_info)
        
        print(f"✅ Enhanced content: {len(enhanced_content)} characters")
        
        # Step 3: Validate against expected outcomes
        expected = notification['expected_outcome']
        
        # Check PDF URL
        pdf_correct = False
        if expected['pdf_url'] in anchor_info['pdf_links']:
            print(f"   ✅ PDF URL correct: {expected['pdf_url']}")
            pdf_correct = True
        else:
            print(f"   ❌ PDF URL mismatch:")
            print(f"      Expected: {expected['pdf_url']}")
            print(f"      Found: {anchor_info['pdf_links']}")
        
        # Check reference numbers
        ref_correct = True
        for expected_ref in expected['reference_numbers']:
            if expected_ref in anchor_info['reference_numbers']:
                print(f"   ✅ Reference found: {expected_ref}")
            else:
                print(f"   ❌ Reference missing: {expected_ref}")
                ref_correct = False
        
        # Store results
        test_result = {
            "notification_id": notification['id'],
            "title": notification['title'],
            "anchor_info": anchor_info,
            "enhanced_content_length": len(enhanced_content),
            "validation": {
                "pdf_correct": pdf_correct,
                "ref_correct": ref_correct,
                "expected_pdf": expected['pdf_url'],
                "found_pdfs": anchor_info['pdf_links'],
                "expected_refs": expected['reference_numbers'],
                "found_refs": anchor_info['reference_numbers']
            }
        }
        
        results.append(test_result)
    
    # Summary
    print(f"\n📊 Pipeline Test Summary")
    print("=" * 80)
    
    pdf_correct_count = sum(1 for r in results if r['validation']['pdf_correct'])
    ref_correct_count = sum(1 for r in results if r['validation']['ref_correct'])
    
    print(f"✅ PDF extraction accuracy: {pdf_correct_count}/{len(results)} tests passed")
    print(f"✅ Reference extraction accuracy: {ref_correct_count}/{len(results)} tests passed")
    print(f"✅ Overall pipeline success: {min(pdf_correct_count, ref_correct_count)}/{len(results)} tests fully passed")
    
    # Save results
    final_results = {
        "test_timestamp": datetime.now().isoformat(),
        "summary": {
            "total_tests": len(results),
            "pdf_accuracy": f"{pdf_correct_count}/{len(results)}",
            "ref_accuracy": f"{ref_correct_count}/{len(results)}",
            "overall_success": f"{min(pdf_correct_count, ref_correct_count)}/{len(results)}"
        },
        "detailed_results": results
    }
    
    with open('complete_notification_pipeline_test_results.json', 'w') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Results saved to: complete_notification_pipeline_test_results.json")
    
    if pdf_correct_count == len(results) and ref_correct_count == len(results):
        print(f"\n🎉 All tests passed! Enhanced link scraping is working perfectly!")
        return True
    else:
        print(f"\n⚠️ Some tests failed. Check the detailed results for issues.")
        return False

if __name__ == "__main__":
    try:
        success = test_complete_pipeline()
        if success:
            print(f"\n✅ Complete notification pipeline test PASSED!")
        else:
            print(f"\n❌ Complete notification pipeline test FAILED!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
