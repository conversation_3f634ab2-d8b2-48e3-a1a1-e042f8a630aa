function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    const successDiv = document.getElementById('successMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    successDiv.style.display = 'none';
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

function showSuccess(message) {
    const errorDiv = document.getElementById('errorMessage');
    const successDiv = document.getElementById('successMessage');
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    errorDiv.style.display = 'none';
    setTimeout(() => {
        successDiv.style.display = 'none';
    }, 5000);
}

async function processData() {
    const button = document.getElementById('downloadBtn');
    const spinner = document.getElementById('spinner');
    
    try {
        button.disabled = true;
        spinner.style.display = 'inline-block';

        const filterType = document.querySelector('input[name="filterType"]:checked').value;
        let dates = {};

        switch(filterType) {
            case 'single':
                dates.single = document.getElementById('singleDateInput').value;
                if (!dates.single) throw new Error('Please select a date');
                break;
            case 'range':
                dates.from = document.getElementById('fromDate').value;
                dates.to = document.getElementById('toDate').value;
                if (!dates.from || !dates.to) throw new Error('Please select both from and to dates');
                if (dates.from > dates.to) throw new Error('From date must be before to date');
                break;
            case 'greater':
                dates.after = document.getElementById('greaterThanDate').value;
                if (!dates.after) throw new Error('Please select a date');
                break;
            case 'less':
                dates.before = document.getElementById('lessThanDate').value;
                if (!dates.before) throw new Error('Please select a date');
                break;
        }

        const queryParams = new URLSearchParams({
            filterType: filterType,
            startDate: dates.from || dates.single || dates.after || '',
            endDate: dates.to || dates.before || ''
        });

        console.log('Sending request with params:', queryParams.toString());
        const response = await fetch(`/report/get-data?${queryParams.toString()}`);
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to fetch data');
        }

        const data = await response.json();
        console.log('Received data:', data);
        
        if (!data || data.length === 0) {
            throw new Error('No data found for the selected criteria');
        }

        function downloadExcel(data) {
            // Create session ID mapping
            const sessionMap = new Map();
            let sessionCounter = 1;

            // First pass: create mapping for unique session IDs
            data.forEach(row => {
                const sessionId = row[2]; // Session ID is at index 2
                if (!sessionMap.has(sessionId)) {
                    sessionMap.set(sessionId, `S${String(sessionCounter).padStart(2, '0')}`);
                    sessionCounter++;
                }
            });

            // Create mapped data with simplified session IDs
            const mappedData = data.map(row => {
                const newRow = [...row];
                newRow[2] = sessionMap.get(row[2]); // Replace session ID with mapped value
                return newRow;
            });

            const ws = XLSX.utils.aoa_to_sheet([
                ['UserName', 'Email', 'Session ID', 'Question', 'Response', 'Upvote', 'Downvote', 'Feedback', 'Reference Sources', 'Timestamp', 'Copied', 'Copy Count'],
                ...mappedData
            ]);

            // Add session ID mapping as a new sheet
            const mappingData = [
                ['Original Session ID', 'Mapped ID'],
                ...Array.from(sessionMap.entries()).map(([original, mapped]) => [original, mapped])
            ];
            const mappingWs = XLSX.utils.aoa_to_sheet(mappingData);

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Chat Data');
            XLSX.utils.book_append_sheet(wb, mappingWs, 'Session ID Mapping');
            
            XLSX.writeFile(wb, `chat_data_${new Date().toISOString().split('T')[0]}.xlsx`);
        }

        downloadExcel(data);
        showSuccess(`Successfully exported ${data.length} records to Excel`);
    } catch (error) {
        console.error('Error:', error);
        showError(error.message);
    } finally {
        button.disabled = false;
        spinner.style.display = 'none';
    }
}

// Show/hide date inputs based on filter type
document.querySelectorAll('input[name="filterType"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
        document.querySelectorAll('.date-inputs').forEach(div => {
            div.classList.remove('active');
        });
        document.getElementById(e.target.value === 'single' ? 'singleDate' : 
                             e.target.value === 'range' ? 'dateRange' :
                             e.target.value === 'greater' ? 'greaterThan' : 'lessThan')
                .classList.add('active');
    });
});

document.getElementById('downloadBtn').addEventListener('click', processData);
