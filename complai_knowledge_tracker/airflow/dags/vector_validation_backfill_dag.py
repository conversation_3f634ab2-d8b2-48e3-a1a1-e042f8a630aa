from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import logging
import sys
from pathlib import Path

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))
from utils.qdrant_utils import qdrant_manager, splade_encoder
from utils.openai_utils import en_embeddings
from utils.config import config
from qdrant_client import QdrantClient, models

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_and_backfill_vectors(**context):
    """
    Check all points in all collections and update any missing vectors.
    Ensures all points have dense, BM25, and SPLADE vectors.
    """
    from utils.document_pipeline import DocumentTypeMapper

    # Import centralized utilities
    from utils.qdrant_utils import qdrant_manager

    batch_size = int(Variable.get("vector_backfill_batch_size", default_var="100"))

    # Get all collection names from DocumentTypeMapper
    collection_names = DocumentTypeMapper.get_all_collection_names()

    logger.info(f"Starting vector validation and backfill for collections: {collection_names}")
    logger.info(f"Vector DB URL: {qdrant_manager.config.full_url}")
    logger.info(f"Batch size: {batch_size}")

    # Use centralized Qdrant client
    client = qdrant_manager.client
    
    # Vector names to check
    expected_vectors = {
        "dense": "dense vector",
        config.qdrant.sparse_vector_name: "BM25 sparse vector",
        config.qdrant.splade_vector_name: "SPLADE sparse vector"
    }

    logger.info(f"Expected vectors: {list(expected_vectors.keys())}")

    # Overall statistics
    overall_stats = {
        "total_collections": len(collection_names),
        "collections_processed": 0,
        "total_points": 0,
        "points_missing_vectors": 0,
        "points_updated": 0,
        "missing_dense": 0,
        "missing_bm25": 0,
        "missing_splade": 0,
        "errors": 0,
        "collection_details": {}
    }

    # Process each collection
    for collection_name in collection_names:
        logger.info(f"\n{'='*60}")
        logger.info(f"Processing collection: {collection_name}")
        logger.info(f"{'='*60}")

        # Check if collection exists
        try:
            collections = client.get_collections()
            existing_collections = [col.name for col in collections.collections]

            if collection_name not in existing_collections:
                logger.warning(f"Collection '{collection_name}' does not exist, skipping...")
                overall_stats["collection_details"][collection_name] = {"status": "not_found"}
                continue

        except Exception as e:
            logger.error(f"Error checking collection existence for {collection_name}: {e}")
            overall_stats["errors"] += 1
            overall_stats["collection_details"][collection_name] = {"status": "error", "error": str(e)}
            continue

        # Collection-specific statistics
        collection_stats = {
            "total_points": 0,
            "points_missing_vectors": 0,
            "points_updated": 0,
            "missing_dense": 0,
            "missing_bm25": 0,
            "missing_splade": 0,
            "errors": 0
        }

        try:
            # Scroll through all points in the collection
            offset = None

            while True:
                # Get batch of points with vectors
                scroll_result = client.scroll(
                    collection_name=collection_name,
                    limit=batch_size,
                    offset=offset,
                    with_vectors=True,
                    with_payload=True
                )

                points = scroll_result[0]
                next_offset = scroll_result[1]

                if not points:
                    break

                collection_stats["total_points"] += len(points)
                logger.info(f"Processing batch of {len(points)} points...")

                for point in points:
                    try:
                        point_id = point.id
                        missing_vectors = []

                        # Check if point has multi-vector format
                        if hasattr(point, 'vector') and isinstance(point.vector, dict):
                            # Multi-vector format - check each expected vector
                            for vector_name in expected_vectors.keys():
                                if vector_name not in point.vector or point.vector[vector_name] is None:
                                    missing_vectors.append(vector_name)

                        elif hasattr(point, 'vector') and point.vector is not None:
                            # Single vector format - assume it's dense, missing sparse vectors
                            missing_vectors = [config.qdrant.sparse_vector_name, config.qdrant.splade_vector_name]

                        else:
                            # No vectors at all
                            missing_vectors = list(expected_vectors.keys())

                        # Update statistics
                        if missing_vectors:
                            collection_stats["points_missing_vectors"] += 1
                            for vector_name in missing_vectors:
                                if vector_name == "dense":
                                    collection_stats["missing_dense"] += 1
                                elif vector_name == config.qdrant.sparse_vector_name:
                                    collection_stats["missing_bm25"] += 1
                                elif vector_name == config.qdrant.splade_vector_name:
                                    collection_stats["missing_splade"] += 1

                        # If vectors are missing, generate and update them
                        if missing_vectors:
                            logger.info(f"Point {point_id} missing vectors: {missing_vectors}")

                            # Get text content for embedding generation
                            text_content = None
                            if hasattr(point, 'payload') and point.payload:
                                # Try different content fields
                                for content_field in ['content', 'chunk_text', 'page_content', 'text']:
                                    if content_field in point.payload and point.payload[content_field]:
                                        text_content = point.payload[content_field]
                                        break

                            if not text_content:
                                logger.warning(f"No text content found for point {point_id}, skipping...")
                                collection_stats["errors"] += 1
                                continue

                            # Use QdrantManager to update the point with missing vectors
                            success = qdrant_manager.upsert_point(
                                collection_name=collection_name,
                                point_id=point_id,
                                payload=point.payload,
                                text_for_embedding=text_content
                            )

                            if success:
                                collection_stats["points_updated"] += 1
                                logger.info(f"✅ Updated point {point_id} with missing vectors: {missing_vectors}")
                            else:
                                collection_stats["errors"] += 1
                                logger.error(f"❌ Failed to update point {point_id}")

                    except Exception as e:
                        collection_stats["errors"] += 1
                        logger.error(f"Error processing point {point.id if hasattr(point, 'id') else 'unknown'}: {e}")

                # Move to next batch
                offset = next_offset
                if offset is None:
                    break

                # Log progress
                logger.info(f"Processed {collection_stats['total_points']} points so far...")

        except Exception as e:
            logger.error(f"Error during vector validation and backfill for collection {collection_name}: {e}")
            collection_stats["errors"] += 1
            overall_stats["collection_details"][collection_name] = {"status": "error", "error": str(e)}
            continue

        # Update overall statistics
        for key in ["total_points", "points_missing_vectors", "points_updated", "missing_dense", "missing_bm25", "missing_splade", "errors"]:
            overall_stats[key] += collection_stats[key]

        overall_stats["collections_processed"] += 1
        overall_stats["collection_details"][collection_name] = {
            "status": "completed",
            **collection_stats
        }

        # Log collection summary
        logger.info(f"\nCollection {collection_name} summary:")
        logger.info(f"  - Points processed: {collection_stats['total_points']}")
        logger.info(f"  - Points missing vectors: {collection_stats['points_missing_vectors']}")
        logger.info(f"  - Points updated: {collection_stats['points_updated']}")
        logger.info(f"  - Errors: {collection_stats['errors']}")

    # Final overall statistics
    logger.info("\n" + "=" * 80)
    logger.info("VECTOR VALIDATION AND BACKFILL COMPLETED FOR ALL COLLECTIONS")
    logger.info("=" * 80)
    logger.info(f"Total collections: {overall_stats['total_collections']}")
    logger.info(f"Collections processed: {overall_stats['collections_processed']}")
    logger.info(f"Total points processed: {overall_stats['total_points']}")
    logger.info(f"Points missing vectors: {overall_stats['points_missing_vectors']}")
    logger.info(f"Points successfully updated: {overall_stats['points_updated']}")
    logger.info(f"Errors encountered: {overall_stats['errors']}")
    logger.info("")
    logger.info("Missing vector breakdown:")
    logger.info(f"  - Missing dense vectors: {overall_stats['missing_dense']}")
    logger.info(f"  - Missing BM25 vectors: {overall_stats['missing_bm25']}")
    logger.info(f"  - Missing SPLADE vectors: {overall_stats['missing_splade']}")
    logger.info("")
    logger.info("Collection details:")
    for collection_name, details in overall_stats["collection_details"].items():
        logger.info(f"  - {collection_name}: {details['status']}")
        if details["status"] == "completed":
            logger.info(f"    Points: {details['total_points']}, Updated: {details['points_updated']}, Errors: {details['errors']}")
    logger.info("=" * 80)

    # Return statistics for downstream tasks
    return overall_stats

def validate_collection_exists(**context):
    """
    Validate that all target collections exist before processing.
    """
    from utils.document_pipeline import DocumentTypeMapper
    from utils.qdrant_utils import qdrant_manager

    # Get all expected collection names
    expected_collections = DocumentTypeMapper.get_all_collection_names()

    logger.info(f"Validating collections exist: {expected_collections}")

    try:
        client = qdrant_manager.client

        # Check which collections exist
        collections = client.get_collections()
        existing_collections = [col.name for col in collections.collections]

        logger.info(f"Existing collections: {existing_collections}")

        # Check each expected collection
        missing_collections = []
        existing_expected_collections = []

        for collection_name in expected_collections:
            if collection_name in existing_collections:
                existing_expected_collections.append(collection_name)
                try:
                    # Get collection info
                    collection_info = client.get_collection(collection_name)
                    logger.info(f"✅ Collection '{collection_name}' exists")
                    logger.info(f"   - Points count: {collection_info.points_count}")
                    logger.info(f"   - Vector config: {collection_info.config.params.vectors}")

                    if hasattr(collection_info.config.params, 'sparse_vectors') and collection_info.config.params.sparse_vectors:
                        logger.info(f"   - Sparse vectors: {list(collection_info.config.params.sparse_vectors.keys())}")
                except Exception as e:
                    logger.warning(f"Could not get info for collection '{collection_name}': {e}")
            else:
                missing_collections.append(collection_name)
                logger.warning(f"❌ Collection '{collection_name}' does not exist")

        if missing_collections:
            logger.warning(f"Missing collections: {missing_collections}")
            logger.info(f"Will process only existing collections: {existing_expected_collections}")

        if not existing_expected_collections:
            raise ValueError(f"None of the expected collections exist. Expected: {expected_collections}, Available: {existing_collections}")

        logger.info(f"Validation completed. Will process {len(existing_expected_collections)} collections.")
        return True

    except Exception as e:
        logger.error(f"Collection validation failed: {e}")
        raise

# Define default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'vector_validation_backfill_dag',
    default_args=default_args,
    description='Validate and backfill missing vectors in Qdrant collection',
    schedule_interval=None,  # Manual trigger only
    catchup=False,
    tags=['qdrant', 'vectors', 'validation', 'backfill']
)

# Define tasks
validate_collection_task = PythonOperator(
    task_id='validate_collection_exists',
    python_callable=validate_collection_exists,
    provide_context=True,
    dag=dag,
)

backfill_vectors_task = PythonOperator(
    task_id='validate_and_backfill_vectors',
    python_callable=validate_and_backfill_vectors,
    provide_context=True,
    dag=dag,
)

# Set task dependencies
validate_collection_task >> backfill_vectors_task
