"""
Comprehensive error handling and logging utilities
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime
from enum import Enum

from .config import config

class ErrorSeverity(str, Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(str, Enum):
    """Error categories for better classification"""
    CONFIGURATION = "configuration"
    NETWORK = "network"
    DATABASE = "database"
    PROCESSING = "processing"
    VALIDATION = "validation"
    EXTERNAL_API = "external_api"
    FILE_SYSTEM = "file_system"
    UNKNOWN = "unknown"

class ErrorHandler:
    """Centralized error handling and logging"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup enhanced logging configuration"""
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Ensure logger has proper handlers
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_error(
        self,
        error: Exception,
        context: Dict[str, Any],
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        additional_info: Optional[Dict[str, Any]] = None
    ):
        """
        Log error with comprehensive context information
        """
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "severity": severity.value,
            "category": category.value,
            "context": context,
            "traceback": traceback.format_exc()
        }
        
        if additional_info:
            error_info["additional_info"] = additional_info
        
        # Log based on severity
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"CRITICAL ERROR: {error_info}")
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(f"HIGH SEVERITY ERROR: {error_info}")
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.error(f"ERROR: {error_info}")
        else:
            self.logger.warning(f"LOW SEVERITY ERROR: {error_info}")
        
        return error_info
    
    def log_warning(
        self,
        message: str,
        context: Dict[str, Any],
        additional_info: Optional[Dict[str, Any]] = None
    ):
        """Log warning with context"""
        warning_info = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "context": context
        }
        
        if additional_info:
            warning_info["additional_info"] = additional_info
        
        self.logger.warning(f"WARNING: {warning_info}")
        return warning_info
    
    def log_info(
        self,
        message: str,
        context: Dict[str, Any],
        additional_info: Optional[Dict[str, Any]] = None
    ):
        """Log info with context"""
        info_data = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "context": context
        }
        
        if additional_info:
            info_data["additional_info"] = additional_info
        
        self.logger.info(f"INFO: {info_data}")
        return info_data

# Global error handler instance
error_handler = ErrorHandler()

def handle_errors(
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    reraise: bool = True,
    default_return: Any = None
):
    """
    Decorator for automatic error handling and logging
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "module": func.__module__,
                    "args": str(args)[:200],  # Limit length
                    "kwargs": str(kwargs)[:200]  # Limit length
                }
                
                error_handler.log_error(
                    error=e,
                    context=context,
                    severity=severity,
                    category=category
                )
                
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator

def safe_execute(
    func: Callable,
    *args,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    default_return: Any = None,
    context: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Any:
    """
    Safely execute a function with error handling
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        exec_context = {
            "function": func.__name__,
            "module": getattr(func, '__module__', 'unknown'),
        }
        
        if context:
            exec_context.update(context)
        
        error_handler.log_error(
            error=e,
            context=exec_context,
            severity=severity,
            category=category
        )
        
        return default_return

class RetryHandler:
    """Handle retries with exponential backoff"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.logger = logging.getLogger(__name__)
    
    def retry_with_backoff(
        self,
        func: Callable,
        *args,
        exceptions: tuple = (Exception,),
        **kwargs
    ) -> Any:
        """
        Retry function with exponential backoff
        """
        import time
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    # Final attempt failed
                    error_handler.log_error(
                        error=e,
                        context={
                            "function": func.__name__,
                            "attempt": attempt + 1,
                            "max_retries": self.max_retries
                        },
                        severity=ErrorSeverity.HIGH,
                        category=ErrorCategory.PROCESSING
                    )
                    raise
                
                # Calculate delay with exponential backoff
                delay = self.base_delay * (2 ** attempt)
                
                self.logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay} seconds..."
                )
                
                time.sleep(delay)
        
        # This should never be reached, but just in case
        if last_exception:
            raise last_exception

# Global retry handler
retry_handler = RetryHandler()

def validate_config():
    """Validate configuration and log any issues"""
    try:
        if not config.validate():
            error_handler.log_error(
                error=ValueError("Configuration validation failed"),
                context={"component": "configuration"},
                severity=ErrorSeverity.CRITICAL,
                category=ErrorCategory.CONFIGURATION
            )
            return False
        
        error_handler.log_info(
            message="Configuration validation successful",
            context={"component": "configuration"}
        )
        return True
        
    except Exception as e:
        error_handler.log_error(
            error=e,
            context={"component": "configuration"},
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.CONFIGURATION
        )
        return False

def log_performance(func: Callable) -> Callable:
    """Decorator to log function performance"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        import time
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            error_handler.log_info(
                message=f"Function {func.__name__} completed successfully",
                context={
                    "function": func.__name__,
                    "execution_time_seconds": round(execution_time, 3),
                    "module": func.__module__
                }
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            error_handler.log_error(
                error=e,
                context={
                    "function": func.__name__,
                    "execution_time_seconds": round(execution_time, 3),
                    "module": func.__module__
                },
                severity=ErrorSeverity.MEDIUM,
                category=ErrorCategory.PROCESSING
            )
            raise
    
    return wrapper
