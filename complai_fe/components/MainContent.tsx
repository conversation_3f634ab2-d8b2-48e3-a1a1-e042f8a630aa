// // //MainContent.tsx
// "use client";

// import type React from "react";
// import { useState, useEffect } from "react";
// import type { ChatMessage, RegulationInfo, ReferenceItem } from "@/types/types";
// import { HomeView } from "./HomeView";
// import { ChatSection } from "./ChatSection";
// import { useChat } from "@/contexts/ChatContext";

// interface MainContentProps {
//   activeView: "home" | "chat";
//   setActiveView: (view: "home" | "chat") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode:string | null) =>void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   showDetailView: boolean;
//   isChatHistoryCollapsed?: boolean;
//   setReferences: (references: ReferenceItem[]) => void;
// }

// export function MainContent({
//   activeView,
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   showDetailView,
//   isChatHistoryCollapsed = false,
//   setReferences,
// }: MainContentProps) {
//   const [inputValue, setInputValue] = useState("");
//   const [initialQuery, setInitialQuery] = useState<string | null>(null);
//   const { messages, isSubmitting } = useChat(); // Get messages and submission state

//   // Flag to track if we're switching to the chat view due to a search query
//   const [isSearching, setIsSearching] = useState(false);

//   // Check for an empty selected session, but only if we're not currently searching
//   useEffect(() => {
//     if (activeView === "chat" && messages.length === 0 && !isSearching && !isSubmitting) {
//       // This is likely an empty session selected from history
//       setActiveView("home");
//     }

//     // Reset the isSearching flag once we have messages or if we're no longer submitting
//     if ((messages.length > 0 || !isSubmitting) && isSearching) {
//       setIsSearching(false);
//     }
//   }, [activeView, messages, setActiveView, isSearching, isSubmitting]);

//   const handleSearch = (query: string) => {
//     if (query.trim()) {
//       // Set the searching flag to prevent flipping back to HomeView
//       setIsSearching(true);

//       if (activeView !== "chat") {
//         setInitialQuery(query.trim());
//       }
//       setActiveView("chat");
//     }
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim()) {
//       handleSearch(inputValue);
//       setInputValue("");
//     }
//   };

//   // Handle visible regulation change from ChatSection
//   const handleVisibleRegulationChange = (regulation: RegulationInfo | null) => {

//     if (regulation) {
//       setCurrentRegulation(regulation);
//     }
//   };

//   // Handle new references from ChatSection
//   const handleFetchReferences = (newReferences: ReferenceItem[]) => {
//     // console.log("checking at maincontent");
//     console.log("refrences at maincontent:",newReferences);
//     // console.log("showDetailView at maincontent:", showDetailView);
//     setReferences(newReferences);
//   };

//   // Calculate content style based on both sidebar states
//   const getContentStyle = () => {
//     const navWidth = isChatHistoryCollapsed ? 80 : 280; // Approximate widths for navigation
//     const detailWidth = showDetailView ? "40%" : "0%"; // Width of right sidebar when shown

//     // if (showDetailView && !isChatHistoryCollapsed) {
//     //   // Both sidebars are expanded - limit main content to prevent overflow
//     //   return {
//     //     flex: "0 0 calc(60% - 280px)", // 60% minus left sidebar width
//     //     maxWidth: "calc(60% - 280px)",
//     //     transition: "all 0.3s ease-in-out",
//     //   };
//     // } else if (showDetailView) {
//     //   // Only right sidebar is expanded, left is collapsed
//     //   return {
//     //     flex: "0 0 calc(60% - 80px)", // 60% minus collapsed left sidebar
//     //     maxWidth: "calc(60% - 80px)",
//     //     transition: "all 0.3s ease-in-out",
//     //   };
//     if (!isChatHistoryCollapsed) {
//       // Only left sidebar is expanded
//       return {
//         flex: "1 1 auto",
//         paddingLeft: "0",
//         transition: "all 0.3s ease-in-out",
//       };
//     } else {
//       // Both sidebars are collapsed
//       return {
//         flex: "1 1 auto",
//         transition: "all 0.3s ease-in-out",
//       };
//     }
//   };

//   return (
//     <main
//       style={getContentStyle()}
//       className={`flex flex-col overflow-y-auto p-6 min-w-0 ${
//         showDetailView ? "border-r border-[#D7CBBC]" : ""
//       }`}
//     >
//       <div className="w-full max-w-[800px] mt-8 mx-auto">
//         {activeView === "home" ? (
//           <HomeView
//             onSearch={handleSearch}
//             inputValue={inputValue}
//             setInputValue={setInputValue}
//             handleSubmit={handleSubmit}
//           />
//         ) : (
//           <ChatSection
//             setActiveView={setActiveView}
//             currentRegulation={currentRegulation}
//             selectedDocCode={selectedDocCode}
//             setselectedDocCode={setselectedDocCode}
//             setCurrentRegulation={setCurrentRegulation}
//             onViewDetail={onViewDetail}
//             initialQuery={initialQuery}
//             setInitialQuery={setInitialQuery}
//             showDetailView={showDetailView}
//             onFetchReferences={handleFetchReferences}
//           />
//         )}
//       </div>
//     </main>
//   );
// }

// "use client";

// import type React from "react";
// import { useState, useEffect } from "react";
// import type { ChatMessage, RegulationInfo, ReferenceItem } from "@/types/types";
// import { HomeView } from "./HomeView";
// import { ChatSection } from "./ChatSection";
// import { ActionItemsView } from "./ActionItemsView";
// import { useChat } from "@/contexts/ChatContext";

// interface MainContentProps {
//   activeView: "home" | "chat" | "action-items";
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode:string | null) =>void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   showDetailView: boolean;
//   isChatHistoryCollapsed?: boolean;
//   setReferences: (references: ReferenceItem[]) => void;
//   onActionItemsClick?: () => void;
// }

// export function MainContent({
//   activeView,
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   showDetailView,
//   isChatHistoryCollapsed = false,
//   setReferences,
//   onActionItemsClick,
// }: MainContentProps) {
//   const [inputValue, setInputValue] = useState("");
//   const [initialQuery, setInitialQuery] = useState<string | null>(null);
//   const { messages, isSubmitting } = useChat(); // Get messages and submission state

//   // Flag to track if we're switching to the chat view due to a search query
//   const [isSearching, setIsSearching] = useState(false);
//   console.log(selectedDocCode);
//   // Check for an empty selected session, but only if we're not currently searching
//   useEffect(() => {
//     if (activeView === "chat" && messages.length === 0 && !isSearching && !isSubmitting) {
//       // This is likely an empty session selected from history
//       setActiveView("home");
//     }

//     // Reset the isSearching flag once we have messages or if we're no longer submitting
//     if ((messages.length > 0 || !isSubmitting) && isSearching) {
//       setIsSearching(false);
//     }
//   }, [activeView, messages, setActiveView, isSearching, isSubmitting]);

//   const handleSearch = (query: string) => {
//     if (query.trim()) {
//       // Set the searching flag to prevent flipping back to HomeView
//       setIsSearching(true);

//       if (activeView !== "chat") {
//         setInitialQuery(query.trim());
//       }
//       setActiveView("chat");
//     }
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim()) {
//       handleSearch(inputValue);
//       setInputValue("");
//     }
//   };

//   // Handle visible regulation change from ChatSection
//   const handleVisibleRegulationChange = (regulation: RegulationInfo | null) => {
//     if (regulation) {
//       setCurrentRegulation(regulation);
//     }
//   };

//   // Handle new references from ChatSection
//   const handleFetchReferences = (newReferences: ReferenceItem[]) => {
//     console.log("references at maincontent:", newReferences);
//     setReferences(newReferences);
//   };

//   // Calculate content style based on both sidebar states
//   const getContentStyle = () => {
//     const navWidth = isChatHistoryCollapsed ? 80 : 280; // Approximate widths for navigation
//     const detailWidth = showDetailView ? "40%" : "0%"; // Width of right sidebar when shown

//     if (!isChatHistoryCollapsed) {
//       // Only left sidebar is expanded
//       return {
//         flex: "1 1 auto",
//         paddingLeft: "0",
//         transition: "all 0.3s ease-in-out",
//       };
//     } else {
//       // Both sidebars are collapsed
//       return {
//         flex: "1 1 auto",
//         transition: "all 0.3s ease-in-out",
//       };
//     }
//   };

//   return (
//     <main
//       style={getContentStyle()}
//       className={`flex flex-col overflow-y-auto px-2 pt-2 pb-6 min-w-0 ${
//         showDetailView ? "border-r border-[#D7CBBC]" : ""
//       }`}
//     >
//       <div className="w-full max-w-[900px] mt-8 mx-auto">
//         {activeView === "home" ? (
//           <HomeView
//             onSearch={handleSearch}
//             inputValue={inputValue}
//             setInputValue={setInputValue}
//             handleSubmit={handleSubmit}
//             onActionItemsClick={onActionItemsClick}
//           />
//         ) : activeView === "chat" ? (
//           <ChatSection
//             setActiveView={setActiveView}
//             currentRegulation={currentRegulation}
//             selectedDocCode={selectedDocCode}
//             setselectedDocCode={setselectedDocCode}
//             setCurrentRegulation={setCurrentRegulation}
//             onViewDetail={onViewDetail}
//             initialQuery={initialQuery}
//             setInitialQuery={setInitialQuery}
//             showDetailView={showDetailView}
//             onFetchReferences={handleFetchReferences}
//           />
//         ) : (
//           <ActionItemsView
//             setActiveView={setActiveView}
//             setCurrentRegulation={setCurrentRegulation}
//             selectedDocCode={selectedDocCode}
//             setselectedDocCode={setselectedDocCode}
//             onViewDetail={onViewDetail}
//             setReferences={setReferences}
//           />
//         )}
//       </div>
//     </main>
//   );
// }

// "use client";

// import type React from "react";
// import { useState, useEffect, useRef } from "react";
// import type { ChatMessage, RegulationInfo, ReferenceItem } from "@/types/types";
// import { HomeView } from "./HomeView";
// import { ChatSection } from "./ChatSection";
// import { ActionItemsView } from "./ActionItemsView";
// import { useChat } from "@/contexts/ChatContext";

// interface MainContentProps {
//   activeView: "home" | "chat" | "action-items";
//   setActiveView: (view: "home" | "chat" | "action-items") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   showDetailView: boolean;
//   isChatHistoryCollapsed?: boolean;
//   setReferences: (references: ReferenceItem[]) => void;
//   setSelectedSpecificRef: (ref: ReferenceItem | null) => void; // Add this line
//   onActionItemsClick?: () => void;
//   onClose: () => void
// }

// export function MainContent({
//   activeView,
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   showDetailView,
//   isChatHistoryCollapsed = false,
//   setReferences,
//   setSelectedSpecificRef, // Add this line
//   onActionItemsClick,
//   onClose,
// }: MainContentProps) {
//   const [inputValue, setInputValue] = useState("");
//   const [initialQuery, setInitialQuery] = useState<string | null>(null);
//   const { messages, isSubmitting } = useChat(); // Get messages and submission state
//   const chatContainerRef = useRef<HTMLDivElement>(null);
//   // Flag to track if we're switching to the chat view due to a search query
//   const [isSearching, setIsSearching] = useState(false);
//   // console.log(selectedDocCode);
//   // Check for an empty selected session, but only if we're not currently searching
//   useEffect(() => {
//     if (
//       activeView === "chat" &&
//       messages.length === 0 &&
//       !isSearching &&
//       !isSubmitting
//     ) {
//       // This is likely an empty session selected from history
//       setActiveView("home");
//     }

//     // Reset the isSearching flag once we have messages or if we're no longer submitting
//     if ((messages.length > 0 || !isSubmitting) && isSearching) {
//       setIsSearching(false);
//     }
//   }, [activeView, messages, setActiveView, isSearching, isSubmitting]);

//   const handleSearch = (query: string) => {
//     if (query.trim()) {
//       // Set the searching flag to prevent flipping back to HomeView
//       setIsSearching(true);

//       if (activeView !== "chat") {
//         setInitialQuery(query.trim());
//       }
//       setActiveView("chat");
//     }
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim()) {
//       handleSearch(inputValue);
//       setInputValue("");
//     }
//   };

//   // Handle visible regulation change from ChatSection
//   const handleVisibleRegulationChange = (regulation: RegulationInfo | null) => {
//     if (regulation) {
//       setCurrentRegulation(regulation);
//     }
//   };

//   // Handle new references from ChatSection
//   const handleFetchReferences = (newReferences: ReferenceItem[]) => {
//     // console.log("references at maincontent:", newReferences);
//     setReferences(newReferences);
//   };

//   // Calculate content style based on both sidebar states
//   const getContentStyle = () => {
//     const navWidth = isChatHistoryCollapsed ? 80 : 280; // Approximate widths for navigation
//     const detailWidth = showDetailView ? "40%" : "0%"; // Width of right sidebar when shown

//     if (!isChatHistoryCollapsed) {
//       // Only left sidebar is expanded
//       return {
//         flex: "1 1 auto",
//         paddingLeft: "0",
//         transition: "all 0.3s ease-in-out",
//       };
//     } else {
//       // Both sidebars are collapsed
//       return {
//         flex: "1 1 auto",
//         transition: "all 0.3s ease-in-out",
//       };
//     }
//   };

//   return (
//     <main
//       style={getContentStyle()}
//       className={`flex flex-col overflow-y-auto px-2 pt-2 pb-6 min-w-0 ${
//         showDetailView ? "border-r border-[#D7CBBC]" : ""
//       }`}
//       ref={chatContainerRef}
//     >
//       <div className="w-full max-w-[900px] mt-8 mx-auto">
//         {activeView === "home" ? (
//           <HomeView
//             onSearch={handleSearch}
//             inputValue={inputValue}
//             setInputValue={setInputValue}
//             handleSubmit={handleSubmit}
//             onActionItemsClick={onActionItemsClick}
//           />
//         ) : activeView === "chat" ? (
//           <ChatSection
//             setActiveView={setActiveView}
//             currentRegulation={currentRegulation}
//             selectedDocCode={selectedDocCode}
//             setselectedDocCode={setselectedDocCode}
//             setCurrentRegulation={setCurrentRegulation}
//             onViewDetail={onViewDetail}
//             initialQuery={initialQuery}
//             setInitialQuery={setInitialQuery}
//             showDetailView={showDetailView}
//             onFetchReferences={handleFetchReferences}
//             setSelectedSpecificRef={setSelectedSpecificRef} // Add this line
//             chatContainerRef={chatContainerRef}
//           />
//         ) : (
//           <ActionItemsView
//             setActiveView={setActiveView}
//             setCurrentRegulation={setCurrentRegulation}
//             selectedDocCode={selectedDocCode}
//             setselectedDocCode={setselectedDocCode}
//             onViewDetail={onViewDetail}
//             setReferences={setReferences}
//             showDetailView={showDetailView}
//             setSelectedSpecificRef={setSelectedSpecificRef}
//             isChatHistoryCollapsed={isChatHistoryCollapsed}
//             onClose={onClose}
//           />
//         )}
//       </div>
//     </main>
//   );
// }




"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import type { ChatMessage, RegulationInfo, ReferenceItem, MainContentProps } from "@/types/main_types";
import { HomeView } from "./HomeView";
import { ChatSection } from "./ChatSection";
import { ActionItemsView } from "./ActionItemsView";
import { useChat } from "@/contexts/ChatContext";



export function MainContent({
  activeView,
  setActiveView,
  currentRegulation,
  selectedDocCode,
  setselectedDocCode,
  setCurrentRegulation,
  onViewDetail,
  showDetailView,
  isChatHistoryCollapsed,
  setReferences,
  setSelectedSpecificRef,
  onActionItemsClick,
  onClose,
  selectedActionItem,
  setSelectedActionItem,
}: MainContentProps) {
  const [inputValue, setInputValue] = useState("");
  const [initialQuery, setInitialQuery] = useState<string | null>(null);
  const { messages, isSubmitting } = useChat(); 
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Flag to track if we're switching to the chat view due to a search query
  const [isSearching, setIsSearching] = useState(false);
  // console.log(selectedDocCode);
  // Check for an empty selected session, but only if we're not currently searching
  // console.log("onclose is called");


  useEffect(() => {
    if (
      activeView === "chat" &&
      messages.length === 0 &&
      !isSearching &&
      !isSubmitting
    ) {
      // This is likely an empty session selected from history
      setActiveView("home");
    }

    // Reset the isSearching flag once we have messages or if we're no longer submitting
    if ((messages.length > 0 || !isSubmitting) && isSearching) {
      setIsSearching(false);
    }
  }, [activeView, messages, setActiveView, isSearching, isSubmitting]);



  const handleSearch = (query: string) => {
    if (query.trim()) {
      // Set the searching flag to prevent flipping back to HomeView
      setIsSearching(true);

      if (activeView !== "chat") {
        setInitialQuery(query.trim());
      }
      setActiveView("chat");
    }
  };



  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      handleSearch(inputValue);
      setInputValue("");
    }
  };

  // Handle new references from ChatSection
  const handleFetchReferences = (newReferences: ReferenceItem[]) => {
    // console.log("references at maincontent:", newReferences);
    setReferences(newReferences);
  };



  // Calculate content style based on both sidebar states
  const getContentStyle = () => {
    const navWidth = isChatHistoryCollapsed ? 80 : 280; // Approximate widths for navigation
    const detailWidth = showDetailView ? "40%" : "0%"; // Width of right sidebar when shown

    if (!isChatHistoryCollapsed) {
      // Only left sidebar is expanded
      return {
        flex: "1 1 auto",
        paddingLeft: "0",
        transition: "all 0.3s ease-in-out",
      };
    } else {
      // Both sidebars are collapsed
      return {
        flex: "1 1 auto",
        transition: "all 0.3s ease-in-out",
      };
    }
  };



  return (
    <main
      style={getContentStyle()}
      className={`flex flex-col overflow-y-auto px-2 pt-2 pb-6 min-w-0 ${
        showDetailView ? "border-r border-[#D7CBBC]" : ""
      }`}
      ref={chatContainerRef}
    >
      <div className="w-full max-w-[900px] mt-8 mx-auto">
        {activeView === "home" ? (
          <HomeView
            onSearch={handleSearch}
            inputValue={inputValue}
            setInputValue={setInputValue}
            handleSubmit={handleSubmit}
            onActionItemsClick={onActionItemsClick}
          />
        ) : activeView === "chat" ? (
          <ChatSection
            setActiveView={setActiveView}
            currentRegulation={currentRegulation}
            selectedDocCode={selectedDocCode}
            setselectedDocCode={setselectedDocCode}
            setCurrentRegulation={setCurrentRegulation}
            onViewDetail={onViewDetail}
            initialQuery={initialQuery}
            setInitialQuery={setInitialQuery}
            showDetailView={showDetailView}
            onFetchReferences={handleFetchReferences}
            setSelectedSpecificRef={setSelectedSpecificRef}
            chatContainerRef={chatContainerRef}
            onClose={onClose}
          />
        ) : (
          <ActionItemsView
            setActiveView={setActiveView}
            setCurrentRegulation={setCurrentRegulation}
            selectedDocCode={selectedDocCode}
            setselectedDocCode={setselectedDocCode}
            onViewDetail={onViewDetail}
            setReferences={setReferences}
            showDetailView={showDetailView}
            setSelectedSpecificRef={setSelectedSpecificRef}
            isChatHistoryCollapsed={isChatHistoryCollapsed}
            onClose={onClose}
            selectedActionItem={selectedActionItem} // Add this line
            setSelectedActionItem={setSelectedActionItem} // Add this line
          />
        )}
      </div>
    </main>
  );
}