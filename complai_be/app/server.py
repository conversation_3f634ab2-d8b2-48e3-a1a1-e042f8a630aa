# # app/main.py
# import os
# from fastapi import <PERSON><PERSON><PERSON>
# from alembic import command
# from alembic.config import Config as Al<PERSON>bicConfig
# from fastapi.concurrency import asynccontextmanager
# from app.db.database import init_db
# from fastapi.staticfiles import StaticFiles
# from app.v1.auth.routes import auth_router
# from app.v1.chat.routes import chat_router
# from app.v1.query.routes import query_router
# from starlette.middleware.cors import CORSMiddleware



# app = FastAPI()

# # Define allowed origins
# origins = [
#     "http://localhost",
#     "http://localhost:5173",  # If you're running a frontend on this port
#     "https://yourfrontenddomain.com",  # Add your frontend domain here
# ]

# # Add CORS middleware
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=origins,  # List of allowed origins
#     allow_credentials=True,  # Allow cookies to be sent
#     allow_methods=["*"],  # Allow all HTTP methods (GET, POST, etc.)
#     allow_headers=["*"],  # Allow all headers
# )

# # Initialize the database
# init_db()

# # Include routes for both services
# app.include_router(auth_router, prefix="/auth", tags=["auth"])
# app.include_router(chat_router, prefix="/chat", tags=["chat"])
# app.include_router(query_router, prefix="/query", tags=["query"])

# def run_migrations():
#     # Create Alembic configuration
#     script_location = os.path.join(os.path.dirname(__file__), 'alembic')
#     alembic_cfg = AlembicConfig(os.path.join(script_location, 'alembic.ini'))
#     alembic_cfg.set_main_option('script_location', script_location)

#     # Generate new migration script
#     command.revision(alembic_cfg, autogenerate=True, message="Auto migration")

#     # Apply the migrations
#     command.upgrade(alembic_cfg, 'head')

# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     # Update DB models
#     run_migrations()
#     yield
#     # Clean up the ML models and release the resources
#     # ml_models.clear()

# # Root endpoint
# @app.get("/")
# async def root():
#     return {"message": "welcome to selkea ai!"}



# app/main.py
# app/main.py
import os
import uvicorn
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from app.v1.auth.routes import auth_router
from app.v1.chat.routes import chat_router
from app.v1.query.routes import query_router
from app.v1.session.routes import session_router
from app.v1.notifications.routes import notification_router
from app.v1.report.routes import router as report_router

from app.v2.query.routers import query_router as v2_query_router
from app.actionitems.routes import router as actionitems_router

from starlette.middleware.cors import CORSMiddleware
from app.db.database import get_db, close_db, get_action_db
from pathlib import Path

app = FastAPI()

# Define allowed origins
origins = [
    "http://localhost:5173",  # If you're running a frontend on this port
    "https://chat.complai-genie.online",
    "*", # Add your frontend domain here
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # List of allowed origins
    allow_credentials=True,  # Allow cookies to be sent
    allow_methods=["*"],  # Allow all HTTP methods (GET, POST, etc.)
    allow_headers=["*"],  # Allow all headers
)



# Include routes for both services
app.include_router(auth_router, prefix="/auth", tags=["auth"])
app.include_router(chat_router, prefix="/chat", tags=["chat"])
app.include_router(query_router, prefix="/query", tags=["query"])
app.include_router(notification_router, prefix="/notification", tags=["notification"])
app.include_router(session_router, prefix="/session", tags=["session"])

app.include_router(v2_query_router, prefix="/v2", tags=["v2_query"])

# Mount static files relative to the report router file.
static_path = Path(__file__).parent / "v1" / "report" / "static"
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
app.include_router(report_router, prefix="/report", tags=["Report"])
app.include_router(actionitems_router, prefix="/action-items", tags=["Action Items"])

# Ensure connection to MongoDB and close it on shutdown
@app.on_event("startup")
async def connect_to_db():
    try:
        # Just ensure the connection happens via the dependency injection mechanism
        db = get_db()
        print("Database connected successfully.")
        db = get_action_db("rbi")
        print("Action Database connected successfully.")
    except Exception as e:
        print(f"Error connecting to the database: {e}")

@app.on_event("shutdown")
async def disconnect_from_db():
    close_db()
    print("Database connection closed.")

@app.get("/")
async def root():
    return {"message": "welcome to selkea ai!"}


if __name__ == "__main__":
    uvicorn.run("server:app",host="0.0.0.0", port=8000, reload=True, workers=16, log_level="info")
