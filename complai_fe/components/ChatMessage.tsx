// //chatmessage.tsx
"use client";

// import { useRef, useState, useEffect } from "react"
// import Image from "next/image"
// import { ThumbsUp, ThumbsDown, MessageSquare, Copy } from "lucide-react"
// import type { ChatMessage as ChatMessageType, RegulationInfo, ReferenceItem } from "@/types/types"
// import { FEEDBACK_ENDPOINT } from "@/app/utils/Api" // Make sure this path is correct
// import loading from "@/components/assets/icons/loading.gif"
// import loading2 from "@/components/assets/icons/loading2.gif"
// import SourceImg from "@/components/assets/icons/sources.png"
// import { ChevronLeft, ChevronRight, X } from "lucide-react";
// import { useAuth } from "@/contexts/AuthContext"
// import { useChat } from "@/contexts/ChatContext";

// // Source Button Component - Each instance manages its own dropdown state
// // SourceButton component with conditional vertical AND horizontal positioning
// const SourceButton = ({
//   docId,
//   chunkId,
//   matchingRef,
//   onSelectReference
// }: {
//   docId: string,
//   chunkId: string,
//   matchingRef: any,
//   onSelectReference: (ref: any) => void
// }) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const dropdownRef = useRef<HTMLDivElement>(null);
//   const buttonRef = useRef<HTMLSpanElement>(null);

//   // Handle outside clicks
//   useEffect(() => {
//     const handleClickOutside = (e: MouseEvent) => {
//       if (
//         isOpen &&
//         dropdownRef.current &&
//         !dropdownRef.current.contains(e.target as Node) &&
//         buttonRef.current &&
//         !buttonRef.current.contains(e.target as Node)
//       ) {
//         setIsOpen(false);
//       }
//     };

//     document.addEventListener('mousedown', handleClickOutside);
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, [isOpen]);

//   // Position the dropdown properly with conditional logic
//   useEffect(() => {
//     const positionDropdown = () => {
//       if (isOpen && dropdownRef.current && buttonRef.current) {
//         const buttonRect = buttonRef.current.getBoundingClientRect();
//         const dropdownEl = dropdownRef.current;

//         // Get parent message container
//         const messageContainer = buttonRef.current.closest('.message-container') ||
//                                 buttonRef.current.closest('.w-full');

//         if (messageContainer) {
//           const containerRect = messageContainer.getBoundingClientRect();

//           // Calculate dropdown dimensions
//           const dropdownWidth = 300; // Width set in style
//           const dropdownHeight = Math.min(dropdownEl.scrollHeight, 200); // Limited by maxHeight

//           // Calculate available space in all directions
//           const spaceAbove = buttonRect.top - containerRect.top;
//           const spaceBelow = containerRect.bottom - buttonRect.bottom;
//           const spaceLeft = buttonRect.left - containerRect.left;
//           const spaceRight = containerRect.right - buttonRect.right;

//           // Decide vertical positioning based on available space
//           const showAbove = spaceAbove >= dropdownHeight || spaceAbove > spaceBelow;

//           // Apply vertical positioning
//           if (showAbove) {
//             // Position above the button
//             dropdownEl.style.bottom = '100%';
//             dropdownEl.style.marginBottom = '10px';
//             dropdownEl.style.top = 'auto';
//             dropdownEl.style.marginTop = '0';
//           } else {
//             // Position below the button
//             dropdownEl.style.top = '100%';
//             dropdownEl.style.marginTop = '10px';
//             dropdownEl.style.bottom = 'auto';
//             dropdownEl.style.marginBottom = '0';
//           }

//           // Decide horizontal positioning based on available space
//           const showLeft = spaceLeft >= dropdownWidth || spaceLeft > spaceRight;

//           // Apply horizontal positioning
//           if (showLeft) {
//             // Position to the left of the button
//             dropdownEl.style.right = '0';
//             dropdownEl.style.marginRight = '10px';
//             dropdownEl.style.left = 'auto';
//             dropdownEl.style.marginLeft = '0';
//           } else {
//             // Position to the right of the button
//             dropdownEl.style.left = '0';
//             dropdownEl.style.marginLeft = '10px';
//             dropdownEl.style.right = 'auto';
//             dropdownEl.style.marginRight = '0';
//           }

//           // Ensure dropdown stays within container bounds
//           const dropdownRect = dropdownEl.getBoundingClientRect();

//           // Check if dropdown extends beyond container right edge
//           if (dropdownRect.right > containerRect.right) {
//             const overflow = dropdownRect.right - containerRect.right;
//             dropdownEl.style.left = 'auto';
//             dropdownEl.style.right = `0px`;
//           }

//           // Check if dropdown extends beyond container left edge
//           if (dropdownRect.left < containerRect.left) {
//             const overflow = containerRect.left - dropdownRect.left;
//             dropdownEl.style.right = 'auto';
//             dropdownEl.style.left = `0px`;
//           }
//         }
//       }
//     };

//     if (isOpen) {
//       // Run immediately and after a short delay to ensure DOM is updated
//       positionDropdown();
//       setTimeout(positionDropdown, 50);
//     }

//     window.addEventListener('resize', positionDropdown);

//     return () => {
//       window.removeEventListener('resize', positionDropdown);
//     };
//   }, [isOpen]);

//   const handleClick = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     e.preventDefault();
//     setIsOpen(!isOpen);
//   };

//   return (
//     <span className="relative inline-block source-button">
//       <span
//         ref={buttonRef}
//         className="inline-flex items-center justify-center px-1 py-0.5 text-[9px] text-[#413A36] font-medium cursor-pointer rounded-[40px] border border-[#D7CAC4] bg-[rgba(255,255,255,0.36)] mx-1"
//         style={{ height: "18px", verticalAlign: "middle" }}
//         onClick={handleClick}
//       >
//         <img src={SourceImg.src || SourceImg} alt="Sources" className="w-3 h-3 mr-1" />
//         <span>Source</span>
//       </span>

//       {isOpen && matchingRef && (
//         <div
//           ref={dropdownRef}
//           className="absolute bg-[#EFEBE5] rounded-lg shadow-lg overflow-hidden"
//           style={{
//             width: "300px",
//             maxHeight: "200px",
//             zIndex: 9999,
//             // Position will be set dynamically by useEffect
//           }}
//           onClick={e => e.stopPropagation()}
//         >
//           <div
//             className="py-2 px-4 bg-[rgba(255,255,255,0.36)] cursor-pointer hover:bg-[#E7E1D8]"
//             onClick={(e) => {
//               e.stopPropagation();
//               onSelectReference(matchingRef);
//               setIsOpen(false);
//             }}
//           >
//             <div className="flex items-start gap-4">
//               <div className="w-4 h-4 mt-0 flex-shrink-0 rounded-full bg-[#FECB66] flex items-center justify-center">
//                 <img src={SourceImg.src || SourceImg} alt="Sources" className="w-4 h-4" />
//               </div>
//               <div>
//                 <h2 className="text-[11px] text-[#494949] mb-0">{matchingRef.title || "Document Reference"}</h2>
//                 <p className="text-[9px] text-[#7B7B7B] mt-1">{docId}</p>
//               </div>
//             </div>
//           </div>
//         </div>
//       )}
//     </span>
//   );
// };

// interface ChatMessageProps {
//   message: ChatMessageType
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode:string | null) =>void;
//   setCurrentRegulation?: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void
//   showDetailView?: boolean;
//   onShowReferences?: (references: ReferenceItem[], messageId?: string) => void
//   sessionId?: string; // Added sessionId prop
// }

// export function ChatMessageComponent({
//   message,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   showDetailView = false,
//   onShowReferences,
//   sessionId
// }: ChatMessageProps) {
//   // State for feedback functionality
//   const [vote, setVote] = useState<'up' | 'down' | null>(message.feedback?.vote || null);
//   const [showFeedbackModal, setShowFeedbackModal] = useState(false);
//   const [feedbackText, setFeedbackText] = useState(message.feedback?.text || '');
//   const [showFeedbackSuccess, setShowFeedbackSuccess] = useState(false);
//   const [showCopied, setShowCopied] = useState(false);

//   const {user}=useAuth();
//   const {currentSessionId} = useChat();

//   // State for adaptive bubble width
//   const [bubbleWidth, setBubbleWidth] = useState("auto");
//   const userBubbleRef = useRef<HTMLDivElement>(null);

//   // State for Sources dropdown
//   const [isSourcesDropdownOpen, setIsSourcesDropdownOpen] = useState(false);
//   const [sourcesCurrentPage, setSourcesCurrentPage] = useState(1);
//   const sourcesItemsPerPage = 1; // Number of references to show per page
//   const sourcesDropdownRef = useRef<HTMLDivElement>(null);
//   const sourcesButtonRef = useRef<HTMLDivElement>(null);
//   const [isDropdownPinned, setIsDropdownPinned] = useState(false);

//   const feedbackInputRef = useRef<HTMLTextAreaElement>(null);

//   // Update your existing useEffect for click outside handling
//   // to also unpin the dropdown when clicking outside
//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       if (
//         sourcesDropdownRef.current &&
//         !sourcesDropdownRef.current.contains(event.target as Node) &&
//         sourcesButtonRef.current &&
//         !sourcesButtonRef.current.contains(event.target as Node)
//       ) {
//         setIsSourcesDropdownOpen(false);
//         setIsDropdownPinned(false); // Also unpin when clicking outside
//       }
//     };

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [sourcesDropdownRef, sourcesButtonRef]);

//   // Set the user bubble width based on content length
//   useEffect(() => {
//     if (message.sender === "user" && userBubbleRef.current) {
//       const contentLength = message.content.length;
//       const contentLines = message.content.split('\n').length;

//       // Calculate appropriate width based on content
//       if (contentLength < 50) {
//         // For very short messages, use narrow width
//         setBubbleWidth("fit-content");
//       } else if (contentLength < 100) {
//         // For short messages
//         setBubbleWidth("60%");
//       } else if (contentLength < 200) {
//         // For medium messages
//         setBubbleWidth("75%");
//       } else {
//         // For long messages
//         setBubbleWidth("90%");
//       }

//       // If there are multiple lines, ensure width is sufficient
//       if (contentLines > 2) {
//         setBubbleWidth("90%");
//       }
//     }
//   }, [message.content, message.sender]);

//  useEffect(() => {
//   const positionDropdown = () => {
//     if (isSourcesDropdownOpen && sourcesDropdownRef.current && sourcesButtonRef.current) {
//       const buttonRect = sourcesButtonRef.current.getBoundingClientRect();
//       const dropdownEl = sourcesDropdownRef.current;

//       // Get parent message container
//       const messageContainer = sourcesButtonRef.current.closest('.message-container') ||
//                               sourcesButtonRef.current.closest('.w-full');

//       if (messageContainer) {
//         const containerRect = messageContainer.getBoundingClientRect();

//         // Calculate dropdown dimensions
//         const dropdownWidth = Math.min(500, containerRect.width - 20); // Limited by container width
//         const dropdownHeight = 400; // Max height

//         // Set width based on container
//         dropdownEl.style.width = `${dropdownWidth}px`;

//         // Calculate available space in all directions
//         const spaceAbove = buttonRect.top - containerRect.top;
//         const spaceBelow = containerRect.bottom - buttonRect.bottom;
//         const spaceLeft = buttonRect.left - containerRect.left;
//         const spaceRight = containerRect.right - buttonRect.right;

//         // Decide vertical positioning based on available space
//         const showAbove = spaceAbove >= dropdownHeight || spaceAbove > spaceBelow;

//         // Apply vertical positioning
//         if (showAbove) {
//           // Position above the button
//           dropdownEl.style.bottom = '100%';
//           dropdownEl.style.marginBottom = '10px';
//           dropdownEl.style.top = 'auto';
//           dropdownEl.style.marginTop = '0';
//         } else {
//           // Position below the button
//           dropdownEl.style.top = '100%';
//           dropdownEl.style.marginTop = '10px';
//           dropdownEl.style.bottom = 'auto';
//           dropdownEl.style.marginBottom = '0';
//         }

//         // Decide horizontal positioning based on available space
//         const showLeft = spaceLeft >= dropdownWidth || spaceLeft > spaceRight;

//         // Apply horizontal positioning
//         if (showLeft) {
//           // Position to the left of the button
//           dropdownEl.style.right = '0';
//           dropdownEl.style.marginRight = '10px';
//           dropdownEl.style.left = 'auto';
//           dropdownEl.style.marginLeft = '0';
//         } else {
//           // Position to the right of the button
//           dropdownEl.style.left = '0';
//           dropdownEl.style.marginLeft = '10px';
//           dropdownEl.style.right = 'auto';
//           dropdownEl.style.marginRight = '0';
//         }

//         // Ensure dropdown stays within container bounds
//         const dropdownRect = dropdownEl.getBoundingClientRect();

//         // Check if dropdown extends beyond container right edge
//         if (dropdownRect.right > containerRect.right) {
//           dropdownEl.style.left = 'auto';
//           dropdownEl.style.right = `0px`;
//         }

//         // Check if dropdown extends beyond container left edge
//         if (dropdownRect.left < containerRect.left) {
//           dropdownEl.style.right = 'auto';
//           dropdownEl.style.left = `0px`;
//         }
//       }
//     }
//   };

//   if (isSourcesDropdownOpen) {
//     // Run immediately and after a short delay to ensure DOM is updated
//     positionDropdown();
//     setTimeout(positionDropdown, 50);
//   }

//   window.addEventListener('resize', positionDropdown);

//   return () => {
//     window.removeEventListener('resize', positionDropdown);
//   };
// }, [isSourcesDropdownOpen]);

//   // Convert markdown-style content to JSX with inline sources buttons
//   const formatAssistantContent = (content: string) => {
//     // Function to process citation patterns and replace with Sources buttons
//     const processCitations = (text: string) => {
//       // Regex to match citation pattern [DOC_ID:XXX, CHUNK_ID:YYY]
//       const citationRegex = /\[DOC_ID:([\w\/-]+),\s*CHUNK_ID:(\d+)\]/g;

//       // Split the text by citation matches
//       const parts = [];
//       let lastIndex = 0;
//       let match;

//       while ((match = citationRegex.exec(text)) !== null) {
//         // Add text before the match
//         if (match.index > lastIndex) {
//           parts.push({
//             type: 'text',
//             content: text.substring(lastIndex, match.index)
//           });
//         }

//         // Add the citation as a source button
//         parts.push({
//           type: 'source',
//           docId: match[1],
//           chunkId: match[2],
//           original: match[0]
//         });

//         lastIndex = match.index + match[0].length;
//       }

//       // Add remaining text
//       if (lastIndex < text.length) {
//         parts.push({
//           type: 'text',
//           content: text.substring(lastIndex)
//         });
//       }

//       // If no citations found, return original text
//       if (parts.length === 0) {
//         return <span>{text}</span>;
//       }

//       // Return the mix of text and source buttons
//       return parts.map((part, idx) => {
//         if (part.type === 'text') {
//           return <span key={`text-${idx}`}>{processBoldText(part.content)}</span>;
//         } else {
//           // Find the matching reference for this citation
//           const matchingRef = message.metadata && Array.isArray(message.metadata)
//             ? message.metadata.find((ref: any) =>
//                 (ref.document_code === part.docId || ref.id === part.docId) &&
//                 (ref.chunk_id?.toString() === part.chunkId || ref.chunk_index?.toString() === part.chunkId)
//               )
//             : null;

//           // Return the SourceButton component for this citation
//           return (
//             <SourceButton
//               key={`source-${idx}`}
//               docId={part.docId}
//               chunkId={part.chunkId}
//               matchingRef={matchingRef}
//               onSelectReference={(ref) => {
//                 if (onShowReferences) {
//                   // Pass reference to the sidebar
//                   onShowReferences([ref], message.id);
//                   setselectedDocCode(part.docId);

//                   // Open the detail view
//                   onViewDetail();
//                 }
//               }}
//             />
//           );
//         }
//       });
//     };

//     // Process bold text within non-citation parts
//     const processBoldText = (text: string) => {
//       const parts = text.split(/(\*\*.*?\*\*)/g);
//       return parts.map((part, index) => {
//         if (part.startsWith("**") && part.endsWith("**")) {
//           return (
//             <strong key={index} className="font-bold">
//               {part.slice(2, -2)}
//             </strong>
//           );
//         }
//         return <span key={index}>{part}</span>;
//       });
//     };

//     return (
//       <div className="space-y-4">
//         {content.split("\n").map((line, index) => {
//           // Handle headings starting with ###
//           if (line.startsWith("### ")) {
//             return (
//               <div key={index}>
//                 <h3 className="text-[16px] font-bold text-kyc-green mt-4 border-b border-kyc-green pb-2">
//                   {line.replace("### ", "").trim()}
//                 </h3>
//               </div>
//             );
//           }

//           // Handle bullet points starting with '-'
//           if (line.startsWith("- ")) {
//             return (
//               <p
//                 key={index}
//                 className="pl-6 text-[16px] text-gray-700 flex items-start gap-2"
//               >
//                 <span className="text-kyc-green mr-2">•</span>
//                 <span className="flex-1 flex-wrap">{processCitations(line.slice(2).trim())}</span>
//               </p>
//             );
//           }

//           // Handle sub-bullet points (e.g., nested with spaces before '-')
//           if (line.match(/^\s+-\s+/)) {
//             const match = line.match(/^\s+/);
//             const depth = match ? match[0].length / 2 : 0;

//             return (
//               <p
//                 key={index}
//                 className={`pl-${6 + depth * 4} text-[16px] text-gray-700 flex items-start gap-2`}
//               >
//                 <span className="text-kyc-green mr-2">•</span>
//                 <span className="flex-1 flex-wrap">{processCitations(line.replace(/^\s+-\s+/, "").trim())}</span>
//               </p>
//             );
//           }

//           // Handle plain text (non-heading, non-bullet points)
//           if (line.trim() !== "") {
//             return (
//               <p key={index} className="text-[16px] text-gray-800 leading-relaxed">
//                 {processCitations(line.trim())}
//               </p>
//             );
//           }

//           // Return null for empty lines or unhandled cases
//           return null;
//         })}
//       </div>
//     );
//   };

//   // Find regulation references in content
//   const findRegulationReferences = (content: string) => {
//     const referenceMatches = content.match(/Para \d+ - [A-Za-z\s]+/g) || [];
//     return referenceMatches.map(match => ({
//       text: match,
//       isHighlighted: match.toLowerCase().includes('detection') || match.toLowerCase().includes('impounding')
//     }));
//   };

//   // Helper function to create regulation from metadata
//   const createOrFindRegulation = (metadata: any[]): RegulationInfo => {
//     if (metadata && metadata.length > 0) {
//       const topMetadata = metadata[0];
//       return {
//         id: topMetadata.document_code || topMetadata.document_id || topMetadata.id || "unknown",
//         title: topMetadata.document_title || topMetadata.title || "Regulation",
//         content: message.content, // Use the message content as regulation content
//         date: topMetadata.date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.dept)
//           ? topMetadata.dept[0].replace(/[\[\]"]/g, "")
//           : typeof topMetadata.dept === 'string'
//             ? topMetadata.dept.replace(/[\[\]"]/g, "")
//             : "RBI",
//         source: topMetadata.document_type || topMetadata.Type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: message.content,
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   // Function to generate source badge text from metadata
//   const getSourceBadgeText = (metadata: any[]): string => {
//     if (!metadata || metadata.length === 0) return "";

//     const firstSource = metadata[0];
//     const docId = firstSource.document_id || firstSource.document_number || firstSource.id || "";
//     const chunkId = firstSource.chunk_index || firstSource.chunk_id || "";

//     if (docId && chunkId) {
//       return `[DOC_ID:${docId}, CHUNK_ID:${chunkId}]`;
//     } else if (docId) {
//       return `[DOC_ID:${docId}]`;
//     }

//     return "";
//   };

//   // Handle vote functionality
//   const handleVote = async (voteType: 'up' | 'down') => {
//     const newVote = vote === voteType ? null : voteType;
//     setVote(newVote);

//     try {
//          const requestBody = {
//         session_id: currentSessionId,
//         message_content: message.content,
//         feedback_text: feedbackText,
//         vote: newVote,
//         username: user?.username
//         };
//         console.log(requestBody);
//         const response = await fetch(FEEDBACK_ENDPOINT, {
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json',
//           },
//           body: JSON.stringify(requestBody),
//         });

//       if (!response.ok) throw new Error('Failed to save vote');
//     } catch (error) {
//       console.error('Error saving vote:', error);
//     }
//   };

//   // Handle feedback submission
//   const handleFeedbackSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     if (feedbackText.trim()) {
//       try {
//          const requestBody = {
//         session_id: currentSessionId,
//         message_content: message.content,
//         feedback_text: feedbackText,
//         vote: vote || "",
//         username: user?.username
//         };
//         console.log(requestBody);
//         const response = await fetch(FEEDBACK_ENDPOINT, {
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json',
//           },
//           body: JSON.stringify(requestBody),
//         });

//         if (!response.ok) throw new Error('Failed to save feedback');

//         setShowFeedbackModal(false);
//         setShowFeedbackSuccess(true);
//         setFeedbackText('');
//         setTimeout(() => setShowFeedbackSuccess(false), 2000);
//       } catch (error) {
//         console.error('Error saving feedback:', error);
//       }
//     }
//   };

//   // Handle copy functionality
//   const handleCopy = () => {
//     navigator.clipboard.writeText(message.content).then(() => {
//       setShowCopied(true);
//       setTimeout(() => setShowCopied(false), 2000);
//     });
//   };

//   // Handle message click to show references in sidebar
//   const handleMessageClick = () => {
//     if (
//       message.sender === 'assistant' &&
//       message.metadata &&
//       Array.isArray(message.metadata) &&
//       onShowReferences &&
//       setCurrentRegulation
//     ) {
//       // Pass references to the sidebar along with the message ID
//       onShowReferences(message.metadata as ReferenceItem[], message.id);

//       // Create regulation info from metadata and set current regulation
//       const relevantRegulation = createOrFindRegulation(message.metadata as ReferenceItem[]);
//       setCurrentRegulation(relevantRegulation);
//     }
//   };

//   // Render user message with responsive sizing
//   if (message.sender === "user") {
//     return (
//       <div
//         ref={userBubbleRef}
//         className="flex justify-end"
//         style={{ maxWidth: "100%" }}
//       >
//         <div
//           className="p-5 bg-[#F8F0DF] rounded-tl-[20px] rounded-tr-[8px] rounded-br-[20px] rounded-bl-[20px]"
//           style={{
//             width: bubbleWidth,
//             maxWidth: "90%",
//             minWidth: "100px",
//             wordBreak: "break-word"
//           }}
//         >
//           <div className="text-black font-medium text-[16px] text-left">
//             {message.content}
//           </div>
//         </div>
//       </div>
//     );
//   }

//   // Render assistant message
//   if (message.sender === "assistant") {
//     // Extract regulation references to display as tags
//     const references = findRegulationReferences(message.content);
//     // Get source badge text if metadata is available
//     const sourceBadge = message.metadata && Array.isArray(message.metadata)
//       ? getSourceBadgeText(message.metadata as any[])
//       : "";

//     return (
//       <div className="space-y-1">
//         <div
//           className="w-full max-w-[] mt-5 bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] rounded-2xl overflow-hidden cursor-pointer"
//           onClick={(e) => {
//             // Only handle the message click if not clicking on a source button or dropdown
//             const target = e.target as Element;
//             const isSourceButton = target.closest('.source-button');
//             const isSourceDropdown = target.closest('[data-is-source-dropdown]');

//             if (!isSourceButton && !isSourceDropdown) {
//               handleMessageClick();
//             }
//           }}
//         >
//           <div className="p-5 pb-0 text-[#413A36] text-[16px]">
//             <div className="mb-2">
//               {/* Format the first paragraph of the response */}
//               {formatAssistantContent(message.content.split('\n\n')[0])}
//               {message.metadata && Array.isArray(message.metadata) && message.metadata.length > 0 && (
//                 <div className="flex items-center mt-1 mb-2">
//                   <div
//                     className="relative inline-block"
//                     onMouseEnter={() => !isDropdownPinned && setIsSourcesDropdownOpen(true)}
//                     onMouseLeave={() => !isDropdownPinned && setIsSourcesDropdownOpen(false)}
//                   >
//                     {/* Add ScrollbarStyle */}
//                     <style jsx global>{`
//                       /* Hide scrollbar for Chrome, Safari and Opera */
//                       .no-scrollbar::-webkit-scrollbar {
//                         display: none;
//                       }

//                       /* Hide scrollbar for IE, Edge and Firefox */
//                       .no-scrollbar {
//                         -ms-overflow-style: none;  /* IE and Edge */
//                         scrollbar-width: none;  /* Firefox */
//                       }
//                     `}</style>

//                     {/* Sources Button */}
//                     <div
//                       ref={sourcesButtonRef}
//                       onClick={(e) => {
//                         e.stopPropagation(); // Prevent message click handler
//                         setIsDropdownPinned(!isDropdownPinned);
//                         setIsSourcesDropdownOpen(true);
//                       }}
//                       className="w-[74px] h-[24px] inline-flex items-center justify-center px-1 py-2 text-[9px] text-[#413A36] font-medium cursor-pointer rounded-[40px] border border-[#D7CAC4] bg-[rgba(255,255,255,0.36)]"
//                     >
//                       <span className="flex items-center">
//                         <span className="w-4 h-4 mr-1 flex items-center justify-center rounded-full">
//                           <img src={SourceImg.src || SourceImg} alt="Sources" className="w-4 h-4" />
//                         </span>
//                         {message.metadata.length > 0 && (
//                           <span className="mr-1 text-[#7B7B7B]">+{message.metadata.length}</span>
//                         )}
//                         Sources
//                       </span>
//                     </div>

//                     {/* Main Sources Dropdown */}
//                    {isSourcesDropdownOpen && (
//                       <div
//                         ref={sourcesDropdownRef}
//                         data-is-source-dropdown="true"
//                         className="absolute bg-[#EFEBE5] rounded-lg shadow-lg z-50 overflow-hidden"
//                         style={{
//                           maxHeight: "400px",
//                           zIndex: 9999,
//                           // Position will be set dynamically by the useEffect
//                         }}
//                         onClick={(e) => e.stopPropagation()}
//                       >
//                         {/* Header - Same as before */}
//                         <div className="py-1 px-4 flex justify-between items-center sticky top-0 bg-[rgba(255,255,255,0.36)]">
//                           <div className="flex items-center">
//                             <button
//                               onClick={(e) => {
//                                 e.stopPropagation();
//                                 if (sourcesCurrentPage > 1) {
//                                   setSourcesCurrentPage(sourcesCurrentPage - 1);
//                                 }
//                               }}
//                               disabled={sourcesCurrentPage === 1}
//                               className={`p-1 rounded ${sourcesCurrentPage === 1 ? 'text-gray-400' : 'text-[#413A36] hover:bg-[#EAE6DE]'}`}
//                             >
//                               <ChevronLeft className="h-4 w-4" />
//                             </button>

//                             <button
//                               onClick={(e) => {
//                                 e.stopPropagation();
//                                 const totalPages = Math.ceil(message.metadata.length / sourcesItemsPerPage);
//                                 if (sourcesCurrentPage < totalPages) {
//                                   setSourcesCurrentPage(sourcesCurrentPage + 1);
//                                 }
//                               }}
//                               disabled={sourcesCurrentPage === Math.ceil(message.metadata.length / sourcesItemsPerPage)}
//                               className={`p-1 rounded ${sourcesCurrentPage === Math.ceil(message.metadata.length / sourcesItemsPerPage) ? 'text-gray-400' : 'text-[#413A36] hover:bg-[#EAE6DE]'}`}
//                             >
//                               <ChevronRight className="h-4 w-4" />
//                             </button>
//                           </div>

//                           <span className="text-xs text-[#494949] mx-2">
//                             {sourcesCurrentPage}/{Math.ceil(message.metadata.length / sourcesItemsPerPage)}
//                           </span>

//                           <button
//                             onClick={(e) => {
//                               e.stopPropagation();
//                               setIsSourcesDropdownOpen(false);
//                               setIsDropdownPinned(false);
//                             }}
//                             className="p-1 rounded text-[#413A36] hover:bg-[#EAE6DE]"
//                           >
//                             <X className="h-4 w-4" />
//                           </button>
//                         </div>

//                         {/* References List - Same as before */}
//                         <div className="overflow-y-auto no-scrollbar" style={{ maxHeight: "240px" }}>
//                           {message.metadata.slice(
//                             (sourcesCurrentPage - 1) * sourcesItemsPerPage,
//                             sourcesCurrentPage * sourcesItemsPerPage
//                           ).map((reference, index) => (
//                             <div
//                               key={index}
//                               className="py-2 px-4 bg-[rgba(255,255,255,0.36)] cursor-pointer hover:bg-[#E7E1D8]"
//                               onClick={(e) => {
//                                 e.stopPropagation();
//                                 setIsSourcesDropdownOpen(false);
//                                 setIsDropdownPinned(false);

//                                 if (onShowReferences && setCurrentRegulation) {
//                                   onShowReferences([reference], message.id);
//                                   const doccode = reference.document_code || reference.id || "unknown"
//                                   setselectedDocCode(doccode);
//                                   onViewDetail();
//                                 }
//                               }}
//                             >
//                               <div className="flex items-start gap-4 ">
//                                 <div className="w-4 h-4 mt-0 flex-shrink-0 rounded-full bg-[#FECB66] flex items-center justify-center">
//                                   <img src={SourceImg.src || SourceImg} alt="Sources" className="w-4 h-4" />
//                                 </div>
//                                 <div>
//                                   <h2 className="text-[11px] text-[#494949] mb-0">{reference.title || "Amendments to Master Direction on Counterfeit Notes"}</h2>
//                                 </div>
//                               </div>
//                             </div>
//                           ))}
//                         </div>
//                       </div>
//                     )}
//                   </div>
//                 </div>
//               )}
//            </div>

//             <div className="mb-4">
//               <ul className="space-y-6">
//                 {/* Process and display bullet points from response */}
//                 {formatAssistantContent(
//                   message.content
//                     .split('\n\n')
//                     .slice(1)
//                     .join('\n\n')
//                 )}

//                 {/* Display additional regulation references */}
//                 {references.length > 1 && references.slice(1, 2).map((ref, index) => (
//                   <div key={index + 100} className="inline-flex items-center px-2 py-1 bg-[#F5EEE0] rounded text-[16px] text-[#413A36] font-medium mt-1 ml-1">
//                     <span>{ref.text}</span>
//                   </div>
//                 ))}
//               </ul>
//             </div>
//           </div>
//         </div>

//         {/* Feedback controls row */}
//         <div className="flex items-center gap-2 px-3 ml-1">
//           <div className="flex items-center gap-2">
//             <button
//               onClick={() => handleVote('up')}
//               className={`p-1 rounded-full transition-all duration-200 ${
//                 vote === 'up'
//                   ? 'bg-[#EAE6DE] text-[#FF6B1C]'
//                   : 'hover:bg-[#EAE6DE] text-gray-400'
//               }`}
//             >
//               <ThumbsUp className={`w-4 h-4 ${vote === 'up' ? 'stroke-2' : ''}`} />
//             </button>
//             <button
//               onClick={() => handleVote('down')}
//               className={`p-1 rounded-full transition-all duration-200 ${
//                 vote === 'down'
//                   ? 'bg-[#EAE6DE] text-[#FF6B1C]'
//                   : 'hover:bg-[#EAE6DE] text-gray-400'
//               }`}
//             >
//               <ThumbsDown className={`w-4 h-4 ${vote === 'down' ? 'stroke-2' : ''}`} />
//             </button>
//             <button
//               onClick={() => setShowFeedbackModal(true)}
//               className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
//             >
//               <MessageSquare className="w-4 h-4" />
//             </button>
//             <button
//               onClick={handleCopy}
//               className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
//             >
//               <Copy className="w-4 h-4" />
//             </button>
//           </div>
//           <span className="text-xs text-gray-500">
//             {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
//           </span>
//         </div>

//         {/* Feedback modal */}
//         {showFeedbackModal && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//             <div className="bg-white rounded-xl p-6 w-96 shadow-xl">
//               <h3 className="text-[16px] font-semibold text-[#413A36] mb-4">
//                 Provide Feedback
//               </h3>
//               <form onSubmit={handleFeedbackSubmit}>
//                 <textarea
//                   ref={feedbackInputRef}
//                   value={feedbackText}
//                   onChange={(e) => setFeedbackText(e.target.value)}
//                   className="w-full h-32 p-3 text-[16px] border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#FF6B1C] focus:border-transparent resize-none"
//                   placeholder="Enter your feedback here..."
//                 />
//                 <div className="flex justify-end gap-3 mt-4">
//                   <button
//                     type="button"
//                     onClick={() => setShowFeedbackModal(false)}
//                     className="px-4 py-2 text-[16px] font-medium text-gray-600 hover:text-gray-800 transition-colors duration-150"
//                   >
//                     Cancel
//                   </button>
//                   <button
//                     type="submit"
//                     className="px-4 py-2 text-[16px] font-medium text-white bg-[#FF6B1C] rounded-lg hover:opacity-90 transition-colors duration-150"
//                   >
//                     Submit
//                   </button>
//                 </div>
//               </form>
//             </div>
//           </div>
//         )}

//         {/* Toast notifications */}
//         {showCopied && (
//           <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
//             <Copy className="w-4 h-4" />
//             Copied to clipboard!
//           </div>
//         )}

//         {showFeedbackSuccess && (
//           <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
//             <MessageSquare className="w-4 h-4" />
//             Feedback received. Thank you!
//           </div>
//         )}
//       </div>
//     )
//   }

//   // Loading message - Updated to match Figma design with loading state text
//  if (message.sender === "loading") {
//     return (
//       <div className={`w-full p-5 bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] bg-opacity-90 rounded-2xl mt-5 transition-opacity duration-500 ${message.fadeState || 'opacity-100'}`}>
//         <div className="flex items-center gap-2">
//           <div className="flex items-center">
//             <Image
//               src= {loading2.src || loading2}
//               alt="Loading"
//               width={20}
//               height={20}
//               className="mr-2"
//             />
//             <span className="text-[#FF6B1C] font-medium">Complai</span>
//           </div>
//           <div className="text-[#494949] font-medium">{message.loadingState || "is analysing the regulation"}</div>
//         </div>
//       </div>
//     )
//   }

//   // Default fallback
//   return null
// }

//chatmessage.tsx

// import { useRef, useState, useEffect } from "react";
// import Image from "next/image";
// import { ThumbsUp, ThumbsDown, MessageSquare, Copy } from "lucide-react";
// import type { ChatMessage as ChatMessageType, RegulationInfo, ReferenceItem } from "@/types/types";
// import { FEEDBACK_ENDPOINT } from "@/app/utils/Api"; // Make sure this path is correct
// import loading from "@/components/assets/icons/loading.gif";
// import loading2 from "@/components/assets/icons/loading2.gif";
// import SourceImg from "@/components/assets/icons/sources.png";
// import { ChevronLeft, ChevronRight, X } from "lucide-react";
// import { useAuth } from "@/contexts/AuthContext";
// import { useChat } from "@/contexts/ChatContext";

// // Source Button Component with dropdown
// const SourceButton = ({
//   docRefs,
//   onSelectReference,
// }: {
//   docRefs: Array<{ docId: string; chunkId: string; matchingRef: any }>;
//   onSelectReference: (ref: any) => void;
// }) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const [currentPage, setCurrentPage] = useState(1);
//   const itemsPerPage = 1; // Number of references to show per page
//   const dropdownRef = useRef<HTMLDivElement>(null);
//   const buttonRef = useRef<HTMLSpanElement>(null);

//   // Handle outside clicks
//   useEffect(() => {
//     const handleClickOutside = (e: MouseEvent) => {
//       if (
//         isOpen &&
//         dropdownRef.current &&
//         !dropdownRef.current.contains(e.target as Node) &&
//         buttonRef.current &&
//         !buttonRef.current.contains(e.target as Node)
//       ) {
//         setIsOpen(false);
//       }
//     };

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [isOpen]);

//   // Position the dropdown properly with conditional logic
//   useEffect(() => {
//     const positionDropdown = () => {
//       if (isOpen && dropdownRef.current && buttonRef.current) {
//         const buttonRect = buttonRef.current.getBoundingClientRect();
//         const dropdownEl = dropdownRef.current;

//         // Get parent message container
//         const messageContainer =
//           buttonRef.current.closest(".message-container") || buttonRef.current.closest(".w-full");

//         if (messageContainer) {
//           const containerRect = messageContainer.getBoundingClientRect();

//           // Calculate dropdown dimensions
//           const dropdownWidth = 300; // Width set in style
//           const dropdownHeight = Math.min(dropdownEl.scrollHeight, 200); // Limited by maxHeight

//           // Calculate available space in all directions
//           const spaceAbove = buttonRect.top - containerRect.top;
//           const spaceBelow = containerRect.bottom - buttonRect.bottom;
//           const spaceLeft = buttonRect.left - containerRect.left;
//           const spaceRight = containerRect.right - buttonRect.right;

//           // Decide vertical positioning based on available space
//           const showAbove = spaceAbove >= dropdownHeight || spaceAbove > spaceBelow;

//           // Apply vertical positioning
//           if (showAbove) {
//             // Position above the button
//             dropdownEl.style.bottom = "100%";
//             dropdownEl.style.marginBottom = "10px";
//             dropdownEl.style.top = "auto";
//             dropdownEl.style.marginTop = "0";
//           } else {
//             // Position below the button
//             dropdownEl.style.top = "100%";
//             dropdownEl.style.marginTop = "10px";
//             dropdownEl.style.bottom = "auto";
//             dropdownEl.style.marginBottom = "0";
//           }

//           // Decide horizontal positioning based on available space
//           const showLeft = spaceLeft >= dropdownWidth || spaceLeft > spaceRight;

//           // Apply horizontal positioning
//           if (showLeft) {
//             // Position to the left of the button
//             dropdownEl.style.right = "0";
//             dropdownEl.style.marginRight = "10px";
//             dropdownEl.style.left = "auto";
//             dropdownEl.style.marginLeft = "0";
//           } else {
//             // Position to the right of the button
//             dropdownEl.style.left = "0";
//             dropdownEl.style.marginLeft = "10px";
//             dropdownEl.style.right = "auto";
//             dropdownEl.style.marginRight = "0";
//           }

//           // Ensure dropdown stays within container bounds
//           const dropdownRect = dropdownEl.getBoundingClientRect();

//           // Check if dropdown extends beyond container right edge
//           if (dropdownRect.right > containerRect.right) {
//             const overflow = dropdownRect.right - containerRect.right;
//             dropdownEl.style.left = "auto";
//             dropdownEl.style.right = `0px`;
//           }

//           // Check if dropdown extends beyond container left edge
//           if (dropdownRect.left < containerRect.left) {
//             const overflow = containerRect.left - dropdownRect.left;
//             dropdownEl.style.right = "auto";
//             dropdownEl.style.left = `0px`;
//           }
//         }
//       }
//     };

//     if (isOpen) {
//       // Run immediately and after a short delay to ensure DOM is updated
//       positionDropdown();
//       setTimeout(positionDropdown, 50);
//     }

//     window.addEventListener("resize", positionDropdown);

//     return () => {
//       window.removeEventListener("resize", positionDropdown);
//     };
//   }, [isOpen]);

//   const handleClick = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     e.preventDefault();
//     setIsOpen(!isOpen);
//   };

//   // Only render one button regardless of number of sources
//   // Only render one button regardless of number of sources
//   return (
//     <span className="relative inline-block source-button">
//       <span
//         ref={buttonRef}
//         className="inline-flex items-center justify-center px-1 py-0.5 text-[9px] text-[#413A36] font-medium cursor-pointer rounded-[40px] border border-[#D7CAC4] bg-[rgba(255,255,255,0.36)] mx-1"
//         style={{ height: "20px", verticalAlign: "middle" }}
//         onClick={handleClick}
//       >
//         <img src={SourceImg.src || SourceImg} alt="Sources" className="w-4 h-4 mr-1" />
//         {docRefs.length > 1 && <span className="mr-1 text-[#7B7B7B]">+{docRefs.length}</span>}
//         <span>Source{docRefs.length > 1 ? "s" : ""}</span>
//       </span>

//       {isOpen && (
//         <div
//           ref={dropdownRef}
//           data-is-source-dropdown="true"
//           className="absolute bg-[#EFEBE5] rounded-lg shadow-lg overflow-hidden"
//           style={{
//             width: "500px",
//             maxHeight: "100px",
//             zIndex: 9999,
//             // Position will be set dynamically by useEffect
//           }}
//           onClick={(e) => e.stopPropagation()}
//         >
//           {/* Header with navigation */}
//           <div className="py-1 px-4 flex justify-between items-center sticky top-0 bg-[rgba(255,255,255,0.36)]">
//             <div className="flex items-center">
//               <button
//                 onClick={(e) => {
//                   e.stopPropagation();
//                   if (currentPage > 1) {
//                     setCurrentPage(currentPage - 1);
//                   }
//                 }}
//                 disabled={currentPage === 1}
//                 className={`p-1 rounded ${currentPage === 1 ? "text-gray-400" : "text-[#413A36] hover:bg-[#EAE6DE]"}`}
//               >
//                 <ChevronLeft className="h-4 w-4" />
//               </button>

//               <button
//                 onClick={(e) => {
//                   e.stopPropagation();
//                   const totalPages = Math.ceil(docRefs.length / itemsPerPage);
//                   if (currentPage < totalPages) {
//                     setCurrentPage(currentPage + 1);
//                   }
//                 }}
//                 disabled={currentPage === Math.ceil(docRefs.length / itemsPerPage)}
//                 className={`p-1 rounded ${currentPage === Math.ceil(docRefs.length / itemsPerPage) ? "text-gray-400" : "text-[#413A36] hover:bg-[#EAE6DE]"}`}
//               >
//                 <ChevronRight className="h-4 w-4" />
//               </button>
//             </div>

//             <span className="text-xs text-[#494949] mx-2">
//               {currentPage}/{Math.ceil(docRefs.length / itemsPerPage)}
//             </span>

//             <button
//               onClick={(e) => {
//                 e.stopPropagation();
//                 setIsOpen(false);
//               }}
//               className="p-1 rounded text-[#413A36] hover:bg-[#EAE6DE]"
//             >
//               <X className="h-4 w-4" />
//             </button>
//           </div>

//           {/* References content */}
//           <div className="overflow-y-auto no-scrollbar" style={{ maxHeight: "160px" }}>
//             {docRefs.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).map((docRef, index) => (
//               <div
//                 key={index}
//                 className="py-2 px-4 bg-[rgba(255,255,255,0.36)] cursor-pointer hover:bg-[#E7E1D8]"
//                 onClick={(e) => {
//                   e.stopPropagation();
//                   setIsOpen(false);
//                   if (docRef.matchingRef) {
//                     onSelectReference(docRef.matchingRef);
//                   }
//                 }}
//               >
//                 <div className="flex items-start gap-4">
//                   <div className="w-4 h-4 mt-0 flex-shrink-0 rounded-full bg-[#FECB66] flex items-center justify-center">
//                     <img src={SourceImg.src || SourceImg} alt="Sources" className="w-4 h-4" />
//                   </div>
//                   <div>
//                     <h2 className="text-[11px] text-[#494949] mb-0">
//                       {docRef.matchingRef?.title || "Document Reference"}
//                     </h2>
//                     <div className="text-[9px] text-[#7B7B7B] mt-1">{docRef.docId}</div>
//                     <div className="text-[9px] text-[#7B7B7B] mt-1">{docRef.matchingRef?.description || ""}</div>
//                   </div>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </span>
//   );
// };
// // Import types for SSE updates
// import { ProcessingStep, SSEEvent, PlanCreatedEvent, NodeOutputEvent } from "@/services/ssequeryservice";
// import { ChevronDown, ChevronUp, Info, Loader2, List, CheckCircle2, AlertCircle } from "lucide-react";

// interface ChatMessageProps {
//   message: ChatMessageType;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode?: (doccode: string | null) => void;
//   setCurrentRegulation?: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   showDetailView?: boolean;
//   onShowReferences?: (references: ReferenceItem[], messageId?: string) => void;
//   sessionId?: string; // Added sessionId prop
// }

// export function ChatMessageComponent({
//   message,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   showDetailView = false,
//   onShowReferences,
//   sessionId,
// }: ChatMessageProps) {
//   // State for feedback functionality
//   const [vote, setVote] = useState<"up" | "down" | null>(message.feedback?.vote || null);
//   const [showFeedbackModal, setShowFeedbackModal] = useState(false);
//   const [feedbackText, setFeedbackText] = useState(message.feedback?.text || "");
//   const [showFeedbackSuccess, setShowFeedbackSuccess] = useState(false);
//   const [showCopied, setShowCopied] = useState(false);
//   // Removed as it's now defined above with initial state

//   const { user } = useAuth();
//   const { currentSessionId } = useChat();

//   // Function to toggle the SSE updates visibility
//   const toggleSseUpdates = () => {
//     setShowSseUpdates((prev) => !prev);
//     // Scroll into view when opened
//     if (!showSseUpdates) {
//       setTimeout(() => {
//         const element = document.getElementById(`sse-updates-${message.id}`);
//         if (element) {
//           element.scrollIntoView({ behavior: "smooth", block: "nearest" });
//         }
//       }, 100);
//     }
//   };

//   // Get SSE updates and events from the message
//   const sseUpdates = message.sseUpdates || [];
//   const sseEvents = message.sseEvents || [];

//   // Initialize showSseUpdates based on message loading state
//   const [showSseUpdates, setShowSseUpdates] = useState(!message.isLoading);

//   // Debug to console
//   useEffect(() => {
//     if (message.sender === "assistant") {
//       console.log("Message has SSE updates:", sseUpdates.length, "events:", sseEvents.length);
//     }
//   }, [message.id, sseUpdates.length, sseEvents.length]);

//   // State for adaptive bubble width
//   const [bubbleWidth, setBubbleWidth] = useState("auto");
//   const userBubbleRef = useRef<HTMLDivElement>(null);

//   // State for Sources dropdown
//   const [isSourcesDropdownOpen, setIsSourcesDropdownOpen] = useState(false);
//   const [sourcesCurrentPage, setSourcesCurrentPage] = useState(1);
//   const sourcesItemsPerPage = 1; // Number of references to show per page
//   const sourcesDropdownRef = useRef<HTMLDivElement>(null);
//   const sourcesButtonRef = useRef<HTMLDivElement>(null);
//   const [isDropdownPinned, setIsDropdownPinned] = useState(false);

//   const feedbackInputRef = useRef<HTMLTextAreaElement>(null);

//   // Update your existing useEffect for click outside handling
//   // to also unpin the dropdown when clicking outside
//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       if (
//         sourcesDropdownRef.current &&
//         !sourcesDropdownRef.current.contains(event.target as Node) &&
//         sourcesButtonRef.current &&
//         !sourcesButtonRef.current.contains(event.target as Node)
//       ) {
//         setIsSourcesDropdownOpen(false);
//         setIsDropdownPinned(false); // Also unpin when clicking outside
//       }
//     };

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [sourcesDropdownRef, sourcesButtonRef]);

//   // Set the user bubble width based on content length
//   useEffect(() => {
//     if (message.sender === "user" && userBubbleRef.current) {
//       const contentLength = message.content.length;
//       const contentLines = message.content.split("\n").length;

//       // Calculate appropriate width based on content
//       if (contentLength < 50) {
//         // For very short messages, use narrow width
//         setBubbleWidth("fit-content");
//       } else if (contentLength < 100) {
//         // For short messages
//         setBubbleWidth("60%");
//       } else if (contentLength < 200) {
//         // For medium messages
//         setBubbleWidth("75%");
//       } else {
//         // For long messages
//         setBubbleWidth("90%");
//       }

//       // If there are multiple lines, ensure width is sufficient
//       if (contentLines > 2) {
//         setBubbleWidth("90%");
//       }
//     }
//   }, [message.content, message.sender]);

//   useEffect(() => {
//     const positionDropdown = () => {
//       if (isSourcesDropdownOpen && sourcesDropdownRef.current && sourcesButtonRef.current) {
//         const buttonRect = sourcesButtonRef.current.getBoundingClientRect();
//         const dropdownEl = sourcesDropdownRef.current;

//         // Get parent message container
//         const messageContainer =
//           sourcesButtonRef.current.closest(".message-container") || sourcesButtonRef.current.closest(".w-full");

//         if (messageContainer) {
//           const containerRect = messageContainer.getBoundingClientRect();

//           // Calculate dropdown dimensions
//           const dropdownWidth = Math.min(500, containerRect.width - 20); // Limited by container width
//           const dropdownHeight = 400; // Max height

//           // Set width based on container
//           dropdownEl.style.width = `${dropdownWidth}px`;

//           // Calculate available space in all directions
//           const spaceAbove = buttonRect.top - containerRect.top;
//           const spaceBelow = containerRect.bottom - buttonRect.bottom;
//           const spaceLeft = buttonRect.left - containerRect.left;
//           const spaceRight = containerRect.right - buttonRect.right;

//           // Decide vertical positioning based on available space
//           const showAbove = spaceAbove >= dropdownHeight || spaceAbove > spaceBelow;

//           // Apply vertical positioning
//           if (showAbove) {
//             // Position above the button
//             dropdownEl.style.bottom = "100%";
//             dropdownEl.style.marginBottom = "10px";
//             dropdownEl.style.top = "auto";
//             dropdownEl.style.marginTop = "0";
//           } else {
//             // Position below the button
//             dropdownEl.style.top = "100%";
//             dropdownEl.style.marginTop = "10px";
//             dropdownEl.style.bottom = "auto";
//             dropdownEl.style.marginBottom = "0";
//           }

//           // Decide horizontal positioning based on available space
//           const showLeft = spaceLeft >= dropdownWidth || spaceLeft > spaceRight;

//           // Apply horizontal positioning
//           if (showLeft) {
//             // Position to the left of the button
//             dropdownEl.style.right = "0";
//             dropdownEl.style.marginRight = "10px";
//             dropdownEl.style.left = "auto";
//             dropdownEl.style.marginLeft = "0";
//           } else {
//             // Position to the right of the button
//             dropdownEl.style.left = "0";
//             dropdownEl.style.marginLeft = "10px";
//             dropdownEl.style.right = "auto";
//             dropdownEl.style.marginRight = "0";
//           }

//           // Ensure dropdown stays within container bounds
//           const dropdownRect = dropdownEl.getBoundingClientRect();

//           // Check if dropdown extends beyond container right edge
//           if (dropdownRect.right > containerRect.right) {
//             dropdownEl.style.left = "auto";
//             dropdownEl.style.right = `0px`;
//           }

//           // Check if dropdown extends beyond container left edge
//           if (dropdownRect.left < containerRect.left) {
//             dropdownEl.style.right = "auto";
//             dropdownEl.style.left = `0px`;
//           }
//         }
//       }
//     };

//     if (isSourcesDropdownOpen) {
//       // Run immediately and after a short delay to ensure DOM is updated
//       positionDropdown();
//       setTimeout(positionDropdown, 50);
//     }

//     window.addEventListener("resize", positionDropdown);

//     return () => {
//       window.removeEventListener("resize", positionDropdown);
//     };
//   }, [isSourcesDropdownOpen]);

//   // Parse multiple document references from citation pattern
//   const parseMultipleDocReferences = (citation: string) => {
//     // Split by semicolon followed by "DOC_ID:" to get multiple document references
//     const docRefsArray = citation.split(/;\s*DOC_ID:/);

//     // Process each reference, adding "DOC_ID:" back to all except the first one
//     const processedRefs = docRefsArray.map((ref, index) => (index === 0 ? ref : `DOC_ID:${ref}`));

//     // Parse each document reference
//     return processedRefs.map((docRef) => {
//       // Extract DOC_ID - everything from "DOC_ID:" until the comma before "CHUNK_ID:"
//       const docIdMatch = docRef.match(/DOC_ID:(.*?)(?=,\s*CHUNK_ID)/);
//       const chunkIdMatch = docRef.match(/CHUNK_ID:\s*(\d+)/);

//       const docId = docIdMatch ? docIdMatch[1].trim() : "";
//       const chunkId = chunkIdMatch ? chunkIdMatch[1] : "";

//       // Find matching reference from metadata
//       const matchingRef =
//         message.metadata && Array.isArray(message.metadata)
//           ? message.metadata.find(
//               (ref: any) =>
//                 (ref.document_code === docId || ref.id === docId) &&
//                 (ref.chunk_id?.toString() === chunkId || ref.chunk_index?.toString() === chunkId),
//             )
//           : null;

//       return { docId, chunkId, matchingRef };
//     });
//   };

//   // Convert markdown-style content to JSX with inline sources buttons
//   const formatAssistantContent = (content: string) => {
//     // Function to process citation patterns and replace with Sources buttons
//     const processCitations = (text: string) => {
//       // Regex to match both single and multiple citation patterns
//       // Match patterns like:
//       // [DOC_ID:XXX, CHUNK_ID:YYY]
//       // [DOC_ID:XXX, CHUNK_ID:YYY; DOC_ID:ZZZ, CHUNK_ID:WWW]

//       // We need to handle two cases:
//       // 1. Multiple references within a single citation (already handled)
//       // 2. Multiple adjacent citations that should be consolidated into one button

//       // First, let's collect all citations that might be adjacent
//       const citationRegex = /\[(DOC_ID:[^[\]]+,\s*CHUNK_ID:\d+(?:;\s*DOC_ID:[^[\]]+,\s*CHUNK_ID:\d+)*)\]/g;

//       // Split the text by citation matches
//       const parts = [];
//       let lastIndex = 0;
//       let match;

//       // Temporary array to collect adjacent citations
//       let adjacentCitations: { docId: string; chunkId: string; matchingRef: ReferenceItem | null | undefined }[] = [];
//       let lastMatchEnd = -1;

//       while ((match = citationRegex.exec(text)) !== null) {
//         // Add text before the match
//         if (match.index > lastIndex) {
//           parts.push({
//             type: "text",
//             content: text.substring(lastIndex, match.index),
//           });
//         }

//         // Get the citation content
//         const citationContent = match[1];
//         const docRefs = parseMultipleDocReferences(citationContent);

//         // Check if this citation is adjacent to the previous one
//         // (No text between them or just whitespace)
//         if (match.index === lastMatchEnd || text.substring(lastMatchEnd, match.index).trim() === "") {
//           // This citation is adjacent to the previous one, collect it
//           adjacentCitations.push(...docRefs);
//         } else {
//           // This is a new citation group, add the previous group if exists
//           if (adjacentCitations.length > 0) {
//             parts.push({
//               type: "source",
//               docRefs: adjacentCitations,
//               original: "[...]", // Original text doesn't matter here
//             });
//             adjacentCitations = [];
//           }

//           // Start a new group with current citation
//           adjacentCitations = [...docRefs];
//         }

//         lastIndex = match.index + match[0].length;
//         lastMatchEnd = lastIndex;
//       }

//       // Add any remaining citations
//       if (adjacentCitations.length > 0) {
//         parts.push({
//           type: "source",
//           docRefs: adjacentCitations,
//           original: "[...]", // Original text doesn't matter here
//         });
//       }

//       // Add remaining text
//       if (lastIndex < text.length) {
//         parts.push({
//           type: "text",
//           content: text.substring(lastIndex),
//         });
//       }

//       // If no citations found, return original text
//       if (parts.length === 0) {
//         return <span>{text}</span>;
//       }

//       // Return the mix of text and source buttons
//       return parts.map((part, idx) => {
//         if (part.type === "text") {
//           return <span key={`text-${idx}`}>{processBoldText(part.content)}</span>;
//         } else {
//           // Return the SourceButton component for this citation or citations group
//           return (
//             <SourceButton
//               key={`source-${idx}`}
//               docRefs={part.docRefs}
//               onSelectReference={(ref) => {
//                 if (onShowReferences) {
//                   // Pass reference to the sidebar
//                   // console.log(ref);
//                   // console.log([ref]);

//                   // Get the document code from the selected reference
//                   const docCode = ref.document_code || ref.id || null;
//                   // console.log(docCode);
//                   onShowReferences([ref], message.id);
//                   // Set the selected doc code to the clicked reference's document code
//                   setselectedDocCode(docCode);

//                   // Open the detail view
//                   onViewDetail();
//                 }
//               }}
//             />
//           );
//         }
//       });
//     };

//     // Process bold text within non-citation parts
//     const processBoldText = (text: string) => {
//       const parts = text.split(/(\*\*.*?\*\*)/g);
//       return parts.map((part, index) => {
//         if (part.startsWith("**") && part.endsWith("**")) {
//           return (
//             <strong key={index} className="font-bold">
//               {part.slice(2, -2)}
//             </strong>
//           );
//         }
//         return <span key={index}>{part}</span>;
//       });
//     };

//     return (
//       <div className="space-y-4">
//         {content.split("\n").map((line, index) => {
//           // Handle headings starting with ###
//           if (line.startsWith("### ")) {
//             return (
//               <div key={index}>
//                 <h3 className="text-[16px] font-bold text-kyc-green mt-4 border-b border-kyc-green pb-2">
//                   {line.replace("### ", "").trim()}
//                 </h3>
//               </div>
//             );
//           }

//           // Handle bullet points starting with '-'
//           if (line.startsWith("- ")) {
//             return (
//               <div key={index} className="pl-6 text-[16px] text-gray-700 flex items-start gap-2">
//                 <span className="text-kyc-green mr-2">•</span>
//                 <span className="flex-1 flex-wrap">{processCitations(line.slice(2).trim())}</span>
//               </div>
//             );
//           }

//           // Handle sub-bullet points (e.g., nested with spaces before '-')
//           if (line.match(/^\s+-\s+/)) {
//             const match = line.match(/^\s+/);
//             const depth = match ? match[0].length / 2 : 0;

//             return (
//               <div key={index} className={`pl-${6 + depth * 4} text-[16px] text-gray-700 flex items-start gap-2`}>
//                 <span className="text-kyc-green mr-2">•</span>
//                 <span className="flex-1 flex-wrap">{processCitations(line.replace(/^\s+-\s+/, "").trim())}</span>
//               </div>
//             );
//           }

//           // Handle plain text (non-heading, non-bullet points)
//           if (line.trim() !== "") {
//             return (
//               <div key={index} className="text-[16px] text-gray-800 leading-relaxed">
//                 {processCitations(line.trim())}
//               </div>
//             );
//           }

//           // Return null for empty lines or unhandled cases
//           return null;
//         })}
//       </div>
//     );
//   };

//   // Find regulation references in content
//   const findRegulationReferences = (content: string) => {
//     const referenceMatches = content.match(/Para \d+ - [A-Za-z\s]+/g) || [];
//     return referenceMatches.map((match) => ({
//       text: match,
//       isHighlighted: match.toLowerCase().includes("detection") || match.toLowerCase().includes("impounding"),
//     }));
//   };

//   // Helper function to create regulation from metadata
//   const createOrFindRegulation = (metadata: any[]): RegulationInfo => {
//     if (metadata && metadata.length > 0) {
//       const topMetadata = metadata[0];
//       return {
//         id: topMetadata.document_code || topMetadata.document_id || topMetadata.id || "unknown",
//         title: topMetadata.document_title || topMetadata.title || "Regulation",
//         content: message.content, // Use the message content as regulation content
//         date: topMetadata.date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.dept)
//           ? topMetadata.dept[0].replace(/[\[\]"]/g, "")
//           : typeof topMetadata.dept === "string"
//             ? topMetadata.dept.replace(/[\[\]"]/g, "")
//             : "RBI",
//         source: topMetadata.document_type || topMetadata.Type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: message.content,
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   // Function to generate source badge text from metadata
//   const getSourceBadgeText = (metadata: any[]): string => {
//     if (!metadata || metadata.length === 0) return "";

//     const firstSource = metadata[0];
//     const docId = firstSource.document_id || firstSource.document_number || firstSource.id || "";
//     const chunkId = firstSource.chunk_index || firstSource.chunk_id || "";

//     if (docId && chunkId) {
//       return `[DOC_ID:${docId}, CHUNK_ID:${chunkId}]`;
//     } else if (docId) {
//       return `[DOC_ID:${docId}]`;
//     }

//     return "";
//   };

//   // Handle vote functionality
//   const handleVote = async (voteType: "up" | "down") => {
//     const newVote = vote === voteType ? null : voteType;
//     setVote(newVote);

//     try {
//       const requestBody = {
//         session_id: currentSessionId,
//         message_content: message.content,
//         feedback_text: feedbackText,
//         vote: newVote,
//         username: user?.username,
//       };
//       // console.log(requestBody);
//       const response = await fetch(FEEDBACK_ENDPOINT, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(requestBody),
//       });

//       if (!response.ok) throw new Error("Failed to save vote");
//     } catch (error) {
//       console.error("Error saving vote:", error);
//     }
//   };

//   // Handle feedback submission
//   const handleFeedbackSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     if (feedbackText.trim()) {
//       try {
//         const requestBody = {
//           session_id: currentSessionId,
//           message_content: message.content,
//           feedback_text: feedbackText,
//           vote: vote || "",
//           username: user?.username,
//         };
//         // console.log(requestBody);
//         const response = await fetch(FEEDBACK_ENDPOINT, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify(requestBody),
//         });

//         if (!response.ok) throw new Error("Failed to save feedback");

//         setShowFeedbackModal(false);
//         setShowFeedbackSuccess(true);
//         setFeedbackText("");
//         setTimeout(() => setShowFeedbackSuccess(false), 2000);
//       } catch (error) {
//         console.error("Error saving feedback:", error);
//       }
//     }
//   };

//   // Handle copy functionality
//   const handleCopy = () => {
//     navigator.clipboard.writeText(message.content).then(() => {
//       setShowCopied(true);
//       setTimeout(() => setShowCopied(false), 2000);
//     });
//   };

//   // Handle message click to show references in sidebar
//   const handleMessageClick = () => {
//     if (
//       message.sender === "assistant" &&
//       message.metadata &&
//       Array.isArray(message.metadata) &&
//       onShowReferences &&
//       setCurrentRegulation
//     ) {
//       // Pass references to the sidebar along with the message ID
//       onShowReferences(message.metadata as ReferenceItem[], message.id);

//       // Create regulation info from metadata and set current regulation
//       const relevantRegulation = createOrFindRegulation(message.metadata as ReferenceItem[]);
//       setCurrentRegulation(relevantRegulation);
//     }
//   };

//   // Render user message with responsive sizing
//   if (message.sender === "user") {
//     return (
//       <div ref={userBubbleRef} className="flex justify-end" style={{ maxWidth: "100%" }}>
//         <div
//           className="p-5 bg-[#F8F0DF] rounded-tl-[20px] rounded-tr-[8px] rounded-br-[20px] rounded-bl-[20px]"
//           style={{
//             width: bubbleWidth,
//             maxWidth: "90%",
//             minWidth: "100px",
//             wordBreak: "break-word",
//           }}
//         >
//           <div className="text-black font-medium text-[16px] text-left">{message.content}</div>
//         </div>
//       </div>
//     );
//   }

//   // Render assistant message
//   if (message.sender === "assistant") {
//     // Extract regulation references to display as tags
//     const references = findRegulationReferences(message.content);
//     // Get source badge text if metadata is available
//     const sourceBadge =
//       message.metadata && Array.isArray(message.metadata) ? getSourceBadgeText(message.metadata as any[]) : "";

//     return (
//       <div className="space-y-1">
//         <div
//           className="w-full max-w-[] mt-5 bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] rounded-2xl overflow-hidden cursor-pointer"
//           onClick={(e) => {
//             // Only handle the message click if not clicking on a source button or dropdown
//             const target = e.target as Element;
//             const isSourceButton = target.closest(".source-button");
//             const isSourceDropdown = target.closest("[data-is-source-dropdown]");

//             if (!isSourceButton && !isSourceDropdown) {
//               handleMessageClick();
//             }
//           }}
//         >
//           <div className="p-5 pb-0 text-[#413A36] text-[16px]">
//             <div className="mb-2">
//               {/* Format the first paragraph of the response */}
//               {formatAssistantContent(message.content.split("\n\n")[0])}
//               {message.metadata && Array.isArray(message.metadata) && message.metadata.length > 0 && (
//                 <div className="flex items-center mt-1 mb-2">
//                   <div
//                     className="relative inline-block"
//                     onMouseEnter={() => !isDropdownPinned && setIsSourcesDropdownOpen(true)}
//                     onMouseLeave={() => !isDropdownPinned && setIsSourcesDropdownOpen(false)}
//                   >
//                     {/* Add ScrollbarStyle */}
//                     <style jsx global>{`
//                       /* Hide scrollbar for Chrome, Safari and Opera */
//                       .no-scrollbar::-webkit-scrollbar {
//                         display: none;
//                       }

//                       /* Hide scrollbar for IE, Edge and Firefox */
//                       .no-scrollbar {
//                         -ms-overflow-style: none; /* IE and Edge */
//                         scrollbar-width: none; /* Firefox */
//                       }
//                     `}</style>
//                   </div>
//                 </div>
//               )}
//             </div>

//             <div className="mb-4">
//               <ul className="space-y-6">
//                 {/* Process and display bullet points from response */}
//                 {formatAssistantContent(message.content.split("\n\n").slice(1).join("\n\n"))}

//                 {/* Display additional regulation references */}
//                 {references.length > 1 &&
//                   references.slice(1, 2).map((ref, index) => (
//                     <div
//                       key={index + 100}
//                       className="inline-flex items-center px-2 py-1 bg-[#F5EEE0] rounded text-[16px] text-[#413A36] font-medium mt-1 ml-1"
//                     >
//                       <span>{ref.text}</span>
//                     </div>
//                   ))}
//               </ul>
//             </div>
//           </div>
//         </div>

//         {/* SSE Updates Collapsible Section */}
//         {/* {message.sender === "assistant" && message.isLoading && (
//           <div className="mt-2 px-3 py-2 flex items-center text-sm text-gray-600">
//             <Loader2 className="w-4 h-4 mr-2 animate-spin" />
//             <span>{message.content || "Processing..."}</span>
//           </div>
//         )} */}
//         {/* Feedback controls row */}
//         <div className="flex items-center gap-2 px-3 ml-1">
//           <div className="flex items-center gap-2">
//             <button
//               onClick={() => handleVote("up")}
//               className={`p-1 rounded-full transition-all duration-200 ${
//                 vote === "up" ? "bg-[#EAE6DE] text-[#FF6B1C]" : "hover:bg-[#EAE6DE] text-gray-400"
//               }`}
//             >
//               <ThumbsUp className={`w-4 h-4 ${vote === "up" ? "stroke-2" : ""}`} />
//             </button>
//             <button
//               onClick={() => handleVote("down")}
//               className={`p-1 rounded-full transition-all duration-200 ${
//                 vote === "down" ? "bg-[#EAE6DE] text-[#FF6B1C]" : "hover:bg-[#EAE6DE] text-gray-400"
//               }`}
//             >
//               <ThumbsDown className={`w-4 h-4 ${vote === "down" ? "stroke-2" : ""}`} />
//             </button>
//             <button
//               onClick={() => setShowFeedbackModal(true)}
//               className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
//             >
//               <MessageSquare className="w-4 h-4" />
//             </button>
//             <button
//               onClick={handleCopy}
//               className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
//             >
//               <Copy className="w-4 h-4" />
//             </button>
//           </div>
//           <span className="text-xs text-gray-500">
//             {new Date(message.timestamp).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
//           </span>
//         </div>

//         {/* Feedback modal */}
//         {showFeedbackModal && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//             <div className="bg-white rounded-xl p-6 w-96 shadow-xl">
//               <h3 className="text-[16px] font-semibold text-[#413A36] mb-4">Provide Feedback</h3>
//               <form onSubmit={handleFeedbackSubmit}>
//                 <textarea
//                   ref={feedbackInputRef}
//                   value={feedbackText}
//                   onChange={(e) => setFeedbackText(e.target.value)}
//                   className="w-full h-32 p-3 text-[16px] border border-gray-200 rounded-lg focus:border-transparent resize-none"
//                   placeholder="Enter your feedback here..."
//                 />
//                 <div className="flex justify-end gap-3 mt-4">
//                   <button
//                     type="button"
//                     onClick={() => setShowFeedbackModal(false)}
//                     className="px-4 py-2 text-[16px] font-medium text-gray-600 hover:text-gray-800 transition-colors duration-150"
//                   >
//                     Cancel
//                   </button>
//                   <button
//                     type="submit"
//                     className="px-4 py-2 text-[16px] font-medium text-white bg-[#FF6B1C] rounded-lg hover:opacity-90 transition-colors duration-150"
//                   >
//                     Submit
//                   </button>
//                 </div>
//               </form>
//             </div>
//           </div>
//         )}

//         {/* Toast notifications */}
//         {showCopied && (
//           <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
//             <Copy className="w-4 h-4" />
//             Copied to clipboard!
//           </div>
//         )}

//         {showFeedbackSuccess && (
//           <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
//             <MessageSquare className="w-4 h-4" />
//             Feedback received. Thank you!
//           </div>
//         )}
//       </div>
//     );
//   }

//   // Loading message - Updated to match Figma design with loading state text
//   if (message.sender === "loading") {
//     return (
//       <div
//         className={`w-full p-5 bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] bg-opacity-90 rounded-2xl mt-5 transition-opacity duration-500 ${message.fadeState || "opacity-100"}`}
//       >
//         <div className="flex items-center gap-2">
//           <div className="flex items-center">
//             <Image src={loading2.src || loading2} alt="Loading" width={20} height={20} className="mr-2" />
//             <span className="text-[#FF6B1C] font-medium">Complai</span>
//           </div>
//           <div className="text-[#494949] font-medium">{message.loadingState || "is analysing the regulation"}</div>
//         </div>
//       </div>
//     );
//   }

//   // Default fallback
//   return null;
// }

import React from "react";
import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import { ThumbsUp, ThumbsDown, MessageSquare, Copy } from "lucide-react";
import type { ChatMessage as ChatMessageType, RegulationInfo, ReferenceItem, ChatMessageProps } from "@/types/main_types";
import { FEEDBACK_ENDPOINT } from "@/app/utils/Api";
import loading from "@/components/assets/icons/loading.gif";
import loading2 from "@/components/assets/icons/loading2.gif";
import SourceImg from "@/components/assets/icons/sources.png";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useChat } from "@/contexts/ChatContext";
import MarkdownRenderer from "@/components/MarkdownRenderer";
import { ProcessingStep, SSEEvent } from "@/services/ssequeryservice";

export const getAllReferencesFromMessage = (message) => {
  let allReferences = [];
  if (
    Array.isArray(message.content) &&
    message.content.length > 0 &&
    message.content[0].answer_part
  ) {
    // Collect all answer_part_references
    message.content.forEach((part) => {
      if (
        part.answer_part_references &&
        Array.isArray(part.answer_part_references) &&
        part.answer_part_references.length > 0
      ) {
        allReferences = [...allReferences, ...part.answer_part_references];
      }
    });
  }
  return allReferences;
};

export const groupReferencesByDocAndPage = (references, message) => {
  const groupedMap = new Map();

  // First, filter out null values and those without revision_s3_url
  const validRefs = references
    .map((ref) => {
      let fullMetadata = null;

      if (Array.isArray(message.metadata)) {
        fullMetadata = message.metadata.find((meta) => {
          const cleanedDocId = ref.document_id?.trim();
          return (
            meta.document_id?.trim() === cleanedDocId ||
            meta.id?.trim() === cleanedDocId ||
            meta.document_code?.trim().startsWith(cleanedDocId)
          );
        });
      }

      // Skip references without revision_s3_url
      if (!fullMetadata?.revision_s3_url && !fullMetadata?.s3_url) {
        return null;
      }

      return {
        ref,
        fullMetadata,
        docId: ref.document_id,
        pageNum: ref.page_number?.toString() || "1",
      };
    })
    .filter((item) => item !== null);

  // Group by docId + page number
  for (const item of validRefs) {
    const key = `${item.docId}-${item.pageNum}`;

    if (!groupedMap.has(key)) {
      groupedMap.set(key, {
        docId: item.docId,
        chunkId: item.pageNum,
        matchingRef: {
          document_code: item.docId,
          document_id: item.docId,
          title:
            item.fullMetadata?.title ||
            item.fullMetadata?.document_title ||
            item.docId,
          chunk_id: parseInt(item.pageNum) || 0,
          positions: item.ref.bboxes
            ? [
              {
                page: parseInt(item.pageNum) || 0,
                bboxes: item.ref.bboxes,
              },
            ]
            : [],
          section_summary:
            item.fullMetadata?.section_summary ||
            item.fullMetadata?.document_summary ||
            "",
          Type:
            item.fullMetadata?.Type || item.fullMetadata?.document_type || "",
          addressee: item.fullMetadata?.addressee || "",
          date_of_issue: item.fullMetadata?.date_of_issue || "",
          dept:
            item.fullMetadata?.dept ||
            item.fullMetadata?.applicable_departments ||
            [],
          pdf_filename:
            item.fullMetadata?.pdf_filename ||
            item.fullMetadata?.document_title ||
            "",
          pdf_link: item.fullMetadata?.pdf_link || "",
          revision_s3_url:
            item.fullMetadata?.revision_s3_url ||
            item.fullMetadata?.s3_url ||
            "",
        },
      });
    } else {
      // If this document and page already exists, add any new bbox positions if they don't already exist
      const existing = groupedMap.get(key);
      if (item.ref.bboxes) {
        // Check if this bbox already exists
        const bboxExists = existing.matchingRef.positions.some(
          (pos) => JSON.stringify(pos.bboxes) === JSON.stringify(item.ref.bboxes)
        );

        if (!bboxExists) {
          existing.matchingRef.positions.push({
            page: parseInt(item.pageNum) || 0,
            bboxes: item.ref.bboxes,
          });
        }
      }
    }
  }

  // Convert map back to array
  return Array.from(groupedMap.values());
};

// Source Button Component with dropdown
const SourceButton = ({
  docRefs,
  message,
  onSelectReference,
}: {
  docRefs: Array<{ docId: string; chunkId: string; matchingRef: any }>;
  message: ChatMessageType;
  onSelectReference: (
    allRefs: any[],
    selectedDocId: string,
    specificRef?: any
  ) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 1; // Number of references to show per page
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLSpanElement>(null);

  // Handle outside clicks
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        isOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Position the dropdown properly with conditional logic
  useEffect(() => {
    const positionDropdown = () => {
      if (isOpen && dropdownRef.current && buttonRef.current) {
        const buttonRect = buttonRef.current.getBoundingClientRect();
        const dropdownEl = dropdownRef.current;

        // Get parent message container
        const messageContainer =
          buttonRef.current.closest(".message-container") ||
          buttonRef.current.closest(".w-full");

        if (messageContainer) {
          const containerRect = messageContainer.getBoundingClientRect();

          // Calculate dropdown dimensions
          const dropdownWidth = 400; // Width set in style
          const dropdownHeight = Math.min(dropdownEl.scrollHeight, 200); // Limited by maxHeight

          // Calculate available space in all directions
          const spaceAbove = buttonRect.top - containerRect.top;
          const spaceBelow = containerRect.bottom - buttonRect.bottom;
          const spaceLeft = buttonRect.left - containerRect.left;
          const spaceRight = containerRect.right - buttonRect.right;

          // Decide vertical positioning based on available space
          const showAbove =
            spaceAbove >= dropdownHeight || spaceAbove > spaceBelow;

          // Apply vertical positioning
          if (showAbove) {
            // Position above the button
            dropdownEl.style.bottom = "100%";
            dropdownEl.style.marginBottom = "10px";
            dropdownEl.style.top = "auto";
            dropdownEl.style.marginTop = "0";
          } else {
            // Position below the button
            dropdownEl.style.top = "100%";
            dropdownEl.style.marginTop = "10px";
            dropdownEl.style.bottom = "auto";
            dropdownEl.style.marginBottom = "0";
          }

          // Decide horizontal positioning based on available space
          const showLeft = spaceLeft >= dropdownWidth || spaceLeft > spaceRight;

          // Apply horizontal positioning
          if (showLeft) {
            // Position to the left of the button
            dropdownEl.style.right = "0";
            dropdownEl.style.marginRight = "10px";
            dropdownEl.style.left = "auto";
            dropdownEl.style.marginLeft = "0";
          } else {
            // Position to the right of the button
            dropdownEl.style.left = "0";
            dropdownEl.style.marginLeft = "10px";
            dropdownEl.style.right = "auto";
            dropdownEl.style.marginRight = "0";
          }

          // Ensure dropdown stays within container bounds
          const dropdownRect = dropdownEl.getBoundingClientRect();

          // Check if dropdown extends beyond container right edge
          if (dropdownRect.right > containerRect.right) {
            const overflow = dropdownRect.right - containerRect.right;
            dropdownEl.style.left = "auto";
            dropdownEl.style.right = `0px`;
          }

          // Check if dropdown extends beyond container left edge
          if (dropdownRect.left < containerRect.left) {
            const overflow = containerRect.left - dropdownRect.left;
            dropdownEl.style.right = "auto";
            dropdownEl.style.left = `0px`;
          }
        }
      }
    };

    if (isOpen) {
      // Run immediately and after a short delay to ensure DOM is updated
      positionDropdown();
      setTimeout(positionDropdown, 50);
    }

    window.addEventListener("resize", positionDropdown);

    return () => {
      window.removeEventListener("resize", positionDropdown);
    };
  }, [isOpen]);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  // Only render one button regardless of number of sources
  return (
    <span className="relative inline-block source-button">
      <span
        ref={buttonRef}
        className="inline-flex items-center justify-center px-1 py-0.5 text-[9px] text-[#413A36] font-medium cursor-pointer rounded-[40px] border border-[#D7CAC4] bg-[rgba(255,255,255,0.36)] mx-1"
        style={{ height: "20px", verticalAlign: "middle" }}
        onClick={handleClick}
      >
        <img
          src={SourceImg.src || SourceImg}
          alt="Sources"
          className="w-4 h-4 mr-1"
        />
        {docRefs.length > 1 && (
          <span className="mr-1 text-[#7B7B7B]">+{docRefs.length}</span>
        )}
        <span>Source{docRefs.length > 1 ? "s" : ""}</span>
      </span>

      {isOpen && (
        <div
          ref={dropdownRef}
          data-is-source-dropdown="true"
          className="absolute bg-[#EFEBE5] rounded-lg shadow-lg overflow-hidden"
          style={{
            width: "400px",
            maxHeight: "100px",
            zIndex: 9999,
            // Position will be set dynamically by useEffect
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header with navigation */}
          <div className="py-1 px-4 flex justify-between items-center sticky top-0 bg-[rgba(255,255,255,0.36)]">
            <div className="flex items-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (currentPage > 1) {
                    setCurrentPage(currentPage - 1);
                  }
                }}
                disabled={currentPage === 1}
                className={`p-1 rounded ${currentPage === 1
                    ? "text-gray-400"
                    : "text-[#413A36] hover:bg-[#EAE6DE]"
                  }`}
              >
                <ChevronLeft className="h-4 w-4" />
              </button>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  const totalPages = Math.ceil(docRefs.length / itemsPerPage);
                  if (currentPage < totalPages) {
                    setCurrentPage(currentPage + 1);
                  }
                }}
                disabled={
                  currentPage === Math.ceil(docRefs.length / itemsPerPage)
                }
                className={`p-1 rounded ${currentPage === Math.ceil(docRefs.length / itemsPerPage)
                    ? "text-gray-400"
                    : "text-[#413A36] hover:bg-[#EAE6DE]"
                  }`}
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>

            <span className="text-xs text-[#494949] mx-2">
              {currentPage}/{Math.ceil(docRefs.length / itemsPerPage)}
            </span>

            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(false);
              }}
              className="p-1 rounded text-[#413A36] hover:bg-[#EAE6DE]"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* References content */}
          <div
            className="overflow-y-auto no-scrollbar"
            style={{ maxHeight: "160px" }}
          >
            {docRefs
              .slice(
                (currentPage - 1) * itemsPerPage,
                currentPage * itemsPerPage
              )
              .map((docRef, index) => (
                <div
                  key={index}
                  className="py-2 px-4 bg-[rgba(255,255,255,0.36)] cursor-pointer hover:bg-[#E7E1D8]"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(false);
                    if (docRef.matchingRef) {
                      // Get all references from all answer parts of this message
                      let allReferences = [];

                      if (
                        Array.isArray(message.content) &&
                        message.content.length > 0 &&
                        message.content[0].answer_part
                      ) {
                        message.content.forEach((part) => {
                          if (
                            part.answer_part_references &&
                            Array.isArray(part.answer_part_references) &&
                            part.answer_part_references.length > 0
                          ) {
                            allReferences = [
                              ...allReferences,
                              ...part.answer_part_references,
                            ];
                          }
                        });
                      }

                      // Process and group all references
                      const groupedRefs = groupReferencesByDocAndPage(
                        allReferences,
                        message
                      );
                      const processedRefs = groupedRefs.map(
                        (ref) => ref.matchingRef
                      );

                      // Send all references to resources and set selected doc ID, along with the specific clicked reference
                      onSelectReference(
                        processedRefs,
                        docRef.matchingRef.document_id ||
                        docRef.matchingRef.document_code,
                        docRef.matchingRef
                      );
                    }
                  }}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-4 h-4 mt-0 flex-shrink-0 rounded-full bg-[#FECB66] flex items-center justify-center">
                      <img
                        src={SourceImg.src || SourceImg}
                        alt="Sources"
                        className="w-4 h-4"
                      />
                    </div>
                    <div>
                      <h2 className="text-[11px] text-[#494949] mb-0">
                        {docRef.matchingRef?.title ||
                          docRef.docId ||
                          "Document Reference"}
                      </h2>
                      <div className="text-[9px] text-[#7B7B7B] mt-1">
                        {docRef.docId}{" "}
                        {docRef.chunkId ? `- Page ${docRef.chunkId}` : ""}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </span>
  );
};

export function ChatMessageComponent({
  message,
  currentRegulation,
  selectedDocCode,
  setselectedDocCode,
  setCurrentRegulation,
  onViewDetail,
  showDetailView = false,
  onShowReferences,
  sessionId,
}: ChatMessageProps) {
  // State for feedback functionality
  const [vote, setVote] = useState<"up" | "down" | null>(
    message.feedback?.vote || null
  );
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackText, setFeedbackText] = useState(
    message.feedback?.text || ""
  );
  const [showFeedbackSuccess, setShowFeedbackSuccess] = useState(false);
  const [showCopied, setShowCopied] = useState(false);

  const { user } = useAuth();
  const { currentSessionId } = useChat();

  // Function to toggle the SSE updates visibility
  // const [showSseUpdates, setShowSseUpdates] = useState(!message.isLoading);

  // Get SSE updates and events from the message
  // const sseUpdates = message.sseUpdates || [];
  // const sseEvents = message.sseEvents || [];

  // State for adaptive bubble width
  const [bubbleWidth, setBubbleWidth] = useState("auto");
  const userBubbleRef = useRef<HTMLDivElement>(null);

  // console.log("messages at chatmessage",message);

  // State for Sources dropdown
  // const [isSourcesDropdownOpen, setIsSourcesDropdownOpen] = useState(false);
  // const [isDropdownPinned, setIsDropdownPinned] = useState(false);
  const feedbackInputRef = useRef<HTMLTextAreaElement>(null);

  // Set the user bubble width based on content length
  useEffect(() => {
    if (message.sender === "user" && userBubbleRef.current) {
      const contentLength = message.content.length;
      const contentLines = message.content.split("\n").length;

      // Calculate appropriate width based on content
      if (contentLength < 50) {
        // For very short messages, use narrow width
        setBubbleWidth("fit-content");
      } else if (contentLength < 100) {
        // For short messages
        setBubbleWidth("60%");
      } else if (contentLength < 200) {
        // For medium messages
        setBubbleWidth("75%");
      } else {
        // For long messages
        setBubbleWidth("90%");
      }

      // If there are multiple lines, ensure width is sufficient
      if (contentLines > 2) {
        setBubbleWidth("90%");
      }
    }
  }, [message.content, message.sender]);

  // Helper function to get document title from metadata
  const getDocumentTitle = (
    documentId: string,
    metadata: any[] | undefined
  ): string => {
    if (!metadata || !Array.isArray(metadata)) return documentId;

    const matchingDoc = metadata.find(
      (item) => item.document_id === documentId || item.id === documentId
    );

    return matchingDoc?.document_title || matchingDoc?.title || documentId;
  };

  // Process bold text within parts
  // Custom markdown renderer without dependencies
  // Custom markdown renderer that allows inline source buttons
  const renderMarkdown = (text: string) => {
    const lines = text.split("\n");
    let renderedContent = [];
    let listItems: JSX.Element[] = [];
    let inList = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Process headers (###)
      if (line.startsWith("###")) {
        if (inList) {
          renderedContent.push(
            <ol key={`list-${i}`} className="list-decimal pl-6 mb-3 space-y-1">
              {listItems}
            </ol>
          );
          listItems = [];
          inList = false;
        }

        renderedContent.push(
          <h3
            key={`h3-${i}`}
            className="text-lg font-bold text-[#413A36] mt-4 mb-2 inline-block mr-2"
          >
            {line.substring(4)}
          </h3>
        );
        continue;
      }

      // Process numbered list items
      const numberedListMatch = line.match(/^\d+\.\s+(.*)$/);
      if (numberedListMatch) {
        if (!inList) {
          inList = true;
        }

        const listItemContent = processBoldText(numberedListMatch[1]);

        listItems.push(
          <li key={`li-${i}`} className="mb-1 text-[#494949]">
            {listItemContent}
          </li>
        );
        continue;
      }

      // End the list if current line is not a list item
      if (inList && !line.match(/^\d+\.\s+(.*)$/)) {
        renderedContent.push(
          <ol key={`list-${i}`} className="list-decimal pl-6 mb-3 space-y-1">
            {listItems}
          </ol>
        );
        listItems = [];
        inList = false;
      }

      // Process regular paragraphs
      if (line.trim().length > 0) {
        const isLastParagraph = i === lines.length - 1;

        if (isLastParagraph) {
          renderedContent.push(
            <span key={`p-${i}`} className="inline">
              {processBoldText(line)}
            </span>
          );
        } else {
          renderedContent.push(
            <p key={`p-${i}`} className="mb-3 text-[#494949]">
              {processBoldText(line)}
            </p>
          );
        }
      }
    }

    // Close any open list at the end
    if (inList) {
      renderedContent.push(
        <ol key="list-final" className="list-decimal pl-6 mb-3 space-y-1">
          {listItems}
        </ol>
      );
    }

    return renderedContent;
  };

  // Function to handle bold text marked with ** **
  const processBoldText = (text: string) => {
    const parts = text.split(/(\*\*.*?\*\*)/g);
    return parts.map((part, index) => {
      if (part.startsWith("**") && part.endsWith("**")) {
        return (
          <strong key={index} className="font-bold text-[#413A36]">
            {part.slice(2, -2)}
          </strong>
        );
      }
      return <span key={index}>{part}</span>;
    });
  };

  // Render the new format content with answer parts
  // Render the new format content with answer parts
  // Updated renderContent function for your ChatMessage component
  const renderContent = () => {
    if (
      Array.isArray(message.content) &&
      message.content.length > 0 &&
      message.content[0].answer_part
    ) {
      return (
        <div className="space-y-6">
          {message.content.map((part, index) => (
            <div
              key={`answer-part-${index}`}
              className="text-[16px] text-gray-800 leading-relaxed"
            >
              <div className="flex items-start gap-2 mb-1">
                <div className="flex-grow">
                  {/* Use inline rendering and put source button right after */}
                  <div className="inline">
                    <MarkdownRenderer content={part.answer_part} inline={true} />
                    {part.answer_part_references?.length > 0 && (
                      <SourceButton
                        docRefs={groupReferencesByDocAndPage(
                          part.answer_part_references,
                          message
                        )}
                        message={message}
                        onSelectReference={(
                          allRefs,
                          selectedDocId,
                          specificRef
                        ) => {
                          if (onShowReferences) {
                            onShowReferences(allRefs, message.id, specificRef);
                            setselectedDocCode?.(selectedDocId);
                            onViewDetail();
                          }
                        }}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    // For string content
    if (typeof message.content === "string") {
      return (
        <div className="text-[16px] text-gray-800 leading-relaxed">
          {renderMarkdown(message.content)}
        </div>
      );
    }

    // Fallback for any other format
    return (
      <div className="text-[16px] text-gray-800 leading-relaxed">
        Unable to display message content
      </div>
    );
  };
  // Handle vote functionality
  const handleVote = async (voteType: "up" | "down") => {
    const newVote = vote === voteType ? null : voteType;
    setVote(newVote);

    try {
      const messageContentDict = message.content.reduce((acc, part, index) => {
        acc[index] = part;
        return acc;
      }, {});

      const requestBody = {
        session_id: currentSessionId,
        message_content: messageContentDict,
        feedback_text: feedbackText,
        vote: newVote,
        username: user?.username,
      };

      const response = await fetch(FEEDBACK_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) throw new Error("Failed to save vote");
    } catch (error) {
      console.error("Error saving vote:", error);
    }
  };

  // Handle feedback submission
  const handleFeedbackSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (feedbackText.trim()) {
      try {
        const messageContentDict = message.content.reduce((acc, part, index) => {
          acc[index] = part;
          return acc;
        }, {});

        const requestBody = {
          session_id: currentSessionId,
          message_content: messageContentDict,
          feedback_text: feedbackText,
          vote: vote,
          username: user?.username,
        };

        const response = await fetch(FEEDBACK_ENDPOINT, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) throw new Error("Failed to save feedback");

        setShowFeedbackModal(false);
        setShowFeedbackSuccess(true);
        setFeedbackText("");
        setTimeout(() => setShowFeedbackSuccess(false), 2000);
      } catch (error) {
        console.error("Error saving feedback:", error);
      }
    }
  };
  // Handle copy functionality
  const handleCopy = () => {
    let contentToCopy = "";

    if (Array.isArray(message.content)) {
      // For new format, concatenate all answer parts
      contentToCopy = message.content
        .map((part) => part.answer_part)
        .join("\n\n");
    } else {
      // Fallback for any other case
      contentToCopy = JSON.stringify(message.content);
    }

    navigator.clipboard.writeText(contentToCopy).then(() => {
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000);
    });
  };

  // Handle message click to show references in sidebar
  const handleMessageClick = () => {
    if (
      message.sender === "assistant" &&
      onShowReferences &&
      setCurrentRegulation
    ) {
      // Extract references from all answer parts if content is an array
      let allReferences = getAllReferencesFromMessage(message);

      if (allReferences.length > 0) {
        // Process and group the references
        const groupedRefs = groupReferencesByDocAndPage(allReferences, message);
        const processedRefs = groupedRefs.map((ref) => ref.matchingRef);

        // Pass only these processed references to the sidebar along with the message ID
        onShowReferences(processedRefs, message.id);

        // Create regulation info from the first reference and set current regulation
        if (processedRefs.length > 0) {
          const relevantRegulation = createOrFindRegulation(processedRefs);
          setCurrentRegulation(relevantRegulation);
        }
      }
    }
  };

  // Helper function to create regulation from metadata
  const createOrFindRegulation = (metadata: any[]): RegulationInfo => {
    if (metadata && metadata.length > 0) {
      const topMetadata = metadata[0];
      return {
        id:
          topMetadata.document_code ||
          topMetadata.document_id ||
          topMetadata.id ||
          "unknown",
        title: topMetadata.document_title || topMetadata.title || "Regulation",
        content: Array.isArray(message.content)
          ? message.content.map((part) => part.answer_part).join("\n\n")
          : JSON.stringify(message.content),
        date: topMetadata.date_of_issue || new Date().toDateString(),
        department: "RBI",
        source:
          topMetadata.document_type || topMetadata.Type || "Master Direction",
      };
    }
    return {
      id: `regulation-${Date.now()}`,
      title: "Regulation Information",
      content: Array.isArray(message.content)
        ? message.content.map((part) => part.answer_part).join("\n\n")
        : JSON.stringify(message.content),
      date: new Date().toDateString(),
      department: "RBI",
      source: "Master Direction",
    };
  };

  // Render user message with responsive sizing
  if (message.sender === "user") {
    return (
      <div
        ref={userBubbleRef}
        className="flex justify-end"
        style={{ maxWidth: "100%" }}
      >
        <div
          className="p-5 bg-[#F8F0DF] rounded-tl-[20px] rounded-tr-[8px] rounded-br-[20px] rounded-bl-[20px]"
          style={{
            width: bubbleWidth,
            maxWidth: "90%",
            minWidth: "100px",
            wordBreak: "break-word",
          }}
        >
          <div className="text-black font-medium text-[16px] text-left">
            {message.content}
          </div>
        </div>
      </div>
    );
  }

  // Render assistant message
  if (message.sender === "assistant") {
    return (
      <div className="space-y-1">
        <div
          className="w-full max-w-[] mt-5 bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] rounded-2xl overflow-hidden cursor-pointer"
          onClick={(e) => {
            // Only handle the message click if not clicking on a source button or dropdown
            const target = e.target as Element;
            const isSourceButton = target.closest(".source-button");
            const isSourceDropdown = target.closest(
              "[data-is-source-dropdown]"
            );

            if (!isSourceButton && !isSourceDropdown) {
              handleMessageClick();
            }
          }}
        >
          <div className="p-5 pb-0 text-[#413A36] text-[16px]">
            {renderContent()}
          </div>
        </div>

        {/* Feedback controls row */}
        <div className="flex items-center gap-2 px-3 ml-1">
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleVote("up")}
              className={`p-1 rounded-full transition-all duration-200 ${vote === "up"
                  ? "bg-[#EAE6DE] text-[#FF6B1C]"
                  : "hover:bg-[#EAE6DE] text-gray-400"
                }`}
            >
              <ThumbsUp
                className={`w-4 h-4 ${vote === "up" ? "stroke-2" : ""}`}
              />
            </button>
            <button
              onClick={() => handleVote("down")}
              className={`p-1 rounded-full transition-all duration-200 ${vote === "down"
                  ? "bg-[#EAE6DE] text-[#FF6B1C]"
                  : "hover:bg-[#EAE6DE] text-gray-400"
                }`}
            >
              <ThumbsDown
                className={`w-4 h-4 ${vote === "down" ? "stroke-2" : ""}`}
              />
            </button>
            <button
              onClick={() => setShowFeedbackModal(true)}
              className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
            >
              <MessageSquare className="w-4 h-4" />
            </button>
            <button
              onClick={handleCopy}
              className="p-1 rounded-full hover:bg-[#EAE6DE] text-gray-400 transition-all duration-200"
            >
              <Copy className="w-4 h-4" />
            </button>
          </div>
          <span className="text-xs text-gray-500">
            {new Date(message.timestamp).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
        </div>

        {/* Feedback modal */}
        {showFeedbackModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 w-96 shadow-xl">
              <h3 className="text-[16px] font-semibold text-[#413A36] mb-4">
                Provide Feedback
              </h3>
              <form onSubmit={handleFeedbackSubmit}>
                <textarea
                  ref={feedbackInputRef}
                  value={feedbackText}
                  onChange={(e) => setFeedbackText(e.target.value)}
                  className="w-full h-32 p-3 text-[16px] border border-gray-200 rounded-lg focus:border-transparent resize-none"
                  placeholder="Enter your feedback here..."
                />
                <div className="flex justify-end gap-3 mt-4">
                  <button
                    type="button"
                    onClick={() => setShowFeedbackModal(false)}
                    className="px-4 py-2 text-[16px] font-medium text-gray-600 hover:text-gray-800 transition-colors duration-150"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-[16px] font-medium text-white bg-[#FF6B1C] rounded-lg hover:opacity-90 transition-colors duration-150"
                  >
                    Submit
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Toast notifications */}
        {showCopied && (
          <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
            <Copy className="w-4 h-4" />
            Copied to clipboard!
          </div>
        )}

        {showFeedbackSuccess && (
          <div className="fixed bottom-4 right-4 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg text-[16px] font-medium flex items-center gap-2 z-50">
            <MessageSquare className="w-4 h-4" />
            Feedback received. Thank you!
          </div>
        )}
      </div>
    );
  }

  // Loading message - Updated to match Figma design with loading state text
  if (message.sender === "loading") {
    return (
      <div
        className={`w-full p-5 bg-transparent rounded-2xl mt-5 transition-all duration-400 ease-in-out transform ${message.fadeState || "opacity-100 scale-100"
          }`}
        style={{
          backdropFilter: "blur(10px)",
        }}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center">
            {/* Enhanced loading spinner with multiple animation layers */}
            <Image
              src={loading2.src || loading2}
              alt="Loading"
              width={20}
              height={20}
              className="mr-2"
            />
            {/* <span className="text-[#FF6B1C] font-medium">Complai</span> */}

            {/* Brand name with enhanced styling */}
            <span className="text-[#FF6B1C] font-bold text-lg tracking-wide animate-pulse">
              Complai
            </span>
          </div>

          {/* Loading text with typewriter effect */}
          <div className="text-[#494949] font-medium text-base">
            <span
              className="inline-block animate-bounce"
              style={{ animationDelay: "0ms" }}
            >
              {message.loadingState || "is analysing the regulation"}
            </span>

            {/* Animated dots */}
          </div>
        </div>

        {/* Progress bar effect */}
        {/* <div className="mt-3 w-full bg-white/30 rounded-full h-1 overflow-hidden">
        <div className="h-full bg-gradient-to-r from-[#FF6B1C] to-[#FFA500] rounded-full animate-pulse">
          <div className="h-full bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
        </div>
      </div> */}
      </div>
    );
  }

  // Default fallback
  return null;
}
