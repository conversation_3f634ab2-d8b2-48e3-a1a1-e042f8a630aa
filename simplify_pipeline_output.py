#!/usr/bin/env python3
"""
Simplify the pipeline output by removing unnecessary keys and RBI website URLs
"""

import json
from pathlib import Path

def simplify_notification_result(notification):
    """Simplify a single notification result"""
    
    # Extract only essential information
    simplified = {
        "id": notification.get("notification_id"),
        "title": notification.get("title"),
        "year": notification.get("year"),
        "category": notification.get("llm_analysis", {}).get("category"),
        "confidence": notification.get("llm_analysis", {}).get("confidence"),
        "requires_kb_update": notification.get("llm_analysis", {}).get("requires_kb_update", False)
    }
    
    # Extract document actions (simplified)
    document_actions = notification.get("affected_documents", {}).get("document_actions", [])
    if document_actions:
        simplified["document_actions"] = []
        for action in document_actions:
            simplified_action = {
                "action_type": action.get("action_type"),
                "document_id": action.get("document_id"),
                "reference_number": action.get("reference_number"),
                "confidence": action.get("confidence")
            }
            # Only add non-null values
            simplified_action = {k: v for k, v in simplified_action.items() if v is not None}
            simplified["document_actions"].append(simplified_action)
    
    # Extract new document URL (but filter out RBI website URLs and NOTI134)
    new_doc_url = notification.get("affected_documents", {}).get("new_document_url", "")
    if new_doc_url and not is_unwanted_url(new_doc_url):
        simplified["new_document_url"] = new_doc_url
    
    # Extract effective date if present
    effective_date = notification.get("affected_documents", {}).get("effective_date")
    if effective_date:
        simplified["effective_date"] = effective_date
    
    # Extract related document IDs
    related_docs = notification.get("affected_documents", {}).get("related_document_ids", [])
    if related_docs:
        simplified["related_document_ids"] = related_docs
    
    # Manual review flag
    requires_review = (
        notification.get("affected_documents", {}).get("requires_manual_review", False) or
        notification.get("update_actions", {}).get("requires_manual_review", False)
    )
    if requires_review:
        simplified["requires_manual_review"] = True
    
    return simplified

def is_unwanted_url(url):
    """Check if URL should be filtered out"""
    unwanted_patterns = [
        "NOTI134",
        "www.rbi.org.in/",
        "https://www.rbi.org.in/",
        "Utkarsh30122022.pdf"
    ]
    
    for pattern in unwanted_patterns:
        if pattern in url:
            return True
    return False

def simplify_pipeline_results(input_file, output_file):
    """Simplify the entire pipeline results file"""
    
    print(f"🔄 Simplifying pipeline results...")
    print(f"   📁 Input: {input_file}")
    print(f"   📁 Output: {output_file}")
    
    # Load the original results
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    original_results = data.get("results", [])
    print(f"   📊 Processing {len(original_results)} notifications")
    
    # Simplify each notification
    simplified_results = []
    removed_urls = 0
    
    for notification in original_results:
        simplified = simplify_notification_result(notification)
        simplified_results.append(simplified)
        
        # Count removed URLs
        original_url = notification.get("affected_documents", {}).get("new_document_url", "")
        if original_url and is_unwanted_url(original_url):
            removed_urls += 1
    
    # Create simplified output structure
    simplified_data = {
        "metadata": {
            "total_notifications": len(simplified_results),
            "simplified_date": "2025-07-10",
            "version": "simplified_v1.0",
            "description": "Simplified notification pipeline results with unnecessary keys removed"
        },
        "summary": {
            "total_notifications": len(simplified_results),
            "removed_unwanted_urls": removed_urls,
            "categories": {},
            "actions": {},
            "kb_updates_required": 0
        },
        "notifications": simplified_results
    }
    
    # Calculate summary statistics
    for notif in simplified_results:
        # Category distribution
        category = notif.get("category", "Unknown")
        simplified_data["summary"]["categories"][category] = simplified_data["summary"]["categories"].get(category, 0) + 1
        
        # Action distribution
        for action in notif.get("document_actions", []):
            action_type = action.get("action_type", "Unknown")
            simplified_data["summary"]["actions"][action_type] = simplified_data["summary"]["actions"].get(action_type, 0) + 1
        
        # KB updates
        if notif.get("requires_kb_update", False):
            simplified_data["summary"]["kb_updates_required"] += 1
    
    # Save simplified results
    with open(output_file, 'w') as f:
        json.dump(simplified_data, f, indent=2)
    
    print(f"   ✅ Simplified results saved")
    print(f"   🗑️ Removed {removed_urls} unwanted URLs")
    print(f"   📊 Categories: {simplified_data['summary']['categories']}")
    print(f"   📊 Actions: {simplified_data['summary']['actions']}")
    print(f"   📊 KB Updates Required: {simplified_data['summary']['kb_updates_required']}")

def main():
    """Main function"""
    input_file = Path("real_pipeline_results.json")
    output_file = Path("simplified_pipeline_results.json")
    
    if not input_file.exists():
        print(f"❌ Input file not found: {input_file}")
        return
    
    simplify_pipeline_results(input_file, output_file)
    print(f"\n🎉 Pipeline output simplified successfully!")
    print(f"📁 Simplified results: {output_file}")

if __name__ == "__main__":
    main()
