// //ActionItemsDetail.tsx

"use client";

import { useState, useEffect, useRef } from "react";
import { RegulationInfo } from "@/types/main_types";
import { Download, Edit2, Plus, X, Check, Trash2 } from "lucide-react";
import loading2 from "@/components/assets/icons/loading2.gif"
import Image from "next/image"
import SourceImg from "@/components/assets/icons/sources.png"
import { useAuth } from "@/contexts/AuthContext";
import { ACTION_ITEMS_EDIT_ENDPOINT, ACTION_ITEMS_DOWNLOAD_EXCEL_ENDPOINT, ACTION_ITEMS_DOWNLOAD_WORD_ENDPOINT } from "@/app/utils/Api"

interface ActionItemsDetailProps {
  regulation: RegulationInfo;
  actionItemsData: any;
  isLoading: boolean;
  onBack: () => void;
  onViewDetail: () => void;
  showDetailView: boolean;
  setReferences: (references: any[]) => void;
  setSelectedSpecificRef: (reference: any) => void;
  onClose: () => void;
}

// Updated interface to match new format
interface ObligationItem {
  guideline: string;
  positions: Array<{
    page_number: number;
    bbox: [number, number, number, number];
  }>;
  obligation: string;
  action_item: string;
}

interface ProcessedObligation {
  id: string;
  title: string;
  description: string;
  sources?: {
    count: number;
    label: string;
  };
  actionItems: Array<{
    id: string;
    description: string;
  }>;
}

interface ProcessedGuideline {
  id: string;
  title: string;
  sources?: {
    count: number;
    label: string;
  };
  obligations: ProcessedObligation[];
}

// API service functions (same as before)
const apiService = {
  downloadExcel: async (docId: string, username: string) => {
    try {
      const response = await fetch(ACTION_ITEMS_DOWNLOAD_EXCEL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doc_id: docId,
          username: username
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to download Excel file');
      }

      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'action_items.xlsx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading Excel:', error);
      throw error;
    }
  },

  downloadWord: async (docId: string, username: string) => {
    try {
      const response = await fetch(ACTION_ITEMS_DOWNLOAD_WORD_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doc_id: docId,
          username: username
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to download Word file');
      }

      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'action_items.docx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading Word:', error);
      throw error;
    }
  },

  saveActionItems: async (username: string, actionItemsData: any, guidelines: ProcessedGuideline[]) => {
    try {
      // Convert processed guidelines back to the flat format
      const obligationsArray: ObligationItem[] = [];
      // console.log("guidelines", guidelines);
      // console.log("action items", actionItemsData);

      guidelines.forEach(guideline => {
        // Find the original guideline data to get positions
        const originalObligations = actionItemsData?.obligations?.filter((item: ObligationItem) =>
          item.guideline === guideline.title
        ) || [];

        // Get positions from the first matching original obligation
        const originalPositions = originalObligations.length > 0 && originalObligations[0].positions
          ? originalObligations[0].positions
          : [];

        guideline.obligations.forEach(obligation => {
          obligation.actionItems.forEach(item => {
            obligationsArray.push({
              guideline: guideline.title,
              positions: originalPositions, // Use the original positions
              obligation: obligation.description,
              action_item: item.description
            });
          });
        });
      });

      const payload = {
        username: username,
        updated_actionables: {
          document_number: actionItemsData.document_number,
          document_title: actionItemsData.document_title,
          obligations: obligationsArray,
          s3_url: actionItemsData.s3_url
        }
      };

      const response = await fetch(ACTION_ITEMS_EDIT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save action items');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error saving action items:', error);
      throw error;
    }
  }
};

const autoResizeTextarea = (textarea: HTMLTextAreaElement) => {
  textarea.style.height = 'auto';
  textarea.style.height = Math.max(textarea.scrollHeight, 80) + '50px';
};

export function ActionItemsDetail({
  regulation,
  actionItemsData,
  isLoading,
  onBack,
  onViewDetail,
  showDetailView,
  setReferences,
  setSelectedSpecificRef,
  onClose,
}: ActionItemsDetailProps) {
  // State to track which item is being hovered
  const [hoveredItemId, setHoveredItemId] = useState<string | null>(null);
  const [hoveredItemType, setHoveredItemType] = useState<'guideline' | 'obligation' | 'action' | null>(null);

  //username retrieval
  const { user } = useAuth();
  const username = user?.username;
  const guidelinesRef = useRef<ProcessedGuideline[]>([]);

  // State for managing guidelines data
  const [guidelines, setGuidelines] = useState<ProcessedGuideline[]>([]);

  // State for edit mode
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editingItemType, setEditingItemType] = useState<'guideline' | 'obligation' | 'action' | null>(null);
  const [editText, setEditText] = useState<string>("");

  // State for saving
  const [isSaving, setIsSaving] = useState(false);

  // Process the API data into guidelines, obligations and action items
// Process the API data into guidelines, obligations and action items
const processActionItemsData = (data: any): ProcessedGuideline[] => {
  if (!data || !data.obligations) return [];

  // Sort obligations by page number first (ascending order)
  const sortedObligations = [...data.obligations].sort((a, b) => {
    // Get the first page number from positions array for comparison
    const aPageNum = a.positions && a.positions.length > 0 ? a.positions[0].page_number : Infinity;
    const bPageNum = b.positions && b.positions.length > 0 ? b.positions[0].page_number : Infinity;
    
    return aPageNum - bPageNum; // Ascending order (smallest page number first)
  });

  // Group by guideline first using sorted obligations
  const groupedByGuideline = sortedObligations.reduce(
    (acc: any, item: ObligationItem, index: number) => {
      const guidelineKey = item.guideline;
      if (!acc[guidelineKey]) {
        acc[guidelineKey] = {
          id: `guide_${Object.keys(acc).length}`,
          title: item.guideline,
          sources: item.positions && item.positions.length > 0 ? {
            count: item.positions.length,
            label: "Source",
          } : undefined,
          obligations: {}
        };
      }

      // Group by obligation within guideline
      const obligationKey = item.obligation;
      if (!acc[guidelineKey].obligations[obligationKey]) {
        acc[guidelineKey].obligations[obligationKey] = {
          id: `obl_${index}`,
          title: obligationKey,
          description: item.obligation,
          actionItems: [],
        };
      }

      acc[guidelineKey].obligations[obligationKey].actionItems.push({
        id: `act_${index}`,
        description: item.action_item,
      });

      return acc;
    },
    {}
  );

  // Convert to final structure
  return Object.values(groupedByGuideline).map((guideline: any) => ({
    ...guideline,
    obligations: Object.values(guideline.obligations)
  }));
};

  // Initialize guidelines data
  useEffect(() => {
    const initialGuidelines = processActionItemsData(actionItemsData);
    setGuidelines(initialGuidelines);
  }, [actionItemsData]);

  useEffect(() => {
    guidelinesRef.current = guidelines;
  }, [guidelines]);

  // State for download dropdown
  const [showDownloadDropdown, setShowDownloadDropdown] = useState(false);

  const handleDownloadExcel = async () => {
    if (!username) {
      alert("Username not found. Please log in again.");
      setShowDownloadDropdown(false);
      return;
    }

    if (isSaving) {
      alert("Please wait for the current changes to be saved before downloading.");
      setShowDownloadDropdown(false);
      return;
    }

    try {
      await apiService.downloadExcel(actionItemsData.document_number, username);
    } catch (error) {
      alert("Failed to download Excel file. Please try again.");
    }
    setShowDownloadDropdown(false);
  };

  const handleDownloadWord = async () => {
    if (!username) {
      alert("Username not found. Please log in again.");
      setShowDownloadDropdown(false);
      return;
    }

    if (isSaving) {
      alert("Please wait for the current changes to be saved before downloading.");
      setShowDownloadDropdown(false);
      return;
    }

    try {
      await apiService.downloadWord(actionItemsData.document_number, username);
    } catch (error) {
      alert("Failed to download Word file. Please try again.");
    }
    setShowDownloadDropdown(false);
  };

  // Handle source button click
  const handleSourceClick = (guidelineId: string) => {
    const guideline = guidelines.find(g => g.id === guidelineId);
    if (!guideline) return;

    // Find the original obligation data from actionItemsData to get positions
    const originalObligation = actionItemsData?.obligations?.find((item: ObligationItem) =>
      item.guideline === guideline.title
    );

    if (originalObligation && originalObligation.positions && originalObligation.positions.length > 0) {
      const referenceItem = {
        Type: "",
        addressee: "",
        chunk_id: originalObligation.positions[0].page_number,
        date_of_issue: "",
        dept: "",
        document_code: actionItemsData.document_number || "",
        id: `ref_${guidelineId}`,
        pdf_filename: "",
        pdf_link: "",
        positions: originalObligation.positions.map(pos => ({
          page: pos.page_number,
          bboxes: pos.bboxes,
        })),
        revision_s3_url: actionItemsData.s3_url || "",
        section_summary: originalObligation.obligation || "",
        title: actionItemsData.document_title || "",
        document_id: actionItemsData.document_number || "",
      };

      setSelectedSpecificRef(referenceItem);
      onViewDetail();
    } else {
      // console.log("No position data available for this guideline");
    }
  };

  // GUIDELINE ACTIONS
  const handleEditGuideline = (guidelineId: string) => {
    const guideline = guidelines.find(g => g.id === guidelineId);
    if (guideline) {
      setEditingItemId(guidelineId);
      setEditingItemType('guideline');
      setEditText(guideline.title);
      setHoveredItemId(null);
    }
  };

  const handleAddGuideline = async (afterGuidelineId?: string) => {
    const newGuidelineId = `guide_${Date.now()}`;
    const newGuideline: ProcessedGuideline = {
      id: newGuidelineId,
      title: "New guideline - click to edit",
      obligations: [{
        id: `obl_${Date.now()}`,
        title: "New obligation - click to edit",
        description: "New obligation - click to edit",
        actionItems: [{
          id: `act_${Date.now()}`,
          description: "New action item - click to edit",
        }],
      }],
    };

    let updatedGuidelines: ProcessedGuideline[];

    if (afterGuidelineId) {
      // Find the index of the guideline to insert after
      const guidelineIndex = guidelinesRef.current.findIndex(g => g.id === afterGuidelineId);

      if (guidelineIndex !== -1) {
        // Insert the new guideline after the specified one
        updatedGuidelines = [...guidelinesRef.current];
        updatedGuidelines.splice(guidelineIndex + 1, 0, newGuideline);
      } else {
        // If guideline not found, add at the end
        updatedGuidelines = [...guidelinesRef.current, newGuideline];
      }
    } else {
      // If no afterGuidelineId provided, add at the end
      updatedGuidelines = [...guidelinesRef.current, newGuideline];
    }

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;

    await saveChanges();

    // Auto-edit the new guideline
    setTimeout(() => {
      handleEditGuideline(newGuidelineId);
    }, 100);
  };

  const handleDeleteGuideline = async (guidelineId: string) => {
    if (guidelines.length <= 1) {
      alert("Cannot delete the last guideline.");
      return;
    }

    const updatedGuidelines = guidelinesRef.current.filter(g => g.id !== guidelineId);
    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;
    await saveChanges();
  };

  // OBLIGATION ACTIONS
  const handleEditObligation = (obligationId: string) => {
    const obligation = guidelines
      .flatMap(g => g.obligations)
      .find(o => o.id === obligationId);

    if (obligation) {
      setEditingItemId(obligationId);
      setEditingItemType('obligation');
      setEditText(obligation.description);
      setHoveredItemId(null);
    }
  };

  const handleAddObligation = async (guidelineId: string, afterObligationId?: string) => {
    const newObligationId = `obl_${Date.now()}`;
    const newObligation: ProcessedObligation = {
      id: newObligationId,
      title: "New obligation - click to edit",
      description: "New obligation - click to edit",
      actionItems: [{
        id: `act_${Date.now()}`,
        description: "New action item - click to edit",
      }],
    };

    const updatedGuidelines = guidelinesRef.current.map(guideline => {
      if (guideline.id === guidelineId) {
        let updatedObligations = [...guideline.obligations];

        if (afterObligationId) {
          const obligationIndex = updatedObligations.findIndex(o => o.id === afterObligationId);
          updatedObligations.splice(obligationIndex + 1, 0, newObligation);
        } else {
          updatedObligations.push(newObligation);
        }

        return {
          ...guideline,
          obligations: updatedObligations,
        };
      }
      return guideline;
    });

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;
    await saveChanges();

    // Auto-edit the new obligation
    setTimeout(() => {
      handleEditObligation(newObligationId);
    }, 100);
  };

  const handleDeleteObligation = async (obligationId: string, guidelineId: string) => {
    const guideline = guidelines.find(g => g.id === guidelineId);
    if (guideline && guideline.obligations.length <= 1) {
      alert("Cannot delete the last obligation. Delete the guideline instead.");
      return;
    }

    const updatedGuidelines = guidelinesRef.current.map(guideline => ({
      ...guideline,
      obligations: guideline.obligations.filter(o => o.id !== obligationId),
    }));

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;
    await saveChanges();
  };

  // ACTION ITEM ACTIONS
  const handleEditActionItem = (actionId: string) => {
    const actionItem = guidelines
      .flatMap(g => g.obligations)
      .flatMap(o => o.actionItems)
      .find(a => a.id === actionId);

    if (actionItem) {
      setEditingItemId(actionId);
      setEditingItemType('action');
      setEditText(actionItem.description);
      setHoveredItemId(null);
    }
  };

  const handleAddActionItem = async (obligationId: string, afterActionId?: string) => {
    const newActionItemId = `act_${Date.now()}`;
    const newActionItem = {
      id: newActionItemId,
      description: "New action item - click to edit",
    };

    const updatedGuidelines = guidelinesRef.current.map(guideline => ({
      ...guideline,
      obligations: guideline.obligations.map(obligation => {
        if (obligation.id === obligationId) {
          let updatedActionItems = [...obligation.actionItems];

          if (afterActionId) {
            const actionIndex = updatedActionItems.findIndex(a => a.id === afterActionId);
            updatedActionItems.splice(actionIndex + 1, 0, newActionItem);
          } else {
            updatedActionItems.push(newActionItem);
          }

          return {
            ...obligation,
            actionItems: updatedActionItems,
          };
        }
        return obligation;
      }),
    }));

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;
    await saveChanges();

    // Auto-edit the new action item
    setTimeout(() => {
      handleEditActionItem(newActionItemId);
    }, 100);
  };

  const handleDeleteActionItem = async (actionId: string, obligationId: string) => {
    const obligation = guidelines
      .flatMap(g => g.obligations)
      .find(o => o.id === obligationId);

    if (obligation && obligation.actionItems.length <= 1) {
      alert("Cannot delete the last action item. Delete the obligation instead.");
      return;
    }

    const updatedGuidelines = guidelinesRef.current.map(guideline => ({
      ...guideline,
      obligations: guideline.obligations.map(obligation => ({
        ...obligation,
        actionItems: obligation.actionItems.filter(a => a.id !== actionId),
      })),
    }));

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;
    await saveChanges();
  };

  // SAVE AND CANCEL EDIT
  const handleSaveEdit = async () => {
    if (!editingItemId || !editText.trim() || !editingItemType) return;

    const updatedGuidelines = guidelinesRef.current.map(guideline => {
      if (editingItemType === 'guideline' && guideline.id === editingItemId) {
        return { ...guideline, title: editText.trim() };
      }

      return {
        ...guideline,
        obligations: guideline.obligations.map(obligation => {
          if (editingItemType === 'obligation' && obligation.id === editingItemId) {
            return { ...obligation, description: editText.trim(), title: editText.trim() };
          }

          return {
            ...obligation,
            actionItems: obligation.actionItems.map(action =>
              editingItemType === 'action' && action.id === editingItemId
                ? { ...action, description: editText.trim() }
                : action
            ),
          };
        }),
      };
    });

    setGuidelines(updatedGuidelines);
    guidelinesRef.current = updatedGuidelines;

    setEditingItemId(null);
    setEditingItemType(null);
    setEditText("");

    await saveChanges();
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
    setEditingItemType(null);
    setEditText("");
  };

  // Save changes to database
  const saveChanges = async () => {
    if (!username) {
      alert("Username not found. Please log in again.");
      return;
    }

    setIsSaving(true);
    try {
      await apiService.saveActionItems(username, actionItemsData, guidelinesRef.current);
    } catch (error) {
      console.error("Failed to save changes:", error);
      alert("Failed to save changes. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // Helper function to find guideline ID for a given obligation or action
  const findGuidelineId = (itemId: string, itemType: 'obligation' | 'action'): string => {
    for (const guideline of guidelines) {
      if (itemType === 'obligation') {
        if (guideline.obligations.some(o => o.id === itemId)) {
          return guideline.id;
        }
      } else {
        for (const obligation of guideline.obligations) {
          if (obligation.actionItems.some(a => a.id === itemId)) {
            return guideline.id;
          }
        }
      }
    }
    return '';
  };

  // Helper function to find obligation ID for a given action
  const findObligationId = (actionId: string): string => {
    for (const guideline of guidelines) {
      for (const obligation of guideline.obligations) {
        if (obligation.actionItems.some(a => a.id === actionId)) {
          return obligation.id;
        }
      }
    }
    return '';
  };

  const handleBackToDocSearch = () => {
    onBack();
    setReferences([]);
    // console.log("onclose is called");
    onClose();
  };


  // Loading state management
  const [loadingPhase, setLoadingPhase] = useState(1);
  const [fadeState, setFadeState] = useState("opacity-100");

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.download-dropdown')) {
        setShowDownloadDropdown(false);
      }
    };

    if (showDownloadDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDownloadDropdown]);

  useEffect(() => {
    if (isLoading) {
      setLoadingPhase(1);
      setFadeState("opacity-100");

      const firstTimer = setTimeout(() => {
        setFadeState("opacity-0");

        setTimeout(() => {
          setLoadingPhase(2);
          setFadeState("opacity-100");

          const secondTimer = setTimeout(() => {
            setFadeState("opacity-0");
          }, 3000);

          return () => clearTimeout(secondTimer);
        }, 500);
      }, 3000);

      return () => clearTimeout(firstTimer);
    }
  }, [isLoading]);

  useEffect(() => {
    if (!isLoading) {
      setLoadingPhase(1);
      setFadeState("opacity-100");
    }
  }, [isLoading]);

  if (isLoading) {
    return (
      <div className="flex flex-col w-full max-w-[900px] mx-2 bg-transpaent py-5 px-4">
        <div className="flex items-center justify-center h-64">
          <div
            className={`w-full p-5 bg-transparent rounded-2xl transition-opacity duration-500 ${fadeState}`}
          >
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                <div className="w-5 h-5 mr-2">
                  <Image
                    src={loading2.src || loading2}
                    alt="Loading"
                    width={20}
                    height={20}
                    className="mr-2"
                  />
                </div>
                <span className="text-[#FF6B1C] font-medium">Complai</span>
              </div>
              <div className="text-[#494949] font-medium">
                {loadingPhase === 1
                  ? "is analysing the regulation"
                  : "is generating action items"}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full max-w-[900px] mx-2 bg-transparent py-5 px-4">
      <button
        onClick={handleBackToDocSearch}
        className="flex items-center gap-2 mb-4 p-2 text-[#494949] hover:text-[#333] transition-colors duration-200"
      >
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="transform"
        >
          <path
            d="M19 12H5M5 12L12 19M5 12L12 5"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
      {/* Title and download button */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div className="flex items-center gap-2 mb-4 md:mb-0">
          <h1 className="text-[#494949] text-[24px] font-bold">
            Action Items
          </h1>
          {isSaving && (
            <div className="flex items-center gap-1 text-[#FF6B1C] text-sm">
              <div className="w-4 h-4">
                <Image
                  src={loading2.src || loading2}
                  alt="Saving..."
                  width={16}
                  height={16}
                />
              </div>
              Saving...
            </div>
          )}
        </div>
        <div className="relative download-dropdown">
          <button
            onClick={() => setShowDownloadDropdown(!showDownloadDropdown)}
            className="flex items-center gap-2 bg-[#D9CCB6] border border-[#CFBFAC] hover:bg-[#E9E6E0] text-[#494949] py-2 px-4 rounded-[8px] transition-colors"
            disabled={isSaving}
          >
            <Download size={20} />
            Download Actions
          </button>

          {showDownloadDropdown && (
            <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-[#CFBFAC] rounded-[8px] shadow-lg z-10">
              <button
                onClick={handleDownloadExcel}
                className="w-full text-left px-4 py-3 text-[#494949] hover:bg-[#F5F3F0] transition-colors rounded-t-[8px] flex items-center gap-2 bg-[#D9CCB6] border-b border-[#CFBFAC]"
              >
                <Download size={20} />
                as Excel
              </button>
              <button
                onClick={handleDownloadWord}
                className="w-full text-left px-4 py-3 text-[#494949] hover:bg-[#F5F3F0] transition-colors rounded-b-[8px] flex items-center gap-2 bg-[#D9CCB6]"
              >
                <Download size={20} />
                as Word
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Table using HTML table for proper cell merging */}
      {/* Fully responsive table without horizontal scroll */}
      <div className="border border-[#A5998F] rounded-lg overflow-hidden">
        <table className="w-full border-collapse table-fixed">
          <colgroup>
            <col className="w-1/3" />
            <col className="w-1/3" />
            <col className="w-1/3" />
          </colgroup>
          <thead>
            <tr className="bg-transparent">
              <th className="text-left pt-4 pb-2 pl-4 text-[12px] font-semibold text-[#81746C] border-r border-b border-[#A5998F]">
                Guidelines
              </th>
              <th className="text-left pt-4 pb-2 pl-4 text-[12px] font-semibold text-[#81746C] border-r border-b border-[#A5998F]">
                Obligations
              </th>
              <th className="text-left pt-4 pb-2 pl-4 text-[12px] font-semibold text-[#81746C] border-b border-[#A5998F]">
                Action Items
              </th>
            </tr>
          </thead>
          <tbody>
            {guidelines.length === 0 ? (
              <tr>
                <td colSpan={3} className="p-8 text-center text-[#7B7B7B]">
                  No action items available for this regulation.
                </td>
              </tr>
            ) : (
              guidelines.map((guideline) => {
                const totalRows = guideline.obligations.reduce((sum, obl) => sum + obl.actionItems.length, 0);
                let currentRow = 0;

                return guideline.obligations.map((obligation) =>
                  obligation.actionItems.map((actionItem, actionIndex) => {
                    const isFirstRowForGuideline = currentRow === 0;
                    const isFirstRowForObligation = actionIndex === 0;
                    const obligationRowSpan = obligation.actionItems.length;

                    const row = (
                      <tr key={`${guideline.id}-${obligation.id}-${actionItem.id}`} className="border-b border-[#A5998F]">
                        {/* Guidelines column - only show for first row */}
                        {isFirstRowForGuideline && (
                          <td
                            rowSpan={totalRows}
                            className="p-2 sm:p-4 border-r border-[#A5998F] align-top relative w-1/3"
                            onMouseEnter={() => {
                              setHoveredItemId(guideline.id);
                              setHoveredItemType('guideline');
                            }}
                            onMouseLeave={() => {
                              setHoveredItemId(null);
                              setHoveredItemType(null);
                            }}
                          >
                            {editingItemId === guideline.id && editingItemType === 'guideline' ? (
                              <div className="flex flex-col sm:flex-row items-start gap-2">
                                <textarea
                                  ref={(el) => {
                                    if (el) {
                                      autoResizeTextarea(el);
                                    }
                                  }}
                                  value={editText}
                                  onChange={(e) => {
                                    setEditText(e.target.value);
                                    autoResizeTextarea(e.target);
                                  }}
                                  className="flex-1 text-[#494949] rounded-md px-2 py-1 sm:px-3 sm:py-2 resize-none bg-transparent border-0 w-full text-xs sm:text-sm outline-none focus:outline-none scrollbar-hide"
                                  style={{
                                    minHeight: '80px',
                                    maxHeight: '400px',
                                    overflowY: 'auto'
                                  }}
                                  autoFocus
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !e.shiftKey) {
                                      e.preventDefault();
                                      handleSaveEdit();
                                    } else if (e.key === 'Escape') {
                                      handleCancelEdit();
                                    }
                                  }}
                                />

                                <div className="flex sm:flex-col gap-1 w-full sm:w-auto justify-center">
                                  <button
                                    onClick={handleSaveEdit}
                                    className="p-1 text-green-600 hover:text-green-800 focus:outline-none flex-1 sm:flex-none"
                                    disabled={isSaving}
                                  >
                                    <Check size={14} />
                                  </button>
                                  <button
                                    onClick={handleCancelEdit}
                                    className="p-1 text-red-600 hover:text-red-800 focus:outline-none flex-1 sm:flex-none"
                                    disabled={isSaving}
                                  >
                                    <X size={14} />
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="pr-8 sm:pr-12">
                                  <p className="text-xs sm:text-sm text-[#7B7B7B] break-words leading-tight">{guideline.title}</p>
                                  {guideline.sources && (
                                    <div className="mt-2 sm:mt-3 cursor-pointer" onClick={() => handleSourceClick(guideline.id)}>
                                      <span className="inline-flex items-center justify-center px-1 py-0.5 text-[8px] sm:text-[9px] text-[#413A36] font-medium cursor-pointer rounded-[40px] border border-[#D7CAC4] bg-[rgba(255,255,255,0.36)]">
                                        <img src={SourceImg.src || SourceImg} alt="Sources" className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                        <span className="hidden sm:inline">{guideline.sources.label}</span>
                                        <span className="sm:hidden">Src</span>
                                      </span>
                                    </div>
                                  )}
                                </div>

                                {/* Guideline action icons */}
                                {hoveredItemId === guideline.id && hoveredItemType === 'guideline' && !isSaving && (
                                  <div className="absolute top-1/2 -translate-y-1/2 right-4 z-10">
                                    <button
                                      onClick={() => handleEditGuideline(guideline.id)}
                                      className="absolute top-2 right-7 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Edit guideline"
                                    >
                                      <Edit2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                    </button>

                                    <button
                                      onClick={() => handleAddGuideline(guideline.id)}
                                      className="absolute -top-6 right-3 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Add obligation"
                                    >
                                      <Plus size={14} className="sm:w-4 sm:h-4" />
                                    </button>

                                    <button
                                      onClick={() => handleDeleteGuideline(guideline.id)}
                                      className="absolute top-2 right-0 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Delete guideline"
                                    >
                                      <Trash2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                    </button>
                                  </div>
                                )}
                              </>
                            )}
                          </td>
                        )}

                        {/* Obligations column - only show for first action item of obligation */}
                        {isFirstRowForObligation && (
                          <td
                            rowSpan={obligationRowSpan}
                            className="p-2 sm:p-4 border-r border-[#A5998F] align-top relative w-1/3"
                            onMouseEnter={() => {
                              setHoveredItemId(obligation.id);
                              setHoveredItemType('obligation');
                            }}
                            onMouseLeave={() => {
                              setHoveredItemId(null);
                              setHoveredItemType(null);
                            }}
                          >
                            {editingItemId === obligation.id && editingItemType === 'obligation' ? (
                              <div className="flex flex-col sm:flex-row items-start gap-2">
                                <textarea
                                  ref={(el) => {
                                    if (el) {
                                      autoResizeTextarea(el);
                                    }
                                  }}
                                  value={editText}
                                  onChange={(e) => {
                                    setEditText(e.target.value);
                                    autoResizeTextarea(e.target);
                                  }}
                                  className="flex-1 text-[#494949] rounded-md px-2 py-1 sm:px-3 sm:py-2 resize-none bg-transparent border-0 w-full text-xs sm:text-sm outline-none focus:outline-none scrollbar-hide"
                                  style={{
                                    minHeight: '60px',
                                    maxHeight: '300px',
                                    overflowY: 'auto'
                                  }}
                                  autoFocus
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !e.shiftKey) {
                                      e.preventDefault();
                                      handleSaveEdit();
                                    } else if (e.key === 'Escape') {
                                      handleCancelEdit();
                                    }
                                  }}
                                />

                                <div className="flex sm:flex-col gap-1 w-full sm:w-auto justify-center">
                                  <button
                                    onClick={handleSaveEdit}
                                    className="p-1 text-green-600 hover:text-green-800 focus:outline-none flex-1 sm:flex-none"
                                    disabled={isSaving}
                                  >
                                    <Check size={14} />
                                  </button>
                                  <button
                                    onClick={handleCancelEdit}
                                    className="p-1 text-red-600 hover:text-red-800 focus:outline-none flex-1 sm:flex-none"
                                    disabled={isSaving}
                                  >
                                    <X size={14} />
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="pr-8 sm:pr-12">
                                  <p className="text-xs sm:text-sm text-[#7B7B7B] break-words leading-tight">{obligation.description}</p>
                                </div>

                                {/* Obligation action icons */}
                                {hoveredItemId === obligation.id && hoveredItemType === 'obligation' && !isSaving && (
                                  <div className="absolute top-1/2 -translate-y-1/2 right-4 z-10">
                                    <button
                                      onClick={() => handleEditObligation(obligation.id)}
                                      className="absolute top-2 right-7 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Edit obligation"
                                    >
                                      <Edit2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                    </button>

                                    <button
                                      onClick={() => handleAddObligation(guideline.id, obligation.id)}
                                      className="absolute -top-6 right-3 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Add obligation below"
                                    >
                                      <Plus size={14} className="sm:w-4 sm:h-4" />
                                    </button>

                                    <button
                                      onClick={() => handleDeleteObligation(obligation.id, guideline.id)}
                                      className="absolute top-2 right-0 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                      title="Delete obligation"
                                    >
                                      <Trash2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                    </button>
                                  </div>
                                )}
                              </>
                            )}
                          </td>
                        )}

                        {/* Action Items column - always show */}
                        <td
                          className="p-2 sm:p-4 align-top relative w-1/3"
                          onMouseEnter={() => {
                            setHoveredItemId(actionItem.id);
                            setHoveredItemType('action');
                          }}
                          onMouseLeave={() => {
                            setHoveredItemId(null);
                            setHoveredItemType(null);
                          }}
                        >
                          {editingItemId === actionItem.id && editingItemType === 'action' ? (
                            <div className="flex flex-col sm:flex-row items-start gap-2">
                              <textarea
                                ref={(el) => {
                                  if (el) {
                                    autoResizeTextarea(el);
                                  }
                                }}
                                value={editText}
                                onChange={(e) => {
                                  setEditText(e.target.value);
                                  autoResizeTextarea(e.target);
                                }}
                                className="flex-1 text-[#494949] rounded-md px-2 py-1 sm:px-3 sm:py-2 resize-none bg-transparent border-0 w-full text-xs sm:text-sm outline-none focus:outline-none scrollbar-hide"
                                style={{
                                  minHeight: '50px',
                                  maxHeight: '250px',
                                  overflowY: 'auto'
                                }}
                                autoFocus
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    handleSaveEdit();
                                  } else if (e.key === 'Escape') {
                                    handleCancelEdit();
                                  }
                                }}
                              />
                              <div className="flex sm:flex-col gap-1 w-full sm:w-auto justify-center">
                                <button
                                  onClick={handleSaveEdit}
                                  className="p-1 text-green-600 hover:text-green-800 focus:outline-none flex-1 sm:flex-none"
                                  disabled={isSaving}
                                >
                                  <Check size={14} />
                                </button>
                                <button
                                  onClick={handleCancelEdit}
                                  className="p-1 text-red-600 hover:text-red-800 focus:outline-none flex-1 sm:flex-none"
                                  disabled={isSaving}
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <>
                              <div className="pr-8 sm:pr-12">
                                <p className="text-xs sm:text-sm text-[#494949] break-words leading-tight">{actionItem.description}</p>
                              </div>

                              {/* Action item action icons */}
                              {hoveredItemId === actionItem.id && hoveredItemType === 'action' && !isSaving && (
                                <div className="absolute top-1/2 -translate-y-1/2 right-4 z-10">
                                  <button
                                    onClick={() => handleEditActionItem(actionItem.id)}
                                    className="absolute top-2 right-7 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                    title="Edit action item"
                                  >
                                    <Edit2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                  </button>

                                  <button
                                    onClick={() => handleAddActionItem(obligation.id, actionItem.id)}
                                    className="absolute -top-6 right-3 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                    title="Add action item below"
                                  >
                                    <Plus size={14} className="sm:w-4 sm:h-4" />
                                  </button>

                                  <button
                                    onClick={() => handleDeleteActionItem(actionItem.id, obligation.id)}
                                    className="absolute top-2 right-0 bg-white rounded-full shadow-md border border-gray-200 p-0.5 sm:p-1 hover:bg-gray-50 focus:outline-none transition-all duration-200"
                                    title="Delete action item"
                                  >
                                    <Trash2 size={12} className="sm:w-[14px] sm:h-[14px]" />
                                  </button>
                                </div>
                              )}
                            </>
                          )}
                        </td>
                      </tr>
                    );

                    currentRow++;
                    return row;
                  })
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}