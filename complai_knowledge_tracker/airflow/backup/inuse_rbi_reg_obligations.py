# dag_ingest_rbi_regulation_action_items.py

from datetime import datetime, timedelta
import os
import uuid
from typing import List, Dict

from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable

import boto3
from bs4 import BeautifulSoup
from pydantic import BaseModel
from openai import OpenAI
from pymongo import MongoClient


# -----------------------------
# Pydantic models for structured output
# -----------------------------
class Obligation(BaseModel):
    circular_number: str
    circular_title: str
    para: str
    obligation: str
    action_item: str

class ObligationList(BaseModel):
    obligations_list: List[Obligation]

# ----------------------------------------------------------------------------
# PDF Parsing and HTML AST Construction
# ----------------------------------------------------------------------------

def parse_pdf_to_html_ast(pdf_path: str) -> BeautifulSoup:
    """
    Parse PDF and build HTML AST (<section>, <h1>-<h6>, <p>, <ul>/<ol>, <li>)
    using font-size, boldness, spacing. Attach data-page/bbox attributes.
    """
    doc = fitz.open(pdf_path)
    # Collect font-size frequencies
    size_counts: Dict[float,int] = {}
    for page in doc:
        blocks = page.get_text("dict")["blocks"]
        for blk in blocks:
            if blk.get("type") != 0:
                continue
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    size = round(span.get("size",0),1)
                    size_counts[size] = size_counts.get(size,0) + 1
    # Determine body text size (most common)
    if not size_counts:
        body_size = 0
    else:
        body_size = max(size_counts, key=size_counts.get)
    # Map larger sizes to heading levels
    heading_sizes = sorted([s for s in size_counts.keys() if s > body_size], reverse=True)
    size_to_level: Dict[float,int] = {}
    for idx,size in enumerate(heading_sizes[:6]):
        size_to_level[size] = idx+1
    max_heading = 6

    soup = BeautifulSoup("", "html.parser")
    root = soup.new_tag("div")
    soup.append(root)

    # Stack: (section_tag, level)
    section_stack: List[tuple] = [(root, 0)]

    for page_num, page in enumerate(doc, start=1):
        for blk in page.get_text("dict")["blocks"]:
            if blk.get("type") != 0:
                continue
            spans = [span for line in blk.get("lines",[]) for span in line.get("spans",[])]
            if not spans:
                continue
            block_max = max(round(span.get("size",0),1) for span in spans)
            lines_text = []
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    lines_text.append(span.get("text",""))
                lines_text.append("\n")
            text = "".join(lines_text).strip()
            if not text:
                continue

            # Heading detection
            if block_max > body_size:
                level = size_to_level.get(round(block_max,1), max_heading)
                tag = soup.new_tag(f"h{level}")
                tag.string = text
                tag["data-page"] = str(page_num)
                tag["data-bbox"] = json.dumps(blk.get("bbox"))
                # Pop stack until parent level < this level
                while section_stack and section_stack[-1][1] >= level:
                    section_stack.pop()
                parent = section_stack[-1][0]
                sec = soup.new_tag("section")
                sec["data-page"] = str(page_num)
                sec["data-bbox"] = json.dumps(blk.get("bbox"))
                parent.append(sec)
                sec.append(tag)
                section_stack.append((sec, level))
            else:
                parent = section_stack[-1][0]
                flat = text.replace("\n"," ").strip()
                # List detection
                if re.match(r"^(\u2022|\*|-|\d+[\.\)])\s+", flat):
                    if re.match(r"^\d+[\.\)]\s+", flat):
                        lst_name = "ol"
                    else:
                        lst_name = "ul"
                    last = parent.contents[-1] if parent.contents else None
                    if not (hasattr(last,"name") and last.name==lst_name):
                        lst = soup.new_tag(lst_name)
                        lst["data-page"] = str(page_num)
                        lst["data-bbox"] = json.dumps(blk.get("bbox"))
                        parent.append(lst)
                    else:
                        lst = last
                    li = soup.new_tag("li")
                    li.string = flat
                    li["data-page"] = str(page_num)
                    li["data-bbox"] = json.dumps(blk.get("bbox"))
                    lst.append(li)
                else:
                    p = soup.new_tag("p")
                    p.string = flat
                    p["data-page"] = str(page_num)
                    p["data-bbox"] = json.dumps(blk.get("bbox"))
                    parent.append(p)
    return soup



# -----------------------------
# Default args and DAG definition
# -----------------------------
default_args = {
    'owner': 'you',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

with DAG(
    dag_id='rbi_regulation_actionables_ingest',
    default_args=default_args,
    description='Ingest extracted RBI obligations into MongoDB',
    schedule_interval='@daily',
    start_date=datetime(2025, 5, 20),
    catchup=False,
    tags=['rbi', 'compliance'],
) as dag:

    @task
    def list_pdf_files() -> List[str]:
        s3 = boto3.client(
            's3',
            aws_access_key_id=Variable.get("aws_access_key_id"),
            aws_secret_access_key=Variable.get("aws_secret_access_key"),
        )
        bucket = Variable.get("s3_bucket_name")
        resp = s3.list_objects_v2(Bucket=bucket, Prefix=Variable.get("s3_prefix", ""))
        return [
            obj['Key']
            for obj in resp.get('Contents', [])
            if obj['Key'].lower().endswith('.pdf')
        ]

    @task
    def download_and_parse(file_key: str) -> str:
        bucket = Variable.get("s3_bucket_name")
        local_path = f"/tmp/{uuid.uuid4()}_{os.path.basename(file_key)}"
        s3 = boto3.client(
            's3',
            aws_access_key_id=Variable.get("aws_access_key_id"),
            aws_secret_access_key=Variable.get("aws_secret_access_key"),
        )
        s3.download_file(bucket, file_key, local_path)
        # convert PDF to HTML AST
        soup = parse_pdf_to_html_ast(local_path)
        return str(soup)

    @task
    def extract_obligations(html_text: str) -> List[Dict]:
        client_openai = OpenAI(api_key=Variable.get("openai_api_key"))

        # prompt adapted to HTML input
        prompt = (
            "Role: You are an expert compliance analyst with extensive experience in RBI regulatory documents.\n"
            "Your task is to extract all obligations from the provided RBI regulation HTML document.\n\n"
            "Goal: Extract a JSON array where each object contains exactly the following fields:\n"
            "  - 'circular_number'\n"
            "  - 'circular_title'\n"
            "  - 'para'\n"
            "  - 'obligation'\n"
            "  - 'action_item'\n\n"
            "Instructions:\n"
            "1. Identify the Circular Number and Title from the HTML header.\n"
            "2. Locate each paragraph/section containing obligations (keywords: 'shall', 'must', 'required to').\n"
            "3. For each, capture the section identifier as 'para', the full obligation text, and generate a concise 'action_item'.\n"
            "4. Output only a JSON array of objects with those five keys, no extras.\n\n"
            f"HTML Document:\n{html_text}"
        )

        completion = client_openai.beta.chat.completions.parse(
            model="gpt-4.1-2025-04-14",
            messages=[
                {"role": "system", "content": "You extract structured obligations from RBI HTML docs."},
                {"role": "user", "content": prompt},
            ],
            response_format=ObligationList
        )
        obligations = completion.choices[0].message.parsed.obligations_list
        # convert Pydantic models to plain dicts
        return [obl.dict() for obl in obligations]

    @task
    def upsert_actionables(file_key: str, actionables: List[Dict]):
        document_id = os.path.splitext(os.path.basename(file_key))[0]
        mongo_uri = Variable.get("mongo_uri")
        db_name = Variable.get("mongo_db_name", "rbi")
        coll = "rbi_regulation_action_items"

        client = MongoClient(mongo_uri)
        db = client[db_name]
        db[coll].update_one(
            {"document_id": document_id},
            {"$set": {"actionables": actionables}},
            upsert=True
        )

    # DAG orchestration
    pdf_keys = list_pdf_files()
    for key in pdf_keys:
        html = download_and_parse(key)
        obligations = extract_obligations(html)
        upsert_actionables(key, obligations)