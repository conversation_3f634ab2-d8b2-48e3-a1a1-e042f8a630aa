// // queryservice.ts
import {QUERY_ENDPOINT} from "@/app/utils/Api"
// interface answer{
//   query:string;
//   response:string;
//   metadata:any[];
// }
// export interface QueryRequest {
//     query: string;
//     session_id: string;
//     username: string;
//   }
  
//   export interface QueryResponse {
//     status: string;
//     answer: answer;
//     sources: any[];
//     suggestions: string;
//   }
  

//   // const DEFAULT_ORIGIN = "localhost:3000";
  
//   export async function fetchQueryResponse(message: string, session_id: string, username:string): Promise<QueryResponse> {
//     try {
//       // console.log(username);
//       // console.log(session_id);
//       const requestBody = {
//           query: message,
//           session_id:session_id,
//           username: username,
//       }
//       // console.log(requestBody);
//       const response = await fetch(QUERY_ENDPOINT, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(requestBody),
//       });
  
//       if (!response.ok) {
//         throw new Error(`API request failed with status ${response.status}`);
//       }
//       // console.log("response:",response);
//       return await response.json();
//     } catch (error) {
//       console.error("Error fetching query response:", error);
//       throw error;
//     }
//   }

export async function fetchQueryResponse(
  message: string,
  session_id: string,
  username: string
): Promise<QueryResponse> {
  try {
    const requestBody = {
      query: message,
      session_id,
      username,
    };

    const response = await fetch(QUERY_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok || !response.body) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");

    let buffer = "";

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      const parts = buffer.split("\n");
      for (let i = 0; i < parts.length; i++) {
        const line = parts[i].trim();
        if (line.startsWith("data:")) {
          const jsonStr = line.replace("data:", "").trim();
          try {
            const parsed = JSON.parse(jsonStr);

            if (parsed.event === "execution_completed") {
              // Return in the expected QueryResponse format
              return {
                status: parsed.status || "completed",
                answer: parsed.answer,
                sources: parsed.sources || [],
                suggestions: parsed.suggestions || "",
              };
            }
          } catch (err) {
            console.warn("Failed to parse JSON:", jsonStr);
          }
        }
      }

      // Preserve last incomplete line (if any)
      buffer = parts[parts.length - 1];
    }

    throw new Error("execution_completed event not found in stream");
  } catch (error) {
    console.error("Error fetching query response:", error);
    throw error;
  }
}
