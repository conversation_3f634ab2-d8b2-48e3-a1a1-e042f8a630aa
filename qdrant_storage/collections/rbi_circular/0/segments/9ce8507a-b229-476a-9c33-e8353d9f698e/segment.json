{"version": 4, "config": {"vector_data": {"dense": {"size": 3072, "distance": "<PERSON><PERSON>e", "storage_type": "InRamChunkedMmap", "index": {"type": "plain", "options": {}}, "quantization_config": null}}, "sparse_vector_data": {"fast-sparse-bm25-splade": {"index": {"full_scan_threshold": null, "index_type": "MutableRam"}, "storage_type": "mmap"}, "fast-sparse-bm25": {"index": {"full_scan_threshold": null, "index_type": "MutableRam"}, "storage_type": "mmap"}}, "payload_storage_type": {"type": "mmap"}}}