#!/usr/bin/env python3
"""
Test script to verify the cleaned-up structure works correctly.
"""

import sys
import json
from pathlib import Path

# Add the parent directory to the path to import DAG modules
sys.path.append(str(Path(__file__).parent.parent))

def test_document_action_model():
    """Test the DocumentAction model"""
    print("🧪 Testing DocumentAction model...")
    
    try:
        from prompts.notification_categorizer import DocumentAction
        
        # Test creating a DocumentAction
        doc_action = DocumentAction(
            document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03",
            action_type="UPDATE_DOCUMENT",
            confidence="high"
        )
        
        print("✅ DocumentAction created successfully")
        print(f"   📄 Document ID: {doc_action.document_id}")
        print(f"   🎯 Action Type: {doc_action.action_type}")
        print(f"   📊 Confidence: {doc_action.confidence}")
        
        # Test serialization
        serialized = doc_action.model_dump()
        print("✅ DocumentAction serialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing DocumentAction: {e}")
        return False

def test_affected_documents_result_model():
    """Test the AffectedDocumentsResult model"""
    print("\n🧪 Testing AffectedDocumentsResult model...")
    
    try:
        from prompts.notification_categorizer import AffectedDocumentsResult, DocumentAction
        
        # Test creating with multiple document actions
        result = AffectedDocumentsResult(
            document_actions=[
                DocumentAction(
                    document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03",
                    action_type="UPDATE_DOCUMENT",
                    confidence="high"
                ),
                DocumentAction(
                    document_id="Master Direction - Credit Card",
                    action_type="REMOVE_DOCUMENT",
                    confidence="medium"
                )
            ],
            document_keywords=["credit card", "banking"],
            has_new_document_link=True,
            new_document_url="https://rbi.org.in/new-doc.pdf",
            rbi_links=["https://rbi.org.in/page1"],
            processing_notes="Successfully processed",
            requires_manual_review=False
        )
        
        print("✅ AffectedDocumentsResult created successfully")
        print(f"   📄 Document Actions: {len(result.document_actions)}")
        print(f"   🔑 Keywords: {result.document_keywords}")
        print(f"   🔗 Has New Document Link: {result.has_new_document_link}")
        print(f"   📝 Processing Notes: {result.processing_notes}")
        
        # Test serialization
        serialized = result.model_dump()
        print("✅ AffectedDocumentsResult serialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AffectedDocumentsResult: {e}")
        return False

def test_conversion_to_update_actions():
    """Test converting the new structure to update actions format"""
    print("\n🔄 Testing conversion to update actions...")
    
    try:
        from prompts.notification_categorizer import DocumentAction
        
        # Mock document actions
        document_actions = [
            {
                "document_id": "DBOD.No.Leg.BC.21/09.07.007/2002-03",
                "action_type": "UPDATE_DOCUMENT",
                "confidence": "high"
            },
            {
                "document_id": "Master Direction - Credit Card",
                "action_type": "REMOVE_DOCUMENT",
                "confidence": "medium"
            }
        ]
        
        # Convert to update actions format (as done in the DAG)
        actions = []
        for doc_action in document_actions:
            actions.append({
                'action_type': doc_action.get('action_type', 'NO_ACTION'),
                'target_document': doc_action.get('document_id', ''),
                'priority': 'high' if doc_action.get('confidence') == 'high' else 'medium',
                'details': f"Action required for {doc_action.get('document_id', '')}: {doc_action.get('action_type', 'NO_ACTION')}"
            })
        
        print("✅ Conversion successful")
        print(f"   📊 Generated Actions: {len(actions)}")
        for action in actions:
            print(f"      - {action['target_document']}: {action['action_type']} (priority: {action['priority']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversion: {e}")
        return False

def test_schema_validation():
    """Test that the schema is valid for OpenAI"""
    print("\n📋 Testing schema validation...")
    
    try:
        from prompts.notification_categorizer import AffectedDocumentsResult, DocumentAction
        
        # Get schemas
        doc_action_schema = DocumentAction.model_json_schema()
        affected_docs_schema = AffectedDocumentsResult.model_json_schema()
        
        # Check DocumentAction schema
        doc_action_props = doc_action_schema.get('properties', {})
        doc_action_required = doc_action_schema.get('required', [])
        
        print(f"✅ DocumentAction schema valid")
        print(f"   📋 Properties: {list(doc_action_props.keys())}")
        print(f"   ✅ Required: {doc_action_required}")
        
        # Check AffectedDocumentsResult schema
        affected_docs_props = affected_docs_schema.get('properties', {})
        affected_docs_required = affected_docs_schema.get('required', [])
        
        print(f"✅ AffectedDocumentsResult schema valid")
        print(f"   📋 Properties: {list(affected_docs_props.keys())}")
        print(f"   ✅ Required: {affected_docs_required}")
        
        # Verify all properties are required (OpenAI requirement)
        all_doc_action_required = set(doc_action_props.keys()) == set(doc_action_required)
        all_affected_docs_required = set(affected_docs_props.keys()) == set(affected_docs_required)
        
        if all_doc_action_required and all_affected_docs_required:
            print("✅ All properties are properly marked as required")
        else:
            print("⚠️  Some properties may not be marked as required")
        
        return all_doc_action_required and all_affected_docs_required
        
    except Exception as e:
        print(f"❌ Error testing schema validation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Cleaned-Up Structure")
    print("=" * 60)
    
    tests = [
        test_document_action_model,
        test_affected_documents_result_model,
        test_conversion_to_update_actions,
        test_schema_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The cleaned-up structure is working correctly.")
        print("🚀 Ready for OpenAI structured output!")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
