# app/v1/chat/routes.py
from fastapi import APIRouter, Depends, Body
from app.v1.chat.service import (
    retrive_user_sessions,
    create_user_session,
    update_user_session,
    retrive_user_session_chat,
    retrive_user_session_chat_metadata,
    save_feedback,
    get_session_feedback,
    get_feedback_stats,
)
from app.db.database import get_db
from app.db.models import SessionRequest, SessionUpdateRequest, ChatsRequest, MetadataRequest
from pydantic import BaseModel
from typing import Union, Dict, Any, Optional
class FeedbackRequest(BaseModel):
    """Request model for saving feedback."""
    session_id: str
    message_content: Union[Dict[str, Any], str]  # Accept both JSON object and string
    feedback_text: Optional[str] = None
    vote: Optional[str] = None  # Could be "up", "down"
    username: Optional[str]

chat_router = APIRouter()

@chat_router.post("/create_session")
def create_session(request: SessionRequest, db=Depends(lambda: get_db())):
    session_id = create_user_session(request.username, db)
    return {"session_id": session_id}

@chat_router.post("/update_session")
def update_session(request: SessionUpdateRequest, db=Depends(lambda: get_db())):
    return update_user_session(request.session_id, request.username, request.title, db)

@chat_router.post("/session")
def retrive_sessions(request: SessionRequest, db=Depends(lambda: get_db())):
    sessions = retrive_user_sessions(db, request.username)
    return {"session": sessions}

@chat_router.post("/session_chats")
def retrive_session_chat(request: ChatsRequest, db=Depends(lambda: get_db())):
    messages = retrive_user_session_chat(request.session_id, request.username, db)
    return {"messages": messages}

@chat_router.post("/metadata_retrival")
def retrive_session_chat_metadata(request: MetadataRequest, db=Depends(lambda: get_db())):
    metadata = retrive_user_session_chat_metadata(request.session_id, request.username, request.content, db)
    return {"metadata": metadata}



@chat_router.post("/feedback")
async def save_message_feedback(request: FeedbackRequest, db=Depends(lambda: get_db())):
    """
    Save feedback for a message.
    
    Args:
        request: Feedback request containing session_id, message_content (JSON), and optional feedback
        db: Database dependency
    """
    result = save_feedback(
        session_id=request.session_id,
        message_content=request.message_content,  # Can now be a dict/JSON object
        feedback_text=request.feedback_text,
        vote=request.vote,
        username=request.username,
        db=db
    )
    return result

@chat_router.get("/feedback/{session_id}")
async def get_feedback_for_session(session_id: str, db=Depends(lambda: get_db())):
    feedbacks = get_session_feedback(session_id, db)
    return {"feedbacks": feedbacks}

@chat_router.get("/feedback/stats/{session_id}")
async def get_session_feedback_stats(session_id: str, db=Depends(lambda: get_db())):
    stats = get_feedback_stats(session_id, db)
    return {"stats": stats}
