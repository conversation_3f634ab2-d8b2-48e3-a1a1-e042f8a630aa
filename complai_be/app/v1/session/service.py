from datetime import datetime
from fastapi import HTTPException, Request

def get_total_sessions(username: str, db) -> int:
    """Get total number of sessions for a user using pymongo."""
    return db["session"].count_documents({"username": username})

def check_session_limits(username: str, session_id: str = None, request: Request = None, db=None):
    """Check if the user has exceeded their session limits based on origin using pymongo."""
    print("request", request)
    origin = request.origin

    # Retrieve the user's limit document from the "user_limits" collection
    user_limit = db["user_limits"].find_one({"username": username})
    if not user_limit:
        user_limit = {
            "username": username,
            "created_at": datetime.utcnow(),
            "origin": origin,
            "max_queries_per_session": 10,
            "max_sessions": 5
        }
        db["user_limits"].insert_one(user_limit)

    # Determine whether limits should be enforced based on origin
    enforce_limits = origin == "https://chat.complai-genie.online/"
    total_sessions = db["session"].count_documents({"username": username})

    # If no session_id is provided (e.g., when creating a new session)
    if not session_id:
        if enforce_limits and total_sessions >= user_limit.get("max_sessions", 5):
            raise HTTPException(
                status_code=403,
                detail={
                    "message": "Free trial ended - Maximum sessions reached",
                    "session_count": total_sessions,
                    "max_sessions": user_limit.get("max_sessions", 5),
                    "can_create_new_session": False
                }
            )
        return {
            "remaining_sessions": user_limit.get("max_sessions", 5) - total_sessions if enforce_limits else "Unlimited",
            "total_sessions": total_sessions,
            "max_sessions": user_limit.get("max_sessions", 5) if enforce_limits else "Unlimited",
            "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5) if enforce_limits else True
        }

    # If checking limits for an existing session
    session = db["session"].find_one({"session_id": session_id})
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    if enforce_limits and session.get("query_count", 0) >= user_limit.get("max_queries_per_session", 10):
        raise HTTPException(
            status_code=403,
            detail={
                "message": "Session limit reached",
                "query_count": session.get("query_count", 0),
                "max_queries_per_session": user_limit.get("max_queries_per_session", 10),
                "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5)
            }
        )

    # Increment query count if limits are enforced
    if enforce_limits:
        db["session"].update_one(
            {"session_id": session_id},
            {"$inc": {"query_count": 1}}
        )
        session["query_count"] = session.get("query_count", 0) + 1

    return {
        "remaining_queries": user_limit.get("max_queries_per_session", 10) - session.get("query_count", 0) if enforce_limits else "Unlimited",
        "total_queries": session.get("query_count", 0),
        "max_queries_per_session": user_limit.get("max_queries_per_session", 10) if enforce_limits else "Unlimited",
        "remaining_sessions": user_limit.get("max_sessions", 5) - total_sessions if enforce_limits else "Unlimited",
        "total_sessions": total_sessions,
        "max_sessions": user_limit.get("max_sessions", 5) if enforce_limits else "Unlimited",
        "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5) if enforce_limits else True
    }
