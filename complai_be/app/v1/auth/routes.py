from app.db.models import SignInRequest, SignUpRequest, UpdateUserRequest
from mongoengine import connect
from fastapi import APIRouter, Depends, HTTPException
from app.v1.auth.service import signup_user, get_user, update_user
from app.db.database import get_db,DATABASE_NAME
from app.db.models import SignInRequest, SignUpRequest

auth_router = APIRouter()


@auth_router.post("/signup")
def signup(request: SignUpRequest, db=Depends(lambda: get_db(database_name=DATABASE_NAME))):
    user = signup_user(db, request.username, request.email, request.password)
    if user:
        return user
    raise HTTPException(status_code=400, detail="Username already taken")


@auth_router.post("/signin")
def get_user_route(request: SignInRequest, db=Depends(lambda: get_db(database_name=DATABASE_NAME))):
    user = get_user(db, request.email, request.password)
    return {"username": user["username"], "email": user["email"]}


@auth_router.put("/update")
def update_user_route(request: UpdateUserRequest, db=Depends(get_db)):
    user = update_user(db, request.username,
                       request.old_password, request.new_password)
    if user:
        return {"message": "User updated successfully"}
    return {"username": user["username"], "email": user["email"]}
