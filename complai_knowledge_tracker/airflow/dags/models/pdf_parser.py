from typing import List, Optional, Dict
from pydantic import BaseModel

class ActionItem(BaseModel):
    item_type: str  # paragraph/table/image
    description: str
    linked_files: List[str] = []

class ParagraphContent(BaseModel):
    para_number: str
    # para_verbatim: str
    content_type: str = "text"  # text/table/image
    obligation: Optional[str]
    directives: Optional[str]
    prohibitions: Optional[str]
    consequences: Optional[str]
    action_point: Optional[str]
    action_items: List[ActionItem] = []

class PageContent(BaseModel):
    page_number: int
    content_summary: str
    paragraphs: List[ParagraphContent] = []
    tables: List[Dict] = []
    images: List[Dict] = []

class StructuredOutput(BaseModel):
    circular_number: str
    circular_title: str
    date_of_issue: str
    document_index: List[str]
    pages: List[PageContent]
    index_summary: Dict[str, str]
    linked_documents: List[str] = []