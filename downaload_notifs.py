import os
import re
import json
import time
import logging
import requests
import boto3
from datetime import datetime
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import fitz  # PyMuPDF for watermark detection
from io import BytesIO

# Config
BASE_DIR = "rbi_docs"
JSON_OUTPUT = "rbi_notifications.json"
S3_BUCKET = "rbi-docs-full-2025-08-05"
MAX_NOTIFS = 200

# AWS S3 Client
aws_access_key = os.getenv("AWS_ACCESS_KEY_ID", "********************")
aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY", "SiWxZMly8N/CIQDjCYqH4f/aAWmR0M70F+QdUd7w")
s3_client = boto3.client(
    's3',
    aws_access_key_id=aws_access_key,
    aws_secret_access_key=aws_secret_key
)

# Logging
logging.basicConfig(
    filename='rbi_scraper.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
def log(msg):
    print(msg)
    logging.info(msg)

# Utilities
def sanitize_filename(name):
    return re.sub(r"[^\w\-_. ]", "_", name).strip()

def detect_watermark(pdf_bytes):
    try:
        doc = fitz.open(stream=pdf_bytes, filetype='pdf')
        stamps = set()
        for page in doc:
            for b in page.get_text('dict').get('blocks', []):
                if b.get('type') != 0:
                    continue
                for line in b.get('lines', []):
                    text = ''.join(span.get('text', '') for span in line.get('spans', [])).strip()
                    lt = text.lower()
                    if 'watermark' in lt or 'confidential' in lt or (text.isupper() and len(text) < 30):
                        stamps.add(text)
        return '; '.join(stamps)
    except Exception as e:
        log(f"⚠️ Watermark detection failed: {e}")
        return ''

# Download and process a PDF
def download_and_process(pdf_url, section, year, title):
    log(f"🔄 Downloading: {title}")
    try:
        resp = requests.get(pdf_url, timeout=15)
        resp.raise_for_status()
        data = resp.content
        wm = detect_watermark(data)
        fname = sanitize_filename(title) + '.pdf'
        local_dir = os.path.join(BASE_DIR, section, year)
        os.makedirs(local_dir, exist_ok=True)
        local_path = os.path.join(local_dir, fname)
        with open(local_path, 'wb') as f:
            f.write(data)
        s3_path = os.path.join(section, year, fname)
        # s3_client.upload_fileobj(BytesIO(data), S3_BUCKET, s3_path)
        return local_path, wm
    except Exception as e:
        log(f"❌ Error processing {title}: {e}")
        return '', ''

# Initialize Selenium
driver_opts = Options()
driver_opts.add_argument('--headless=new')
driver = webdriver.Chrome(options=driver_opts)
wait = WebDriverWait(driver, 10)

# Scrape Notifications
section = 'Notifications'
url = 'https://rbi.org.in/Scripts/NotificationUser.aspx'
log(f"🔎 Scraping section: {section}")
driver.get(url)
time.sleep(1)

# Expand archives toggle
try:
    toggle = driver.find_element(By.ID, 'divArchiveMain')
    driver.execute_script('arguments[0].click();', toggle)
    log('✅ Archives expanded')
    time.sleep(1)
except:
    log('ℹ️ No archives toggle found')

# Identify year headers
links = driver.find_elements(By.CSS_SELECTOR, 'h2.accordionButton.year a')
years = sorted([int(link.text.strip()) for link in links if link.text.strip().isdigit()], reverse=True)
log(f"📅 Found years: {years}")

# Collect all notifications, stopping after MAX_NOTIFS
checklist = []
for year_text in years:
    try:
        link = driver.find_element(By.XPATH, f"//h2[contains(@class,'accordionButton') and contains(.,'{year_text}')]//a")
        driver.execute_script('arguments[0].scrollIntoView(true); arguments[0].click();', link)
        time.sleep(0.5)
        try:
            all_mon = driver.find_element(By.XPATH, f"//a[contains(@onclick, \"GetYearMonth('{year_text}', '0')\")]" )
            driver.execute_script('arguments[0].click();', all_mon)
            time.sleep(0.5)
        except:
            pass
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        tbl_div = soup.find('div', id='example-min')
        if not tbl_div:
            continue
        tbl = tbl_div.find('table', class_='tablebg')
        date_text = ''
        for row in tbl.find_all('tr'):
            cols = row.find_all('td')
            if len(cols) == 1 and cols[0].find('b'):
                date_text = cols[0].get_text(strip=True)
                continue
            if len(cols) >= 2 and cols[1].find('a', href=True):
                title = cols[0].get_text(strip=True) or f"Doc_{int(time.time())}"
                pdf_link = urljoin(driver.current_url, cols[1].find('a')['href'])
                date_obj = datetime.strptime(date_text, '%B %d, %Y') if re.match(r"[A-Za-z]", date_text) else datetime.min
                checklist.append({'Section': section, 'Year': str(year_text), 'Date': date_text, 'Title': title, 'PDF Link': pdf_link, 'DateObj': date_obj})
        log(f"ℹ️ Collected {len(checklist)} entries so far")
        if len(checklist) >= MAX_NOTIFS:
            log(f"ℹ️ Reached {MAX_NOTIFS} entries, stopping early")
            break
    except Exception as e:
        log(f"⚠️ Error year {year_text}: {e}")
        continue

driver.quit()

# Sort by date and take the most recent MAX_NOTIFS
checklist = sorted(checklist, key=lambda x: x['DateObj'], reverse=True)[:MAX_NOTIFS]
for item in checklist:
    del item['DateObj']

# Download, watermark, upload in parallel
results = []
with ThreadPoolExecutor(max_workers=5) as executor:
    futures = {executor.submit(download_and_process, it['PDF Link'], it['Section'], it['Year'], it['Title']): it for it in checklist}
    for fut in futures:
        it = futures[fut]
        lp, wm = fut.result()
        it['Local Path'] = lp
        it['Watermark'] = wm
        results.append(it)

# Save JSON
with open(JSON_OUTPUT, 'w', encoding='utf-8') as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
log(f"✅ Saved {len(results)} notifications to {JSON_OUTPUT}")
