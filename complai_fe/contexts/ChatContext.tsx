// // contexts/ChatContext.tsx
// "use client"
// import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// import { chatService } from '@/services/sessionservice';
// import { GroupedChat } from '@/types/chat';
// import { useAuth } from './AuthContext';

// interface ChatContextType {
//   currentSessionId: string | null;
//   setCurrentSessionId: (id: string | null) => void;
//   chats: GroupedChat[];
//   isLoading: boolean;
//   error: string | null;
//   refreshChats: () => Promise<void>;
//   createNewChat: () => Promise<string>;
// }

// const ChatContext = createContext<ChatContextType | undefined>(undefined);

// export function ChatProvider({ children }: { children: ReactNode }) {
//   const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
//   const [chats, setChats] = useState<GroupedChat[]>([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   const {user} = useAuth();

//   // Get current username - in a real app, this would come from auth context
//   const getUsername = () => {
//     // Replace with actual auth logic
//     const usernames = user?.username
//     return usernames;
//   };

//   // Transform API session data into UI-friendly format
//   const transformSessionData = (sessions: any[]): GroupedChat[] => {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);

//     const yesterday = new Date(today);
//     yesterday.setDate(yesterday.getDate() - 1);

//     const lastWeekStart = new Date(today);
//     lastWeekStart.setDate(lastWeekStart.getDate() - 7);

//     return sessions.map(session => {
//       const createdDate = new Date(session.created_at);
//       let dateGroup: "Today" | "Yesterday" | "Last Week" | "Older";

//       if (createdDate >= today) {
//         dateGroup = "Today";
//       } else if (createdDate >= yesterday) {
//         dateGroup = "Yesterday";
//       } else if (createdDate >= lastWeekStart) {
//         dateGroup = "Last Week";
//       } else {
//         dateGroup = "Older";
//       }

//       return {
//         id: session.id,
//         // If this is a new session (created in the last minute), set title to "New Chat" regardless of API response
//         title: createdDate > new Date(Date.now() - 60000) && !session.title ? "New Chat" : session.title,
//         date: dateGroup
//       };
//     });
//   };

//   // Fetch chat sessions
//   const fetchChats = async () => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       const username = getUsername();
//       const data = await chatService.fetchSessions(username);

//       const transformedChats = transformSessionData(data.session);
//       setChats(transformedChats);
//     } catch (err) {
//       console.error("Error fetching chat sessions:", err);
//       setError("Failed to load chat history");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Create a new chat session
//   const createNewChat = async (): Promise<string> => {
//     try {
//       const username = getUsername();
//       const result = await chatService.createSession(username);

//       // Store the new session ID
//       const newSessionId = result.session_id;
//       setCurrentSessionId(newSessionId);

//       // Add the new chat to the list with the title "New Chat"
//       const newChat: GroupedChat = {
//         id: newSessionId,
//         title: "New Chat",
//         date: "Today"
//       };

//       // Add the new chat to the beginning of the list
//       setChats(prevChats => [newChat, ...prevChats]);

//       return newSessionId;
//     } catch (err) {
//       console.error("Error creating new chat session:", err);
//       throw err;
//     }
//   };

//   // Initial fetch of chats
//   useEffect(() => {
//     fetchChats();
//   }, []);

//   return (
//     <ChatContext.Provider
//       value={{
//         currentSessionId,
//         setCurrentSessionId,
//         chats,
//         isLoading,
//         error,
//         refreshChats: fetchChats,
//         createNewChat
//       }}
//     >
//       {children}
//     </ChatContext.Provider>
//   );
// }

// // Custom hook to use the chat context
// export function useChat() {
//   const context = useContext(ChatContext);
//   if (context === undefined) {
//     throw new Error('useChat must be used within a ChatProvider');
//   }
//   return context;
// }
// contexts/ChatContext.tsx
// "use client"
// import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// import { chatService } from '@/services/sessionservice';
// import { GroupedChat, ChatMessage } from '@/types/chat';
// import { useAuth } from './AuthContext';

// interface ChatContextType {
//   currentSessionId: string | null;
//   setCurrentSessionId: (id: string | null) => void;
//   chats: GroupedChat[];
//   messages: ChatMessage[];
//   setMessages: (messages: ChatMessage[]) => void;
//   isLoading: boolean;
//   error: string | null;
//   refreshChats: () => Promise<void>;
//   createNewChat: () => Promise<string>;
//   loadSession: (sessionId: string) => Promise<void>;
// }

// const ChatContext = createContext<ChatContextType | undefined>(undefined);

// export function ChatProvider({ children }: { children: ReactNode }) {
//   const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
//   const [chats, setChats] = useState<GroupedChat[]>([]);
//   const [messages, setMessages] = useState<ChatMessage[]>([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   const { user } = useAuth();

//   // Get current username - in a real app, this would come from auth context
//   const getUsername = () => {
//     // Replace with actual auth logic
//     const username = user?.username;
//     return username;
//   };

//   // Transform API session data into UI-friendly format
//   const transformSessionData = (sessions: any[]): GroupedChat[] => {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);

//     const yesterday = new Date(today);
//     yesterday.setDate(yesterday.getDate() - 1);

//     const lastWeekStart = new Date(today);
//     lastWeekStart.setDate(lastWeekStart.getDate() - 7);

//     return sessions.map(session => {
//       const createdDate = new Date(session.created_at);
//       let dateGroup: "Today" | "Yesterday" | "Last Week" | "Older";

//       if (createdDate >= today) {
//         dateGroup = "Today";
//       } else if (createdDate >= yesterday) {
//         dateGroup = "Yesterday";
//       } else if (createdDate >= lastWeekStart) {
//         dateGroup = "Last Week";
//       } else {
//         dateGroup = "Older";
//       }

//       return {
//         id: session.id,
//         // If this is a new session (created in the last minute), set title to "New Chat" regardless of API response
//         title: createdDate > new Date(Date.now() - 60000) && !session.title ? "New Chat" : session.title,
//         date: dateGroup
//       };
//     });
//   };

//   // Transform API chat messages to our app format
//   const transformMessagesData = (messagesData: any[]): ChatMessage[] => {
//     if (!messagesData || !Array.isArray(messagesData)) {
//       return [];
//     }

//     return messagesData.map(msg => {
//       // Determine message type (user or assistant)
//       const isUserMessage = msg.query !== undefined;

//       // Format timestamp
//       const timestamp = msg.timestamp && msg.timestamp.$date
//         ? new Date(msg.timestamp.$date)
//         : new Date();

//       // Format metadata if available
//       const metadata = msg.metadata && Array.isArray(msg.metadata)
//         ? msg.metadata
//         : undefined;

//       return {
//         id: `${isUserMessage ? 'user' : 'assistant'}-${timestamp.getTime()}-${Math.random().toString(36).substring(2, 9)}`,
//         content: isUserMessage ? msg.query : msg.content,
//         sender: isUserMessage ? 'user' : 'assistant',
//         timestamp: timestamp,
//         metadata: metadata
//       };
//     });
//   };

//   // Fetch chat sessions
//   const fetchChats = async () => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       const data = await chatService.fetchSessions(username);

//       const transformedChats = transformSessionData(data.session);
//       setChats(transformedChats);
//     } catch (err) {
//       console.error("Error fetching chat sessions:", err);
//       setError("Failed to load chat history");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Load a specific chat session
//   const loadSession = async (sessionId: string) => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       // Set current session ID first to update UI
//       setCurrentSessionId(sessionId);

//       // Fetch session messages
//       const data = await chatService.fetchSessionChats(sessionId, username);

//       // Transform and set messages
//       const transformedMessages = transformMessagesData(data.messages);
//       setMessages(transformedMessages);

//     } catch (err) {
//       console.error("Error loading chat session:", err);
//       setError("Failed to load chat session");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Create a new chat session
//   const createNewChat = async (): Promise<string> => {
//     try {
//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       const result = await chatService.createSession(username);

//       // Store the new session ID
//       const newSessionId = result.session_id;
//       setCurrentSessionId(newSessionId);

//       // Clear existing messages for the new chat
//       setMessages([]);

//       // Add the new chat to the list with the title "New Chat"
//       const newChat: GroupedChat = {
//         id: newSessionId,
//         title: "New Chat",
//         date: "Today"
//       };

//       // Add the new chat to the beginning of the list
//       setChats(prevChats => [newChat, ...prevChats]);

//       return newSessionId;
//     } catch (err) {
//       console.error("Error creating new chat session:", err);
//       throw err;
//     }
//   };

//   // Initial fetch of chats
//   useEffect(() => {
//     if (user) {
//       fetchChats();
//     }
//   }, [user]);

//   return (
//     <ChatContext.Provider
//       value={{
//         currentSessionId,
//         setCurrentSessionId,
//         chats,
//         messages,
//         setMessages,
//         isLoading,
//         error,
//         refreshChats: fetchChats,
//         createNewChat,
//         loadSession
//       }}
//     >
//       {children}
//     </ChatContext.Provider>
//   );
// }

// // Custom hook to use the chat context
// export function useChat() {
//   const context = useContext(ChatContext);
//   if (context === undefined) {
//     throw new Error('useChat must be used within a ChatProvider');
//   }
//   return context;
// }
// "use client"
// import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// import { chatService } from '@/services/sessionservice';
// import { GroupedChat, ChatMessage } from '@/types/chat';
// import { useAuth } from './AuthContext';

// interface ChatContextType {
//   currentSessionId: string | null;
//   setCurrentSessionId: (id: string | null) => void;
//   chats: GroupedChat[];
//   messages: ChatMessage[];
//   setMessages: (messages: ChatMessage[]) => void;
//   isLoading: boolean;
//   error: string | null;
//   refreshChats: () => Promise<void>;
//   createNewChat: () => Promise<string>;
//   loadSession: (sessionId: string) => Promise<void>;
// }

// const ChatContext = createContext<ChatContextType | undefined>(undefined);

// export function ChatProvider({ children }: { children: ReactNode }) {
//   const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
//   const [chats, setChats] = useState<GroupedChat[]>([]);
//   const [messages, setMessages] = useState<ChatMessage[]>([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   const { user } = useAuth();

//   // Get current username - in a real app, this would come from auth context
//   const getUsername = () => {
//     // Replace with actual auth logic
//     const username = user?.username;
//     return username;
//   };

//   // Transform API session data into UI-friendly format
//   const transformSessionData = (sessions: any[]): GroupedChat[] => {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);

//     const yesterday = new Date(today);
//     yesterday.setDate(yesterday.getDate() - 1);

//     const lastWeekStart = new Date(today);
//     lastWeekStart.setDate(lastWeekStart.getDate() - 7);

//     return sessions.map(session => {
//       const createdDate = new Date(session.created_at);
//       let dateGroup: "Today" | "Yesterday" | "Last Week" | "Older";

//       if (createdDate >= today) {
//         dateGroup = "Today";
//       } else if (createdDate >= yesterday) {
//         dateGroup = "Yesterday";
//       } else if (createdDate >= lastWeekStart) {
//         dateGroup = "Last Week";
//       } else {
//         dateGroup = "Older";
//       }

//       return {
//         id: session.id,
//         // If this is a new session (created in the last minute), set title to "New Chat" regardless of API response
//         title: createdDate > new Date(Date.now() - 60000) && !session.title ? "New Chat" : session.title,
//         date: dateGroup,
//         created_at: createdDate
//       };
//     });
//   };

//   // Process metadata to ensure it's in the correct format
//   const processMetadata = (metadata: any) => {
//     if (!metadata) return undefined;

//     // Ensure the metadata is an array
//     if (!Array.isArray(metadata)) {
//       console.warn("Metadata is not an array:", metadata);
//       return undefined;
//     }

//     // Process each metadata item to ensure they have required fields
//     return metadata.map(item => {
//       // Make sure all required fields are present
//       return {
//         id: item.id || `meta-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
//         title: item.title || "Unknown Document",
//         document_code: item.doc_code || "",
//         date_of_issue: item.revision_date_of_issue || "",
//         dept: item.departments || "",
//         Type: item.type || "",
//         section_summary: item.section_summary,
//         pdf_filename: item.revision_pdf_filename,
//         pdf_link: item.revision_pdf_link,
//         revision_s3_url: item.revision_s3_url,
//         collection_name: item._collection_name,
//         rects: item.rects,
//         page_numbers: item.page_numbers,
//       };
//     });
//   };

//   // Transform API chat messages to our app format
//   const transformMessagesData = (messagesData: any[]): ChatMessage[] => {
//     if (!messagesData || !Array.isArray(messagesData)) {
//       return [];
//     }

//     const transformedMessages: ChatMessage[] = [];

//     messagesData.forEach(msg => {
//       // Format timestamp
//       const timestamp = msg.timestamp && msg.timestamp.$date
//         ? new Date(msg.timestamp.$date)
//         : new Date();

//       // Generate a unique ID for each message (not the same for user and assistant)
//       const baseTimestamp = timestamp.getTime();
//       const randomId = Math.random().toString(36).substring(2, 9);

//       // Create user message from query with unique ID
//       transformedMessages.push({
//         id: `user-${baseTimestamp}-${randomId}`,
//         content: msg.query,
//         sender: 'user',
//         timestamp: new Date(timestamp), // Create a new date object to avoid reference issues
//       });

//       // Create assistant message from content with metadata (with a different unique ID)
//       const metadata = msg.metadata && Array.isArray(msg.metadata)
//         ? msg.metadata
//         : undefined;

//       transformedMessages.push({
//         id: `assistant-${baseTimestamp}-${randomId}`,
//         content: msg.content,
//         sender: 'assistant',
//         timestamp: new Date(timestamp.getTime() + 1), // Add 1ms to ensure correct order
//         metadata: processMetadata(metadata)
//       });
//     });

//     // Sort messages by timestamp to ensure proper order
//     return transformedMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
//   };

//   // Fetch chat sessions
//   const fetchChats = async () => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       const data = await chatService.fetchSessions(username);

//       const transformedChats = transformSessionData(data.session);
//       setChats(transformedChats);
//     } catch (err) {
//       console.error("Error fetching chat sessions:", err);
//       setError("Failed to load chat history");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Load a specific chat session
//   const loadSession = async (sessionId: string) => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       // Set current session ID first to update UI
//       setCurrentSessionId(sessionId);

//       // Fetch session messages
//       const data = await chatService.fetchSessionChats(sessionId, username);

//       // For debugging
//       console.log("Raw session data:", data.messages);

//       // Transform and set messages
//       const transformedMessages = transformMessagesData(data.messages);
//       console.log("Transformed messages:", transformedMessages);

//       setMessages(transformedMessages);

//     } catch (err) {
//       console.error("Error loading chat session:", err);
//       setError("Failed to load chat session");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Create a new chat session
//   const createNewChat = async (): Promise<string> => {
//     try {
//       const username = getUsername();
//       if (!username) {
//         throw new Error("No username available");
//       }

//       const result = await chatService.createSession(username);

//       // Store the new session ID
//       const newSessionId = result.session_id;
//       setCurrentSessionId(newSessionId);

//       // Clear existing messages for the new chat
//       setMessages([]);

//       // Add the new chat to the list with the title "New Chat"
//       const newChat: GroupedChat = {
//         id: newSessionId,
//         title: "New Chat",
//         date: "Today"
//       };

//       // Add the new chat to the beginning of the list
//       setChats(prevChats => [newChat, ...prevChats]);

//       return newSessionId;
//     } catch (err) {
//       console.error("Error creating new chat session:", err);
//       throw err;
//     }
//   };

//   // Initial fetch of chats
//   useEffect(() => {
//     if (user) {
//       fetchChats();
//     }
//   }, [user]);

//   return (
//     <ChatContext.Provider
//       value={{
//         currentSessionId,
//         setCurrentSessionId,
//         chats,
//         messages,
//         setMessages,
//         isLoading,
//         error,
//         refreshChats: fetchChats,
//         createNewChat,
//         loadSession
//       }}
//     >
//       {children}
//     </ChatContext.Provider>
//   );
// }

// // Custom hook to use the chat context
// export function useChat() {
//   const context = useContext(ChatContext);
//   if (context === undefined) {
//     throw new Error('useChat must be used within a ChatProvider');
//   }
//   return context;
// }

"use client";
import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useRef,
} from "react";
import { chatService } from "@/services/sessionservice";
import { GroupedChat, ChatMessage } from "@/types/main_types";
import { useAuth } from "./AuthContext";

interface ChatContextType {
  currentSessionId: string | null;
  setCurrentSessionId: (id: string | null) => void;
  chats: GroupedChat[];
  messages: ChatMessage[];
  setMessages: (messages: ChatMessage[]) => void;
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
  refreshChats: () => Promise<void>;
  createNewChat: () => Promise<string>;
  loadSession: (sessionId: string) => Promise<void>;
  fetchUserChats: (username: string) => Promise<void>;
  updateSessionTitle: (sessionId: string, title: string) => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: ReactNode }) {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [chats, setChats] = useState<GroupedChat[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use refs to prevent duplicate API calls
  const isFetchingRef = useRef(false);
  const isCreatingSessionRef = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { user } = useAuth();

  // Get current username - in a real app, this would come from auth context
  const getUsername = () => {
    // Replace with actual auth logic
    const username = user?.username;
    return username;
  };

  // Transform API session data into UI-friendly format
  const transformSessionData = (sessions: any[]): GroupedChat[] => {
    // Handle empty or undefined sessions
    if (!sessions || !Array.isArray(sessions)) {
      return [];
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const lastWeekStart = new Date(today);
    lastWeekStart.setDate(lastWeekStart.getDate() - 7);

    return sessions.map((session) => {
      const createdDate = new Date(session.created_at);
      let dateGroup: "Today" | "Yesterday" | "Last Week" | "Older";

      if (createdDate >= today) {
        dateGroup = "Today";
      } else if (createdDate >= yesterday) {
        dateGroup = "Yesterday";
      } else if (createdDate >= lastWeekStart) {
        dateGroup = "Last Week";
      } else {
        dateGroup = "Older";
      }

      return {
        id: session.id || session.session_id, // Handle both id formats
        // If this is a new session (created in the last minute), set title to "New Chat" regardless of API response
        title:
          createdDate > new Date(Date.now() - 60000) && !session.title
            ? "New Chat"
            : session.title,
        date: dateGroup,
        created_at: createdDate,
      };
    });
  };

  // Process metadata to ensure it's in the correct format
  const processMetadata = (metadata: any) => {
    if (!metadata) return undefined;

    // Ensure the metadata is an array
    if (!Array.isArray(metadata)) {
      console.warn("Metadata is not an array:", metadata);
      return undefined;
    }

    // Process each metadata item to ensure they have required fields
    return metadata.map((item) => {
      // Make sure all required fields are present
      return {
        id:
          item._id ||
          `meta-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        title: item.document_title || "Unknown Document",
        document_code: item.document_id || "",
        date_of_issue: item.revision_date_of_issue || "",
        dept: item.departments || "",
        Type: item.document_type || "",
        section_summary: item.summary,
        revision_s3_url: item.s3_url,
        collection_name: item._collection_name,
        positions: item.positions,
      };
    });
  };

  // Transform API chat messages to our app format
  const transformMessagesData = (messagesData: any[]): ChatMessage[] => {
    if (!messagesData || !Array.isArray(messagesData)) {
      return [];
    }

    const transformedMessages: ChatMessage[] = [];

    messagesData.forEach((msg) => {
      // Format timestamp
      const timestamp =
        msg.timestamp && msg.timestamp.$date
          ? new Date(msg.timestamp.$date)
          : new Date();

      // Generate a unique ID for each message (not the same for user and assistant)
      const baseTimestamp = timestamp.getTime();
      const randomId = Math.random().toString(36).substring(2, 9);

      // Create user message from query with unique ID
      transformedMessages.push({
        id: `user-${baseTimestamp}-${randomId}`,
        content: msg.query,
        sender: "user",
        timestamp: new Date(timestamp), // Create a new date object to avoid reference issues
      });

      // Create assistant message from content with metadata (with a different unique ID)
      const metadata =
        msg.metadata && Array.isArray(msg.metadata) ? msg.metadata : undefined;

      // console.log("metadata", metadata);

      transformedMessages.push({
        id: `assistant-${baseTimestamp}-${randomId}`,
        content: msg.content,
        sender: "assistant",
        timestamp: new Date(timestamp.getTime() + 1), // Add 1ms to ensure correct order
        metadata: processMetadata(metadata),
      });
    });

    // Sort messages by timestamp to ensure proper order
    return transformedMessages.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
  };

  // Fetch chat sessions - internal method with debouncing
  const fetchChats = async () => {
    // Prevent duplicate calls
    if (isFetchingRef.current) {
      return;
    }

    isFetchingRef.current = true;

    try {
      setIsLoading(true);
      setError(null);

      const username = getUsername();
      if (!username) {
        throw new Error("No username available");
      }

      const data = await chatService.fetchSessions(username);
      // console.log(data);
      const transformedChats = transformSessionData(data.session);
      setChats(transformedChats);
    } catch (err) {
      console.error("Error fetching chat sessions:", err);
      setError("Failed to load chat history");
    } finally {
      setIsLoading(false);

      // Clear the fetching status after a short delay to prevent rapid re-fetches
      setTimeout(() => {
        isFetchingRef.current = false;
      }, 300);
    }
  };

  // Expose fetchUserChats with debouncing for direct use from components
  const fetchUserChats = async (username: string) => {
    if (!username) {
      console.error("No username provided to fetchUserChats");
      return;
    }

    // Prevent duplicate calls
    if (isFetchingRef.current) {
      return;
    }

    // Clear any pending timeouts
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    isFetchingRef.current = true;
    setIsLoading(true);
    setError(null);

    try {
      const response = await chatService.fetchSessions(username);

      if (response && response.session) {
        // Process the chats data
        // console.log("demo1", response.session);
        const transformedChats = transformSessionData(response.session);
        // console.log("demo2", transformedChats);
        setChats(transformedChats);

        // console.log("User chats fetched successfully:", transformedChats.length);
      } else {
        console.warn("No sessions found in response:", response);
        setChats([]);
      }
    } catch (err) {
      console.error("Failed to fetch user chats:", err);
      setError("Failed to load chats");
      setChats([]);
    } finally {
      setIsLoading(false);

      // Clear the fetching status after a short delay to prevent rapid re-fetches
      fetchTimeoutRef.current = setTimeout(() => {
        isFetchingRef.current = false;
      }, 300);
    }
  };

  // Load a specific chat session
  const loadSession = async (sessionId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const username = getUsername();
      if (!username) {
        throw new Error("No username available");
      }

      // Set current session ID first to update UI
      setCurrentSessionId(sessionId);

      // Save to localStorage for persistence
      if (typeof window !== "undefined") {
        localStorage.setItem("currentSessionId", sessionId);
        localStorage.setItem("activeView", "chat");
      }

      // Fetch session messages
      const data = await chatService.fetchSessionChats(sessionId, username);
      // console.log("data:", data);
      // Transform and set messages
      const transformedMessages = transformMessagesData(data.messages || []);
      setMessages(transformedMessages);
    } catch (err) {
      console.error("Error loading chat session:", err);
      setError("Failed to load chat session");
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new chat session with protection against duplicate calls
  const createNewChat = async (): Promise<string> => {
    // Prevent duplicate session creation
    if (isCreatingSessionRef.current) {
      // If we're already creating a session, return the current one or wait
      if (currentSessionId) {
        return currentSessionId;
      }

      // Wait for the current operation to complete
      await new Promise((resolve) => setTimeout(resolve, 500));

      if (currentSessionId) {
        return currentSessionId;
      }
    }

    isCreatingSessionRef.current = true;

    try {
      const username = getUsername();
      if (!username) {
        throw new Error("No username available");
      }

      const result = await chatService.createSession(username);

      // Store the new session ID
      const newSessionId = result.session_id;
      setCurrentSessionId(newSessionId);

      // Save to localStorage for persistence
      if (typeof window !== "undefined") {
        localStorage.setItem("currentSessionId", newSessionId);
      }

      // Clear existing messages for the new chat
      setMessages([]);

      // Add the new chat to the list with the title "New Chat"
      const newChat: GroupedChat = {
        id: newSessionId,
        title: "New Chat",
        date: "Today",
        created_at: new Date(),
      };

      // Add the new chat to the beginning of the list
      setChats((prevChats) => {
        // Check if this session already exists in the list
        if (prevChats.some((chat) => chat.id === newSessionId)) {
          return prevChats;
        }
        return [newChat, ...prevChats];
      });

      // console.log("New chat created successfully:", newSessionId);
      return newSessionId;
    } catch (err) {
      console.error("Error creating new chat session:", err);
      throw err;
    } finally {
      // Reset the creating session flag after a short delay
      setTimeout(() => {
        isCreatingSessionRef.current = false;
      }, 300);
    }
  };

  const updateSessionTitle = async (sessionId: string, title: string) => {
    setChats((prevChats) => {
      return prevChats.map((chat) => {
        if (chat.id === sessionId) {
          return { ...chat, title };
        }
        return chat;
      });
    });
  };

  // Cleanup function for timeouts
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  // Initial fetch of chats - only once when user is available
  useEffect(() => {
    if (user && !isFetchingRef.current) {
      fetchChats();
    }
  }, [user]);

  return (
    <ChatContext.Provider
      value={{
        currentSessionId,
        setCurrentSessionId,
        chats,
        messages,
        setMessages,
        isLoading,
        isSubmitting,
        error,
        refreshChats: fetchChats,
        createNewChat,
        loadSession,
        fetchUserChats,
        updateSessionTitle,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
}

// Custom hook to use the chat context
export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
}
