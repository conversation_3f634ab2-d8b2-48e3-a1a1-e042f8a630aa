<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chat Data Export</title>
  <!-- Use url_for to reference static files -->
  <script src="https://api.complai-genie.online/static/js/xlsx.full.min.js"></script>
  <link rel="stylesheet" href="https://api.complai-genie.online/static/css/styles.css">
  <style>
    .logo-container {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo {
      width: 100px;
      height: auto;
    }
    /* Password modal styles */
    #passwordModal {
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    #passwordModalContent {
      background: #fff;
      padding: 20px;
      border-radius: 5px;
      text-align: center;
    }
    #passwordModal input[type="password"] {
      padding: 10px;
      width: 200px;
      margin-top: 10px;
    }
    #passwordModal button {
      padding: 10px 20px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <!-- Password Modal -->
  <div id="passwordModal">
    <div id="passwordModalContent">
      <h2>Please Enter Password</h2>
      <input type="password" id="passwordInput" placeholder="Enter password">
      <br>
      <button onclick="checkPassword()">Submit</button>
      <p id="passwordError" style="color: red;"></p>
    </div>
  </div>

  <div id="mainContent" style="display: none;">
    <div class="container">
      <div class="logo-container">
        <img src="https://www.selkea.co.in/_next/image?url=https%3A%2F%2Fhebbkx1anhila5yf.public.blob.vercel-storage.com%2FLogo%2520(1)-NzJVBB3fcA0QzO1B0XBnOrwJeC4EgE.png&w=384&q=75" alt="Selkea Logo" class="logo">
      </div>
      <h1>Chat Data Export</h1>
      
      <div class="filter-section">
        <div class="filter-type">
          <label class="radio-label">
            <input type="radio" name="filterType" value="single" checked>
            Single Date
          </label>
          <label class="radio-label">
            <input type="radio" name="filterType" value="range">
            Date Range
          </label>
          <label class="radio-label">
            <input type="radio" name="filterType" value="greater">
            Greater Than
          </label>
          <label class="radio-label">
            <input type="radio" name="filterType" value="less">
            Less Than
          </label>
        </div>

        <div id="singleDate" class="date-inputs active">
          <div class="input-group">
            <label>Select Date</label>
            <input type="date" id="singleDateInput">
          </div>
        </div>

        <div id="dateRange" class="date-inputs">
          <div class="input-group">
            <label>From Date</label>
            <input type="date" id="fromDate">
          </div>
          <div class="input-group">
            <label>To Date</label>
            <input type="date" id="toDate">
          </div>
        </div>

        <div id="greaterThan" class="date-inputs">
          <div class="input-group">
            <label>After Date</label>
            <input type="date" id="greaterThanDate">
          </div>
        </div>

        <div id="lessThan" class="date-inputs">
          <div class="input-group">
            <label>Before Date</label>
            <input type="date" id="lessThanDate">
          </div>
        </div>
      </div>

      <button id="downloadBtn">
        <span>Download Excel</span>
        <div class="spinner" id="spinner"></div>
      </button>
      
      <div id="errorMessage"></div>
      <div id="successMessage"></div>
    </div>
  </div>

  <script>
    function checkPassword() {
      var password = document.getElementById("passwordInput").value;
      // POST the password to the /login endpoint.
      fetch('/report/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({ 'password': password })
      })
      .then(response => response.json())
      .then(data => {
        if (data.message === "Login successful") {
          // Successful login; hide modal and show main content.
          document.getElementById("passwordModal").style.display = "none";
          document.getElementById("mainContent").style.display = "block";
        } else {
          throw new Error("Login failed");
        }
      })
      .catch(error => {
        document.getElementById("passwordError").innerText = error.message;
      });
    }
   </script>
  <script src="https://api.complai-genie.online/static/js/main.js"></script>
</body>
</html>
