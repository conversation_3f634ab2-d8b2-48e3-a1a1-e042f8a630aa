import requests
import time
from qdrant_client import QdrantClient

SRC_QDRANT_URL = ""
DST_QDRANT_URL = "http://localhost:6333"
BATCH_SIZE = 10

def get_collections(qdrant_url):
    resp = requests.get(f"{qdrant_url}/collections")
    resp.raise_for_status()
    return [c["name"] for c in resp.json()["result"]["collections"]]

def get_collection_info(qdrant_url, collection):
    resp = requests.get(f"{qdrant_url}/collections/{collection}")
    resp.raise_for_status()
    return resp.json()["result"]

def delete_collection(qdrant_url, collection):
    resp = requests.delete(f"{qdrant_url}/collections/{collection}")
    if resp.ok:
        print(f"Dropped existing collection {collection}.")


def get_total_points(qdrant_url, collection):
    resp = requests.get(f"{qdrant_url}/collections/{collection}")
    resp.raise_for_status()
    return resp.json()["result"]["points_count"]

def get_doc_id(collection):
    info = get_collection_info(SRC_QDRANT_URL, collection)
    # Always drop and recreate for schema correctness
    total = get_total_points(SRC_QDRANT_URL, collection)
    print(f"Total points: {total}")
    if total == 0:
        print("Skipping empty collection.")
        return

    next_offset = None
    doc_id_list = []
    client = QdrantClient(url=SRC_QDRANT_URL, port=6333)
    while True:
        points, next_offset = client.scroll(
            collection_name=collection,
            # limit=BATCH_SIZE,
            with_payload=True,
            offset=next_offset
        )
        print(f"Exported {len(points)} points")
        if not points:
            break
        for p in points:
            payload = p.payload
            metada = payload.get("metadata",{})
            doc_id = metada.get("document_id")
            s3_url = metada.get("s3_url")
            title = 
            if doc_id:
                doc_id_list.append(doc_id)
        if not next_offset:
            break
    return doc_id_list
                

def main():
    collections = ['rbi_master_direction']
    print(f"Found collections: {collections}")
    for collection in collections:
        doc_ids = get_doc_id(collection)
        print(doc_ids)

if __name__ == "__main__":
    main()
