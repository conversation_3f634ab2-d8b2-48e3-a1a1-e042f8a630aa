# RBI ETL Pipeline

## Getting Started

To set up and run the RBI ETL Pipeline using Docker Compose, follow the steps below:

### Prerequisites

- Docker
- Docker Compose
- An `.env` file with Amazon credentials in the same folder

### Build and Start the Services

1. **Build the Docker images:**

    ```sh
    docker-compose build
    ```

2. **Start the services:**

    ```sh
    docker-compose up
    ```

### Stopping the Services

To stop the running services, use:

```sh
docker-compose down
```

### Additional Information

For more details on the available services and configurations, refer to the `docker-compose.yml` file.
