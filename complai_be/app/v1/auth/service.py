from fastapi import HTTPException
from datetime import datetime
from core.security import get_password_hash, verify_password

#from app.db.models import User

def signup_user(db, username: str, email: str, password: str):
    # Check if a user with the given username exists
    existing_user = db["user"].find_one({"username": username})
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists.")

    # Check if a user with the given email exists
    existing_user = db["user"].find_one({"email": email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already exists.")
    # Hash the password before saving
    hashed_password = get_password_hash(password)
    user_doc = {
        "username": username,
        "email": email,
        "hashed_password": hashed_password,
        "created_at": datetime.utcnow()
    }
    # Insert the user document into the "user" collection
    result = db["user"].insert_one(user_doc)
    user_doc["_id"] = str(result.inserted_id)
    return user_doc


def get_user(db, email: str, password: str):
    # Find the user by email
    user = db["user"].find_one({"email": email})
    if user and verify_password(password, user.get("hashed_password")):
        user["id"] = str(user["_id"])
        return user
    else:
        raise HTTPException(
            status_code=400, detail="Invalid email or password")


def update_user(db, username: str, old_password: str, new_password: str):
    # Find the user by username
    user = db["user"].find_one({"username":username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Verify the old password
    if not verify_password(old_password, user.hashed_password):
        raise HTTPException(status_code=400, detail="Invalid old password")

    # Hash the new password
    hashed_password = get_password_hash(new_password)

    # Update the user's password
    user["hashed_password"] = hashed_password
    result = db["user"].upsert(user)  

    return {"msg": "Password updated successfully"}
