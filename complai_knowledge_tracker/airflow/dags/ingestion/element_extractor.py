# element_extractor.py
import re

def extract_markdown_elements(chunk: str, pages_sorted: list) -> (list, list):
    """
    Extracts structured elements (headings, images, etc.) and internal references from a markdown chunk.
    
    For images, it looks up OCR JSON data for bounding box info.
    
    Args:
        chunk (str): Markdown text chunk.
        pages_sorted (list): List of OCR pages (sorted by page index).
    
    Returns:
        tuple: (elements, internal_references)
            elements: List of dicts with element details.
            internal_references: List of strings (e.g., "Figure 1").
    """
    elements = []
    
    # --- Extract headings ---
    heading_pattern = re.compile(r'^(#+)\s+(.*)', re.MULTILINE)
    for match in heading_pattern.finditer(chunk):
        level = len(match.group(1))
        text = match.group(2).strip()
        elements.append({
            "type": "heading",
            "level": level,
            "text": text,
            "bounding_boxes": []  # Fill in later if positional info is available
        })
    
    # --- Extract images ---
    image_pattern = re.compile(r'!\[.*?\]\((.*?)\)')
    for match in image_pattern.finditer(chunk):
        img_id = match.group(1).strip()
        bboxes = []
        # Search OCR pages for matching image id.
        for page in pages_sorted:
            for img in page.get("images", []):
                if img.get("id") == img_id:
                    bbox = {
                        "page_index": page["index"],
                        "top_left": (img["top_left_x"], img["top_left_y"]),
                        "bottom_right": (img["bottom_right_x"], img["bottom_right_y"])
                    }
                    bboxes.append(bbox)
        elements.append({
            "type": "image",
            "image_id": img_id,
            "bounding_boxes": bboxes
        })
    
    # --- Extract internal references (e.g., "Figure 1", "Annexure 1") ---
    ref_pattern = re.compile(r'\b(Figure|Annexure)\s+(\d+)\b')
    internal_references = []
    for match in ref_pattern.finditer(chunk):
        ref_type = match.group(1)
        ref_number = match.group(2)
        ref_str = f"{ref_type} {ref_number}"
        internal_references.append(ref_str)
    
    return elements, internal_references
