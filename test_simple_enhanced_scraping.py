#!/usr/bin/env python3
"""
Simple test for enhanced link scraping functionality
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def _process_relative_url(href):
    """Process relative URLs to absolute URLs"""
    if href.startswith('http'):
        return href
    if not href.startswith('http'):
        if 'rdocs' in href or '.pdf' in href.lower():
            return f"https://rbidocs.rbi.org.in/{href}"
        else:
            return f"https://www.rbi.org.in/{href}"
    return href

def extract_anchor_information(html_content):
    """Extract comprehensive anchor information"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        anchor_info = {
            'anchors': [],
            'reference_numbers': [],
            'rbi_links': [],
            'document_links': [],
            'pdf_links': [],
            'rbi_page_urls': [],
            'navigation_links': []
        }
        
        # Extract all anchor tags
        for link in soup.find_all('a', href=True):
            href = link['href'].strip()
            text = link.get_text(strip=True)
            
            if not href or not text:
                continue
            
            processed_href = _process_relative_url(href)
            
            anchor_data = {
                'text': text,
                'href': processed_href,
                'original_href': href
            }
            anchor_info['anchors'].append(anchor_data)
            
            # Categorize links
            if processed_href.lower().endswith('.pdf'):
                anchor_info['pdf_links'].append(processed_href)
                anchor_info['document_links'].append(processed_href)
                logger.info(f"   📄 Found direct PDF link: {processed_href}")
            
            elif 'rbi.org.in' in processed_href:
                if 'NotificationUser.aspx' in processed_href or 'Scripts/' in processed_href:
                    anchor_info['rbi_page_urls'].append(processed_href)
                    logger.info(f"   📋 Found RBI page URL: {processed_href}")
                else:
                    anchor_info['rbi_links'].append(processed_href)
        
        # Extract reference numbers from text
        text_content = soup.get_text()
        ref_patterns = [
            r'FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
            r'FEMA\s+\d+\([A-Z]\)/\d{4}-[A-Z]+',  # FEMA 23(R)/2015-RB
            r'RBI/\d{4}-\d{2}/\d+',  # RBI/2025-26/64
            r'[A-Z]+\.No\.[A-Za-z\.]+\d+/[\d\.]+/\d{4}-?\d*'  # DBOD.No.Leg.BC.21/09.07.007/2002-03
        ]
        
        for pattern in ref_patterns:
            matches = re.findall(pattern, text_content)
            for match in matches:
                clean_match = re.sub(r'\s+', ' ', match.strip())
                if clean_match not in anchor_info['reference_numbers']:
                    anchor_info['reference_numbers'].append(clean_match)
        
        return anchor_info
        
    except Exception as e:
        logger.error(f"Error extracting anchor information: {e}")
        return {
            'anchors': [], 'reference_numbers': [], 'rbi_links': [],
            'document_links': [], 'pdf_links': [], 'rbi_page_urls': [], 'navigation_links': []
        }

def test_enhanced_scraping():
    """Test enhanced scraping with realistic notification data"""
    
    print("🧪 Testing Enhanced Link Scraping with Realistic Data")
    print("=" * 80)
    
    # Test with realistic notification content that has direct PDF links
    test_notifications = [
        {
            "title": "FEMA Amendment - Direct PDF Link",
            "rss_description": """
            <p>Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025</p>
            <p>Amendment to Foreign Exchange Management (Remittance of Assets) Regulations, 2015</p>
            <p>In exercise of the powers conferred by clause (a) of sub-section (3) of section 46 of the Foreign Exchange Management Act, 1999 (42 of 1999), the Reserve Bank of India hereby makes the following amendment to the Foreign Exchange Management (Remittance of Assets) Regulations, 2015 (Notification No. FEMA 23(R)/2015-RB dated 1 May 2015):</p>
            <p><a href='https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF'>Download PDF</a></p>
            <p><a href='https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12567&Mode=0'>View Notification</a></p>
            """,
            "expected_pdf": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF",
            "expected_refs": ["FEMA 23(R)/(6)/2025-RB", "FEMA 23(R)/2015-RB"]
        },
        {
            "title": "RBI Circular - Multiple References",
            "rss_description": """
            <p>Notification Nos. RBI/2025-26/64; DoR.MCS.REC.38/01.01.001/2025-26 dated 2 July 2025</p>
            <p>Master Direction on Regulatory Retail Portfolio</p>
            <p>This direction is issued in exercise of powers conferred under Section 35A of the Banking Regulation Act, 1949.</p>
            <p><a href='/rdocs/notification/PDFs/RBI202526064.PDF'>Download Master Direction</a></p>
            <p><a href='https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566'>View Online</a></p>
            """,
            "expected_pdf": "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/RBI202526064.PDF",
            "expected_refs": ["RBI/2025-26/64"]
        },
        {
            "title": "Press Release - No PDF",
            "rss_description": """
            <p>Press Release: 2024-2025/1234 dated 15 June 2025</p>
            <p>RBI announces new monetary policy measures</p>
            <p>The Reserve Bank of India today announced several measures to support economic growth.</p>
            <p><a href='https://www.rbi.org.in/Scripts/BS_PressReleaseDisplay.aspx?prid=54321'>View Press Release</a></p>
            """,
            "expected_pdf": None,
            "expected_refs": []
        }
    ]
    
    results = []
    
    for i, notification in enumerate(test_notifications, 1):
        print(f"\n🔍 Test {i}: {notification['title']}")
        print("-" * 60)
        
        # Extract anchor information
        anchor_info = extract_anchor_information(notification['rss_description'])
        
        print(f"✅ Extracted anchor info:")
        print(f"   📄 PDF links: {len(anchor_info['pdf_links'])}")
        print(f"   🔗 RBI page URLs: {len(anchor_info['rbi_page_urls'])}")
        print(f"   📋 Reference numbers: {len(anchor_info['reference_numbers'])}")
        
        # Check PDF links
        if anchor_info['pdf_links']:
            print(f"   📄 PDF links found:")
            for url in anchor_info['pdf_links']:
                print(f"      • {url}")
            
            # Check if expected PDF was found
            if notification['expected_pdf']:
                if notification['expected_pdf'] in anchor_info['pdf_links']:
                    print(f"   ✅ Expected PDF found: {notification['expected_pdf']}")
                else:
                    print(f"   ❌ Expected PDF not found: {notification['expected_pdf']}")
                    print(f"      Found instead: {anchor_info['pdf_links']}")
        else:
            print(f"   📄 No PDF links found")
            if notification['expected_pdf']:
                print(f"   ❌ Expected PDF but none found: {notification['expected_pdf']}")
            else:
                print(f"   ✅ No PDF expected and none found")
        
        # Check reference numbers
        if anchor_info['reference_numbers']:
            print(f"   📋 Reference numbers found:")
            for ref in anchor_info['reference_numbers']:
                print(f"      • {ref}")
            
            # Check if expected references were found
            for expected_ref in notification['expected_refs']:
                if expected_ref in anchor_info['reference_numbers']:
                    print(f"   ✅ Expected reference found: {expected_ref}")
                else:
                    print(f"   ❌ Expected reference not found: {expected_ref}")
        else:
            print(f"   📋 No reference numbers found")
            if notification['expected_refs']:
                print(f"   ❌ Expected references but none found: {notification['expected_refs']}")
        
        # Create enhanced content for LLM
        enhanced_content = notification['rss_description'][:2000]
        enhanced_content += f"\n\n=== EXTRACTED INFORMATION FOR PROCESSING ==="
        
        if anchor_info['reference_numbers']:
            enhanced_content += f"\nEXTRACTED_REFERENCE_NUMBERS: {', '.join(anchor_info['reference_numbers'])}"
        
        if anchor_info['pdf_links']:
            enhanced_content += f"\nEXTRACTED_PDF_LINKS: {', '.join(anchor_info['pdf_links'][:3])}"
            enhanced_content += f"\n*** USE THESE EXACT URLs FOR new_document_url - DO NOT MODIFY ***"
        else:
            enhanced_content += f"\nEXTRACTED_PDF_LINKS: (none found)"
            enhanced_content += f"\n*** NO PDF LINKS AVAILABLE - LEAVE new_document_url EMPTY ***"
        
        enhanced_content += f"\n=== END EXTRACTED INFORMATION ==="
        
        print(f"   📝 Enhanced content created ({len(enhanced_content)} characters)")
        
        # Store results
        test_result = {
            "notification": notification,
            "anchor_info": anchor_info,
            "enhanced_content_length": len(enhanced_content),
            "pdf_check": {
                "expected": notification['expected_pdf'],
                "found": anchor_info['pdf_links'],
                "correct": notification['expected_pdf'] in anchor_info['pdf_links'] if notification['expected_pdf'] else len(anchor_info['pdf_links']) == 0
            },
            "ref_check": {
                "expected": notification['expected_refs'],
                "found": anchor_info['reference_numbers'],
                "all_found": all(ref in anchor_info['reference_numbers'] for ref in notification['expected_refs'])
            }
        }
        
        results.append(test_result)
    
    # Summary
    print(f"\n📊 Test Summary")
    print("=" * 80)
    
    pdf_correct = sum(1 for r in results if r['pdf_check']['correct'])
    ref_correct = sum(1 for r in results if r['ref_check']['all_found'])
    
    print(f"✅ PDF extraction accuracy: {pdf_correct}/{len(results)} tests passed")
    print(f"✅ Reference extraction accuracy: {ref_correct}/{len(results)} tests passed")
    
    # Save results
    final_results = {
        "test_timestamp": datetime.now().isoformat(),
        "summary": {
            "total_tests": len(results),
            "pdf_accuracy": f"{pdf_correct}/{len(results)}",
            "ref_accuracy": f"{ref_correct}/{len(results)}"
        },
        "detailed_results": results
    }
    
    with open('simple_enhanced_scraping_test_results.json', 'w') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Results saved to: simple_enhanced_scraping_test_results.json")
    
    return final_results

if __name__ == "__main__":
    try:
        results = test_enhanced_scraping()
        print(f"\n🎉 Enhanced scraping test completed!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
