#!/usr/bin/env python3
"""
Test recreating collection with proper SPLADE vector support
"""

from qdrant_client import QdrantClient, models
from qdrant_client.http.models import VectorParams, Distance

def test_collection_recreation():
    """Test recreating collection with SPLADE support"""
    
    client = QdrantClient(url="http://localhost:6333")
    collection_name = "rbi_master_direction"
    backup_collection_name = "rbi_master_direction_backup"
    
    try:
        print("=== COLLECTION RECREATION TEST ===")
        print("This will show what needs to be done, but won't actually recreate the collection")
        print("(We don't want to lose your data!)")
        
        # Get current collection info
        info = client.get_collection(collection_name)
        current_vectors = info.config.params.vectors
        current_sparse_vectors = getattr(info.config.params, 'sparse_vectors', {}) or {}
        points_count = info.points_count
        
        print(f"\nCurrent collection info:")
        print(f"  - Points count: {points_count}")
        print(f"  - Dense vector config: {current_vectors}")
        print(f"  - Sparse vectors: {list(current_sparse_vectors.keys())}")
        
        # What the new collection should look like
        print(f"\nProposed new collection configuration:")
        
        # For hybrid format, we keep the single dense vector but add all sparse vectors
        new_vectors_config = current_vectors  # Keep existing dense vector config
        new_sparse_vectors_config = {
            "fast-sparse-bm25": models.SparseVectorParams(),
            "fast-sparse-bm25-splade": models.SparseVectorParams()
        }
        
        print(f"  - Dense vector config: {new_vectors_config}")
        print(f"  - Sparse vectors: {list(new_sparse_vectors_config.keys())}")
        
        print(f"\nTo fix this properly, we would need to:")
        print(f"1. Create backup collection with new schema")
        print(f"2. Copy all {points_count} points with their existing vectors")
        print(f"3. Generate SPLADE vectors for all points")
        print(f"4. Replace original collection")
        
        # Check if we can at least create a test collection with the right schema
        test_collection_name = "test_splade_schema"
        
        print(f"\nTesting creation of collection with SPLADE schema...")
        
        # Delete test collection if it exists
        try:
            client.delete_collection(test_collection_name)
        except:
            pass
        
        # Create test collection with proper schema
        client.create_collection(
            collection_name=test_collection_name,
            vectors_config=new_vectors_config,
            sparse_vectors_config=new_sparse_vectors_config
        )
        
        print(f"✅ Successfully created test collection '{test_collection_name}' with SPLADE schema")
        
        # Verify the test collection
        test_info = client.get_collection(test_collection_name)
        test_sparse_vectors = getattr(test_info.config.params, 'sparse_vectors', {}) or {}
        print(f"Test collection sparse vectors: {list(test_sparse_vectors.keys())}")
        
        # Clean up test collection
        client.delete_collection(test_collection_name)
        print(f"✅ Cleaned up test collection")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_collection_recreation()
