#!/usr/bin/env python3
"""
Simple test to verify DocumentTypeMapper logic without dependencies
"""

from enum import Enum

# Copy the DocumentType enum and DocumentTypeMapper class locally for testing
class DocumentType(str, Enum):
    """Document type enumeration"""
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    CIRCULAR = "circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    PUBLICATION = "publication"
    OTHER = "other"

class DocumentTypeMapper:
    """Utility class to map between different document type naming conventions"""
    
    # Mapping from RSS feed categories to DocumentType enum values
    RSS_TO_DOCUMENT_TYPE = {
        "circulars": DocumentType.CIRCULAR,
        "directions": DocumentType.CIRCULAR,  # Directions are treated as circulars
        "master_circulars": DocumentType.MASTER_CIRCULAR,
        "master_directions": DocumentType.MASTER_DIRECTION,
        "notifications": DocumentType.NOTIFICATION,
        "press_releases": DocumentType.PRESS_RELEASE,
        "speeches": DocumentType.SPEECHES,
        "tenders": DocumentType.OTHER,  # Tenders are treated as other documents
        "publications": DocumentType.PUBLICATION,
    }
    
    # Mapping from DocumentType to collection names (with rbi_ prefix)
    DOCUMENT_TYPE_TO_COLLECTION = {
        DocumentType.MASTER_DIRECTION: "rbi_master_direction",
        DocumentType.MASTER_CIRCULAR: "rbi_master_circular", 
        DocumentType.CIRCULAR: "rbi_circular",
        DocumentType.NOTIFICATION: "rbi_notification",
        DocumentType.PRESS_RELEASE: "rbi_press_release",
        DocumentType.SPEECHES: "rbi_speech",
        DocumentType.TENDER: "rbi_tender",
        DocumentType.PUBLICATION: "rbi_publication",
        DocumentType.OTHER: "rbi_other",
    }
    
    @classmethod
    def rss_category_to_collection_name(cls, rss_category: str) -> str:
        """Convert RSS feed category to Qdrant collection name"""
        document_type = cls.RSS_TO_DOCUMENT_TYPE.get(rss_category, DocumentType.OTHER)
        return cls.DOCUMENT_TYPE_TO_COLLECTION[document_type]
    
    @classmethod
    def document_type_to_collection_name(cls, document_type: DocumentType) -> str:
        """Convert DocumentType to Qdrant collection name"""
        return cls.DOCUMENT_TYPE_TO_COLLECTION[document_type]
    
    @classmethod
    def get_all_collection_names(cls) -> list:
        """Get all possible collection names"""
        return list(cls.DOCUMENT_TYPE_TO_COLLECTION.values())
    
    @classmethod
    def categorize_document_title(cls, title: str) -> str:
        """Categorize document based on title and return collection name"""
        title_lower = title.lower()
        
        if "circular" in title_lower and "master" not in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.CIRCULAR]
        elif "direction" in title_lower and "master" not in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.CIRCULAR]  # Directions treated as circulars
        elif "master circular" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.MASTER_CIRCULAR]
        elif "master direction" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.MASTER_DIRECTION]
        elif "notification" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.NOTIFICATION]
        elif "press release" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.PRESS_RELEASE]
        elif "speech" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.SPEECHES]
        elif "tender" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.TENDER]
        elif "publication" in title_lower:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.PUBLICATION]
        else:
            return cls.DOCUMENT_TYPE_TO_COLLECTION[DocumentType.OTHER]

def test_collection_naming():
    """Test collection naming logic"""
    print("🧪 Testing Collection Naming Logic")
    print("=" * 50)
    
    # Test RSS category to collection name mapping
    print("\n1. RSS Category to Collection Name Mapping:")
    test_cases = [
        ("circulars", "rbi_circular"),
        ("master_circulars", "rbi_master_circular"),
        ("master_directions", "rbi_master_direction"),
        ("notifications", "rbi_notification"),
        ("press_releases", "rbi_press_release"),
        ("speeches", "rbi_speech"),
        ("publications", "rbi_publication"),
    ]
    
    all_passed = True
    for rss_category, expected_collection in test_cases:
        actual_collection = DocumentTypeMapper.rss_category_to_collection_name(rss_category)
        if actual_collection == expected_collection:
            print(f"   ✅ {rss_category} -> {actual_collection}")
        else:
            print(f"   ❌ {rss_category} -> {actual_collection} (expected {expected_collection})")
            all_passed = False
    
    # Test document type to collection name mapping
    print("\n2. DocumentType to Collection Name Mapping:")
    for doc_type in DocumentType:
        collection_name = DocumentTypeMapper.document_type_to_collection_name(doc_type)
        print(f"   ✅ {doc_type.value} -> {collection_name}")
    
    # Test title categorization
    print("\n3. Title Categorization:")
    title_test_cases = [
        ("RBI Master Direction on Banking Regulations", "rbi_master_direction"),
        ("RBI Master Circular on Credit Risk", "rbi_master_circular"),
        ("RBI Circular on Payment Systems", "rbi_circular"),
        ("RBI Notification on New Guidelines", "rbi_notification"),
        ("RBI Press Release on Policy Changes", "rbi_press_release"),
        ("Governor's Speech at Banking Conference", "rbi_speech"),
        ("Annual Report Publication", "rbi_publication"),
        ("Some Other Document", "rbi_other"),
    ]
    
    for title, expected_collection in title_test_cases:
        actual_collection = DocumentTypeMapper.categorize_document_title(title)
        if actual_collection == expected_collection:
            print(f"   ✅ '{title}' -> {actual_collection}")
        else:
            print(f"   ❌ '{title}' -> {actual_collection} (expected {expected_collection})")
            all_passed = False
    
    # Test semantic retriever compatibility
    print("\n4. Semantic Retriever Compatibility:")
    expected_semantic_collections = {
        "rbi_master_direction",
        "rbi_master_circular", 
        "rbi_circular",
        "rbi_notification",
        "rbi_press_release",
        "rbi_speech",
        "rbi_publication",
        "rbi_other",
    }
    
    mapper_collections = set(DocumentTypeMapper.get_all_collection_names())
    
    if mapper_collections == expected_semantic_collections:
        print("   ✅ Collection names match semantic retriever expectations!")
    else:
        missing_in_mapper = expected_semantic_collections - mapper_collections
        extra_in_mapper = mapper_collections - expected_semantic_collections
        
        if missing_in_mapper:
            print(f"   ❌ Missing in DocumentTypeMapper: {missing_in_mapper}")
            all_passed = False
        if extra_in_mapper:
            print(f"   ❌ Extra in DocumentTypeMapper: {extra_in_mapper}")
            all_passed = False
    
    # Show all collection names
    print("\n5. All Collection Names:")
    all_collections = DocumentTypeMapper.get_all_collection_names()
    for collection in sorted(all_collections):
        print(f"   - {collection}")
    
    print(f"\n📊 Total collections: {len(all_collections)}")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Collection naming is consistent.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the collection naming implementation.")
        return False

if __name__ == "__main__":
    success = test_collection_naming()
    exit(0 if success else 1)
