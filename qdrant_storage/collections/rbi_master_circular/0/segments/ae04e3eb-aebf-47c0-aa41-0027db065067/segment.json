{"version": 2241, "config": {"vector_data": {"": {"size": 3072, "distance": "<PERSON><PERSON>e", "storage_type": "InRamChunkedMmap", "index": {"type": "hnsw", "options": {"m": 32, "ef_construct": 150, "full_scan_threshold": 10000, "max_indexing_threads": 0, "on_disk": false}}, "quantization_config": null}}, "sparse_vector_data": {"fast-sparse-bm25": {"index": {"full_scan_threshold": null, "index_type": "ImmutableRam"}, "storage_type": "on_disk"}}, "payload_storage_type": {"type": "on_disk"}}}