{"family": "backend-task", "networkMode": "awsvpc", "containerDefinitions": [{"name": "backend", "image": "116981798651.dkr.ecr.ap-south-1.amazonaws.com/backend:latest", "memory": 512, "cpu": 256, "essential": true, "portMappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}]}], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::116981798651:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::116981798651:role/ecsTaskExecutionRole"}