import yaml
import json
from app.v2.query.utils.completion_client import completion_client
from datetime import datetime
from langfuse.decorators import observe
from typing import List, Optional, Dict

class AmbiguityResolver:
    def __init__(self, model: str, prompt_file: str):
        self.model = model
        self.prompt = self._load_prompt(prompt_file)

    @observe(as_type="generation")
    def run(self, query: str) -> Dict:
        final_prompt = self.prompt.format(query=query, today=datetime.now().strftime("%Y-%m-%d"))
        messages = [{"role": "system", "content": final_prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)

        try:
            analysis = json.loads(response.strip())

            return {
                "is_valid": analysis["is_valid"],
                "validation_reason": analysis["validation_reason"],
                "required_parameters": {
                    "ambiguous_terms": analysis["required_parameters"].get("ambiguous_terms", []),
                    "time_range": analysis["required_parameters"].get("time_range"),
                    "regulatory_references": analysis["required_parameters"].get("regulatory_references", [])
                },
                "clarification_question": analysis.get("clarification_question"),
                "llm_follow_up": analysis.get("llm_follow_up")
            }
        except json.JSONDecodeError:
            # Fallback for non-JSON responses
            return {
                "is_valid": False,
                "validation_reason": "Failed to parse response",
                "required_parameters": {
                    "ambiguous_terms": [],
                    "time_range": None,
                    "regulatory_references": []
                },
                "clarification_question": "Could you please rephrase your query?",
                "llm_follow_up": "I couldn’t understand your query. Could you provide more details?"
            }

    def _load_prompt(self, prompt_file: str) -> str:
        with open(prompt_file, "r") as f:
            data = yaml.safe_load(f)
        return data.get("prompt", "")
