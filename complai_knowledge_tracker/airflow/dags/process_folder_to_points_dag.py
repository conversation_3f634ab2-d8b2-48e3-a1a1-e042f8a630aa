from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from datetime import datetime, timedelta
import logging
from dateutil import parser

# --- UTILS ---
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "utils"))
from utils.pdf_utils import parse_pdf_to_html_ast, chunk_html_ast, extract_obligations, strip_data_attributes, extract_positions_from_html
from utils.qdrant_utils import upsert_to_qdrant, splade_sparse, ingest_chunks_to_qdrant
from utils.database_utils import db_manager
from utils.openai_utils import en_embeddings



# SPLADE setup (reuse from splade_qdrant_vectors.py)
SPLADE_VECTOR_NAME = "fast-sparse-bm25-splade"
# Use vector_db_host and clean up any carriage returns
vector_db_host = Variable.get("vector_db_host", default_var="localhost")
if vector_db_host:
    vector_db_host = vector_db_host.strip().replace('\r', '').replace('\n', '')
QDRANT_URL = vector_db_host

mongodb_uri = Variable.get("documentdb_uri", default_var="mongodb://localhost:27017/")


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def categorize_document(title):
    """Categorize document based on title and return collection name"""
    from utils.document_pipeline import DocumentTypeMapper
    return DocumentTypeMapper.categorize_document_title(title)


def scrape_rss_feeds():
    """Scrape RSS feeds and return articles"""
    import feedparser
    import requests
    from bs4 import BeautifulSoup
    import re
    import os
    import uuid
    import boto3
    from datetime import datetime

    logger.info("Starting RSS feed scraping")
    articles = []

    RSS_FEEDS = {
        "notifications": "https://rbi.org.in/notifications_rss.xml",
        "press_releases": "https://rbi.org.in/pressreleases_rss.xml",
        "publications": "https://rbi.org.in/Publication_rss.xml"
    }

    S3_BUCKET_NAME = Variable.get("s3_bucket_name", default_var="rbi-notifications")
    aws_access_key_id = Variable.get("aws_access_key_id")
    aws_secret_access_key = Variable.get("aws_secret_access_key")
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )

    def cleanup_rss_xml_description(data):
        """Parse RBI notification from RSS description"""
        return parse_rbi_notification(data)

    def parse_rbi_notification(data):
        """Parse RBI notification XML to extract key fields"""
        soup = BeautifulSoup(data, 'html.parser')
        document = {}

        # Extract circular number
        circular_nums = []
        p_tags = soup.find_all('p')
        for p in p_tags:
            if re.search(r'[A-Z]+/\d{4}-\d{2}/\d+', p.get_text()) or re.search(r'RBI/\d{4}-\d{2}/\d+', p.get_text()):
                circular_nums.append(p.get_text().strip())

        document['circular_numbers'] = circular_nums

        # Extract press release number
        press_release_number = None
        press_release_tags = soup.find_all(
            'p', text=re.compile(r'Press Release: \d{4}-\d{4}/\d+'))
        for tag in press_release_tags:
            match = re.search(r'\d{4}-\d{4}/\d+', tag.get_text().strip())
            if match:
                press_release_number = match.group()
                break

        document['press_release_number'] = press_release_number

        # Extract date
        pub_date = soup.find('pubDate')
        date = None
        if pub_date:
            try:
                date = pub_date.get_text().strip()
                document['date_iso'] = datetime.strptime(
                    date, '%a, %d %b %Y %H:%M:%S').isoformat()
            except ValueError:
                pass

        document['date'] = date

        # Extract addressee
        addressee = None
        if date:
            for i, p in enumerate(p_tags):
                if date in p.get_text():
                    if i + 1 < len(p_tags):
                        addressee = p_tags[i + 1].get_text().strip()
                    break

        document['addressee'] = addressee

        # Extract subject/title
        title_tags = soup.find_all('p', class_='head')
        for tag in title_tags:
            if tag.get_text().strip() and not tag.get_text().strip().startswith('Annex'):
                document['subject'] = tag.get_text().strip()
                break

        # Extract main text
        main_text = []
        found_subject = False
        for p in p_tags:
            if not found_subject and p.get('class') and 'head' in p.get('class'):
                found_subject = True
                continue
            if found_subject:
                main_text.append(p.get_text().strip())

        document['main_text'] = '\n\n'.join(main_text)

        return document

    def sanitize_filename(filename):
        """Sanitize filenames"""
        sanitized = re.sub(r"[^a-zA-Z0-9_\-]", "_", filename)
        return sanitized[:50]

    def upload_to_s3(file_path, s3_key):
        """Upload file to S3"""
        try:
            s3_client.upload_file(file_path, S3_BUCKET_NAME, s3_key)
            s3_url = f"https://{S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"
            logger.info(f"Uploaded {file_path} to S3 as {s3_key}")
            return s3_url
        except Exception as e:
            logger.error(f"Error uploading to S3: {e}")
            return None

    def article_exists_in_s3(s3_key):
        """Check if article exists in S3"""
        try:
            s3_client.head_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
            return True
        except:
            return False

    def extract_pdf_link(article_url):
        """Extract PDF link from the table header <a> tag in the article page."""
        response = requests.get(article_url)
        soup = BeautifulSoup(response.content, "html.parser")
        table_header = soup.find("td", class_="tableheader")
        if table_header:
            pdf_anchor = table_header.find("a", href=re.compile(r".*\.pdf", re.IGNORECASE))
            if pdf_anchor:
                return pdf_anchor["href"]
        return None

    def download_pdf(pdf_url, pdf_filepath):
        """Download PDF file"""
        response = requests.get(pdf_url)
        with open(pdf_filepath, "wb") as pdf_file:
            pdf_file.write(response.content)
        logger.info(f"Downloaded PDF from {pdf_url} to {pdf_filepath}")

    def convert_published_date(pub_date):
        """
        Convert a published_date value to standardized YYYY-MM-DD format.
        Uses the new date standardization utility for consistent storage.
        """
        from utils.date_utils import standardize_date_for_storage
        return standardize_date_for_storage(pub_date)

    for feed_name, feed_url in RSS_FEEDS.items():
        try:
            feed = feedparser.parse(feed_url)
            for entry in feed.entries:
                title = entry.title
                link = entry.link
                published_date =convert_published_date(entry.published)

                # Parse the article content
                doc_info = cleanup_rss_xml_description(entry.description)

                # Categorize document
                category = categorize_document(title)

                # Extract PDF link if available
                pdf_link = extract_pdf_link(link)

                article_data = {
                    "title": title,
                    "link": link,
                    "published_date": published_date,
                    "category": category,
                    "doc_info": doc_info,
                    "feed_name": feed_name,
                    "id": str(uuid.uuid4()),
                    "ingestion_timestamp": datetime.now().strftime('%Y-%m-%d')
                }

                if pdf_link:
                    # Get current date in yyyymmdd format
                    today = datetime.now().strftime('%Y%m%d')
                    pdf_filename = sanitize_filename(title) + ".pdf"
                    s3_key = f"{today}/{category}/{pdf_filename}"
                    
                    if not article_exists_in_s3(s3_key):
                        pdf_filepath = os.path.join("/tmp", pdf_filename)
                        download_pdf(pdf_link, pdf_filepath)
                        s3_url = upload_to_s3(pdf_filepath, s3_key)
                        article_data["pdf_link"] = pdf_link
                        article_data["s3_url"] = f"https://{S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"
                        logger.info(f"Processed article: {title}")
                    else:
                        logger.info(f"Article already exists in S3: {title}")
                        article_data["pdf_link"] = pdf_link
                        article_data["s3_url"] = f"https://{S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"

                articles.append(article_data)
        except Exception as e:
            logger.error(f"Error processing feed {feed_name}: {e}")
            continue

    logger.info(f"Scraped {len(articles)} articles")
    return articles


def store_articles_in_mongo(**context):
    """Store articles in MongoDB"""
    from pymongo import MongoClient
    import requests

    task_instance = context['task_instance']
    articles = task_instance.xcom_pull(
        task_ids='scrape_and_store_group.scrape_rss_feeds')

    if not articles:
        logger.warning("No articles received from scraping task")
        return

    logger.info(f"Storing {len(articles)} articles in MongoDB")

    def download_tls_ca_file():
        try:
            response = requests.get(
                "https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
            response.raise_for_status()
            with open("/tmp/global-bundle.pem", "wb") as file:
                file.write(response.content)
            logger.info("TLS CA file downloaded successfully")
        except requests.RequestException as e:
            logger.error(f"Failed to download TLS CA file: {e}")
            raise

    download_tls_ca_file()

    client = None
    try:

        params = {
            "tls": True,
            "tlsCAFile": "/tmp/global-bundle.pem",
            "replicaSet": "rs0",
            "readPreference": "secondaryPreferred",
            "retryWrites": False
        }
        if "docker" in mongodb_uri:
            params = {}

        client = MongoClient(
            mongodb_uri,
            **params
        )

        db = client["rss_feed_db"]
        collection_name = "articles"
        if collection_name not in db.list_collection_names():
            collection = db.create_collection(collection_name)
        else:
            collection = db[collection_name]
        for article in articles:
            collection.update_one(
                {"link": article["link"]},
                {"$set": article},
                upsert=True
            )
        logger.info("Successfully stored articles in MongoDB")
    except Exception as e:
        logger.error(f"Error storing articles in MongoDB: {e}")
        raise
    finally:
        if client:
            client.close()


# --- ENHANCED PROCESSING ---
def process_articles(**context):
    """
    Process articles by generating dense, BM25, and SPLADE vectors, storing them in Qdrant (and MongoDB),
    and handling PDF content by chunking and storing each chunk similarly.
    """
    import os, requests, tempfile
    from utils.qdrant_utils import qdrant_manager

    task_instance = context['task_instance']
    articles = task_instance.xcom_pull(task_ids='scrape_and_store_group.scrape_rss_feeds')
    if not articles:
        logger.warning("No articles received from storage task")
        return
    logger.info(f"Processing {len(articles)} articles")

    for article in articles:
        # Process PDF
        pdf_url = article.get("pdf_link")
        if pdf_url:
            try:
                # Download PDF to a temporary file
                with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmpf:
                    response = requests.get(pdf_url)
                    tmpf.write(response.content)
                    pdf_path = tmpf.name

                # Parse PDF into an HTML AST and then chunk it
                soup = parse_pdf_to_html_ast(pdf_path)
                chunks = chunk_html_ast(soup, max_chars=3000)

                for idx, chunk in enumerate(chunks):
                    chunk_id = int(hash(f"{article['id']}_chunk{idx}") % (2**31))
                    chunk_payload = {
                        "article_id": article["id"],
                        "chunk_index": idx,
                        "content": chunk["content"],
                        "positions": chunk.get("positions", extract_positions_from_html(chunk["content"])),
                        "category": article.get("category"),
                        "title": article.get("title"),
                        "published_date": article.get("published_date"),
                        "link": article.get("link"),
                    }
                    chunk_text = chunk["content"]

                    # Use QdrantManager to properly handle multi-vector upsert
                    # This will automatically generate all 3 vectors (dense, BM25, SPLADE)
                    # and format them correctly for Qdrant multi-vector structure
                    # Use the collection name based on document category
                    collection_name = article.get("category", "rbi_other")  # category is already the collection name
                    success = qdrant_manager.upsert_point(
                        collection_name=collection_name,
                        point_id=chunk_id,
                        payload=chunk_payload,
                        text_for_embedding=chunk_text
                    )

                    success_mongo_addition = db_manager.upsert_document(
                        document=chunk_payload,
                        db_name="rbi_regulation_db",
                        collection_name=collection_name,
                        unique_key="article_id"
                    )

                    if success and success_mongo_addition:
                        logger.info(f"✅ Successfully upserted chunk {idx} for article {article.get('id')} with all 3 vectors")
                    elif not success_mongo_addition:
                        logger.error(f"❌ Failed to upsert chunk {idx} for article {article.get('id')} to MongoDB")
                    elif not success:
                        logger.error(f"❌ Failed to upsert chunk {idx} for article {article.get('id')} to Qdrant")
                    else:
                        logger.error(f"❌ Failed to upsert chunk {idx} for article {article.get('id')}")

                # Clean up temporary PDF file
                os.remove(pdf_path)
            except Exception as e:
                logger.error(f"Error processing PDF for article {article.get('id')}: {e}")


# Define default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# # Define the DAG
# dag = DAG(
#     'rss_feed_etl_dag',
#     default_args=default_args,
#     description='A simple RSS feed ETL DAG',
#     schedule_interval=timedelta(days=1),
#     catchup=False,  
# )

# # Define task groups
# with TaskGroup("scrape_and_store_group", dag=dag) as scrape_and_store_group:
#     scrape_task = PythonOperator(
#         task_id='scrape_rss_feeds',
#         python_callable=scrape_rss_feeds,
#         dag=dag,
#     )

#     store_task = PythonOperator(
#         task_id='store_articles_in_mongo',
#         python_callable=store_articles_in_mongo,
#         provide_context=True,
#         dag=dag,
#     )

#     scrape_task >> store_task

# with TaskGroup("process_group", dag=dag) as process_group:
#     process_task = PythonOperator(
#         task_id='process_articles',
#         python_callable=process_articles,
#         provide_context=True,
#         dag=dag,
#     )

# # Set task dependencies
# scrape_and_store_group >> process_group
