[{"chunk": "![img-0.jpeg](img-0.jpeg)\n\nRBI/DPSS/2024-25/123\nCO.DPSS.OVRST.No.S447/06-26-002/2024-25\nJuly 30, 2024\n\nThe Chairman / Managing Director / Chief Executive Officer\nAuthorised Non-bank Payment System Operators\nMadam / Dear Sir,\n\n", "positions": [{"page_index": 0, "rectangles": [[366.*************, 130.**************, 369.*************, 144.*************], [72.**************, 156.**************, 191.*************, 168.*************], [72.**************, 175.*************, 529.*************, 187.**************], [72.**************, 193.*************, 75.**************, 206.**************], [72.**************, 212.**************, 364.*************, 225.*************], [72.**************, 227.**************, 316.*************, 239.*************], [72.**************, 242.*************, 75.**************, 254.**************], [72.**************, 256.**************, 161.*************, 269.*************]]}]}, {"chunk": "# Master Directions on Cyber Resilience and Digital Payment Security Controls for nonbank Payment System Operators\n\nThe safety and security of payment systems is a key objective of the Reserve Bank of India (RBI). To ensure that the authorised non-bank Payment System Operators (PSOs) are resilient to existing and emerging information systems and cyber security risks, it was announced in the Statement on Developmental and Regulatory Policies issued as part of Monetary Policy Statement dated April 08, 2022 that RBI will issue directions on Cyber Resilience and Payment Security Controls for Payment System Operators (PSOs).\n2. Accordingly, a draft Master Direction was published on June 02, 2023 seeking comments / feedback from stakeholders. Based on the feedback received, it has been decided to issue the final Directions, covering robust governance mechanisms for identification, assessment, monitoring and management of these risks. The Directions also cover baseline security measures for ensuring system resiliency as well as safe and secure digital payment transactions. However, they shall endeavour to migrate to latest security standards. The existing instructions on security and risk mitigation measures for payments done using cards, Prepaid Payment Instruments (PPIs) and mobile banking continue to be applicable as hitherto. In case of any discrepancy in applicability of guidelines, the instructions provided in this Master Direction shall prevail.\n\n3. These Directions are issued under Section 10 (2) read with Section 18 of Payment and Settlement Systems Act, 2007 (Act 51 of 2007).\n\nYours faithfully,\n(<PERSON><PERSON><PERSON><PERSON>)\nChief General Manager\n\n|                              Index                              | Page |\n| :-------------------------------------------------------------: | :--: |\n|                     Section I - Preliminary                     |  4   |\n|                          Introduction                           |  4   |\n|                  Short Title and Commencement                   |  4   |\n|                          Applicability                          |  5   |\n|                             Purpose                             |  5   |\n|                Section II - Governance Controls                 |  5   |\n|                   Cyber Security Preparedness                   |  5   |\n|                 Risk Assessment and Monitoring                  |  6   |\n| Section III - Baseline Information Security Measures / Controls |  6   |\n|                      Inventory Management                       |  6   |\n|                 Identity and Access Management                  |  6   |\n|                        Network Security                         |  7   |\n|                 Application Security Life Cycle                 |  8   |\n|                        Security Testing                         |  8   |\n|                     Vendor Risk Management                      |  9   |\n|                          Data Security                          |  9   |\n|             Patch and Change Management Life Cycle              |  10  |\n|                        Incident Response                        |  10  |\n|                 Business Continuity Plan (BCP)                  |  10  |\n|            Application Programming Interfaces (APIs)            |  11  |\n|                  Employee Awareness / Training                  |  11  |\n|                         Cloud Security                          |  11  |\n|                     Other Security Measures                     |  12  |\n|    Section IV - Digital Payment Security Measures / Controls    |  13  |\n|                         Mobile Payments                         |  14  |\n|                          Card Payments                          |  14  |\n|                   Prepaid Payment Instruments                   |  15  |\n|                            Acronyms                             |  16  |", "positions": [{"page_index": 0, "rectangles": [[106.58000183105469, 370.*************, 122.62735748291016, 382.76837158203125], [125.**************, 370.*************, 162.08433532714844, 382.76837158203125], [164.74497985839844, 370.*************, 186.1736297607422, 382.76837158203125], [188.977783203125, 370.*************, 207.29315185546875, 382.76837158203125], [210.1699981689453, 370.*************, 264.5861511230469, 382.76837158203125], [267.39031982421875, 370.*************, 316.31719970703125, 382.76837158203125], [319.1499938964844, 370.*************, 365.6394348144531, 382.76837158203125], [368.443603515625, 370.*************, 408.1986389160156, 382.76837158203125], [410.9034423828125, 370.*************, 462.91290283203125, 382.76837158203125], [465.5956115722656, 370.*************, 504.7655029296875, 382.76837158203125], [507.5696716308594, 370.*************, 526.3377075195312, 382.76837158203125], [72.**************, 389.53875732421875, 112.99344635009766, 401.8483581542969], [116.15088653564453, 389.53875732421875, 128.3280029296875, 401.8483581542969], [131.5399932861328, 389.53875732421875, 171.9291229248047, 401.8483581542969], [175.10000610351562, 389.53875732421875, 196.28578186035156, 401.8483581542969], [199.44322204589844, 389.53875732421875, 248.33938598632812, 401.8483581542969], [251.57000732421875, 389.53875732421875, 308.933837890625, 401.8483581542969], [311.9698486328125, 389.53875732421875, 355.4453430175781, 401.8483581542969], [358.6358947753906, 389.53875732421875, 380.0538330078125, 401.8483581542969], [383.2112731933594, 389.53875732421875, 413.1517639160156, 401.8483581542969], [416.1877746582031, 389.53875732421875, 457.0467834472656, 401.8483581542969], [460.29998779296875, 389.53875732421875, 489.0260314941406, 401.8483581542969], [492.2200012207031, 389.53875732421875, 500.6919860839844, 401.8483581542969], [503.84942626953125, 389.53875732421875, 526.4703979492188, 401.8483581542969], [72.**************, 408.498779296875, 526.4324340820312, 420.8083801269531], [72.**************, 427.*************, 120.79871368408203, 439.76837158203125], [123.23855590820312, 427.*************, 155.57472229003906, 439.76837158203125], [158.01455688476562, 427.*************, 211.2273712158203, 439.76837158203125], [213.66720581054688, 427.*************, 244.09347534179688, 439.76837158203125], [246.53330993652344, 427.*************, 271.56097412109375, 439.76837158203125], [274.0008239746094, 427.*************, 292.1947326660156, 439.76837158203125], [294.63458251953125, 427.*************, 322.2191162109375, 439.76837158203125], [324.*************, 427.*************, 345.9772033691406, 439.76837158203125], [348.41705322265625, 427.*************, 369.7242431640625, 439.76837158203125], [372.1640930175781, 427.*************, 390.2254943847656, 439.76837158203125], [392.66534423828125, 427.*************, 421.4134826660156, 439.76837158203125], [423.85333251953125, 427.*************, 473.95281982421875, 439.76837158203125], [476.3926696777344, 427.*************, 491.5837097167969, 439.76837158203125], [494.0235595703125, 427.*************, 526.3596801757812, 439.76837158203125], [72.**************, 446.*************5, 473.8091125488281, 458.7483825683594], [72.**************, 484.3587646484375, 81.21316528320312, 496.6683654785156], [108.*************, 484.3587646484375, 171.06944274902344, 496.6683654785156], [174.3800048828125, 484.3587646484375, 183.58737182617188, 496.6683654785156], [186.84417724609375, 484.3587646484375, 211.8718719482422, 496.6683654785156], [215.1507568359375, 484.3587646484375, 251.8918914794922, 496.6683654785156], [255.19285583496094, 484.3587646484375, 301.5497741699219, 496.6683654785156], [304.8286437988281, 484.3587646484375, 327.3612976074219, 496.6683654785156], [330.61810302734375, 484.3587646484375, 380.86114501953125, 496.6683654785156], [384.30999755859375, 484.3587646484375, 399.6191711425781, 496.6683654785156], [402.9100036621094, 484.3587646484375, 429.8807373046875, 496.6683654785156], [433.1375427246094, 484.3587646484375, 451.5633239746094, 496.6683654785156], [454.8421936035156, 484.3587646484375, 482.3291320800781, 496.6683654785156], [485.6199951171875, 484.3587646484375, 529.6090698242188, 496.6683654785156], [72.**************, 503.31878662109375, 267.5861511230469, 515.62841796875], [270.04998779296875, 503.31878662109375, 526.4876708984375, 515.62841796875], [72.**************, 522.***********, 84.34464263916016, 534.*************], [87.**************, 522.***********, 116.0107192993164, 534.*************], [119.05999755859375, 522.***********, 137.3643341064453, 534.*************], [140.27890014648438, 522.***********, 163.60641479492188, 534.*************], [166.6999969482422, 522.***********, 221.75241088867188, 534.*************], [224.66697692871094, 522.***********, 269.3569030761719, 534.*************], [272.2714538574219, 522.***********, 305.92138671875, 534.*************], [308.8359375, 522.***********, 369.************, 534.*************], [372.4263916015625, 522.***********, 437.29913330078125, 534.*************], [440.260009765625, 522.***********, 456.2569885253906, 534.*************], [459.2929992675781, 522.***********, 526.405029296875, 534.*************], [72.**************, 541.***********, 350.1791076660156, 553.*************], [353.5683898925781, 541.***********, 526.4847412109375, 553.*************], [72.**************, 560.3187866210938, 526.35107421875, 572.62841796875], [72.**************, 579.***********, 134.4662322998047, 591.*************], [139.6999969482422, 579.***********, 402.374755859375, 591.*************], [404.8299865722656, 579.***********, 445.5675354003906, 591.*************], [447.82000732421875, 579.***********, 499.2333068847656, 591.*************], [504.*************, 579.***********, 526.*************, 591.*************], [72.**************, 598.***********, 526.*************, 610.*************], [72.**************, 617.************, 523.*************, 629.*************], [72.**************, 636.*************, 526.*************, 648.*************], [72.**************, 655.*************, 179.**************, 667.*************]]}]}, {"chunk": "# Master Directions on Cyber Resilience and Digital Payment Security Controls for nonbank Payment System Operators (PSOs)\n\n## Section I\n\n## Preliminary\n\n## Introduction\n\n1. In exercise of the powers conferred under Section 10 (2) read with Section 18 of the Payment and Settlement Systems Act, 2007 (PSS Act), the Reserve Bank of India (RBI, Bank) being satisfied that it is necessary and expedient in the public interest so to do, issues the Master Directions hereinafter specified.\n\n## Short Title and Commencement\n\n2. These Master Directions shall be called the Reserve Bank of India (Cyber Resilience and Digital Payment Security Controls for non-bank PSOs) Master Directions, 2024 (Master Directions, Directions).\n3. The Directions shall come into effect on the day they are placed on the official website of the RBI. In order to provide adequate time to put in place the necessary compliance structure, a phased implementation approach ${ }^{1}$ is prescribed as under -\n\n| Regulated Entity               |   Timeline    |\n| ------------------------------ | :-----------: |\n| Large non-bank PSOs ${ }^{2}$  | April 1, 2025 |\n| Medium non-bank PSOs ${ }^{3}$ | April 1, 2026 |\n| Small non-bank PSOs ${ }^{4}$  | April 1, 2028 |\n\n[^0]\n[^0]:    ${ }^{1}$ The timelines prescribed in the instructions issued earlier by RBI shall continue to be applicable as hitherto.\n${ }^{2}$ For the purpose of these Directions, Clearing Corporation of India Limited (CCIL), National Payments Corporation of India (NPCI), NPCI Bharat Bill Pay Limited, Card Payment Networks, Non-bank ATM Networks, White Label ATM Operators (WLAOs), Large PPI Issuers, Trade Receivables Discounting System (TReDS) Operators, Bharat Bill Payment Operating Units (BBPOUs) and Payment Aggregators (PAs) are considered as large non-bank PSOs.\n${ }^{3}$ Cross-border (in-bound) Money Transfer Operators under Money Transfer Service Scheme (MTSS) and Medium PPI Issuers are considered as medium non-bank PSOs.\n${ }^{4}$ Small PPI Issuers and Instant Money Transfer Operators are considered as small non-bank PSOs.\nCategorisation of authorised non-bank PPI Issuers into small, medium and large is as per the Oversight Framework for Financial Market Infrastructures (FMIs) and Retail Payment Systems (RPSs). If a PPI Issuer moves to a higher category, the timeline of the category to which it moves into, would apply. For instance, if a small (or medium) PPI issuer moves into medium category (or large), it will need to comply with these Directions within a period of two (or one) years from the time of new categorisation, as the case may be.", "positions": [{"page_index": 3, "rectangles": [[108.*************, 365.*************, 526.*************, 378.*************], [72.**************, 384.*************, 128.*************, 397.*************], [72.**************, 712.************, 133.**************, 721.************], [135.**************, 712.************, 146.**************, 721.************], [148.*************, 712.************, 194.**************, 721.************], [197.*************, 712.************, 238.**************, 721.************], [240.7413330078125, 712.************, 255.82077026367188, 721.************], [258.2410583496094, 712.************, 288.2106628417969, 721.************], [290.6309509277344, 712.************, 309.05694580078125, 721.************], [311.59674072265625, 712.************, 337.4827575683594, 721.************], [340.0225524902344, 712.************, 375.84869384765625, 721.************], [378.26898193359375, 712.************, 395.85833740234375, 721.************], [398.27862548828125, 712.************, 420.718505859375, 721.************], [423.1089172363281, 712.************, 431.5948181152344, 721.************], [434.1346130371094, 712.************, 445.0507507324219, 721.************], [447.441162109375, 712.************, 463.357***********, 721.************], [465.77752685546875, 712.************, 481.5740966796875, 721.************], [484.29998779296875, 712.************, 525.634033203125, 721.************], [72.**************, 724.***********, 438.659912109375, 734.***********], [440.*************, 724.***********, 525.************, 734.***********], [72.**************, 736.*************, 417.*************, 746.*************], [419.*************, 736.*************, 525.*************, 746.*************], [72.**************, 748.*************, 525.*************, 758.*************], [72.**************, 760.*************, 426.************, 770.*************]]}]}, {"chunk": "# Applicability\n\n4. The provisions of these Directions shall apply to all authorised non-bank PSOs.\n5. To effectively identify, monitor, control and manage cyber and technology related risks arising out of linkages of PSOs with unregulated entities who are part of their digital payments ecosystem (like payment gateways, third party service providers, vendors, etc.), PSOs shall ensure adherence to these Directions by such unregulated entities as well, subject to mutual agreement. An organisational policy in this respect, approved by the Board, shall be put in place.\n\n## Purpose\n\n6. These Directions aim to improve safety and security of the payment systems operated by PSOs by providing a framework for overall information security preparedness with an emphasis on cyber resilience.\n\n## Section II <br> Governance Controls\n\n7. The Board of Directors (Board) of the PSO shall be responsible for ensuring adequate oversight over information security risks, including cyber risk and cyber resilience. However, primary oversight may be delegated to a sub-committee of the Board, headed by a member with experience in information / cyber security, which shall meet at least once every quarter.\n8. The PSO shall formulate a Board approved Information Security (IS) policy to manage potential information security risks covering all applications and products concerning payment systems as well as management of risks that have materialised. The policy shall be reviewed annually. It shall cover at the minimum, (i) roles and responsibilities of Board / sub-committees of the Board, senior management and other key personnel; (ii) measures to identify, assess, manage and monitor cyber security risk which shall also include various types of security controls for ensuring cyber resiliency along with processes for training and awareness of employees / stakeholders.\n\n## Cyber Security Preparedness\n\n9. The PSO shall prepare a distinct Board approved Cyber Crisis Management Plan (CCMP) to detect, contain, respond and recover from cyber threats and cyber attacks. Relevant guidelines from CERT-In / National Critical Information Infrastructure Protection Centre (NCIIPC) / IDRBT and other agencies may be referred for guidance.", "positions": [{"page_index": 4, "rectangles": [[108.*************, 109.*************1, 526.*************, 121.3783950805664], [72.**************, 128.*************, 526.3828125, 140.**************], [72.**************, 146.**************, 526.*************, 159.*************], [72.**************, 165.*************, 526.************, 178.**************], [72.**************, 185.*************, 127.75391387939453, 197.**************], [131.************, 185.*************, 526.************, 197.**************], [72.**************, 203.**************, 101.37312316894531, 216.*************], [93.**************, 412.6987609863281, 526.4637451171875, 425.00836181640625], [93.**************, 431.67877197265625, 526.************, 443.9883728027344], [93.**************, 450.6387634277344, 188.20449829101562, 462.9483642578125], [93.**************, 469.5987854003906, 526.4785766601562, 481.90838623046875], [93.**************, 488.55877685546875, 138.0849609375, 500.8683776855469], [140.4199981689453, 488.55877685546875, 197.94944763183594, 500.8683776855469], [200.2457733154297, 488.55877685546875, 241.10482788085938, 500.8683776855469], [243.52999877929688, 488.55877685546875, 269.27911376953125, 500.8683776855469], [271.6099853515625, 488.55877685546875, 316.2998962402344, 500.8683776855469], [318.6182861328125, 488.55877685546875, 332.49554443359375, 500.8683776855469], [334.8139343261719, 488.55877685546875, 396.0191345214844, 500.8683776855469], [398.3500061035156, 488.55877685546875, 419.77911376953125, 500.8683776855469], [422.1099853515625, 488.55877685546875, 467.4491271972656, 500.8683776855469], [469.7799987792969, 488.55877685546875, 526.492431640625, 500.8683776855469], [93.**************, 507.518798828125, 445.*************, 519.8284301757812], [448.4200134277344, 507.518798828125, 526.4837646484375, 519.8284301757812], [93.**************, 526.5987548828125, 199.19912719726562, 538.9083862304688], [202.12472534179688, 526.5987548828125, 526.4857788085938, 538.9083862304688], [93.**************, 545.5587768554688, 99.56640625, 557.868408203125], [103.20960998535156, 545.5587768554688, 183.9291229248047, 557.868408203125], [187.72999572753906, 545.5587768554688, 199.91912841796875, 557.868408203125], [203.57000732421875, 545.5587768554688, 221.8853759765625, 557.868408203125], [225.6279296875, 545.5587768554688, 260.9669494628906, 557.868408203125], [264.7315979003906, 545.5587768554688, 297.6749572753906, 557.868408203125], [301.4396057128906, 545.5587768554688, 368.6953125, 557.868408203125], [372.4599609375, 545.5587768554688, 393.7671813964844, 557.868408203125], [397.5318298339844, 545.5587768554688, 425.56243896484375, 557.868408203125], [429.30999755859375, 545.5587768554688, 449.5691223144531, 557.868408203125], [453.2200012207031, 545.5587768554688, 507.64910888671875, 557.868408203125], [511.4200134277344, 545.5587768554688, 526.4785766601562, 557.868408203125], [93.**************, 564.518798828125, 526.4885864257812, 576.8284301757812], [93.**************, 583.************, 131.27328491210938, 595.*************], [134.8944091796875, 583.************, 173.37986755371094, 595.*************], [177.00099182128906, 583.************, 206.34532165527344, 595.*************], [209.988525390625, 583.************, 222.35911560058594, 595.*************], [226.00999450683594, 583.************, 266.8580017089844, 595.*************], [270.5011901855469, 583.************, 312.07781982421875, 595.*************], [315.*************, 583.************, 331.6669921875, 595.*************], [335.1887512207031, 583.************, 380.5190124511719, 595.*************], [384.14013671875, 583.************, 414.08062744140625, 595.*************], [417.72381591796875, 583.************, 466.609130859375, 595.*************], [470.260009765625, 583.************, 500.3219299316406, 595.*************], [503.*************, 583.************, 526.*************, 595.*************], [93.**************, 602.*************, 419.*************, 614.*************], [480.**************, 678.*************, 526.*************, 690.************], [93.**************, 697.*************, 526.*********, 709.*************], [93.**************, 716.*************, 424.************, 728.*************]]}]}, {"chunk": "# Risk Assessment and Monitoring\n\n10. The Board shall entrust the responsibility and accountability for implementing the IS policy and the cyber resilience framework as well as for continuously assessing the overall IS posture of PSO to a senior level executive with expertise in areas of information security including cyber security. [e.g. Chief Information Security Officer (CISO)].\n11. The PSO shall define appropriate Key Risk Indicators (KRIs) to identify potential risk events and Key Performance Indicators (KPIs) to assess the effectiveness of security controls. These KRIs and KPIs shall be continuously monitored by the sub-committee of the Board referred to in paragraph 7.\n12. IT assessment reports (such as system audit, VA / PT reports, etc.) shall be placed before the sub-committee responsible for IT oversight in the meeting due immediately after such report is available.\n13. The PSO shall, undertake a cyber risk assessment exercise relating to launch of new product / services / technologies or undertaking major changes to infrastructure or processes of existing product / services. Action points emanating from such assessment shall be implemented under the oversight of the CISO or equivalent executive.\n\n## Section III\n\nBaseline Information Security Measures / Controls\n\n## 14. Inventory Management\n\n(a) The PSO shall maintain a record of all the key roles, information assets (applications, data, infrastructure, personnel, services, etc.), critical functions, processes and thirdparty service providers, and classify and document their levels of usage, criticality and business value.\n(b) A complete process flow diagram of network resources, inter-connections and dependencies, and data flows with other information assets, including any other thirdparty systems, shall be created and maintained.\n(c) Asset information shall necessarily include an identifier, network address, asset location, asset owner name and End of Life Support (EoLS). All assets (hardware or software) approaching EoLS shall be assessed to evaluate risks associated with the continued use of the unsupported asset.\n\n## 15. Identity and Access Management\n\n(a) Policies, procedures and controls that address access privileges as well as administration of access rights must be established.\n\n(b) All individuals having access to the IT environment of the PSO shall be assigned a digital identity, which shall be maintained and monitored till termination.\n(c) Default authentication settings in systems / software / services shall be deactivated and changed before they are rolled out to live environment.\n(d) Access to systems and different environments (development, test, production, etc.) shall be based on need-to-have, need-to-know and based on the principle of least privilege ${ }^{5}$.\n(e) The use of privileged accounts ${ }^{6}$ shall be with multi-factor authentication and tightly monitored. Appropriate controls, including rotation policy, shall be implemented.\n(f) Necessary security controls, including centralised mechanism to whitelist / blacklist, shall be put in place to ensure secure use of removable media and portable devices (eg. smartphones, laptops, etc.).\n(g) In case of remote / work from home situations, adequate precautions, including multifactor authentication mechanism, shall be in place.\n(h) The PSO shall define and implement procedures that limit, lock and terminate system and remote sessions after a pre-defined period of inactivity.\n(i) PSO shall have physical and environmental safeguards, with periodic testing, to protect access to its information assets from natural disasters and other threats.", "positions": [{"page_index": 5, "rectangles": [[213.**************, 146.**************, 235.**************, 159.*************], [238.**************, 146.**************, 443.9291076660156, 159.*************], [93.**************, 165.*************, 526.4728393554688, 178.**************], [93.**************, 185.*************, 526.2763671875, 197.**************], [93.**************, 203.**************, 134.96063232421875, 216.*************], [138.25999450683594, 203.**************, 526.4391479492188, 216.*************], [93.**************, 222.*************, 272.0180358886719, 235.**************], [93.**************, 241.*************, 526.4264526367188, 254.**************], [93.**************, 260.89874267578125, 526.4033813476562, 273.2083435058594], [93.**************, 279.*************, 182.60911560058594, 292.*************], [93.**************, 298.81878662109375, 526.392822265625, 311.1283874511719], [93.**************, 317.77874755859375, 133.17214965820312, 330.0883483886719], [136.0867156982422, 317.77874755859375, 142.2691192626953, 330.0883483886719], [145.07327270507812, 317.77874755859375, 188.47152709960938, 330.0883483886719], [191.3750457763672, 317.77874755859375, 197.5574493408203, 330.0883483886719], [200.36160278320312, 317.77874755859375, 265.232666015625, 330.0883483886719], [268.1361999511719, 317.77874755859375, 280.9205322265625, 330.0883483886719], [283.8350830078125, 317.77874755859375, 344.3453674316406, 330.0883483886719], [347.1495361328125, 317.77874755859375, 377.7634582519531, 330.0883483886719], [380.5896911621094, 317.77874755859375, 425.30169677734375, 330.0883483886719], [428.0837707519531, 317.77874755859375, 440.4044189453125, 330.0883483886719], [443.3079528808594, 317.77874755859375, 510.5415344238281, 330.0883483886719], [513.445068359375, 317.77874755859375, 526.1079711914062, 330.0883483886719], [93.**************, 336.73876953125, 288.90240478515625, 349.0483703613281], [292.3699951171875, 336.73876953125, 526.4866333007812, 349.0483703613281], [93.**************, 355.81878662109375, 473.8091125488281, 368.1283874511719], [271.7300109863281, 393.6187744140625, 326.77911376953125, 405.9283752441406], [165.86000061035156, 412.5787658691406, 432.4991149902344, 424.88836669921875], [114.**************, 431.55877685546875, 117.68912506103516, 443.8683776855469], [72.**************, 444.1587829589844, 87.33320617675781, 456.*************], [93.**************, 596.0787353515625, 109.28912353515625, 608.3883666992188], [114.**************, 596.0787353515625, 145.25648498535156, 608.3883666992188], [147.85089111328125, 596.0787353515625, 205.21473693847656, 608.3883666992188], [207.77601623535156, 596.0787353515625, 233.3998565673828, 608.3883666992188], [235.83969116210938, 596.0787353515625, 293.8548583984375, 608.3883666992188], [296.4161376953125, 596.0787353515625, 334.3053894042969, 608.3883666992188], [336.8446044921875, 596.0787353515625, 352.1571044921875, 608.3883666992188], [354.7183837890625, 596.0787353515625, 402.51055908203125, 608.3883666992188], [405.07183837890625, 596.0787353515625, 446.6485290527344, 608.3883666992188], [449.2098083496094, 596.0787353515625, 494.8023681640625, 608.3883666992188], [497.3636474609375, 596.0787353515625, 526.************, 608.3883666992188], [114.**************, 615.*************, 407.77911376953125, 627.***********], [410.95001220703125, 615.*************, 526.395263671875, 627.***********], [114.**************, 634.02880859375, 526.3429565429688, 646.3384399414062], [114.**************, 653.1087646484375, 310.4342956542969, 665.4183959960938], [72.**************, 684.5487670898438, 87.33320617675781, 696.8583984375], [93.**************, 684.5487670898438, 276.2391*********, 696.8583984375], [93.**************, 703.6287841796875, 109.88912200927734, 715.9384155273438], [114.**************, 703.6287841796875, 158.57025146484375, 715.9384155273438], [163.0856170654297, 703.6287841796875, 221.39193725585938, 715.9384155273438], [225.87417602539062, 703.6287841796875, 247.18138122558594, 715.9384155273438], [251.6636199951172, 703.6287841796875, 293.3616943359375, 715.9384155273438], [297.7225036621094, 703.6287841796875, 319.2615661621094, 715.9384155273438], [323.7438049316406, 703.6287841796875, 365.795166015625, 715.9384155273438], [370.27740478515625, 703.6287841796875, 407.66986083984375, 715.9384155273438], [412.152099609375, 703.6287841796875, 461.5229187011719, 715.9384155273438], [465.98309326171875, 703.6287841796875, 480.71044921875, 715.9384155273438], [485.1706237792969, 703.6287841796875, 507.073974609375, 715.9384155273438], [511.55621337890625, 703.6287841796875, 526.2835693359375, 715.9384155273438], [114.**************, 722.5887451171875, 366.4423828125, 734.8983764648438]]}, {"page_index": 6, "rectangles": [[93.**************, 71.**************, 109.88912200927734, 83.**************], [114.**************, 71.**************, 526.3961791992188, 83.**************], [114.**************, 90.**************, 460.7619323730469, 102.**************], [93.**************, 109.*************1, 109.28912353515625, 121.3783950805664], [114.**************, 109.*************1, 526.*************, 121.3783950805664], [114.**************, 128.*************, 402.1391296386719, 140.**************], [169.82000732421875, 224.*************, 501.78656005859375, 236.**************], [93.**************, 243.**************, 106.88912200927734, 255.*************], [114.**************, 243.**************, 526.1952514648438, 255.*************], [114.**************, 262.0987548828125, 526.2576904296875, 274.4083557128906], [114.**************, 281.05877685546875, 133.61984252929688, 293.3683776855469], [136.66688537597656, 281.05877685546875, 272.3816223144531, 293.3683776855469], [93.**************, 337.93878173828125, 109.88912200927734, 350.2483825683594], [114.**************, 337.93878173828125, 526.2352905273438, 350.2483825683594], [114.**************, 356.*************, 403.1581115722656, 369.*************], [93.**************, 375.*************, 106.16912078857422, 388.*************], [114.**************, 375.*************, 168.6608123779297, 388.*************], [170.89999389648438, 375.*************, 197.75912475585938, 388.*************], [199.97000122070312, 375.*************, 512.2091064453125, 388.*************], [514.4199829101562, 375.*************, 526.4866943359375, 388.*************], [114.**************, 394.81878662109375, 502.2012939453125, 407.1283874511719]]}]}, {"chunk": "# 16. Network Security\n\nThe PSO shall put in place the following measures to protect its network and systems from external threats:\n(a) Network devices shall be configured and checked periodically for security rules;\n(b) A Security Operations Centre (SOC) shall ensure proactive and centralised monitoring of comprehensive network and system logs collected and management of security incidents with effective tools for detection, escalation and quick response;\n(c) Automated mechanisms (eg. Security Information and Event Management (SIEM) system), which correlate all network and system alerts and any other anomalous activity across its business units to detect multi-faceted attacks, shall be established;\n(d) Anti-malware solutions shall be implemented so as to prevent, detect and contain malware attacks by scanning all incoming data to prevent malware from being installed and infecting a system;\n\n[^0]\n[^0]:    ${ }^{5}$ Principle of least privilege refers to granting a user or a process only those privileges or access to the specific data, resources and applications that are/is necessary for intended function or to complete a required task.\n${ }^{6}$ Privileged accounts refer to a user account that has more privileges than ordinary user e.g. Administrator level accounts. Such user may be able to install or remove software, upgrade the operating system, or modify system or application configurations. They might also have access to files that are not normally accessible to standard users.\n\n(e) Multi-layered boundary defences shall be incorporated into IS systems to efficiently monitor the network traffic and filter the flow of data in and out of the organisation. There shall be adequate measures to detect and remedy unusual activities / events;\n(f) Network segmentation shall be made based on role, location and environment (production, testing, development, etc.) to segregate systems and data of varying criticality; Whitelisting solutions shall be in place to ensure that only permitted applications and services with validated needs are running. Whitelisting of ports may also be ensured with constant monitoring; and\n(g) PSOs shall allow devices (such as laptop, desktop, mobile, etc.) to be connected to its network only after ensuring that they meet the prescribed security measures / requirements.", "positions": [{"page_index": 6, "rectangles": [[72.**************, 426.3787841796875, 87.33320617675781, 438.6883850097656], [93.**************, 426.3787841796875, 186.32911682128906, 438.6883850097656], [93.**************, 445.************, 526.2667846679688, 457.7883605957031], [93.**************, 464.*************5, 203.5191192626953, 476.7483825683594], [93.**************, 483.3699951171875, 111.35599517822266, 496.7499694824219], [114.**************, 484.***********, 504.28912353515625, 496.5483703613281], [93.**************, 503.2900390625, 111.35599517822266, 516.6700439453125], [114.**************, 504.1588134765625, 125.00910186767578, 516.4684448242188], [129.**************, 504.1588134765625, 172.63856506347656, 516.4684448242188], [177.59552001953125, 504.1588134765625, 234.4073944091797, 516.4684448242188], [239.36434936523438, 504.1588134765625, 275.299560546875, 516.4684448242188], [280.25653076171875, 504.1588134765625, 314.53912353515625, 516.4684448242188], [319.510009765625, 504.1588134765625, 345.0191345214844, 516.4684448242188], [349.8699951171875, 504.1588134765625, 386.5448913574219, 516.4684448242188], [391.5018615722656, 504.1588134765625, 439.0953063964844, 516.4684448242188], [444.0412292480469, 504.1588134765625, 465.34844970703125, 516.4684448242188], [470.1839599609375, 504.1588134765625, 526.3995971679688, 516.4684448242188], [114.**************, 523.***********, 526.440185546875, 535.*************], [114.**************, 542.19873046875, 518.9290771484375, 554.5083618164062], [93.**************, 561.1300048828125, 110.75599670410156, 574.510009765625], [114.**************, 561.998779296875, 257.3898010253906, 574.3084106445312], [445.51251220703125, 724.***********, 525.5510864257812, 734.***********], [72.**************, 736.*************, 110.73854064941406, 746.*************], [113.54000091552734, 736.*************, 524.6898803710938, 746.*************], [72.**************, 748.*************, 230.04934692382812, 758.*************], [232.5393524169922, 748.*************, 524.6063232421875, 758.*************], [72.**************, 760.*************, 149.3219757080078, 770.*************]]}, {"page_index": 7, "rectangles": [[93.**************, 71.09996032714844, 111.35599517822266, 84.4799575805664], [114.**************, 71.96876525878906, 526.4286499023438, 84.27835845947266], [114.**************, 91.*************1, 523.*************, 103.3783950805664], [114.**************, 110.**************, 528.6491088867188, 122.*************], [93.**************, 128.9600067138672, 107.99600219726562, 142.3400115966797], [114.**************, 129.8288116455078, 158.1065673828125, 142.13841247558594], [161.02113342285156, 129.8288116455078, 230.09844970703125, 142.13841247558594], [232.97000122070312, 129.8288116455078, 258.5938415527344, 142.13841247558594], [261.5083923339844, 129.8288116455078, 276.8208923339844, 142.13841247558594], [279.7354431152344, 129.8288116455078, 310.4156188964844, 142.13841247558594], [313.3900146484375, 129.8288116455078, 346.34442138671875, 142.13841247558594], [349.25897216796875, 129.8288116455078, 364.4610595703125, 142.13841247558594], [367.3645935058594, 129.8288116455078, 391.8733825683594, 142.13841247558594], [394.8210754394531, 129.8288116455078, 435.7391*********, 142.13841247558594], [438.70001220703125, 129.8288116455078, 460.1291198730469, 142.13841247558594], [463.05999755859375, 129.8288116455078, 526.4736938476562, 142.13841247558594], [114.**************, 148.*************, 526.080810546875, 161.**************], [114.**************, 167.86878967285156, 162.9291229248047, 180.1783905029297], [165.74000549316406, 167.86878967285156, 225.27874755859375, 180.1783905029297], [228.3147430419922, 167.86878967285156, 274.7991027832031, 180.1783905029297], [277.8351135253906, 167.86878967285156, 303.45892333984375, 180.1783905029297], [306.49493408203125, 167.86878967285156, 321.80743408203125, 180.1783905029297], [324.84344482421875, 167.86878967285156, 336.44647216796875, 180.1783905029297], [339.471435546875, 167.86878967285156, 368.89910888671875, 180.1783905029297], [371.95001220703125, 167.86878967285156, 384.2706604003906, 180.1783905029297], [387.2956237792969, 167.86878967285156, 423.97052001953125, 180.1783905029297], [426.8740539550781, 167.86878967285156, 448.3027038574219, 180.1783905029297], [451.3387145996094, 167.86878967285156, 474.5668640136719, 180.1783905029297], [477.6028747558594, 167.86878967285156, 526.3555297851562, 180.1783905029297], [114.**************, 186.8288116455078, 401.21844482421875, 199.13841247558594], [403.7907409667969, 186.8288116455078, 526.3236083984375, 199.13841247558594], [114.**************, 205.7887725830078, 342.1391296386719, 218.09837341308594], [93.**************, 224.71995544433594, 111.35599517822266, 238.09996032714844], [114.**************, 225.58876037597656, 526.38330078125, 237.8983612060547], [114.**************, 244.6687774658203, 526.*************, 256.9783630371094], [114.**************, 263.65875244140625, 181.88912963867188, 275.9683532714844]]}]}, {"chunk": "# 17. Application Security Life Cycle (ASLC)\n\n(a) The PSO shall follow a 'secure by design' approach such as Secure-Software Development Life Cycle (S-SDLC) for design and development of products / services and ensure that no security weaknesses are introduced during the build process.\n(b) The PSO shall implement a multi-tier application architecture, that ensures segregation of database layer from other layers, while developing digital payment products and services.\n(c) The ASLC guidelines shall apply to procured products / services as well. Further, the PSO shall obtain the source code of all critical applications procured from third-party vendors. In case obtaining source code is not possible, there shall be an escrow arrangement for the source code to ensure continuity of services.\n\n## 18. Security Testing\n\n(a) The PSO shall ensure that all its applications are subjected to rigorous security testing, such as source code review, VA, PT, etc., through qualified professionals at adequate frequency (at least on annual basis) in authenticated mode.\n(b) If the source code is not owned by the PSO, it shall obtain a certificate from the application developer stating that the application is free of vulnerabilities, malwares and any covert channels in the code. Fresh certificate shall be obtained for any changes to source code.\n(c) Deficiencies reported in the security testing shall be resolved in a time bound manner. Any recurring observation shall necessarily be reported to the Board sub-committee responsible for IT oversight along with detailed analysis for recurrence and resolution.\n(d) Any deployment or redeployment of new or existing services supporting critical functions, applications and infrastructure components must be done only after\n\nconducting security audits, including VA / PT, and addressing the resultant observations.", "positions": [{"page_index": 7, "rectangles": [[72.**************, 295.21875, 87.33320617675781, 307.5283508300781], [93.**************, 371.17877197265625, 109.88912200927734, 383.4883728027344], [114.**************, 371.17877197265625, 136.7662353515625, 383.4883728027344], [141.**************, 371.17877197265625, 168.1999969482422, 383.4883728027344], [173.39984130859375, 371.17877197265625, 199.023681640625, 383.4883728027344], [204.22352600097656, 371.17877197265625, 258.1539611816406, 383.4883728027344], [263.3537902832031, 371.17877197265625, 272.4176330566406, 383.4883728027344], [277.6174621582031, 371.17877197265625, 323.00262451171875, 383.4883728027344], [328.2699890136719, 371.17877197265625, 383.8122253417969, 383.4883728027344], [388.************, 371.17877197265625, 452.68914794921875, 383.4883728027344], [457.7799987792969, 371.17877197265625, 479.1976013183594, 383.4883728027344], [484.3974304199219, 371.17877197265625, 526.4708251953125, 383.4883728027344], [114.**************, 390.1387634277344, 526.3836669921875, 402.4483642578125], [114.**************, 409.2187805175781, 224.73300170898438, 421.52838134765625], [93.**************, 428.17877197265625, 109.28912353515625, 440.4883728027344], [114.**************, 428.17877197265625, 463.84912109375, 440.4883728027344], [466.8961486816406, 428.17877197265625, 526.4349365234375, 440.4883728027344], [114.**************, 447.1587829589844, 526.48046875, 459.*************], [114.**************, 466.1187744140625, 156.8091278076172, 478.4283752441406], [161.40176391601562, 466.1187744140625, 526.3919677734375, 478.4283752441406], [114.**************, 485.0787658691406, 431.3448181152344, 497.38836669921875], [72.**************, 525.6387939453125, 87.33320617675781, 537.9484252929688], [93.**************, 525.6387939453125, 182.00912475585938, 537.9484252929688], [93.**************, 544.71875, 109.88912200927734, 557.0283813476562], [114.**************, 544.71875, 136.64480590820312, 557.0283813476562], [138.86000061035156, 544.71875, 255.17747497558594, 557.0283813476562], [257.45001220703125, 544.71875, 526.3198852539062, 557.0283813476562], [114.**************, 563.6787719726562, 526.4880981445312, 575.9884033203125], [114.**************, 582.6387939453125, 452.4491271972656, 594.9484252929688], [93.**************, 601.5987548828125, 109.88912200927734, 613.9083862304688], [114.**************, 601.5987548828125, 526.3338623046875, 613.9083862304688], [114.**************, 620.5887451171875, 526.2462768554688, 632.8983764648438], [114.**************, 639.5487670898438, 305.556884765625, 651.8583984375], [310.7300109863281, 639.5487670898438, 526.3191528320312, 651.8583984375], [114.**************, 658.5087890625, 234.54757690429688, 670.8184204101562], [93.**************, 677.46875, 109.28912353515625, 689.7783813476562], [114.**************, 677.46875, 523.3314819335938, 689.7783813476562], [114.**************, 696.5487670898438, 526.4564819335938, 708.8583984375], [114.**************, 715.5087890625, 523.36572265625, 727.8184204101562]]}]}, {"chunk": "# 19. Vendor Risk Management\n\nThe PSO shall be guided by the Framework for Outsourcing of Payment and Settlementrelated Activities by PSOs issued by RBI vide circular dated August 03, 2021 (updated from time to time).\n(a) The PSO shall put in place necessary security controls for preventing infiltration into its network from vendor environments.\n(b) The PSO shall adhere to the relevant legal and regulatory requirements relating to geographical location of infrastructure and processing, storage and usage of data and also ensure compliance by their vendors.\n(c) In case of vendors involved in critical processes and activities ${ }^{7}$, the PSO shall obtain certified assurance from an independent auditor on the vendor's cyber resilience capabilities.\n\n## 20. Data Security\n\n(a) The PSO shall put in place a comprehensive data leak prevention policy for confidentiality, integrity, availability and protection of business and customer information (both in transit and at rest) in respect of data available with it or at vendor managed facilities, commensurate with the criticality and sensitivity of the information held / transmitted.\n(b) The PSO shall employ suitable mechanism to ensure traceability and visibility of data assets.\n(c) The PSO shall develop and implement an Information Security Management System (ISMS) based on applicable standards.\n(d) Application and database security controls shall focus on secure handling, processing, storage and protection of data, in particular, Personally Identifiable Information (PII). Data in transit and rest shall be secured through either data or channel encryption or both.\n(e) The PSO storing card (debit / credit / prepaid) data shall adhere to PCI-DSS guidelines and obtain PCI-DSS certification.\n(f) The PSO shall have necessary systems and procedures in place to periodically (at least on a half-yearly basis) test the backed-up data to ensure recovery without loss of transactions or audit-trails.\n\n[^0]\n[^0]:    ${ }^{7}$ Critical processes as defined in the Framework for Outsourcing of Payment and Settlement-related Activities by PSOs issued by RBI vide circular dated August 03, 2021 (updated from time to time).", "positions": [{"page_index": 8, "rectangles": [[72.**************, 121.54878234863281, 87.33320617675781, 133.85838317871094], [93.**************, 197.62879943847656, 109.88912200927734, 209.9384002685547], [114.**************, 197.62879943847656, 526.1220092773438, 209.9384002685547], [114.**************, 216.58876037597656, 301.08563232421875, 228.8983612060547], [93.**************, 235.5487823486328, 109.88912200927734, 247.85838317871094], [114.**************, 235.5487823486328, 526.2279052734375, 247.85838317871094], [114.**************, 254.5387725830078, 526.169189453125, 266.8483581542969], [114.**************, 273.498779296875, 336.038330078125, 285.8083801269531], [72.**************, 361.93878173828125, 87.33320617675781, 374.2483825683594], [93.**************, 361.93878173828125, 172.88912963867188, 374.2483825683594], [93.**************, 381.0187683105469, 109.88912200927734, 393.328369140625], [114.**************, 381.0187683105469, 136.64480590820312, 393.328369140625], [140.3000030517578, 381.0187683105469, 166.5310516357422, 393.328369140625], [170.0528106689453, 381.0187683105469, 195.67665100097656, 393.328369140625], [199.31985473632812, 381.0187683105469, 217.5137939453125, 393.328369140625], [221.15699768066406, 381.0187683105469, 232.76004028320312, 393.328369140625], [236.2597198486328, 381.0187683105469, 265.6151123046875, 393.328369140625], [269.25830078125, 381.0187683105469, 278.3221435546875, 393.328369140625], [281.96533203125, 381.0187683105469, 359.554443359375, 393.328369140625], [363.1755676269531, 381.0187683105469, 387.474609375, 393.328369140625], [391.1177978515625, 381.0187683105469, 414.345947265625, 393.328369140625], [417.9891*********, 381.0187683105469, 472.2728271484375, 393.328369140625], [475.7945861816406, 381.0187683105469, 506.81695556640625, 393.328369140625], [510.70001220703125, 381.0187683105469, 526.*************, 393.328369140625], [114.**************, 399.978759765625, 187.37359619140625, 412.2883605957031], [192.33055114746094, 399.978759765625, 236.87696838378906, 412.2883605957031], [241.83392333984375, 399.978759765625, 295.3888854980469, 412.2883605957031], [300.3458557128906, 399.978759765625, 321.7966003417969, 412.2883605957031], [326.7425231933594, 399.978759765625, 378.144775390625, 412.2883605957031], [383.10174560546875, 399.978759765625, 395.27886962890625, 412.2883605957031], [400.23583984375, 399.978759765625, 446.7473449707031, 412.2883605957031], [451.7043151855469, 399.978759765625, 473.01153564453125, 412.2883605957031], [477.968505859375, 399.978759765625, 526.1360473632812, 412.2883605957031], [114.**************, 419.05877685546875, 526.41064453125, 431.3683776855469], [114.**************, 438.03875732421875, 526.4766235351562, 450.3483581542969], [114.**************, 456.998779296875, 202.1341094970703, 469.3083801269531], [93.**************, 475.9587707519531, 109.88912200927734, 488.26837158203125], [114.**************, 475.9587707519531, 526.2685546875, 488.26837158203125], [114.**************, 494.91876220703125, 149.48912048339844, 507.2283630371094], [93.**************, 513.8787841796875, 109.28912353515625, 526.1884155273438], [114.**************, 513.8787841796875, 526.3031005859375, 526.1884155273438], [114.**************, 532.8387451171875, 304.3033142089844, 545.1483764648438], [93.**************, 551.7987670898438, 109.88912200927734, 564.1083984375], [114.**************, 551.7987670898438, 171.4649658203125, 564.1083984375], [176.88560485839844, 551.7987670898438, 198.33633422851562, 564.1083984375], [203.75697326660156, 551.7987670898438, 252.07907104492188, 564.1083984375], [257.4997253417969, 551.7987670898438, 298.3476867675781, 564.1083984375], [303.7904052734375, 551.7987670898438, 345.3780517578125, 564.1083984375], [350.8207702636719, 551.7987670898438, 376.3231506347656, 564.1083984375], [381.765869140625, 551.7987670898438, 411.23162841796875, 564.1083984375], [416.65228271484375, 551.7987670898438, 431.84332275390625, 564.1083984375], [437.2860412597656, 551.7987670898438, 473.36474609375, 564.1083984375], [478.6639404296875, 551.7987670898438, 526.25732421875, 564.1083984375], [114.**************, 570.7587890625, 173.9691162109375, 583.0684204101562], [177.02000427246094, 570.7587890625, 216.66465759277344, 583.0684204101562], [219.70065307617188, 570.7587890625, 241.0299530029297, 583.0684204101562], [244.05491638183594, 570.7587890625, 295.34674072265625, 583.0684204101562], [298.38275146484375, 570.7587890625, 310.55987548828125, 583.0684204101562], [313.47442626953125, 570.7587890625, 341.*************, 583.0684204101562], [344.1099853515625, 570.7587890625, 355.7130126953125, 583.0684204101562], [358.73797607421875, 570.7587890625, 410.*************, 583.0684204101562], [413.1099853515625, 570.7587890625, 467.2832336425781, 583.0684204101562], [470.3192443847656, 570.7587890625, 526.4024047851562, 583.0684204101562], [114.**************, 589.8387451171875, 198.22593688964844, 602.1483764648438], [202.95106506347656, 589.8387451171875, 526.3890380859375, 602.1483764648438], [114.**************, 608.8287353515625, 247.43911743164062, 621.1383666992188], [93.**************, 627.7887573242188, 109.88912200927734, 640.098388671875], [114.**************, 627.7887573242188, 136.7662353515625, 640.098388671875], [139.82000732421875, 627.7887573242188, 166.04000854492188, 640.098388671875], [169.0760040283203, 627.7887573242188, 205.12161254882812, 640.098388671875], [208.15760803222656, 627.7887573242188, 232.58914184570312, 640.098388671875], [235.49266052246094, 627.7887573242188, 266.1507263183594, 640.098388671875], [269.1867370605469, 627.7887573242188, 275.25872802734375, 640.098388671875], [278.29473876953125, 627.7887573242188, 308.2352294921875, 640.098388671875], [311.271240234375, 627.7887573242188, 317.3432312011719, 640.098388671875], [320.3792419433594, 627.7887573242188, 363.75543212890625, 640.098388671875], [366.79144287109375, 627.7887573242188, 391.2119445800781, 640.098388671875], [394.2479553222656, 627.7887573242188, 419.87176513671875, 640.098388671875], [422.90777587890625, 627.7887573242188, 460.31134033203125, 640.098388671875], [463.2148742675781, 627.7887573242188, 475.5355224609375, 640.098388671875], [478.4390563964844, 627.7887573242188, 526.4746704101562, 640.098388671875], [114.**************, 646.748779296875, 328.09912109375, 659.0584106445312], [93.**************, 665.708740234375, 106.88912200927734, 678.0183715820312], [114.**************, 665.708740234375, 526.2022094726562, 678.0183715820312], [114.**************, 684.6687622070312, 526.3372802734375, 696.9783935546875], [114.**************, 703.6287841796875, 255.8300018310547, 715.9384155273438]]}]}, {"chunk": "# 21. Patch and Change Management Life Cycle\n\n(a) The PSO shall put in place a documented policy and process to identify and implement patches to technology and software assets released by OEMs / others.\n(b) Security patches shall be applied to the relevant systems and applications within an appropriate time frame from their release. In case of critical patches released to tackle well-known / reported attacks, the PSO shall have a mechanism to apply them immediately.\n(c) Any change to system, technology, application, source code, etc., shall be managed using robust change management processes and after ensuring that overall integrity of the IT set-up is not compromised.\n(d) Patches and changes shall be implemented in production environment after testing and validating the same in other environments (e.g. development, testing, etc.).\n\n## 22. Incident Response\n\n(a) The PSO shall put in place a Board approved incident response mechanism, which shall include provisions to promptly notify its senior management, relevant employees and regulatory, supervisory and relevant public authorities, of cyber incidents.\n(b) Response strategies shall incorporate readiness to meet various incident scenarios based on situational awareness and potential impact, consistent communication and co-ordination with stakeholders.\n(c) Post-incident analysis, including forensic analysis (wherever necessary), shall be conducted to determine the impact and root cause of incidents. Adequate measures shall be taken to avoid recurrence of similar incidents.\n(d) Unusual incidents like cyber-attacks, outage of critical system / infrastructure, internal fraud, settlement delay, etc., shall be reported to RBI in the Incident Reporting Format (Annex 1) within 6 hours of detection. Indicative list of types of incidents to be reported is in Annex 2. Any cyber security incident shall also be reported to CERT-In.\n\n## 23. Business Continuity Plan (BCP)\n\n(a) The PSO shall develop a BCP based on different cyber threat scenarios, including extreme but plausible events to which it may be exposed. It shall be reviewed at least once a year and include a comprehensive cyber incident response, resumption and recovery plan, to manage cyber security events or incidents.\n(b) The BCP shall be designed to enable rapid recovery from any adverse event and facilitate safe resumption of critical operations aligned with Recovery Time Objective (RTO) and Recovery Point Objective (RPO) while ensuring the security of processes and data.\n\n(c) The PSO shall strive to achieve near-zero RPO.\n(d) The PSO shall set up a Disaster Recovery (DR) facility in a different seismic zone than the Primary Data Centre (PDC). There shall be a defined methodology for reconciliation of data so as to ensure that there is no data loss while resuming operations from the DR.\n(e) DR drills shall be conducted on a half-yearly or more frequent basis. Any divergence from the RTO and RPO shall be analysed and the deficiency be rectified on urgent basis.", "positions": [{"page_index": 9, "rectangles": [[72.**************, 71.00880432128906, 87.33320617675781, 83.31839752197266], [93.**************, 71.00880432128906, 317.89910888671875, 83.31839752197266], [93.**************, 90.**************, 109.88912200927734, 102.**************], [114.**************, 90.**************, 136.64480590820312, 102.**************], [139.82000732421875, 90.**************, 166.05105590820312, 102.**************], [169.20849609375, 90.**************, 194.83233642578125, 102.**************], [197.8683319091797, 90.**************, 216.18370056152344, 102.**************], [219.3411407470703, 90.**************, 230.94418334960938, 102.**************], [233.96914672851562, 90.**************, 263.32452392578125, 102.**************], [266.4819641113281, 90.**************, 275.5458068847656, 102.**************], [278.7032470703125, 90.**************, 342.24951171875, 102.**************], [345.4069519042969, 90.**************, 376.307861328125, 102.**************], [379.4653015136719, 90.**************, 400.9160461425781, 102.**************], [404.06243896484375, 90.**************, 445.6501159667969, 102.**************], [448.6861267089844, 90.**************, 460.8632507324219, 102.**************], [464.02069091796875, 90.**************, 501.8658142089844, 102.**************], [505.02325439453125, 90.**************, 526.2090454101562, 102.**************], [114.**************, 109.*************1, 512.9290771484375, 121.3783950805664], [93.**************, 128.*************, 109.88912200927734, 140.**************], [114.**************, 128.*************, 526.1911010742188, 140.**************], [114.**************, 146.**************, 313.47247314453125, 159.*************], [315.77984619140625, 146.**************, 526.2243041992188, 159.*************], [114.**************, 165.*************, 172.06640625, 178.**************], [174.48416137695312, 165.*************, 180.55616760253906, 178.**************], [182.99600219726562, 165.*************, 226.95730590820312, 178.**************], [229.3971405029297, 165.*************, 270.2451171875, 178.**************], [272.6849670410156, 165.*************, 290.9892883300781, 178.**************], [293.42913818359375, 165.*************, 319.6491394042969, 178.**************], [321.967529296875, 165.*************, 347.5913391113281, 178.**************], [350.03118896484375, 165.*************, 376.74798583984375, 178.**************], [379.1657409667969, 165.*************, 388.37310791015625, 178.**************], [390.7908630371094, 165.*************, 450.0093994140625, 178.**************], [452.4492492675781, 165.*************, 464.923828125, 178.**************], [467.3636779785156, 165.*************, 496.4651184082031, 178.**************], [498.90496826171875, 165.*************, 526.4497680664062, 178.**************], [114.**************, 185.*************, 176.36912536621094, 197.**************], [93.**************, 203.**************, 109.28912353515625, 216.*************], [114.**************, 203.**************, 526.2684936523438, 216.*************], [114.**************, 222.*************, 526.3333129882812, 235.**************], [114.**************, 241.*************, 290.0863952636719, 254.**************], [93.**************, 260.89874267578125, 109.88912200927734, 273.2083435058594], [114.**************, 260.89874267578125, 526.*************, 273.2083435058594], [114.**************, 279.*************, 364.82586669921875, 292.*************], [367.9722595214844, 279.*************, 500.089111328125, 292.*************], [72.**************, 311.41876220703125, 87.33320617675781, 323.7283630371094], [93.**************, 311.41876220703125, 197.3991241455078, 323.7283630371094], [93.**************, 330.498779296875, 109.88912200927734, 342.8083801269531], [114.**************, 330.498779296875, 526.2186279296875, 342.8083801269531], [114.**************, 349.*************, 526.3142700195312, 361.76837158203125], [114.**************, 368.41876220703125, 491.31781005859375, 380.7283630371094], [93.**************, 387.3787841796875, 109.88912200927734, 399.6883850097656], [114.**************, 387.3787841796875, 526.3694458007812, 399.6883850097656], [114.**************, 406.3387756347656, 526.3934326171875, 418.64837646484375], [114.**************, 425.29876708984375, 269.3269348144531, 437.6083679199219], [93.**************, 444.2787780761719, 109.28912353515625, 456.58837890625], [114.**************, 444.2787780761719, 526.2680053710938, 456.58837890625], [114.**************, 463.***********, 421.5872802734375, 475.5483703613281], [424.7447204589844, 463.***********, 526.1802368164062, 475.5483703613281], [114.**************, 482.31878662109375, 376.2276306152344, 494.6283874511719], [93.**************, 501.2787780761719, 109.88912200927734, 513.58837890625], [114.**************, 501.2787780761719, 526.249755859375, 513.58837890625], [114.**************, 520.***********, 526.2465209960938, 532.*************], [114.**************, 539.19873046875, 292.0959167480469, 551.5083618164062], [294.4100036621094, 539.19873046875, 526.3603515625, 551.5083618164062], [114.**************, 558.1588134765625, 180.68911743164062, 570.4684448242188], [183.8465576171875, 558.1588134765625, 484.0302429199219, 570.4684448242188], [72.**************, 595.958740234375, 87.33320617675781, 608.2683715820312], [93.**************, 595.958740234375, 262.6791076660156, 608.2683715820312], [93.**************, 615.*************, 109.88912200927734, 627.***********], [114.**************, 615.*************, 526.4244384765625, 627.***********], [114.**************, 634.02880859375, 390.0191345214844, 646.3384399414062], [392.8299865722656, 634.02880859375, 526.4465942382812, 646.3384399414062], [114.**************, 653.1087646484375, 526.3340454101562, 665.4183959960938], [114.**************, 672.*************, 407.3455810546875, 684.***********], [93.**************, 691.02880859375, 109.88912200927734, 703.3384399414062], [114.**************, 691.02880859375, 526.1879272460938, 703.3384399414062], [114.**************, 709.98876953125, 526.246337890625, 722.*************], [114.**************, 728.9487915039062, 526.2353515625, 741.2584228515625], [114.**************, 747.90478515625, 160.5133056640625, 760.2144165039062]]}, {"page_index": 10, "rectangles": [[93.**************, 71.**************, 109.28912353515625, 83.**************], [114.**************, 71.**************, 348.2218322753906, 83.**************], [93.**************, 90.**************, 109.88912200927734, 102.**************], [114.**************, 90.**************, 526.*************, 102.**************], [114.**************, 109.*************1, 159.67425537109375, 121.3783950805664], [161.89999389648438, 109.*************1, 305.13299560546875, 121.3783950805664], [310.3699951171875, 109.*************1, 440.2114562988281, 121.3783950805664], [442.4200134277344, 109.*************1, 526.42333984375, 121.3783950805664], [114.**************, 128.*************, 363.550048828125, 140.**************], [365.95001220703125, 128.*************, 421.0837707519531, 140.**************], [423.30999755859375, 128.*************, 526.*************, 140.**************], [114.**************, 146.**************, 231.39010620117188, 159.*************], [93.**************, 165.*************, 109.88912200927734, 178.**************], [114.**************, 165.*************, 444.856689453125, 178.**************], [447.9037170410156, 165.*************, 526.4813842773438, 178.**************], [114.**************, 185.*************, 526.1470947265625, 197.**************], [114.**************, 203.**************, 143.3681640625, 216.*************]]}]}, {"chunk": "# 24. Application Programming Interfaces (APIs)\n\n(a) To safeguard applications against risks emanating from insecure APIs, the PSO shall put in place, inter-alia, the following measures:\n(i) Authentication and Authorisation - Establish identity of the communicating applications;\n(ii) Confidentiality - Ensure that the message content is not tampered with;\n(iii) Integrity - Resources are reliably transferred; and\n(iv) Availability and Threat Protection - APIs are available when needed; anomalous ${ }^{8}$ activities identified and mitigative action taken.\n(b) The PSO shall adhere to relevant standards and globally recognised frameworks on API security.\n\n## 25. Employee Awareness / Training\n\n(a) The PSO shall ensure periodic repeated training / awareness programs on information security issues for its employees and vendors managing its information assets.\n(b) A system of periodic evaluation of cyber security awareness amongst employees shall be operationalised. Employees with an awareness level below a benchmark score may be restricted / prohibited from accessing information assets.\n(c) Board members and key senior management personnel shall be provided training and sensitised on information security and cyber risks.\n\n## 26. Cloud Security\n\n(a) The PSO subscribing to cloud services shall put in place a cloud operation policy (as a part of board approved Information Security Policy), highlighting, inter-alia, activities that can be located in cloud servers, clearly identified roles and responsibilities for cloud service provider (CSP), data localisation, protection and recoverability requirements.\n\n[^0]\n[^0]:    ${ }^{8}$ Activity that is sufficiently different than historical activity.\n\n(b) It shall be ensured that data pertaining to the PSO is clearly segregated and stringent access control measures are implemented. Multi-tenancy environments shall be protected against data integrity and confidentiality risks, and against co-mingling of data.\n(c) The PSO shall ensure that the CSP is subjected to periodic (at least annually) independent information and cyber security audits. Such audit reports and the CSPs overall performance, its cyber resilience capabilities shall be reviewed annually by the Board sub-committee responsible for IT oversight.", "positions": [{"page_index": 10, "rectangles": [[72.**************, 235.4287872314453, 87.33320617675781, 247.73838806152344], [93.**************, 406.3387756347656, 109.88912200927734, 418.64837646484375], [114.**************, 406.3387756347656, 526.3410034179688, 418.64837646484375], [114.**************, 425.29876708984375, 176.33363342285156, 437.6083679199219], [72.**************, 456.8787841796875, 87.33320617675781, 469.1883850097656], [93.**************, 456.8787841796875, 265.1991271972656, 469.1883850097656], [90.**************, 475.9587707519531, 106.52912139892578, 488.26837158203125], [110.**************, 475.9587707519531, 526.3789672851562, 488.26837158203125], [110.**************, 494.91876220703125, 493.0346984863281, 507.2283630371094], [93.**************, 513.8787841796875, 109.88912200927734, 526.1884155273438], [114.**************, 513.8787841796875, 526.2772827148438, 526.1884155273438], [114.**************, 532.8387451171875, 236.10415649414062, 545.1483764648438], [240.69679260253906, 532.8387451171875, 526.45751953125, 545.1483764648438], [114.**************, 551.7987670898438, 458.83612060546875, 564.1083984375], [93.**************, 570.7587890625, 109.28912353515625, 583.0684204101562], [114.**************, 570.7587890625, 526.2022094726562, 583.0684204101562], [114.**************, 589.8387451171875, 378.8707580566406, 602.1483764648438], [72.**************, 621.*************, 87.33320617675781, 633.************], [93.**************, 621.*************, 174.**************, 633.************], [90.**************, 640.*************, 106.52912139892578, 652.*************], [110.**************, 640.*************, 526.4732666015625, 652.*************], [110.**************, 659.*************, 526.474609375, 671.*************], [110.**************, 678.*************, 526.466552734375, 690.************], [110.**************, 697.*************, 139.66639709472656, 709.*************], [145.20848083496094, 697.*************, 183.10304260253906, 709.*************], [188.64512634277344, 697.*************, 231.42514038085938, 709.*************], [236.9893035888672, 697.*************, 273.034912109375, 709.*************], [278.59906005859375, 697.*************, 303.1630859375, 709.*************], [308.7051696777344, 697.*************, 369.1159973144531, 709.*************], [374.6801452636719, 697.*************, 425.97198486328125, 709.*************], [431.5361328125, 697.*************, 452.98687744140625, 709.*************], [458.5289611816406, 697.*************, 526.2261962890625, 709.*************], [110.**************, 716.*************, 177.5225830078125, 728.*************]]}, {"page_index": 11, "rectangles": [[90.**************, 71.**************, 106.52912139892578, 83.**************], [110.**************, 71.**************, 526.4660034179688, 83.**************], [110.**************, 90.**************, 147.69247436523438, 102.**************], [150.7284698486328, 90.**************, 186.79617309570312, 102.**************], [189.83216857910156, 90.**************, 241.**************, 102.**************], [244.**************, 90.**************, 263.4015197753906, 102.**************], [266.5589599609375, 90.**************, 332.53912353515625, 102.**************], [338.8098449707031, 90.**************, 407.1777038574219, 102.**************], [410.33514404296875, 90.**************, 479.2689208984375, 102.**************], [482.4263610839844, 90.**************, 508.0501708984375, 102.**************], [511.2076110839844, 90.**************, 526.3986206054688, 102.**************], [110.**************, 109.*************1, 526.4223022460938, 121.3783950805664], [110.**************, 128.*************, 134.86399841308594, 140.**************], [90.**************, 146.**************, 105.92912292480469, 159.*************], [110.**************, 146.**************, 132.32479858398438, 159.*************], [135.1289520263672, 146.**************, 161.36000061035156, 159.*************], [164.042724609375, 146.**************, 189.66656494140625, 159.*************], [192.47071838378906, 146.**************, 229.14561462402344, 159.*************], [231.80625915527344, 146.**************, 253.2349090576172, 159.*************], [255.91763305664062, 146.**************, 274.2219543457031, 159.*************], [277.026123046875, 146.**************, 302.64996337890625, 159.*************], [305.4541320800781, 146.**************, 316.3174743652344, 159.*************], [319.12164306640625, 146.**************, 369.2211608886719, 159.*************], [371.90386962890625, 146.**************, 384.08099365234375, 159.*************], [386.8851623535156, 146.**************, 428.4617919921875, 159.*************], [431.1445007324219, 146.**************, 447.0421142578125, 159.*************], [449.8462829589844, 146.**************, 475.95587158203125, 159.*************], [478.7600402832031, 146.**************, 526.*************, 159.*************], [110.**************, 165.*************, 357.**************, 178.**************], [361.*************, 165.*************, 526.*************, 178.**************], [110.**************, 185.*************, 526.*************, 197.**************], [110.**************, 203.**************, 353.*************, 216.*************]]}]}, {"chunk": "# 27. Other Security Measures\n\n(a) The PSO shall ensure that all payment transactions, including cash withdrawals, involving debit to the account conducted through electronic modes (bank accounts / debit cards / credit cards / PPIs, etc.) are permitted only by validation through multifactor authentication, except where explicitly permitted / relaxed.\n(b) The PSO shall ensure secure configuration of its entire IT infrastructure. It shall also equip its servers with adequate security measures so that unauthorised / spoofed transactions are not done and the authentication process is robust, secure and centralised.\n(c) The PSO shall put in place a real-time / near real-time fraud monitoring solution to identify suspicious transactional behaviour and generate alerts.\n(d) The PSO shall have a manned facility to function on $24 \\times 7 \\times 365$ basis for facilitating swift resolution of unauthorised / fraudulent transactions reported by customers and also to provide prompt response to Law Enforcement Agencies (LEAs).\n(e) The PSO shall put in place a mechanism to capture, analyse, store and archive audit logs in a systematic manner. Log messages shall provide relevant information to uniquely identify the user that initiated an action, the action and parameters of that particular action. Access to log data shall be provided on a controlled basis. Audit logs shall be preserved for a period of at least five years.\n(f) The PSO shall ensure that the payment architecture operated by them is robust, scalable and commensurate with the transaction volumes; this shall be reviewed by the sub-committee of the Board.\n(g) The PSO shall employ secure mail and messaging systems to ensure that inbound and outbound traffic through mail, messages or any other media are secure.\n(h) The PSO shall subscribe to anti-phishing / anti-rogue app services for identifying and taking down phishing websites / rogue applications.\n\n(i) The PSO shall, on an ongoing basis, either directly or through its participants and service providers, create public awareness on precautionary measures to safeguard against frauds and cyber threats while using digital payment products.", "positions": [{"page_index": 11, "rectangles": [[72.**************, 241.7887725830078, 87.33320617675781, 254.09837341308594], [93.**************, 337.66998291015625, 111.35599517822266, 351.0499572753906], [114.**************, 338.53875732421875, 465.0183410644531, 350.8483581542969], [468.17578125, 338.53875732421875, 526.1688232421875, 350.8483581542969], [114.**************, 357.6187744140625, 526.4613037109375, 369.9283752441406], [114.**************, 376.5787658691406, 177.**************, 388.88836669921875], [179.**************, 376.5787658691406, 198.44049072265625, 388.88836669921875], [200.85824584960938, 376.5787658691406, 219.23912048339844, 388.88836669921875], [221.69000244140625, 376.5787658691406, 249.11912536621094, 388.88836669921875], [251.57000732421875, 376.5787658691406, 273.020751953125, 388.88836669921875], [275.4385070800781, 376.5787658691406, 293.7428283691406, 388.88836669921875], [296.18267822265625, 376.5787658691406, 367.6335754394531, 388.88836669921875], [370.07342529296875, 376.5787658691406, 411.6611022949219, 388.88836669921875], [414.1009521484375, 376.5787658691406, 425.0857238769531, 388.88836669921875], [427.40411376953125, 376.5787658691406, 464.2948303222656, 388.88836669921875], [466.73468017578125, 376.5787658691406, 502.81341552734375, 388.88836669921875], [505.2311706542969, 376.5787658691406, 526.4169311523438, 388.88836669921875], [114.**************, 395.53875732421875, 170.95712280273438, 407.8483581542969], [93.**************, 414.4700012207031, 110.75599670410156, 427.8499755859375], [114.**************, 415.3387756347656, 526.3554077148438, 427.64837646484375], [114.**************, 434.*************5, 421.*************, 446.7483825683594], [93.**************, 511.3299560546875, 111.35599517822266, 524.7099609375], [114.**************, 512.19873046875, 526.337646484375, 524.5083618164062], [114.**************, 531.***********, 260.5011901855469, 543.*************], [265.2483825683594, 531.***********, 526.1455078125, 543.*************], [114.**************, 550.***********, 526.1691284179688, 562.*************], [114.**************, 569.19873046875, 196.3712158203125, 581.5083618164062], [200.5995330810547, 569.19873046875, 494.3291320800781, 581.5083618164062], [498.5799865722656, 569.19873046875, 526.5000610351562, 581.5083618164062], [114.**************, 588.1587524414062, 390.31951904296875, 600.*************], [93.**************, 607.1199951171875, 107.99600219726562, 620.5], [114.**************, 607.98876953125, 526.*************, 620.*************], [114.**************, 627.*************, 526.3837280273438, 639.***********], [114.**************, 646.02880859375, 271.2411804199219, 658.3384399414062], [93.**************, 664.9599609375, 111.35599517822266, 678.3399658203125], [114.**************, 665.8287353515625, 526.1685791015625, 678.1383666992188], [114.**************, 684.9087524414062, 484.47100830078125, 697.2183837890625], [93.**************, 703.8399658203125, 111.35599517822266, 717.219970703125], [114.**************, 704.708740234375, 526.3717651367188, 717.0183715820312], [114.**************, 723.7887573242188, 364.7592468261719, 736.098388671875]]}, {"page_index": 12, "rectangles": [[93.**************, 71.09996032714844, 107.39599609375, 84.4799575805664], [114.**************, 71.96876525878906, 526.28125, 84.27835845947266], [114.**************, 91.*************1, 526.3612060546875, 103.3783950805664], [114.**************, 110.**************, 454.*************, 122.*************]]}]}, {"chunk": "# Section IV\n\n## Digital Payment Security Measures / Controls\n\nIn addition to the extant instructions applicable to PSOs for digital payment transactions, the following instructions shall also be applicable.\n28. The PSO shall facilitate its members / participants have mechanisms for online alerts based on various parameters such as failed transactions, transaction velocity, as well as in case of new account parameters (eg., excessive activity), time zone, geo-location, IP address origin (in respect of unusual patterns, prohibited zones / rogue IPs), behavioural biometrics, transaction origination from point of compromise, transactions to mobile wallets / mobile numbers / VPAs on whom vishing or other types of fraud are registered / recorded, declined transactions, transactions with no approval code, etc.\n29. While sending SMS / e-mail alert or any other notification to customers, either by PSO or payment system participants, the following shall be ensured -\n(a) Bank account number / card number / other confidential information are redacted / masked to the extent possible.\n(b) Online payment transactions shall mention merchant name (not the payment gateway / aggregator) and amount; for fund transfers, name of the beneficiary and debit amount. The PSO shall ensure that the name is taken from the system of the entity maintaining the beneficiary account.\n(c) In cases where the OTP is a factor of authentication, the PSO shall ensure that the OTP is mentioned at the end of the notification message and the message shall also refer the specific transaction.\n30. The PSO shall provide a facility on its mobile application / website that would enable customers, with necessary authentication, to identify / mark a fraudulent transaction for seamless and immediate notification to the issuer of payment instrument. It shall also ensure facilitation of such mechanism by the system participants.\n\n## Mobile Payments\n\n31. The PSO providing / facilitating / processing mobile payment services transactions shall comply with the following security practices and risk mitigation measures and shall also ensure that the participants in its payment system comply with these instructions:\n\n(a) PSO shall put in place a mechanism to ensure that the mobile application is free from any anomalies or exceptions for which the application was not programmed.\n(b) The PSO shall ensure that an authenticated session, together with its encryption protocol, remains intact throughout an interaction with the customer. In case of any interference or if the customer closes the application, the session shall be terminated, and the affected transactions resolved or reversed out.\n(c) The PSO shall ensure device binding ${ }^{9}$ / finger printing of mobile applications with the device and SIM. In case the mobile application remains unused beyond a policy determined specified period, the PSO shall ensure device binding is performed again.\n(d) The PSO shall ensure that an online session on mobile application is automatically terminated after a fixed period of inactivity and customers are prompted to re-login.\n(e) The PSO shall, where applicable, set down the maximum number of failed log-in or authentication attempts after which access to the mobile application is blocked. There shall be a secure procedure to re-activate the access to blocked product / service. The customer shall be notified for failed log-in or authentication attempts, immediately.\n(f) The PSO shall put in place a control mechanism, to identify any presence of remote access applications (to the extent possible) and prohibit access to the mobile payment application while the remote access is live.\n(g) Whenever there is a change in registered mobile number or email ID linked to the payment instrument there shall be a cooling period of minimum 12 hours before allowing any payment transaction through online modes / channels.", "positions": [{"page_index": 12, "rectangles": [[271.1300048828125, 147.8288116455078, 327.3791198730469, 160.13841247558594], [179.05999755859375, 166.7887725830078, 419.29913330078125, 179.09837341308594], [72.**************, 185.74879455566406, 75.**************, 198.0583953857422], [72.**************, 198.58876037597656, 526.4857177734375, 210.8983612060547], [72.**************, 217.5487823486328, 293.39910888671875, 229.85838317871094], [72.**************, 249.**************, 87.33320617675781, 261.4183654785156], [93.**************, 249.**************, 526.4751586914062, 261.4183654785156], [93.**************, 268.0987548828125, 526.4497680664062, 280.4083557128906], [93.**************, 287.17877197265625, 526.45166015625, 299.4883728027344], [93.**************, 306.1387939453125, 526.1851196289062, 318.4483947753906], [93.**************, 325.0987854003906, 149.6990509033203, 337.40838623046875], [152.50320434570312, 325.0987854003906, 209.31507873535156, 337.40838623046875], [212.11923217773438, 325.0987854003906, 265.9281921386719, 337.40838623046875], [268.73236083984375, 325.0987854003906, 293.8594055175781, 337.40838623046875], [296.7739562988281, 325.0987854003906, 323.6011657714844, 337.40838623046875], [326.5157165527344, 325.0987854003906, 338.6928405761719, 337.40838623046875], [341.6073913574219, 325.0987854003906, 407.5271911621094, 337.40838623046875], [410.4417419433594, 325.0987854003906, 472.7735595703125, 337.40838623046875], [475.6881103515625, 325.0987854003906, 487.865234375, 337.40838623046875], [490.6694030761719, 325.0987854003906, 526.140869140625, 337.40838623046875], [93.**************, 344.05877685546875, 526.292236328125, 356.3683776855469], [93.**************, 363.0187683105469, 445.6483459472656, 375.328369140625], [72.**************, 381.978759765625, 87.33320617675781, 394.2883605957031], [93.**************, 458.7699890136719, 111.35599517822266, 472.14996337890625], [114.**************, 459.6387634277344, 526.308349609375, 471.9483642578125], [114.**************, 478.7187805175781, 380.2220153808594, 491.02838134765625], [382.4300231933594, 478.7187805175781, 473.48797607421875, 491.02838134765625], [475.8999938964844, 478.7187805175781, 526.4852905273438, 491.02838134765625], [114.**************, 497.67877197265625, 154.40911865234375, 509.9883728027344], [157.82000732421875, 497.67877197265625, 526.*************, 509.9883728027344], [114.**************, 516.7587890625, 290.15911865234375, 529.0684204101562], [93.**************, 535.6900024414062, 110.75599670410156, 549.0700073242188], [114.**************, 536.5587768554688, 526.4718627929688, 548.868408203125], [114.**************, 555.6387939453125, 526.450927734375, 567.9484252929688], [114.**************, 574.5987548828125, 255.35911560058594, 586.9083862304688], [72.**************, 606.1587524414062, 87.33320617675781, 618.*************], [93.**************, 606.1587524414062, 526.4471435546875, 618.*************], [93.**************, 625.*************, 526.466552734375, 637.*************], [93.**************, 644.1087646484375, 460.8724670410156, 656.4183959960938], [465.5799865722656, 644.1087646484375, 526.4699096679688, 656.4183959960938], [93.**************, 663.*************, 410.022216796875, 675.*************]]}, {"page_index": 13, "rectangles": [[93.**************, 109.99998474121094, 111.35599517822266, 123.3799819946289], [114.**************, 110.86878967285156, 526.3286743164062, 123.17838287353516], [114.**************, 129.*************, 449.841064453125, 142.**************], [453.3517761230469, 129.*************, 526.3040771484375, 142.**************], [114.**************, 148.*************, 526.4631958007812, 161.**************], [114.**************, 167.86878967285156, 380.529541015625, 180.1783905029297], [201.**************, 206.74879455566406, 526.482421875, 219.0583953857422], [114.**************, 225.7088165283203, 523.41455078125, 238.01841735839844], [93.**************, 244.63999938964844, 111.35599517822266, 258.0199890136719], [114.**************, 245.**************, 526.4727172851562, 257.8183898925781], [114.**************, 264.6187744140625, 517.1199340820312, 276.9283752441406], [93.**************, 283.54998779296875, 111.35599517822266, 296.9299621582031], [114.**************, 284.41876220703125, 526.4723510742188, 296.7283630371094], [114.**************, 303.498779296875, 492.3990173339844, 315.8083801269531], [494.739990234375, 303.498779296875, 526.*************, 315.8083801269531], [114.**************, 322.*************, 523.*************, 334.76837158203125], [114.**************, 341.41876220703125, 136.7662353515625, 353.7283630371094], [142.08750915527344, 341.41876220703125, 190.37649536132812, 353.7283630371094], [195.**************, 341.41876220703125, 221.5138397216797, 353.7283630371094], [226.95655822753906, 341.41876220703125, 242.1586456298828, 353.7283630371094], [247.57928466796875, 341.41876220703125, 286.153076171875, 353.7283630371094], [291.474365234375, 341.41876220703125, 307.3719787597656, 353.7283630371094], [312.6932678222656, 341.41876220703125, 342.1479797363281, 353.7283630371094], [347.5906982421875, 341.41876220703125, 377.55303955078125, 353.7283630371094], [382.97369384765625, 341.41876220703125, 395.7580261230469, 353.7283630371094], [401.20074462890625, 341.41876220703125, 472.6516418457031, 353.7283630371094], [478.0943603515625, 341.41876220703125, 526.*************, 353.7283630371094], [114.**************, 360.498779296875, 176.34466552734375, 372.8083801269531], [93.**************, 379.42999267578125, 107.99600219726562, 392.8099670410156], [114.**************, 380.29876708984375, 526.4689331054688, 392.6083679199219], [114.**************, 399.3787841796875, 151.8910369873047, 411.6883850097656], [155.17999267578125, 399.3787841796875, 216.3526611328125, 411.6883850097656], [219.52999877929688, 399.3787841796875, 235.41656494140625, 411.6883850097656], [238.69544982910156, 399.3787841796875, 256.9997863769531, 411.6883850097656], [260.2786560058594, 399.3787841796875, 293.1005554199219, 411.6883850097656], [296.3794250488281, 399.3787841796875, 343.3656311035156, 411.6883850097656], [346.6445007324219, 399.3787841796875, 367.95172119140625, 411.6883850097656], [371.2305908203125, 399.3787841796875, 410.3011169433594, 411.6883850097656], [413.5799865722656, 399.3787841796875, 450.85235595703125, 411.6883850097656], [454.1312255859375, 399.3787841796875, 466.308349609375, 411.6883850097656], [469.4657897949219, 399.3787841796875, 487.7811584472656, 411.6883850097656], [490.9385986328125, 399.3787841796875, 526.*************, 411.6883850097656], [114.**************, 418.3387756347656, 367.09912109375, 430.64837646484375], [93.**************, 437.2900085449219, 111.35599517822266, 450.66998291015625], [114.**************, 438.1587829589844, 526.1029052734375, 450.*************], [114.**************, 457.***********, 526.2353515625, 469.5483703613281], [114.**************, 476.*************, 442.*************, 488.**************]]}]}, {"chunk": "# Card Payments\n\n32. The PSO shall ensure that terminals installed at merchants for capturing card details for payments or otherwise are validated against the PCI-P2PE program; PoS terminals with PIN entry installed at the merchants for capturing card payments (including the double swipe terminals) shall be approved by the PCI-PTS program.\n33. The card networks shall facilitate implementation of transaction limits at card, Bank Identification Number (BIN) as well as at card issuer level. Such limits shall mandatorily be set at the card network switch itself. The card networks shall institute an alert mechanism on a $24 \\times 7 \\times 365$ basis, to be triggered to the card issuer in case of any suspicious incident. Card networks shall ensure that card details of the customers are\n    [^0]\n    [^0]:    ${ }^{9}$ The device binding shall be preferably implemented through a combination of hardware, software and service information.\n\nstored in an encrypted form at any of their server locations. They shall also ensure that processing of the card details in readable format is performed in a secure manner.", "positions": [{"page_index": 13, "rectangles": [[93.**************, 609.*************, 115.**************, 621.***********], [117.*************, 609.*************, 142.**************, 621.***********], [144.**************, 609.*************, 192.**************, 621.***********], [194.**************, 609.*************, 220.15280151367188, 621.***********], [222.59263610839844, 609.*************, 266.27911376953125, 621.***********], [268.8500061035156, 609.*************, 346.3287048339844, 621.***********], [348.867919921875, 609.*************, 361.09912109375, 621.***********], [363.*************, 609.*************, 420.*************, 621.***********], [423.0431213378906, 609.*************, 451.16192626953125, 621.***********], [453.6017761230469, 609.*************, 465.7789001464844, 621.***********], [468.*************, 609.*************, 495.88916015625, 621.***********], [498.*************, 609.*************, 526.4905395507812, 621.***********], [93.**************, 628.02880859375, 379.5723571777344, 640.3384399414062], [383.20452880859375, 628.02880859375, 526.3490600585938, 640.3384399414062], [93.**************, 646.98876953125, 108.69320678710938, 659.*************], [111.**************, 646.98876953125, 129.41647338867188, 659.*************], [132.36415100097656, 646.98876953125, 144.64064025878906, 659.*************], [147.47792053222656, 646.98876953125, 165.9036865234375, 659.*************], [168.81825256347656, 646.98876953125, 193.24978637695312, 659.*************], [196.15330505371094, 646.98876953125, 237.74099731445312, 659.*************], [240.6555633544922, 646.98876953125, 274.2171630859375, 659.*************], [277.1206970214844, 646.98876953125, 302.8548889160156, 659.*************]]}, {"page_index": 14, "rectangles": [[93.**************, 71.**************, 386.*************, 83.**************], [389.*************, 71.**************, 526.*************, 83.**************], [93.**************, 90.**************, 492.*************, 102.**************]]}]}, {"chunk": "# Prepaid Payment Instruments\n\n34. PPI issuers are encouraged to communicate OTP and transaction alerts with users in a language of their choice, including vernacular languages.\n35. The PPI issuers - banks and non-banks - shall put in place suitable cooling period for funds transfer and cash withdrawal after such funds are electronically loaded on to the PPI.", "positions": []}, {"chunk": "# Acronyms\n\n|   API    |               Application Programming Interface                |\n| :------: | :------------------------------------------------------------: |\n|   ASLC   |                Application Security Life Cycle                 |\n|   ATM    |                    Automated Teller Machine                    |\n|   BCP    |                    Business Continuity Plan                    |\n|   BIN    |                   Bank Identification Number                   |\n|   CCMP   |                  Cyber Crisis Management Plan                  |\n| CERT-In  |            Indian Computer Emergency Response Team             |\n|   <PERSON><PERSON><PERSON>   |               Chief Information Security Officer               |\n|   CSP    |                     Cloud Service Provider                     |\n|    DR    |                       Disaster Recovery                        |\n|   EoLS   |                      End of Life support                       |\n|  IDRBT   |  Institute for Development and Research in Banking Technology  |\n|    IP    |                       Internet Protocol                        |\n|    IS    |                       Information System                       |\n|   ISMS   |             Information Security Management System             |\n|    IT    |                     Information Technology                     |\n|   KPI    |                   Key Performance Indicator                    |\n|   KRI    |                       Key Risk Indicator                       |\n|   LEA    |                     Law Enforcement Agency                     |\n|  NCIIPC  | National Critical Information Infrastructure Protection Centre |\n|   OEM    |                Original Equipment Manufacturer                 |\n|   OTP    |                       One Time Password                        |\n|   PCI    |                     Payment Card Industry                      |\n| PCI-DSS  |          Payment Card Industry-Data Security Standard          |\n| PCI-P2PE |        Payment Card Industry-Point to Point Encryption         |\n| PCI-PTS  |         Payment Card Industry-PIN Transaction Security         |\n|   PDC    |                      Primary Data Centre                       |\n|   PIN    |                 Personal Identification Number                 |\n|   PoS    |                         Point of Sale                          |\n|   PPI    |                   Prepaid Payment Instrument                   |\n|   PSO    |                    Payment System Operator                     |\n|   PSS    |                 Payment and Settlement Systems                 |\n|    PT    |                      Penetration Testing                       |\n|   RBI    |                     Reserve Bank of India                      |\n|   RPO    |                    Recovery Point Objective                    |\n|   RTO    |                    Recovery Time Objective                     |\n|   SDLC   |                Software Development Life Cycle                 |\n|  S-SDLC  |             Secure-Software Development Life Cycle             |\n|   SIEM   |           Security Information and Event Management            |\n|   SIM    |                Subscriber Identification Module                |\n|   SMS    |                     Short Message Service                      |\n|   SOC    |                   Security Operations Centre                   |\n|    VA    |                    Vulnerability Assessment                    |\n|   VPA    |                    Virtual Payment Address                     |\n|   WLAO   |                    White Label ATM Operator                    |", "positions": []}]