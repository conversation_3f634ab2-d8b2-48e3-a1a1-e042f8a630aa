from langchain_openai import AzureChatOpenAI
from langchain.schema.messages import SystemMessage, HumanMessage, AIMessage
from langchain.schema import BaseMessage
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from config import settings
import json


class AnswerPart(BaseModel):
    knowledge_chunk_id: str
    answer_start_index: int
    answer_end_index: int
    knowledge_chunk_start_index: int
    knowledge_chunk_end_index: int
    rank: int
    reason: str


class AnswerModel(BaseModel):
    answer: str
    answer_parts: List[AnswerPart]


class CompletionClient:
    def __init__(self) -> None:

        # Initialize main chat client
        self.chat_client = AzureChatOpenAI(
            azure_deployment=settings.OPENAI_CHAT_MODEL,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            temperature=1.0,
        )

        # Initialize structured output client (using mini model)
        self.structured_client = AzureChatOpenAI(
            # Use the same model for structured output
            azure_deployment=settings.OPENAI_CHAT_MODEL,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            temperature=0.0,

        )

    def _convert_messages(self, messages: List[Dict[str, str]]) -> List[BaseMessage]:
        """Convert OpenAI format messages to LangChain format"""
        langchain_messages = []
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")

            if role == "system":
                langchain_messages.append(SystemMessage(content=content))
            elif role == "user":
                langchain_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                langchain_messages.append(AIMessage(content=content))

        return langchain_messages

    def completion(
        self,
        messages: List[Dict[str, str]],
        model: str = None,  # This parameter is ignored since we use the configured deployment
        tools: Optional[List[Dict]] = None
    ):
        """
        Get chat completion using LangChain Azure OpenAI
        Note: tools parameter is not directly supported in basic LangChain, 
        would need additional setup for function calling
        """
        langchain_messages = self._convert_messages(messages)

        # Get response from LangChain
        response = self.chat_client.invoke(langchain_messages)

        # Format response to match original interface
        content = response.content
        if content:
            content = content.replace("```html", "").replace("```", "")

        # LangChain doesn't return usage info by default, so we return None
        # Tool calls would need additional setup with function calling
        return content, None, None

    async def completion_stream(
        self,
        messages: List[Dict[str, str]],
        model: str = None,  # Ignored
        tools: Optional[List[Dict]] = None
    ):
        """Streaming completion using LangChain"""
        langchain_messages = self._convert_messages(messages)

        # Use astream for async streaming
        stream = self.chat_client.astream(langchain_messages)
        return stream

    def completion_structured_as_answer_model(
        self,
        required_model,
        messages: List[Dict[str, str]],
        model: str = None,  # Ignored, uses structured_client
        tools: Optional[List[Dict]] = None,
    ):
        """
        Get structured output using LangChain with_structured_output
        """
        langchain_messages = self._convert_messages(messages)

        # Use LangChain's structured output feature
        structured_llm = self.structured_client.with_structured_output(
            required_model,
            method="function_calling"
        )

        try:
            parsed_response = structured_llm.invoke(langchain_messages)
            return parsed_response
        except Exception as e:
            raise ValueError(f"Failed to generate structured response: {e}")

    # Alternative method using prompt-based structured output (fallback)
    def completion_structured_with_prompt(
        self,
        required_model,
        messages: List[Dict[str, str]],
        model: str = None,
    ):
        """
        Fallback method for structured output using prompt engineering
        """
        # Get the schema
        schema = required_model.schema()

        # Add schema instruction to the last message
        langchain_messages = self._convert_messages(messages)

        # Add structured output instruction
        structure_prompt = f"""
        Please respond with a valid JSON object that matches this exact schema:
        {json.dumps(schema, indent=2)}
        
        Ensure your response is valid JSON and follows the schema exactly.
        """

        # Append to system message or create new one
        if langchain_messages and isinstance(langchain_messages[0], SystemMessage):
            langchain_messages[0].content += "\n\n" + structure_prompt
        else:
            langchain_messages.insert(
                0, SystemMessage(content=structure_prompt))

        response = self.structured_client.invoke(langchain_messages)

        try:
            # Try to parse the JSON response
            response_text = response.content.strip()
            if response_text.startswith("```json"):
                response_text = response_text.replace(
                    "```json", "").replace("```", "").strip()

            parsed_data = json.loads(response_text)
            parsed_response = required_model.parse_obj(parsed_data)
            return parsed_response
        except Exception as e:
            raise ValueError(f"Failed to parse structured response: {e}")


completion_client = CompletionClient()
