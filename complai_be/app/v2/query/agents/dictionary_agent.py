from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.http import models as qmodels
from langfuse.decorators import observe

from typing import List, Dict

class DefinitionRetriever:
    def __init__(self, client: QdrantClient, collection: str, embed_model: str, top_k: int = 5):
        self.client = client
        self.collection = "abbreviation_dictionary_banking"
        self.embed_model = embed_model
        self.top_k = top_k
        self.client_openai = OpenAI()

    @observe(as_type="embedding")
    def run(self, query: str) -> List[Dict[str, str]]:
        """
        Retrieve the best matching definition or term for a user query.
        """
        self.collection = "abbreviation_dictionary_banking"
        # # test if the collection is correct by fetching any element
        # try:
        #     self.client.get_collection(collection_name=self.collection)
        #     # get any element from the collection
        #     t = self.client.scroll(
        #         collection_name=self.collection,
        #         limit=1,
        #         offset=0
        #     )
        #     print(t)
        # except Exception as e:
        #     return {"error": f"Collection '{self.collection}' not found. Please check the collection name."}

        # Embed the query
        embed_response = self.client_openai.embeddings.create(
            input=query,
            model=self.embed_model
        )
        query_vector = embed_response.data[0].embedding


        # Search in Qdrant
        results = self.client.search(
            collection_name=self.collection,
            query_vector=query_vector,
            limit=self.top_k,
            with_payload=True,
        )

        # Check if results are empty return an empty list
        if not results:
            return []

        return [
            {"term": top_hit.payload["term"], 
             "definition": top_hit.payload["definition"]
             }
             for top_hit in results
        ]