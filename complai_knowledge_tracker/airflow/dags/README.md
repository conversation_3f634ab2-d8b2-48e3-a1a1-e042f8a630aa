# Refactored DAGs and Utils

This directory contains the refactored and cleaned up DAGs and utilities for processing RBI documents and RSS feeds with multi-vector Qdrant ingestion.

## Overview

The refactoring addresses the following issues from the original messy codebase:
- Code duplication across DAGs
- Inconsistent error handling
- Poor separation of concerns
- Missing configuration management
- Inefficient vector embedding strategies
- Lack of proper testing

## Architecture

### Core Components

1. **Configuration Management** (`utils/config.py`)
   - Centralized configuration for all services
   - Environment-specific settings
   - Validation and error checking

2. **Document Processing Pipeline** (`utils/document_pipeline.py`)
   - Unified pipeline for PDF processing
   - Metadata extraction
   - Obligation extraction for Master Directions
   - Multi-vector embedding generation

3. **Multi-Vector Qdrant Support** (`utils/qdrant_utils.py`)
   - Dense vectors (OpenAI embeddings)
   - Sparse vectors (BM25)
   - SPLADE vectors for enhanced retrieval
   - Consistent collection management

4. **Enhanced Database Utils** (`utils/database_utils.py`)
   - MongoDB/DocumentDB connection management
   - Proper error handling and retries
   - Context managers for safe operations

5. **OpenAI Integration** (`utils/openai_utils.py`)
   - Key rotation for rate limit handling
   - Enhanced embedding generation
   - Structured data extraction

6. **Error Handling & Logging** (`utils/error_handling.py`)
   - Comprehensive error categorization
   - Performance monitoring
   - Retry mechanisms with exponential backoff

### DAGs

1. **RBI Regulations Processor** (`rbi_regulations_processor.py`)
   - Processes PDF files from S3
   - Extracts metadata and obligations
   - Ingests to Qdrant with multi-vector support
   - Batch processing with configurable concurrency

2. **RSS Feed Processor** (`rss_feed_processor.py`)
   - Scrapes RBI RSS feeds
   - Downloads and processes PDFs
   - Stores articles and metadata
   - Consistent processing patterns

## Key Improvements

### 1. Multi-Vector Embedding Strategy

The refactored system supports three types of vectors for enhanced retrieval:

- **Dense Vectors**: OpenAI text-embedding-3-large (3072 dimensions)
- **Sparse Vectors**: BM25 using Qdrant/FastEmbed
- **SPLADE Vectors**: Neural sparse vectors for better semantic matching

### 2. Configuration Management

All configuration is centralized in `utils/config.py`:

```python
from utils.config import config

# Access OpenAI settings
api_key = config.openai.get_next_key()

# Access Qdrant settings
qdrant_url = config.qdrant.full_url

# Access database settings
db_uri = config.database.uri
```

### 3. Document Processing Pipeline

Unified processing pipeline handles:

```python
from utils.document_pipeline import document_processor

result = document_processor.process_pdf_from_path(
    pdf_path="/path/to/document.pdf",
    collection_name="rbi_regulations",
    source_info={"source": "s3", "key": "documents/doc.pdf"}
)
```

### 4. Error Handling

Comprehensive error handling with categorization:

```python
from utils.error_handling import handle_errors, ErrorSeverity, ErrorCategory

@handle_errors(
    severity=ErrorSeverity.HIGH,
    category=ErrorCategory.PROCESSING
)
def process_document(doc_path):
    # Processing logic here
    pass
```

### 5. Database Operations

Safe database operations with connection management:

```python
from utils.database_utils import db_manager

# Upsert document
success = db_manager.upsert_document(
    document=metadata,
    db_name="rbi",
    collection_name="regulations"
)

# Find document
doc = db_manager.find_document(
    query={"document_number": "RBI/2024-25/123"}
)
```

## Configuration Requirements

Set the following Airflow Variables:

- `openai_api_keys`: Pipe-separated OpenAI API keys
- `vector_db_host`: Qdrant host
- `documentdb_uri`: MongoDB/DocumentDB connection string
- `s3_bucket_name`: S3 bucket for documents
- `aws_access_key_id`: AWS access key
- `aws_secret_access_key`: AWS secret key

## Usage

### Running the DAGs

1. **RBI Regulations Processor**:
   - Trigger manually or set schedule
   - Processes all PDFs in S3 bucket
   - Configurable batch size and concurrency

2. **RSS Feed Processor**:
   - Runs daily by default
   - Scrapes RSS feeds and processes new articles
   - Downloads PDFs and extracts content

### Testing

Run integration tests:

```bash
cd complai_knowledge_tracker/airflow/dags
python -m pytest tests/test_integration.py -v
```

## Performance Optimizations

1. **Batch Processing**: Configurable batch sizes to prevent overwhelming systems
2. **Connection Pooling**: Efficient database connection management
3. **Caching**: Duplicate detection to avoid reprocessing
4. **Rate Limiting**: OpenAI key rotation and request throttling
5. **Chunking Strategy**: Optimized document chunking with overlap

## Monitoring and Logging

- Structured logging with context information
- Performance metrics tracking
- Error categorization and severity levels
- Comprehensive audit trails

## Migration from Old DAGs

To migrate from the old DAGs:

1. Update Airflow Variables with new configuration
2. Test the new DAGs in a development environment
3. Gradually migrate processing to new DAGs
4. Monitor performance and error rates
5. Decommission old DAGs once stable

## Future Enhancements

1. **Metrics Dashboard**: Real-time monitoring of processing metrics
2. **Advanced Chunking**: Semantic chunking based on document structure
3. **Quality Scoring**: Document quality assessment and filtering
4. **Incremental Processing**: Delta processing for updated documents
5. **Multi-language Support**: Support for documents in multiple languages

## Support

For issues or questions:
1. Check the logs for detailed error information
2. Review the configuration settings
3. Run the integration tests to verify setup
4. Check Qdrant and database connectivity
