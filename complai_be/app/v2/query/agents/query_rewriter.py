import yaml
from app.v2.query.utils.completion_client import completion_client

class QueryRewriter:
    def __init__(self, model: str, prompt_file: str):
        self.model = model
        self.prompt = self._load_prompt(prompt_file)
        
    def _load_prompt(self, prompt_file: str) -> str:
        with open(prompt_file, "r") as f:
            data = yaml.safe_load(f)
        return data.get("prompt", "")
    
    def run(self, query: str) -> str:
        final_prompt = self.prompt.format(original_query=query)
        messages = [{"role": "system", "content": final_prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)
        rewritten = response.strip()
        return rewritten if rewritten else query
