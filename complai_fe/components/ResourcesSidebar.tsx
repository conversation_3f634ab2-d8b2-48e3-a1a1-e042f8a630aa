//ResourcesSidebar.tsx
import { useState, useEffect, useRef, useCallback } from "react";
import {
  X,
  Download,
  ExternalLink,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import Image from "next/image";
// import type { RegulationInfo } from "@/types/types";
import type { ResourcesSidebarProps } from "@/types/chat";
import {
  Pdf<PERSON><PERSON><PERSON>,
  PdfHighlighter,
  Highlight,
  AreaHighlight,
  Popup,
  IHighlight,
} from "react-pdf-highlighter";
// import download_icon from "@/components/assets/icons/download-icon.svg";
import "react-pdf-highlighter/dist/style.css";
import { set } from "date-fns";


// Document type priority configuration
const DOCUMENT_TYPE_PRIORITY = {
  "master_direction": 0,
  "master_circular": 1,
  "notification": 2,
  "press_release": 3,
  "speech": 4,
  "tender": 5,
  "publication": 6,
  "other": 7,
};

// Helper function to get document type priority
const getDocumentTypePriority = (docType: string | undefined): number => {
  if (!docType) return DOCUMENT_TYPE_PRIORITY.other;
  
  const normalizedType = docType.toLowerCase().replace(/[_\s-]/g, '_');
  return DOCUMENT_TYPE_PRIORITY[normalizedType as keyof typeof DOCUMENT_TYPE_PRIORITY] ?? DOCUMENT_TYPE_PRIORITY.other;
};

// Helper function to sort references by document type priority
const sortReferencesByPriority = (references: ReferenceItem[]): ReferenceItem[] => {
  return [...references].sort((a, b) => {
    const priorityA = getDocumentTypePriority(a.Type);
    const priorityB = getDocumentTypePriority(b.Type);
    
    // Primary sort by priority (lower number = higher priority)
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }
    
    // Secondary sort by title alphabetically if same priority
    const titleA = a.title || '';
    const titleB = b.title || '';
    return titleA.localeCompare(titleB);
  });
};

// Updated ReferenceItem type to match the new metadata format
export interface ReferenceItem {
  Type?: string;
  addressee?: string;
  chunk_id?: number;
  date_of_issue?: string;
  dept?: string[] | string;
  document_code?: string;
  id?: string;
  pdf_filename?: string;
  pdf_link?: string;
  // Updated positions format with bboxes (array of number arrays)
  positions?: {
    page: number;
    bboxes: number[][];
  }[];
  revision_s3_url?: string;
  section_summary?: string;
  title?: string;
  document_id?: string; // For backward compatibility
  // For backward compatibility with the previous format
  content?: string;
  date?: string;
  department?: string;
  source?: string;
  _allChunks?: ReferenceItem[]; // Add this for grouping
}

// Add a global style to hide scrollbars and fix PDF rendering
const ScrollbarStyle = () => (
  <style jsx global>{`
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
    }

    /* Override PDF scaling */
    .pdf-container {
      max-width: 100%;
      max-height: 100%;
      overflow: hidden;
    }

    /* Make pdf pages fit inside container */
    .pdf-highlighter-container {
      width: 100%;
      height: 100%;
      overflow: auto;
    }

    /* Force the PDF to scale properly */
    .react-pdf__Document {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .react-pdf__Page {
      max-width: 100% !important;
      box-shadow: none !important;
      margin: 0 !important;
    }

    .react-pdf__Page__canvas {
      max-width: 100% !important;
      height: auto !important;
    }
  `}</style>
);

// TriggerComponent to handle loading and scrolling
const TriggerComponent = ({ onLoad }: { onLoad: () => void }) => {
  useEffect(() => {
    setTimeout(() => {
      // console.log("before onload");
      onLoad?.();
    }, 400);
  }, [onLoad]);

  return null;
};

// Helper functions for hash management
const resetHash = () => {
  document.location.hash = "";
};

const parseIdFromHash = () =>
  document.location.hash.slice("#highlight-".length);

export function ResourcesSidebar({
  showDetailView,
  setShowDetailView,
  selectedDocCode,
  currentRegulation,
  onClose,
  references = [],
  onCollapseChat,
  selectedSpecificRef, // Add this line
}: ResourcesSidebarProps) {
  const [selectedReference, setSelectedReference] = useState<ReferenceItem | null>(null);
  const [showPdf, setShowPdf] = useState(false);
  const [pdfLoadFailed, setPdfLoadFailed] = useState(false);
  const [isOnclickReference, setIsOnclickReference] = useState(false);
  const scrollViewerToRef = useRef<(highlight: IHighlight) => void>(() => { });

  // New state for sources navigation
  const [sourceIndex, setSourceIndex] = useState(0);
  const [docSources, setDocSources] = useState<ReferenceItem[]>([]);

  // New state for grouped references
  const [groupedReferences, setGroupedReferences] = useState<ReferenceItem[]>([]);
  // console.log("showDetailView at resources", showDetailView);
  // console.log("selected doccode:", selectedDocCode);
  // console.log(references);

  // Reset PDF load status when selecting a new reference
  useEffect(() => {
    if (selectedReference) {
      setPdfLoadFailed(false);
    }
  }, [selectedReference]);

  // Group references by document_id when they change - UPDATED with priority sorting
  useEffect(() => {
    if (references && references.length > 0) {
      // First, sort all references by priority
      const sortedReferences = sortReferencesByPriority(references);
      
      // Create a map to store grouped references
      const groupMap = new Map<string, ReferenceItem>();

      // Group by document_id or document_code
      sortedReferences.forEach(ref => {
        const docId = ref.document_id || ref.document_code || ref.id;
        if (!docId) return;

        if (!groupMap.has(docId)) {
          // Store the first occurrence of this document
          groupMap.set(docId, {
            ...ref,
            // Store all chunks of this document in a custom property
            _allChunks: [ref]
          });
        } else {
          // Add this reference to the _allChunks array of the first occurrence
          const existingGroup = groupMap.get(docId);
          if (existingGroup) {
            existingGroup._allChunks = [...(existingGroup._allChunks || []), ref];

            // Sort _allChunks by chunk_id if available
            existingGroup._allChunks.sort((a, b) => {
              if (a.chunk_id !== undefined && b.chunk_id !== undefined) {
                return a.chunk_id - b.chunk_id;
              }
              return 0;
            });
          }
        }
      });

      // Convert map values to array and sort by priority again to ensure proper order
      const groupedArray = Array.from(groupMap.values());
      const finalSortedGroups = sortReferencesByPriority(groupedArray);
      
      setGroupedReferences(finalSortedGroups);
    } else {
      setGroupedReferences([]);
    }
  }, [references]);

  console.log("Grouped References:", groupedReferences);
  // Find related document chunks for the Sources navigation
 useEffect(() => {
  if (selectedReference) {
    // Find all references with the same document code/id
    const docId = selectedReference.document_code || selectedReference.id;
    if (docId) {
      console.log("references at Resources:", references);
      const relatedSources = references.filter(
        (ref) =>
          (ref.document_code === docId || ref.id === docId) &&
          ref.chunk_id !== undefined
      );

      // Sort by chunk_id if available
      const sortedSources = [...relatedSources].sort((a, b) => {
        if (a.chunk_id !== undefined && b.chunk_id !== undefined) {
          return a.chunk_id - b.chunk_id;
        }
        return 0;
      });

      setDocSources(
        sortedSources.length > 0 ? sortedSources : [selectedReference]
      );

      // Find index of current reference in sources based on the specific selectedReference
      const currentIndex = sortedSources.findIndex(
        (src) => 
          src.chunk_id === selectedReference.chunk_id &&
          JSON.stringify(src.positions) === JSON.stringify(selectedReference.positions)
      );
      setSourceIndex(currentIndex >= 0 ? currentIndex : 0);
    }
  }
}, [selectedReference, references]);
  // Handler for clicking on a reference
  // Handler for clicking on a reference
  const handleReferenceClick = (reference: ReferenceItem) => {
  setIsOnclickReference(true);
  setShowDetailView(true);
  console.log("Selected Reference:", reference);
  setSelectedReference(reference);
  setShowPdf(true);

  // Collapse chat history sidebar if the function is provided
  if (onCollapseChat) {
    // console.log("entered resorcessidebar");
    onCollapseChat();
  }
};

 useEffect(() => {
  if (
    showDetailView &&
    selectedDocCode &&
    references &&
    !isOnclickReference &&
    references.length > 0
  ) {
    console.log("references:", references);
    
    // If we have a specific reference, use that; otherwise find the first matching one
    let matchingReference;
    
    if (selectedSpecificRef && 
        (selectedSpecificRef.document_code === selectedDocCode || 
         selectedSpecificRef.id === selectedDocCode ||
         selectedSpecificRef.document_id === selectedDocCode)) {
      matchingReference = selectedSpecificRef;
    } else {
      matchingReference = references.find(
        (ref) =>
          ref.document_code === selectedDocCode || 
          ref.id === selectedDocCode ||
          ref.document_id === selectedDocCode
      );
    }

    if (matchingReference) {
      setSelectedReference(matchingReference);
      setShowPdf(true);
    }
  }
}, [showDetailView, selectedDocCode, references, selectedSpecificRef]); // Add selectedSpecificRef to dependencies

  // Handler to navigate through document sources
  const handleSourceNavigation = (direction: "next" | "prev") => {
    if (docSources.length <= 1) return;

    let newIndex;
    if (direction === "next") {
      newIndex = (sourceIndex + 1) % docSources.length;
    } else {
      newIndex = (sourceIndex - 1 + docSources.length) % docSources.length;
    }

    setSourceIndex(newIndex);
    setSelectedReference(docSources[newIndex]);

    // Scroll to highlight for the new source
    setTimeout(() => {
      const firstHighlight = getHighlightForSource(docSources[newIndex]);
      // console.log(firstHighlight);
      if (firstHighlight && scrollViewerToRef.current) {
        scrollViewerToRef.current(firstHighlight);
      }
    }, 500);
  };

  // Get the first highlight for a source - UPDATED for new bboxes format
  const getHighlightForSource = (source: ReferenceItem): IHighlight | null => {
    if (!source?.positions || source.positions.length === 0) {
      return null;
    }

    // Find position with minimum page number
    const minPagePosition = source.positions.reduce(
      (min, pos) => (pos.page < min.page ? pos : min),
      source.positions[0]
    );

    // console.log(minPagePosition);

    if (
      !minPagePosition ||
      !minPagePosition.bboxes ||
      minPagePosition.bboxes.length === 0 ||
      !Array.isArray(minPagePosition.bboxes[0]) ||
      minPagePosition.bboxes[0].length < 4
    ) {
      return null;
    }

    // Use the first bbox from the bboxes array
    const firstBbox = minPagePosition.bboxes[0];
    return {
      position: {
        boundingRect: {
          x1: firstBbox[0],
          y1: firstBbox[1],
          x2: firstBbox[2],
          y2: firstBbox[3],
          width: 600,
          height: 840,
          pageNumber: minPagePosition.page,
        },
        rects: [
          {
            x1: firstBbox[0],
            y1: firstBbox[1],
            x2: firstBbox[2],
            y2: firstBbox[3],
            width: 600,
            height: 840,
            pageNumber: minPagePosition.page,
          },
        ],
        pageNumber: minPagePosition.page,
      },
      content: { text: "" },
      comment: { text: "", emoji: "" },
      id: "0",
    };
  };

  // Handler to close the PDF view
  const handleClosePdf = () => {
    if(isOnclickReference){
      setIsOnclickReference(false);
    }
    setShowPdf(false);
    setSelectedReference(null);
    setPdfLoadFailed(false);
    onClose();
  };
  
  useEffect(() => {
  if (!showDetailView) {
    handleClosePdf();
  }
}, [handleClosePdf]);


  // Handler to download the PDF
  const handleDownloadPdf = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (selectedReference?.revision_s3_url) {
      try {
        // Fetch the PDF file
        const response = await fetch(selectedReference.revision_s3_url);
        const blob = await response.blob();

        // Create a blob URL for the PDF
        const blobUrl = window.URL.createObjectURL(blob);

        // Create a temporary anchor element to trigger the download
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = `${selectedReference.title || "document"}.pdf`;

        // Append to the document, click, and clean up
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Release the blob URL to free up memory
        setTimeout(() => {
          window.URL.revokeObjectURL(blobUrl);
        }, 100);
      } catch (error) {
        console.error("Error downloading PDF:", error);
        // Fallback method if fetch fails
        const link = document.createElement("a");
        link.href = selectedReference.revision_s3_url;
        link.download = `${selectedReference.title || "document"}.pdf`;
        link.target = "_blank";
        link.rel = "noopener noreferrer";
        link.setAttribute("download", "");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  // Function to get highlight by ID - UPDATED for new bboxes format
  const getHighlightById = (id: string) => {
    if (
      !selectedReference?.positions ||
      selectedReference.positions.length === 0
    ) {
      return null;
    }

    // Find position with minimum page number
    const minPagePosition = selectedReference.positions.reduce(
      (min, pos) => (pos.page < min.page ? pos : min),
      selectedReference.positions[0]
    );

    if (
      !minPagePosition ||
      !minPagePosition.bboxes ||
      minPagePosition.bboxes.length === 0 ||
      !Array.isArray(minPagePosition.bboxes[0]) ||
      minPagePosition.bboxes[0].length < 4
    ) {
      return null;
    }

    // console.log("minPagePosition", minPagePosition);
    // Use the first bbox from the bboxes array
    const firstBbox = minPagePosition.bboxes[0];
    // console.log(firstBbox);
    return {
      position: {
        boundingRect: {
          x1: firstBbox[0],
          y1: firstBbox[1],
          x2: firstBbox[2],
          y2: firstBbox[3],
          width: 600,
          height: 840,
          pageNumber: minPagePosition.page,
        },
        rects: [
          {
            x1: firstBbox[0],
            y1: firstBbox[1],
            x2: firstBbox[2],
            y2: firstBbox[3],
            width: 600,
            height: 840,
            pageNumber: minPagePosition.page,
          },
        ],
        pageNumber: minPagePosition.page,
      },
      content: { text: "" },
      comment: { text: "", emoji: "" },
      id: "0",
    };
  };

  // Function to scroll to highlight from hash
  const scrollToHighlightFromHash = () => {
    const highlight = getHighlightById(parseIdFromHash());
    if (highlight) {
      scrollViewerToRef.current(highlight);
    }
  };

  const formatContent = (content: string) => {
    // Make sure the content is a stable string and not dynamically changing between SSR and client
    if (typeof content !== "string" || !content.trim()) return null;

    const sections = content.split(/\n[#\-*]/g);

    return sections.map((section, index) => {
      const trimmedSection = section.trim();

      if (!trimmedSection) return null;

      const isHighlighted =
        trimmedSection.toLowerCase().includes("detection") ||
        trimmedSection.toLowerCase().includes("authority") ||
        trimmedSection.toLowerCase().includes("impounding");

      return (
        <div
          key={index}
          className={`mt-4 ${isHighlighted ? "bg-[#FFF8F2] p-3 rounded-md" : ""
            }`}
        >
          <p className="text-[#494949] whitespace-pre-wrap">{trimmedSection}</p>
        </div>
      );
    });
  };

  // Fallback regulation if none is provided
  const fallbackRegulation: ReferenceItem = {
    id: "fallback",
    title: "No Regulation Selected",
    content: "Scroll through the chat to view regulation details.",
    date: new Date().toDateString(),
    department: "RBI",
    source: "Master Direction",
  };

  const activeRegulation = currentRegulation || fallbackRegulation;

  // Handle PDF errors and provide fallback
  const handlePdfError = () => {
    // console.log("PDF failed to load:", selectedReference?.revision_s3_url);
    setPdfLoadFailed(true);
  };

  // Function to open PDF in new tab
  const openPdfInNewTab = () => {
    if (selectedReference?.revision_s3_url) {
      window.open(selectedReference.revision_s3_url, "_blank");
    }
  };

  // Directly embed the PDF using an iframe as a fallback approach
  const EmbeddedPdfViewer = () => {
    if (!selectedReference?.revision_s3_url) return null;

    // Create a valid URL for PDF.js viewer
    const pdfViewerUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(
      selectedReference.revision_s3_url
    )}`;

    return (
      <iframe
        src={pdfViewerUrl}
        className="w-full h-full border-0"
        title="PDF Viewer"
      />
    );
  };

  // PDF Viewer component with highlighting functionality - UPDATED for new bbox format
 // Add these imports at the top of your file


const PdfViewer = () => {
  const [selectedText, setSelectedText] = useState('');
  const [showCopyTooltip, setShowCopyTooltip] = useState(false);
  const pdfContainerRef = useRef(null);

  if (!selectedReference || !selectedReference.revision_s3_url) {
    return <div className="p-6 text-center">No PDF URL available</div>;
  }

  // Handle text selection within the PDF
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim());
      setShowCopyTooltip(true);
      
      // Hide tooltip after 3 seconds
      setTimeout(() => setShowCopyTooltip(false), 3000);
    } else {
      setSelectedText('');
      setShowCopyTooltip(false);
    }
  }, []);

  // Copy selected text to clipboard
  const copyToClipboard = useCallback(async () => {
    if (selectedText) {
      try {
        await navigator.clipboard.writeText(selectedText);
        setShowCopyTooltip(false);
        // You can add a success notification here
      } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = selectedText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
    }
  }, [selectedText]);

  // Handle keyboard shortcuts (Ctrl+C)
  useEffect(() => {
    const handleKeyDown = (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'c' && selectedText) {
        event.preventDefault();
        copyToClipboard();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedText]);

  // Convert reference data to highlights format
  const highlights = selectedReference.positions
    ? selectedReference.positions
      .flatMap((position, posIndex) => {
        if (!position.bboxes || !Array.isArray(position.bboxes)) return [];

        return position.bboxes
          .map((bbox, bboxIndex) => {
            if (!Array.isArray(bbox) || bbox.length < 4) return null;

            return {
              position: {
                boundingRect: {
                  x1: bbox[0],
                  y1: bbox[1],
                  x2: bbox[2],
                  y2: bbox[3],
                  width: 600,
                  height: 840,
                  pageNumber: position.page,
                },
                rects: [
                  {
                    x1: bbox[0],
                    y1: bbox[1],
                    x2: bbox[2],
                    y2: bbox[3],
                    width: 600,
                    height: 840,
                    pageNumber: position.page,
                  },
                ],
                pageNumber: position.page,
              },
              content: { text: "" },
              comment: { text: "", emoji: "" },
              id: `pos-${posIndex}-bbox-${bboxIndex}`,
            };
          })
          .filter(Boolean);
      })
      .filter(Boolean)
    : [];

  return (
    <div className="flex flex-col h-full">
      {/* Copy Tooltip */}
      {showCopyTooltip && selectedText && (
        <div className="fixed top-4 right-4 z-50 bg-[#FF6B1C] text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
          <span className="text-sm">Text selected</span>
          <button
            onClick={copyToClipboard}
            className="bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded text-xs transition-colors"
          >
            Copy
          </button>
        </div>
      )}

      {/* Header section */}
      <div className="pt-6 pl-6 pr-6 pb-0 flex justify-between items-center bg-[#DDD1C5] z-20">
        <div>
          <h2 className="text-[14px] font-semibold text-[#494949]">
            {selectedReference.title || "Regulation Document"}
          </h2>
          <div className="text-[10px] text-[#7B7B7B] mt-1">
            {selectedReference.Type || "Master Direction"}&nbsp;•&nbsp;
            {Array.isArray(selectedReference.dept)
              ? selectedReference.dept[0]
              : selectedReference.dept || "RBI"}
            &nbsp;•&nbsp;
            {selectedReference.document_code || selectedReference.id || ""}
          </div>
        </div>
        <div className="flex items-center gap-6">
          <button
            onClick={handleDownloadPdf}
            className="text-[#7B7B7B] hover:text-[#494949] transition-colors duration-200"
            title="Download PDF"
          >
            <Download className="h-6 w-6" />
          </button>
          <button
            onClick={handleClosePdf}
            className="text-[#7B7B7B] hover:text-[#494949] transition-colors duration-200"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Sources navigation bar */}
      {docSources.length >= 1 && (
        <div className="flex justify-center items-center mt-2 mb-0">
          <div
            className="flex justify-center items-center py-1 px-5 bg-[#FF6B1C] text-white rounded-full"
            style={{ width: "150px" }}
          >
            <button
              onClick={() => handleSourceNavigation("prev")}
              className="p-1 hover:bg-[rgba(255,255,255,0.2)] rounded-full"
              disabled={docSources.length <= 1}
            >
              <ChevronUp className="h-4 w-4" />
            </button>

            <div className="flex items-center mx-0">
              <span className="text-sm font-medium">Highlights</span>
              <span className="ml-2 text-xs">
                {sourceIndex + 1}/{docSources.length}
              </span>
            </div>

            <button
              onClick={() => handleSourceNavigation("next")}
              className="p-1 hover:bg-[rgba(255,255,255,0.2)] rounded-full"
              disabled={docSources.length <= 1}
            >
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Container for PDF with copy functionality */}
      <div className="flex-1 flex m-6 mt-1">
        <div className="flex-1 rounded-xl shadow-sm border border-[#D7CBBC] bg-white overflow-hidden">
          {!pdfLoadFailed ? (
            <div
              ref={pdfContainerRef}
              className="w-full h-full pdf-container"
              style={{ 
                position: "relative",
                userSelect: "text", // Enable text selection
                WebkitUserSelect: "text" // Safari support
              }}
              onMouseUp={handleTextSelection}
              onKeyUp={handleTextSelection}
            >
              <div className="pdf-highlighter-container">
                <PdfLoader
                  url={selectedReference.revision_s3_url}
                  beforeLoad={
                    <div className="flex justify-center items-center h-full">
                      <div className="animate-pulse flex flex-col items-center">
                        <div className="h-12 w-12 bg-[#F8F0DF] rounded-full mb-4"></div>
                        <div className="text-[#7B7B7B]">Loading PDF...</div>
                      </div>
                    </div>
                  }
                  onError={handlePdfError}
                  errorMessage={
                    <div className="flex flex-col items-center justify-center h-full p-6">
                      <div className="bg-[#FFF8F2] p-6 rounded-md border border-[#FECB66] max-w-md text-center">
                        <h3 className="text-[#494949] font-medium mb-3">
                          Unable to load PDF
                        </h3>
                        <p className="text-[#7B7B7B] mb-4">
                          The document might be unavailable or in an
                          unsupported format.
                        </p>
                        <div className="flex justify-center gap-3">
                          <button
                            onClick={openPdfInNewTab}
                            className="px-3 py-2 bg-[#FF6B1C] text-white rounded-md hover:bg-opacity-90 transition-colors"
                          >
                            Open in New Tab
                          </button>
                          <button
                            onClick={handleDownloadPdf}
                            className="px-3 py-2 bg-[#EDE5DC] text-[#494949] rounded-md hover:bg-opacity-90 transition-colors"
                          >
                            Download PDF
                          </button>
                        </div>
                      </div>
                    </div>
                  }
                >
                  {(pdfDocument) => (
                    <div 
                      style={{ 
                        width: "100%", 
                        height: "100%",
                        userSelect: "text", // Enable text selection in PDF
                        WebkitUserSelect: "text"
                      }}
                    >
                      <TriggerComponent onLoad={scrollToHighlightFromHash} />
                      <PdfHighlighter
                        pdfDocument={pdfDocument}
                        enableAreaSelection={(event) => event.altKey}
                        onScrollChange={resetHash}
                        scrollRef={(scrollTo) => {
                          scrollViewerToRef.current = scrollTo;
                        }}
                        onSelectionFinished={(
                          position,
                          content,
                          hideTipAndSelection,
                          transformSelection
                        ) => {
                          // Allow default text selection behavior
                          return <></>;
                        }}
                        highlightTransform={(
                          highlight,
                          index,
                          setTip,
                          hideTip,
                          viewportToScaled,
                          screenshot,
                          isScrolledTo
                        ) => {
                          const isTextHighlight = !highlight.content?.image;
                          const component = isTextHighlight ? (
                            <Highlight
                              isScrolledTo={false}
                              position={highlight.position}
                              comment={highlight.comment}
                            />
                          ) : (
                            <AreaHighlight
                              isScrolledTo={false}
                              highlight={highlight}
                              onChange={() => { }}
                            />
                          );

                          return (
                            <Popup
                              popupContent={<></>}
                              onMouseOver={(popupContent) =>
                                setTip(highlight, () => popupContent)
                              }
                              onMouseOut={hideTip}
                              key={index}
                            >
                              {component}
                            </Popup>
                          );
                        }}
                        highlights={highlights}
                      />
                    </div>
                  )}
                </PdfLoader>
              </div>
            </div>
          ) : (
            // Enhanced fallback with copy support
            <div className="w-full h-full">
              <div 
                style={{ 
                  userSelect: "text",
                  WebkitUserSelect: "text"
                }}
                onMouseUp={handleTextSelection}
              >
                <EmbeddedPdfViewer />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

  // If showing the PDF view
  if (showPdf && selectedReference) {
    return (
      <aside
        style={{
          flex: "0 0 50%",
          maxWidth: "40%",
          transition: "all 0.3s ease-in-out",
          position: "relative",
          height: "100vh",
        }}
        className="bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D7CBBC] overflow-hidden flex flex-col"
      >
        <ScrollbarStyle />
        <PdfViewer />
      </aside>
    );
  }

  if (!showDetailView) {
    return (
      <aside className="w-[244px] bg-gradient-to- from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D1C3BE] p-6 pt-24 overflow-hidden transition-all duration-300">
        <ScrollbarStyle />
        <div className="h-full overflow-y-auto no-scrollbar">
          <h2 className="text-sm text-[#7B7B7B] font-medium mb-4">
            REGULATIONS FROM CHAT
          </h2>
          <div className="space-y-1">
            {groupedReferences && groupedReferences.length > 0 ? (
              // Display grouped references with click handler
              groupedReferences.map((ref, index) => (
                <div
                  key={index}
                  className="px-2 py-2 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer"
                  onClick={() => handleReferenceClick(ref)}
                >
                  <h4 className="text-sm font-medium text-[#494949]">
                    {ref.title}
                  </h4>
                 <p className="text-xs text-[#7B7B7B]">{ref.date_of_issue}</p>
                  <p className="text-[10px] text-[#7B7B7B] capitalize">
                    {ref.Type?.replace(/_/g, ' ') || 'Other'}
                    {ref._allChunks && ref._allChunks.length > 1 && (
                      <span> • {ref._allChunks.length} sources</span>
                    )}
                  </p>
                </div>
              ))
            ) : (
              <></>
            )}
          </div>
        </div>
      </aside>
    );
  }

  const sidebarStyle = {
    flex: "0 0 50%",
    maxWidth: "40%",
    transition: "all 0.3s ease-in-out",
  };

  return (
    <aside
      style={sidebarStyle}
      className="bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D7CBBC] overflow-hidden"
    >
      <ScrollbarStyle />
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-[#D7CBBC] flex justify-between items-center sticky top-0 bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] z-20">
          <div>
            <h2 className="text-sm font-semibold text-[#494949]">
              {activeRegulation.title}
            </h2>
            <div className="text-xs text-[#7B7B7B]">
              {activeRegulation.source} •{" "}
              {Array.isArray(activeRegulation.dept)
                ? activeRegulation.dept[0]
                : activeRegulation.department}
              {activeRegulation.id}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-[#7B7B7B] hover:text-[#494949]"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto no-scrollbar">
          <div className="p-6">
            <div className="bg-white rounded-xl p-6 border border-[#D7CBBC]">
              <div className="text-center mb-6">
                <div className="flex justify-center mb-4">
                  <div className="w-10 h-10 bg-[#FECB66] rounded-full"></div>
                </div>
                <div className="text-xs text-[#494949] mb-2 font-medium">
                  RESERVE BANK OF INDIA
                </div>
                <div className="text-xs font-medium text-[#7B7B7B]">
                  DEPARTMENT OF{" "}
                  {Array.isArray(activeRegulation.dept)
                    ? activeRegulation.dept[0]
                    : activeRegulation.department}
                </div>
                <div className="text-xs font-medium text-[#7B7B7B]">
                  MASTER CIRCULAR- 2025-26
                </div>
              </div>
              <div className="space-y-4">
                <h2 className="font-semibold text-[#494949]">
                  {activeRegulation.title}
                </h2>
                {formatContent(activeRegulation.content)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}
