import openai
from typing import List
from config import settings

class OpenaiAiClient:
    def __init__(self) -> None:
        openai.api_key = settings.OPENAI_API_KEY  # set key directly

    def completion(self, messages: List[dict], model: str = "gpt-4o") -> str:
        completion = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=0
        )
        response = completion.choices[0].message["content"].strip()
        return response

# Instantiate the AI client
ai_client = OpenaiAiClient()
