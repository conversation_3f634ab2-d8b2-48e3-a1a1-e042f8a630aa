#!/usr/bin/env python3
"""
Test script for SPLADE search functionality in the semantic retriever
"""

import sys
import logging
from unittest.mock import Mock, patch, MagicMock

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_splade_encoder():
    """Test SPLADE encoder functionality"""
    print("🧪 Testing SPLADE Encoder...")
    
    try:
        from semantic_retriever import SpladeEncoder
        
        # Initialize encoder
        encoder = SpladeEncoder()
        
        # Test encoding
        test_text = "Banking regulations require compliance with risk management procedures."
        splade_vector = encoder.encode(test_text)
        
        if splade_vector and "indices" in splade_vector and "values" in splade_vector:
            print(f"✅ SPLADE encoding successful")
            print(f"   - Indices: {len(splade_vector['indices'])}")
            print(f"   - Values: {len(splade_vector['values'])}")
            print(f"   - Sample indices: {splade_vector['indices'][:10]}")
            print(f"   - Sample values: {splade_vector['values'][:10]}")
            return True
        else:
            print("❌ SPLADE encoding failed - invalid output format")
            return False
            
    except Exception as e:
        print(f"❌ SPLADE encoder test failed: {e}")
        return False

def test_semantic_retriever_with_splade():
    """Test semantic retriever with SPLADE search"""
    print("\n🧪 Testing Semantic Retriever with SPLADE...")
    
    try:
        # Mock dependencies
        with patch('semantic_retriever.QdrantClient') as MockQdrantClient, \
             patch('semantic_retriever.AzureOpenAIEmbeddings') as MockEmbeddings, \
             patch('semantic_retriever.FastEmbedSparse') as MockSparse, \
             patch('semantic_retriever.completion_client') as mock_completion_client:
            
            # Setup mocks
            mock_client = MockQdrantClient.return_value
            mock_embeddings = MockEmbeddings.return_value
            mock_sparse = MockSparse.return_value
            mock_completion_client.chat_client = MagicMock()
            
            # Mock search results
            mock_search_result = Mock()
            mock_search_result.payload = {
                "page_content": "Test document content about banking regulations",
                "document_id": "TEST-001",
                "metadata": {"type": "regulation"}
            }
            mock_client.search.return_value = [mock_search_result]
            
            from semantic_retriever import SemanticRetriever
            
            # Initialize retriever
            retriever = SemanticRetriever(
                client=mock_client,
                collection="test_collection",
                embed_model="test-embed-model",
                chat_model="test-chat-model"
            )
            
            # Check SPLADE initialization
            if retriever.splade_encoder:
                print("✅ SPLADE encoder initialized in retriever")
                print(f"   - SPLADE enabled: {retriever.enable_splade_search}")
                print(f"   - SPLADE vector name: {retriever.splade_vector_name}")
            else:
                print("⚠️ SPLADE encoder not initialized (expected in test environment)")
            
            # Test search stats
            stats = retriever.get_search_stats()
            print(f"✅ Search stats retrieved: {stats}")
            
            # Test SPLADE enable/disable
            retriever.enable_splade(False)
            print(f"✅ SPLADE disabled: {retriever.enable_splade_search}")
            
            retriever.enable_splade(True)
            print(f"✅ SPLADE re-enabled: {retriever.enable_splade_search}")
            
            return True
            
    except Exception as e:
        print(f"❌ Semantic retriever test failed: {e}")
        return False

def test_splade_search_integration():
    """Test SPLADE search integration"""
    print("\n🧪 Testing SPLADE Search Integration...")
    
    try:
        # Mock the complete search flow
        with patch('semantic_retriever.QdrantClient') as MockQdrantClient, \
             patch('semantic_retriever.AzureOpenAIEmbeddings') as MockEmbeddings, \
             patch('semantic_retriever.FastEmbedSparse') as MockSparse, \
             patch('semantic_retriever.completion_client') as mock_completion_client, \
             patch('semantic_retriever.SpladeEncoder') as MockSpladeEncoder:
            
            # Setup mocks
            mock_client = MockQdrantClient.return_value
            mock_embeddings = MockEmbeddings.return_value
            mock_sparse = MockSparse.return_value
            mock_completion_client.chat_client = MagicMock()
            
            # Mock SPLADE encoder
            mock_splade_encoder = MockSpladeEncoder.return_value
            mock_splade_encoder.encode.return_value = {
                "indices": [1, 5, 10, 15, 20],
                "values": [0.8, 0.6, 0.4, 0.3, 0.2]
            }
            
            # Mock search results
            mock_search_result = Mock()
            mock_search_result.payload = {
                "page_content": "Banking regulation document about compliance requirements",
                "document_id": "REG-001",
                "positions": [{"page": 1, "bbox": [100, 200, 300, 400]}]
            }
            mock_client.search.return_value = [mock_search_result]
            
            # Mock vector store search results
            from langchain.schema import Document
            mock_doc = Document(
                page_content="Test document content",
                metadata={"document_id": "DOC-001"}
            )
            
            from semantic_retriever import SemanticRetriever
            
            # Initialize retriever
            retriever = SemanticRetriever(
                client=mock_client,
                collection="test_collection",
                embed_model="test-embed-model",
                chat_model="test-chat-model"
            )
            
            # Mock vector stores to return documents
            for dt, vs in retriever.vector_stores.items():
                mock_retriever = Mock()
                mock_retriever.invoke.return_value = [mock_doc]
                vs.as_retriever.return_value = mock_retriever
            
            # Test search with SPLADE
            test_query = "What are the compliance requirements for banks?"
            
            # Test individual search methods
            print("   Testing hybrid search...")
            hybrid_results = retriever._run_hybrid_search(test_query)
            print(f"   ✅ Hybrid search returned {len(hybrid_results)} results")
            
            print("   Testing SPLADE search...")
            splade_results = retriever._run_splade_search(test_query)
            print(f"   ✅ SPLADE search returned {len(splade_results)} results")
            
            print("   Testing multi-query search...")
            multi_query_results = retriever._run_multi_query_search(test_query)
            print(f"   ✅ Multi-query search returned {len(multi_query_results)} results")
            
            # Test full search
            print("   Testing full search with all strategies...")
            results, positions = retriever.run(test_query)
            print(f"   ✅ Full search returned {len(results)} unique results")
            print(f"   ✅ Position info for {len(positions)} documents")
            
            return True
            
    except Exception as e:
        print(f"❌ SPLADE search integration test failed: {e}")
        return False

def test_splade_vector_format():
    """Test SPLADE vector format compatibility"""
    print("\n🧪 Testing SPLADE Vector Format...")
    
    try:
        # Test vector format
        sample_splade_vector = {
            "indices": [1, 5, 10, 15, 20, 25],
            "values": [0.8, 0.6, 0.5, 0.4, 0.3, 0.2]
        }
        
        # Validate format
        if "indices" in sample_splade_vector and "values" in sample_splade_vector:
            indices = sample_splade_vector["indices"]
            values = sample_splade_vector["values"]
            
            if len(indices) == len(values):
                print("✅ SPLADE vector format is valid")
                print(f"   - Vector dimension: {len(indices)}")
                print(f"   - Index range: {min(indices)} to {max(indices)}")
                print(f"   - Value range: {min(values):.3f} to {max(values):.3f}")
                return True
            else:
                print("❌ SPLADE vector format invalid - indices and values length mismatch")
                return False
        else:
            print("❌ SPLADE vector format invalid - missing indices or values")
            return False
            
    except Exception as e:
        print(f"❌ SPLADE vector format test failed: {e}")
        return False

def main():
    """Run all SPLADE search tests"""
    print("🚀 Starting SPLADE Search Tests\n")
    
    tests = [
        ("SPLADE Encoder", test_splade_encoder),
        ("Semantic Retriever with SPLADE", test_semantic_retriever_with_splade),
        ("SPLADE Search Integration", test_splade_search_integration),
        ("SPLADE Vector Format", test_splade_vector_format),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 SPLADE SEARCH TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All SPLADE search tests passed!")
        print("\n💡 SPLADE Search Features:")
        print("   ✅ Neural sparse vectors for enhanced retrieval")
        print("   ✅ Parallel search with dense + BM25 + SPLADE")
        print("   ✅ Configurable enable/disable functionality")
        print("   ✅ Integration with existing semantic retriever")
    else:
        print("⚠️ Some tests failed. Check the implementation.")

if __name__ == "__main__":
    main()
