

# Standard library imports
import logging
import os
import sys
import tempfile
import uuid
from datetime import datetime, timedelta
from pathlib import Path

# Third-party imports
import boto3
import feedparser
import requests
from bs4 import BeautifulSoup
from dateutil import parser
from pymongo import MongoClient
from qdrant_client import models
from langchain_qdrant import QdrantVectorStore, RetrievalMode

# Airflow imports
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator
from airflow.utils.task_group import TaskGroup

# Local utility imports
sys.path.append(str(Path(__file__).parent / "utils"))
from utils.pdf_utils import (
    parse_pdf_to_html_ast,
    chunk_html_ast
)
from utils.qdrant_utils import qdrant_manager
from utils.database_utils import db_manager
from utils.openai_utils import openai_manager, strip_data_attributes, en_embeddings

# Prompt and model imports
from prompts.notification_categorizer import (
    NotificationCategorizationResult,
    AffectedDocumentsResult,
    UpdateActionResult,
    NOTIFICATION_CATEGORIZER_PROMPT,
    AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT,
    UPDATE_ACTION_DETERMINER_PROMPT
)

# Configuration
SPLADE_VECTOR_NAME = "fast-sparse-bm25-splade"


def download_tls_ca_file():
    """Download TLS CA file for DocumentDB connection"""
    try:
        import requests
        response = requests.get(
            "https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        response.raise_for_status()
        with open("/tmp/global-bundle.pem", "wb") as file:
            file.write(response.content)
        logger.info("TLS CA file downloaded successfully")
    except Exception as e:
        logger.error(f"Failed to download TLS CA file: {e}")
        raise


# Qdrant Logging Utilities
def log_qdrant_operation_start(operation_type: str, collection: str, document_id: str = None, chunks_count: int = None):
    """Log the start of a Qdrant operation with comprehensive details"""
    logger.info(f"🚀 QDRANT OPERATION START: {operation_type}")
    logger.info(f"   📊 Collection: {collection}")
    if document_id:
        logger.info(f"   📄 Document ID: {document_id}")
    if chunks_count:
        logger.info(f"   📦 Chunks: {chunks_count}")
    logger.info(f"   🔗 Vector DB Host: {Variable.get('vector_db_host', 'localhost')}")
    logger.info(f"   🎯 Vector Types: Dense (OpenAI 1536d) + Sparse (BM25) + SPLADE")
    logger.info(f"   ⚙️ Method: {operation_type}")

def log_qdrant_operation_end(operation_type: str, success: bool, collection: str, document_id: str = None, chunks_count: int = None, error: str = None):
    """Log the end of a Qdrant operation with results"""
    status = "✅ SUCCESS" if success else "❌ FAILED"
    logger.info(f"🏁 QDRANT OPERATION END: {operation_type} - {status}")
    logger.info(f"   📊 Collection: {collection}")
    if document_id:
        logger.info(f"   📄 Document ID: {document_id}")
    if chunks_count:
        logger.info(f"   📦 Chunks Processed: {chunks_count}")
    if error:
        logger.error(f"   ❌ Error: {error}")

# Qdrant Search Functions
def search_qdrant_with_title_filter(collection_name: str, query_text: str, title_to_match: str,
                                   limit: int = 10, score_threshold: float = 0.6) -> list:
    """
    Search Qdrant collection with vector similarity and exact title filter.

    This function replaces $regex usage with exact match filters as required by Qdrant.

    Args:
        collection_name: Name of the Qdrant collection to search
        query_text: Text to generate query vector from
        title_to_match: Exact title to match (e.g., "DoR.SOG (DEA Fund) No.37/30.01.002/2025-26")
        limit: Maximum number of results to return
        score_threshold: Minimum similarity score threshold

    Returns:
        List of search results with payload and scores
    """
    try:

        logger.info(f"🔍 QDRANT SEARCH WITH TITLE FILTER")
        logger.info(f"   📊 Collection: {collection_name}")
        logger.info(f"   🎯 Title filter: {title_to_match}")
        logger.info(f"   📝 Query: {query_text[:100]}...")
        logger.info(f"   📏 Limit: {limit}, Threshold: {score_threshold}")

        # Generate query vector for semantic search
        query_vector = en_embeddings.embed_query(query_text)
        logger.info(f"   🔢 Query vector generated (dimension: {len(query_vector)})")

        # Create exact match filter for metadata.title (no $regex)
        title_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.title",
                    match=models.MatchValue(value=title_to_match)
                )
            ]
        )

        logger.info(f"   🔧 Filter created: exact match on metadata.title")

        # Perform vector search with filter
        search_results = qdrant_manager.client.search(
            collection_name=collection_name,
            query_vector=query_vector,
            query_filter=title_filter,
            limit=limit,
            score_threshold=score_threshold,
            with_payload=True,
            with_vectors=False  # Don't return vectors to save bandwidth
        )

        logger.info(f"   ✅ Search completed: {len(search_results)} results found")

        # Convert results to a more usable format
        formatted_results = []
        for result in search_results:
            formatted_results.append({
                "id": result.id,
                "score": result.score,
                "payload": result.payload,
                "metadata": result.payload.get("metadata", {}),
                "content": result.payload.get("page_content", "")
            })

        return formatted_results

    except Exception as e:
        logger.error(f"❌ Error in Qdrant search with title filter: {e}")
        logger.error(f"   Collection: {collection_name}")
        logger.error(f"   Title: {title_to_match}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return []


def search_qdrant_with_multiple_filters(collection_name: str, query_text: str, filters: dict,
                                       limit: int = 10, score_threshold: float = 0.6) -> list:
    """
    Search Qdrant collection with vector similarity and multiple exact match filters.

    Example usage:
        filters = {
            "metadata.title": "DoR.SOG (DEA Fund) No.37/30.01.002/2025-26",
            "metadata.document_type": "master_direction",
            "metadata.published_date": "2025-01-15"
        }
        results = search_qdrant_with_multiple_filters("rbi_master_direction", "query text", filters)

    Args:
        collection_name: Name of the Qdrant collection to search
        query_text: Text to generate query vector from
        filters: Dictionary of field->value pairs for exact matching
        limit: Maximum number of results to return
        score_threshold: Minimum similarity score threshold

    Returns:
        List of search results with payload and scores
    """
    try:

        logger.info(f"🔍 QDRANT SEARCH WITH MULTIPLE FILTERS")
        logger.info(f"   📊 Collection: {collection_name}")
        logger.info(f"   🎯 Filters: {filters}")
        logger.info(f"   📝 Query: {query_text[:100]}...")

        # Generate query vector for semantic search
        query_vector = en_embeddings.embed_query(query_text)

        # Create exact match filters (no $regex)
        filter_conditions = []
        for field_key, field_value in filters.items():
            filter_conditions.append(
                models.FieldCondition(
                    key=field_key,
                    match=models.MatchValue(value=field_value)
                )
            )

        combined_filter = models.Filter(must=filter_conditions)

        logger.info(f"   🔧 Created {len(filter_conditions)} exact match conditions")

        # Perform vector search with filters
        search_results = qdrant_manager.client.search(
            collection_name=collection_name,
            query_vector=query_vector,
            query_filter=combined_filter,
            limit=limit,
            score_threshold=score_threshold,
            with_payload=True,
            with_vectors=False
        )

        logger.info(f"   ✅ Search completed: {len(search_results)} results found")

        # Convert results to a more usable format
        formatted_results = []
        for result in search_results:
            formatted_results.append({
                "id": result.id,
                "score": result.score,
                "payload": result.payload,
                "metadata": result.payload.get("metadata", {}),
                "content": result.payload.get("page_content", "")
            })

        return formatted_results

    except Exception as e:
        logger.error(f"❌ Error in Qdrant search with multiple filters: {e}")
        logger.error(f"   Collection: {collection_name}")
        logger.error(f"   Filters: {filters}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return []


# Airflow Variables
vector_db_host = Variable.get("vector_db_host", default_var="localhost")
if vector_db_host:
    vector_db_host = vector_db_host.strip().replace('\r', '').replace('\n', '')

mongodb_uri = Variable.get("documentdb_uri", default_var="mongodb://localhost:27017/")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# --- ENHANCED LOGGING AND MONITORING ---

class DAGMonitor:
    """Comprehensive monitoring and logging for RSS Feed ETL DAG"""

    def __init__(self):
        self.session_id = str(uuid.uuid4())[:8]
        self.start_time = datetime.now()
        self.metrics = {
            "rss_processing": {
                "feeds_processed": 0,
                "articles_scraped": 0,
                "articles_failed": 0,
                "pdfs_downloaded": 0,
                "s3_uploads": 0
            },
            "llm_analysis": {
                "notifications_analyzed": 0,
                "kb_updates_required": 0,
                "analysis_failures": 0
            },
            "kb_updates": {
                "documents_updated": 0,
                "documents_removed": 0,
                "documents_added": 0,
                "temporary_notes_added": 0,
                "update_failures": 0
            },
            "vector_processing": {
                "chunks_processed": 0,
                "qdrant_upserts": 0,
                "mongo_upserts": 0,
                "processing_failures": 0
            }
        }

    def log_session_start(self, dag_run_id: str = None):
        """Log the start of a DAG session"""
        logger.info("=" * 80)
        logger.info(f"🚀 RSS FEED ETL DAG SESSION STARTED")
        logger.info(f"   Session ID: {self.session_id}")
        logger.info(f"   Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if dag_run_id:
            logger.info(f"   DAG Run ID: {dag_run_id}")
        logger.info("=" * 80)

    def log_session_end(self):
        """Log the end of a DAG session with comprehensive summary"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        logger.info("=" * 80)
        logger.info(f"🏁 RSS FEED ETL DAG SESSION COMPLETED")
        logger.info(f"   Session ID: {self.session_id}")
        logger.info(f"   Duration: {duration}")
        logger.info(f"   End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("")
        logger.info("📊 SESSION METRICS:")

        for category, metrics in self.metrics.items():
            logger.info(f"   {category.upper()}:")
            for metric, value in metrics.items():
                logger.info(f"     - {metric}: {value}")

        # Calculate success rates
        total_articles = self.metrics["rss_processing"]["articles_scraped"]
        if total_articles > 0:
            success_rate = ((total_articles - self.metrics["rss_processing"]["articles_failed"]) / total_articles) * 100
            logger.info(f"   OVERALL SUCCESS RATE: {success_rate:.1f}%")

        logger.info("=" * 80)

    def log_task_start(self, task_name: str):
        """Log the start of a specific task"""
        logger.info(f"🔄 Starting task: {task_name} (Session: {self.session_id})")

    def log_task_end(self, task_name: str, success: bool = True, details: str = ""):
        """Log the end of a specific task"""
        status = "✅ COMPLETED" if success else "❌ FAILED"
        logger.info(f"{status}: {task_name} (Session: {self.session_id})")
        if details:
            logger.info(f"   Details: {details}")

    def increment_metric(self, category: str, metric: str, value: int = 1):
        """Increment a specific metric"""
        if category in self.metrics and metric in self.metrics[category]:
            self.metrics[category][metric] += value

    def log_notification_analysis(self, title: str, category: str, confidence: str, requires_update: bool):
        """Log notification analysis results"""
        self.increment_metric("llm_analysis", "notifications_analyzed")
        if requires_update:
            self.increment_metric("llm_analysis", "kb_updates_required")

        logger.info(f"🔍 Notification Analysis:")
        logger.info(f"   Title: {title[:60]}...")
        logger.info(f"   Category: {category}")
        logger.info(f"   Confidence: {confidence}")
        logger.info(f"   Requires KB Update: {requires_update}")

    def log_kb_update_action(self, action_type: str, target: str, success: bool, details: str = ""):
        """Log knowledge base update actions"""
        metric_map = {
            "UPDATE_DOCUMENT": "documents_updated",
            "REMOVE_DOCUMENT": "documents_removed",
            "ADD_DOCUMENT": "documents_added",
            "ADD_TEMPORARY_NOTE": "temporary_notes_added"
        }

        if success and action_type in metric_map:
            self.increment_metric("kb_updates", metric_map[action_type])
        elif not success:
            self.increment_metric("kb_updates", "update_failures")

        status = "✅" if success else "❌"
        logger.info(f"{status} KB Update: {action_type} for {target}")
        if details:
            logger.info(f"   Details: {details}")

    def log_vector_processing(self, article_id: str, chunks_count: int, success: bool):
        """Log vector processing results"""
        if success:
            self.increment_metric("vector_processing", "chunks_processed", chunks_count)
            self.increment_metric("vector_processing", "qdrant_upserts", chunks_count)
            self.increment_metric("vector_processing", "mongo_upserts", chunks_count)
        else:
            self.increment_metric("vector_processing", "processing_failures")

        status = "✅" if success else "❌"
        logger.info(f"{status} Vector Processing: {article_id} ({chunks_count} chunks)")

    def log_error_with_context(self, error: Exception, context: dict = None):
        """Log errors with additional context"""
        logger.error(f"❌ ERROR in session {self.session_id}: {str(error)}")
        if context:
            logger.error(f"   Context: {context}")

        # Log stack trace for debugging
        import traceback
        logger.error(f"   Stack trace: {traceback.format_exc()}")


# Create global monitor instance
dag_monitor = DAGMonitor()


def categorize_document(title):
    """Categorize document based on title and return collection name"""
    from utils.document_pipeline import DocumentTypeMapper
    return DocumentTypeMapper.categorize_document_title(title)


# --- LLM-BASED NOTIFICATION PROCESSING ---

class NotificationProcessor:
    """Handles LLM-based notification processing and categorization"""

    def __init__(self):
        self.client = openai_manager.get_client()
        self.max_content_length = 2000

    def _make_llm_call(self, prompt: str, response_format, system_message: str = "You are an expert RBI regulatory analyst."):
        """Make a structured LLM call with error handling"""
        try:
            logger.info(f"Making LLM call with model: gpt-4.1-mini-2025-04-14")
            response = openai_manager.with_key_rotation(
                lambda: self.client.beta.chat.completions.parse(
                    model="gpt-4.1-mini-2025-04-14",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,
                    response_format=response_format
                )
            )
            result = response.choices[0].message.parsed.model_dump()
            logger.info(f"LLM call successful, result: {result}")
            return result
        except Exception as e:
            logger.error(f"LLM call failed with error: {e}")
            logger.error(f"Prompt was: {prompt[:200]}...")
            raise

    def analyze_notification(self, title: str, rss_description: str, link: str) -> dict:
        """
        Categorize RBI notification and determine its impact on knowledge base
        """
        try:
            logger.info(f"🔍 Starting notification analysis for: {title[:50]}...")
            prompt = NOTIFICATION_CATEGORIZER_PROMPT.format(
                title=title,
                content=rss_description[:self.max_content_length],
                link=link
            )

            logger.info(f"📝 Prompt created, making LLM call...")
            result = self._make_llm_call(prompt, NotificationCategorizationResult)
            logger.info(f"✅ Notification categorized as: {result.get('category')} (confidence: {result.get('confidence')})")
            return result

        except Exception as e:
            logger.error(f"❌ Error in notification analysis for '{title[:50]}...': {e}")
            logger.error(f"📋 Falling back to default categorization")
            return self._get_fallback_categorization()

    def _get_fallback_categorization(self) -> dict:
        """Return fallback categorization when LLM analysis fails"""
        return {
            "category": "Informational",
            "confidence": "low",
            "reasoning": "LLM analysis failed, using fallback",
            "affects_regulations": False,
            "keywords_found": [],
            "requires_kb_update": False
        }


    def extract_affected_documents(self, title: str, rss_description: str, category: str) -> dict:
        """
        Extract specific documents affected by the notification with their update actions
        Enhanced with anchor information extraction
        """
        try:
            logger.info(f"🔍 EXTRACTING AFFECTED DOCUMENTS")
            logger.info(f"   📋 Title: {title[:80]}...")
            logger.info(f"   📂 Category: {category}")
            logger.info(f"   📄 Description length: {len(rss_description)} chars")
            logger.info(f"   ✂️ Content truncated to: {self.max_content_length} chars")

            # Extract anchor information before processing
            anchor_info = extract_anchor_information(rss_description)
            logger.info(f"   🔗 Anchor extraction: {len(anchor_info['anchors'])} anchors, "
                       f"{len(anchor_info['reference_numbers'])} references, "
                       f"{len(anchor_info['pdf_links'])} PDF links")

            # Enhance document links by following RBI pages if needed
            enhanced_anchor_info = enhance_document_links(anchor_info)
            logger.info(f"   📄 Enhanced links: {len(enhanced_anchor_info['pdf_links'])} total PDF links")

            # Enhance the content with anchor information for LLM processing
            enhanced_content = rss_description[:self.max_content_length]

            # Add critical URL instruction header
            enhanced_content += f"\n\n=== EXTRACTED INFORMATION FOR PROCESSING ==="

            if enhanced_anchor_info['reference_numbers']:
                enhanced_content += f"\nEXTRACTED_REFERENCE_NUMBERS: {', '.join(enhanced_anchor_info['reference_numbers'])}"

            if enhanced_anchor_info['pdf_links']:
                enhanced_content += f"\nEXTRACTED_PDF_LINKS: {', '.join(enhanced_anchor_info['pdf_links'][:3])}"  # Limit to first 3
                enhanced_content += f"\n*** USE THESE EXACT URLs FOR new_document_url - DO NOT MODIFY ***"
            else:
                enhanced_content += f"\nEXTRACTED_PDF_LINKS: (none found)"
                enhanced_content += f"\n*** NO PDF LINKS AVAILABLE - LEAVE new_document_url EMPTY ***"

            if enhanced_anchor_info['rbi_page_urls']:
                enhanced_content += f"\nEXTRACTED_RBI_PAGE_URLS: {', '.join(enhanced_anchor_info['rbi_page_urls'][:2])}"  # Limit to first 2

            # Add comprehensive scraped links information
            if enhanced_anchor_info.get('all_scraped_links'):
                enhanced_content += f"\n\n=== COMPREHENSIVE SCRAPED LINKS FROM RBI PAGES ==="
                for i, scraped_data in enumerate(enhanced_anchor_info['all_scraped_links'][:2]):  # Limit to first 2 pages
                    enhanced_content += f"\n--- PAGE {i+1} SCRAPED LINKS ---"

                    if scraped_data['pdf_links']:
                        enhanced_content += f"\nPDF_LINKS_FOUND:"
                        for pdf_info in scraped_data['pdf_links'][:5]:  # Limit to first 5 PDFs
                            enhanced_content += f"\n  • '{pdf_info['text']}' -> {pdf_info['url']}"
                            if pdf_info['context']:
                                enhanced_content += f" (Context: {pdf_info['context'][:100]}...)"

                    if scraped_data['rbi_links']:
                        enhanced_content += f"\nOTHER_RBI_LINKS:"
                        for link_info in scraped_data['rbi_links'][:3]:  # Limit to first 3
                            enhanced_content += f"\n  • '{link_info['text']}' -> {link_info['url']}"

                    if scraped_data['page_title']:
                        enhanced_content += f"\nPAGE_TITLE: {scraped_data['page_title']}"

                enhanced_content += f"\n*** IMPORTANT: Use the exact PDF URLs from above for new_document_url ***"
                enhanced_content += f"\n*** Choose the most relevant PDF based on the notification content ***"

            if enhanced_anchor_info['anchors']:
                anchor_texts = [f"'{a['text']}' -> {a['href']}" for a in enhanced_anchor_info['anchors'][:5]]  # Limit to first 5
                enhanced_content += f"\nEXTRACTED_ANCHOR_INFO: {'; '.join(anchor_texts)}"

            enhanced_content += f"\n=== END EXTRACTED INFORMATION ==="

            prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                title=title,
                content=enhanced_content,
                category=category
            )

            logger.info(f"   📝 Prompt length: {len(prompt)} chars")
            logger.info(f"   🤖 Making LLM call for affected documents extraction...")

            result = self._make_llm_call(
                prompt,
                AffectedDocumentsResult,
                "You are an expert in RBI regulatory document analysis and knowledge base management. You specialize in identifying affected documents from regulatory notifications and determining precise update actions required for maintaining an accurate regulatory knowledge base."
            )

            # Enhanced logging for affected documents result
            document_actions = result.get('document_actions', [])
            document_keywords = result.get('document_keywords', [])
            rbi_links = result.get('rbi_links', [])

            logger.info(f"✅ AFFECTED DOCUMENTS EXTRACTION COMPLETE")
            logger.info(f"   📊 Document actions found: {len(document_actions)}")
            logger.info(f"   🔑 Document keywords: {len(document_keywords)} - {document_keywords[:5]}")
            logger.info(f"   🔗 RBI links found: {len(rbi_links)}")
            logger.info(f"   📄 Has new document link: {result.get('has_new_document_link', False)}")
            logger.info(f"   ⚠️ Requires manual review: {result.get('requires_manual_review', False)}")

            # Handle case where no document actions are found
            if not document_actions:
                logger.warning(f"⚠️ No document actions found in LLM result")

                # Check if this should have document actions based on content
                if any(keyword in title.lower() for keyword in ['direction', 'circular', 'master', 'notification']):
                    logger.info(f"🔧 Creating fallback ADD_DOCUMENT action for regulatory document")

                    # Create a fallback ADD_DOCUMENT action
                    fallback_action = {
                        'action_type': 'ADD_DOCUMENT',
                        'document_id': enhanced_anchor_info.get('reference_numbers', [title])[0] if enhanced_anchor_info.get('reference_numbers') else title,
                        'document_title': title,
                        'confidence': 'medium',
                        'reasoning': 'Fallback action created for regulatory document with missing LLM actions'
                    }

                    # Add reference number if available
                    if enhanced_anchor_info.get('reference_numbers'):
                        fallback_action['reference_number'] = enhanced_anchor_info['reference_numbers'][0]
                        if len(enhanced_anchor_info['reference_numbers']) > 1:
                            # Combine multiple references with semicolon
                            fallback_action['reference_number'] = '; '.join(enhanced_anchor_info['reference_numbers'])

                    result['document_actions'] = [fallback_action]
                    logger.info(f"   ✅ Created fallback action: {fallback_action['action_type']} for {fallback_action['document_id']}")

                elif enhanced_anchor_info.get('notification_pdf_url'):
                    logger.info(f"🔍 No document actions but found notification PDF, processing content")
                    result = self._process_notification_pdf_fallback(result, enhanced_anchor_info, title, rss_description)

            # Apply validation and confidence enhancement
            validated_result = self._validate_and_enhance_document_actions(result, enhanced_anchor_info, title)

            # Log each document action in detail
            validated_actions = validated_result.get('document_actions', [])
            for i, action in enumerate(validated_actions):
                logger.info(f"   📋 Document Action {i+1}:")
                logger.info(f"      - Document ID: {action.get('document_id', 'N/A')}")
                logger.info(f"      - Action Type: {action.get('action_type', 'N/A')}")
                logger.info(f"      - Document Title: {action.get('document_title', 'N/A')[:50]}...")
                logger.info(f"      - Reference Number: {action.get('reference_number', 'N/A')}")
                logger.info(f"      - Confidence: {action.get('confidence', 'N/A')}")
                logger.info(f"      - Update Location: {action.get('update_location', 'N/A')}")
                logger.info(f"      - Reasoning: {action.get('reasoning', 'N/A')[:60]}...")

            return validated_result

        except Exception as e:
            logger.error(f"❌ ERROR EXTRACTING AFFECTED DOCUMENTS: {e}")
            logger.error(f"   📋 Title: {title[:50]}...")
            logger.error(f"   📂 Category: {category}")
            import traceback
            logger.error(f"   📚 Stack trace: {traceback.format_exc()}")
            return self._get_fallback_affected_documents()

    def _get_fallback_affected_documents(self) -> dict:
        """Return fallback when affected documents extraction fails"""
        return {
            "document_actions": [],
            "document_keywords": [],
            "has_new_document_link": False,
            "new_document_url": "",
            "rbi_links": [],
            "processing_notes": "LLM analysis failed",
            "requires_manual_review": True
        }

    def _validate_and_enhance_document_actions(self, result: dict, anchor_info: dict, title: str) -> dict:
        """
        Validate and enhance document actions with improved confidence scoring and validation gates
        """
        try:
            logger.info(f"🔍 VALIDATING AND ENHANCING DOCUMENT ACTIONS")

            document_actions = result.get('document_actions', [])
            enhanced_actions = []
            validation_issues = []

            for i, action in enumerate(document_actions):
                enhanced_action = dict(action)  # Copy the action
                action_type = action.get('action_type', '')
                reference_number = action.get('reference_number', '')
                original_date = action.get('original_date', '')
                confidence = action.get('confidence', 'medium')

                # Validation Gate 1: Critical fields for UPDATE/REMOVE/ADD actions
                if action_type in ['UPDATE_DOCUMENT', 'REMOVE_DOCUMENT', 'ADD_DOCUMENT']:
                    if not reference_number:
                        # Try to find reference from anchor info
                        if anchor_info['reference_numbers']:
                            enhanced_action['reference_number'] = anchor_info['reference_numbers'][0]
                            enhanced_action['confidence'] = 'medium'  # Downgrade confidence
                            logger.info(f"   ✅ Enhanced action {i+1} with reference from anchors: {anchor_info['reference_numbers'][0]}")
                        else:
                            validation_issues.append(f"Action {i+1}: Missing reference_number for {action_type}")
                            enhanced_action['confidence'] = 'low'
                            enhanced_action['requires_manual_review'] = True

                    if not original_date:
                        validation_issues.append(f"Action {i+1}: Missing original_date for {action_type}")
                        enhanced_action['confidence'] = 'medium' if confidence == 'high' else 'low'

                # Validation Gate 2: FEMA amendment specific validation
                document_id = action.get('document_id', '')
                if 'FEMA' in document_id and '(' in document_id and ')' in document_id:
                    # This looks like a FEMA amendment - should have related principal document
                    if action_type == 'ADD_DOCUMENT':
                        # Check if there's a corresponding UPDATE_DOCUMENT for the principal
                        principal_ref = document_id.replace('/(', '/').split('/(')[0] + '-RB'
                        has_principal_update = any(
                            a.get('action_type') == 'UPDATE_DOCUMENT' and principal_ref in a.get('document_id', '')
                            for a in document_actions
                        )
                        if not has_principal_update:
                            validation_issues.append(f"Action {i+1}: FEMA amendment missing corresponding UPDATE_DOCUMENT for principal regulation")
                            enhanced_action['requires_manual_review'] = True

                # Confidence Enhancement Rules
                if reference_number and original_date and action_type in ['UPDATE_DOCUMENT', 'REMOVE_DOCUMENT']:
                    # Check for exact pattern matches in title/content
                    pattern_keywords = [
                        'reference is invited', 'will stand withdrawn', 'shall stand withdrawn',
                        'Previous Guidelines superseded', 'hereby superseded', 'sunset clause'
                    ]

                    title_lower = title.lower()
                    if any(keyword in title_lower for keyword in pattern_keywords):
                        enhanced_action['confidence'] = 'high'
                        logger.info(f"   ⬆️ Upgraded action {i+1} confidence to HIGH (pattern match)")

                # Add validation metadata
                enhanced_action['validation_status'] = 'passed' if not validation_issues else 'issues_found'
                enhanced_action['validation_notes'] = '; '.join(validation_issues) if validation_issues else 'All validations passed'

                enhanced_actions.append(enhanced_action)

            # Update result with enhanced actions
            enhanced_result = dict(result)
            enhanced_result['document_actions'] = enhanced_actions

            # Enhance with link information from anchor_info
            if anchor_info.get('pdf_links'):
                enhanced_result['pdf_links'] = anchor_info['pdf_links']
                if not enhanced_result.get('new_document_url') and anchor_info['pdf_links']:
                    enhanced_result['new_document_url'] = anchor_info['pdf_links'][0]
                    logger.info(f"   📄 Enhanced with PDF link: {anchor_info['pdf_links'][0]}")

            # Add notification PDF URL for potential processing
            if anchor_info.get('notification_pdf_url'):
                enhanced_result['notification_pdf_url'] = anchor_info['notification_pdf_url']
                logger.info(f"   📋 Enhanced with notification PDF: {anchor_info['notification_pdf_url']}")

            # Fix has_new_document_link flag logic
            has_pdf_url = bool(enhanced_result.get('new_document_url') or enhanced_result.get('notification_pdf_url'))
            enhanced_result['has_new_document_link'] = has_pdf_url
            if has_pdf_url:
                logger.info(f"   ✅ Set has_new_document_link=True (found PDF URLs)")
            else:
                logger.info(f"   ❌ Set has_new_document_link=False (no PDF URLs found)")

            # Generate accurate processing notes
            processing_notes_parts = []
            if result.get('processing_notes'):
                processing_notes_parts.append(result.get('processing_notes'))

            # Add validation summary
            if validation_issues:
                processing_notes_parts.append(f"Validation issues: {len(validation_issues)}")
                enhanced_result['requires_manual_review'] = True
                logger.warning(f"   ⚠️ Validation issues found: {len(validation_issues)}")
                for issue in validation_issues:
                    logger.warning(f"      - {issue}")
            else:
                processing_notes_parts.append("All validations passed")
                logger.info(f"   ✅ All validations passed for {len(enhanced_actions)} actions")

            # Add anchor processing summary
            if anchor_info.get('reference_numbers'):
                processing_notes_parts.append(f"Extracted {len(anchor_info['reference_numbers'])} reference numbers")
            if anchor_info.get('pdf_links'):
                processing_notes_parts.append(f"Found {len(anchor_info['pdf_links'])} PDF links")
            if anchor_info.get('rbi_page_urls'):
                processing_notes_parts.append(f"Found {len(anchor_info['rbi_page_urls'])} RBI page URLs")

            enhanced_result['processing_notes'] = " | ".join(processing_notes_parts)

            return enhanced_result

        except Exception as e:
            logger.error(f"❌ Error in validation and enhancement: {e}")
            # Return original result if validation fails
            result['requires_manual_review'] = True
            result['processing_notes'] = f"{result.get('processing_notes', '')} | Validation failed: {str(e)}"
            return result



    def _process_notification_pdf_fallback(self, result, anchor_info, title, rss_description):
        """
        Process the notification PDF itself when no additional documents are found
        """
        try:
            notification_pdf_url = anchor_info.get('notification_pdf_url')
            if not notification_pdf_url:
                return result

            logger.info(f"📄 Processing notification PDF as fallback: {notification_pdf_url}")

            # Download and extract PDF content
            pdf_content = download_and_extract_pdf_content(notification_pdf_url, max_chars=12000)

            if pdf_content:
                # Enhanced prompt for notification PDF analysis
                enhanced_prompt = f"""
                You are analyzing an RBI notification PDF directly since no additional regulatory documents were identified for update/removal.

                **TASK**: Extract related document IDs and determine if this notification itself should be processed as a new document.

                **NOTIFICATION DETAILS:**
                Title: {title}
                RSS Description: {rss_description[:1000]}

                **PDF CONTENT** (first 12,000 characters):
                {pdf_content}

                **INSTRUCTIONS:**
                1. Look for references to existing regulatory documents (circular numbers, master directions, etc.)
                2. Determine if this notification introduces new regulatory content worth storing
                3. Extract any document IDs mentioned for cross-referencing
                4. If this is substantive regulatory content, create an ADD_DOCUMENT action

                **FOCUS ON:**
                - Document reference numbers in the PDF content
                - Whether this contains new regulatory guidance vs. just informational content
                - Related document IDs for cross-referencing
                """

                # Make LLM call with enhanced content
                enhanced_result = self._make_llm_call(
                    enhanced_prompt,
                    AffectedDocumentsResult,
                    "You are an expert in RBI regulatory document analysis specializing in notification content extraction."
                )

                # Merge results
                if enhanced_result:
                    result['document_actions'] = enhanced_result.get('document_actions', [])
                    result['related_document_ids'] = enhanced_result.get('related_document_ids', [])
                    result['notification_pdf_url'] = notification_pdf_url
                    result['processing_notes'] = f"{result.get('processing_notes', '')} | Processed notification PDF content ({len(pdf_content)} chars)"

                    logger.info(f"   ✅ Enhanced with PDF content analysis")
                    logger.info(f"   📋 Found {len(result['document_actions'])} actions from PDF")
                    logger.info(f"   🔗 Found {len(result.get('related_document_ids', []))} related document IDs")

            return result

        except Exception as e:
            logger.error(f"❌ Error processing notification PDF fallback: {e}")
            result['processing_notes'] = f"{result.get('processing_notes', '')} | PDF fallback failed: {str(e)}"
            return result

    def create_simplified_result(self, notification_id: int, title: str, year: str, llm_analysis: dict, affected_documents: dict, update_actions: dict) -> dict:
        """Create simplified notification result with only essential information"""

        # Extract only essential information
        simplified = {
            "id": notification_id,
            "title": title,
            "year": year,
            "category": llm_analysis.get("category"),
            "confidence": llm_analysis.get("confidence"),
            "requires_kb_update": llm_analysis.get("requires_kb_update", False)
        }

        # Extract document actions (simplified)
        document_actions = affected_documents.get("document_actions", [])
        if document_actions:
            simplified["document_actions"] = []
            for action in document_actions:
                simplified_action = {
                    "action_type": action.get("action_type"),
                    "document_id": action.get("document_id"),
                    "reference_number": action.get("reference_number"),
                    "confidence": action.get("confidence")
                }
                # Only add non-null values
                simplified_action = {k: v for k, v in simplified_action.items() if v is not None}
                simplified["document_actions"].append(simplified_action)

        # Extract new document URL
        new_doc_url = affected_documents.get("new_document_url", "")
        if new_doc_url:
            simplified["new_document_url"] = new_doc_url

        # Extract effective date if present
        effective_date = affected_documents.get("effective_date")
        if effective_date:
            simplified["effective_date"] = effective_date

        # Extract related document IDs
        related_docs = affected_documents.get("related_document_ids", [])
        if related_docs:
            simplified["related_document_ids"] = related_docs

        # Manual review flag
        requires_review = (
            affected_documents.get("requires_manual_review", False) or
            update_actions.get("requires_manual_review", False)
        )
        if requires_review:
            simplified["requires_manual_review"] = True

        return simplified

    def determine_update_actions(self, title: str, category: str, affected_documents_result: dict, rss_description: str) -> dict:
        """
        Enhanced function to determine specific actions needed for knowledge base updates
        Works with the enhanced document actions and metadata from extract_affected_documents
        """
        import json

        try:
            logger.info(f"🔧 DETERMINING UPDATE ACTIONS")
            logger.info(f"   📋 Title: {title[:60]}...")
            logger.info(f"   📂 Category: {category}")

            # Extract document actions from the enhanced result
            document_actions = affected_documents_result.get('document_actions', [])
            logger.info(f"   📊 Processing {len(document_actions)} document actions")

            # Convert document actions to update actions with enhanced metadata
            actions = []

            for i, doc_action in enumerate(document_actions):
                logger.info(f"      📋 Processing document action {i+1}:")
                logger.info(f"         - Document ID: {doc_action.get('document_id', 'N/A')}")
                logger.info(f"         - Action Type: {doc_action.get('action_type', 'N/A')}")
                logger.info(f"         - Confidence: {doc_action.get('confidence', 'N/A')}")

                # Build comprehensive action with enhanced metadata
                action_obj = {
                    'action_type': doc_action.get('action_type', 'NO_ACTION'),
                    'target_document': doc_action.get('document_id', ''),
                    'priority': 'high' if doc_action.get('confidence') == 'high' else 'medium',
                    'details': self._build_action_details(doc_action),
                    'document_metadata': {
                        'document_title': doc_action.get('document_title'),
                        'reference_number': doc_action.get('reference_number'),
                        'department': doc_action.get('department'),
                        'original_date': doc_action.get('original_date'),
                        'update_location': doc_action.get('update_location'),
                        'sunset_withdraw_date': doc_action.get('sunset_withdraw_date'),
                        'confidence': doc_action.get('confidence'),
                        'reasoning': doc_action.get('reasoning')
                    }
                }

                # Add enhanced URL handling
                if doc_action.get('action_type') == 'ADD_DOCUMENT':
                    new_doc_url = affected_documents_result.get('new_document_url')
                    notification_pdf_url = affected_documents_result.get('notification_pdf_url')

                    if new_doc_url:
                        action_obj['new_document_url'] = new_doc_url
                        logger.info(f"         ✅ Has new document URL: {new_doc_url}")
                    elif notification_pdf_url:
                        action_obj['notification_pdf_url'] = notification_pdf_url
                        logger.info(f"         📄 Will use notification PDF: {notification_pdf_url}")
                    else:
                        logger.warning(f"         ⚠️ ADD_DOCUMENT action missing document URL")

                actions.append(action_obj)
                logger.info(f"         ✅ Created enhanced action: {action_obj['action_type']}")

            # Build comprehensive result with enhanced metadata
            result = {
                'actions': actions,
                'processing_notes': affected_documents_result.get('processing_notes', ''),
                'requires_manual_review': affected_documents_result.get('requires_manual_review', False),
                'superseded_documents': affected_documents_result.get('superseded_documents', []),
                'amendment_details': affected_documents_result.get('amendment_details'),
                'effective_date': affected_documents_result.get('effective_date'),
                'new_document_url': affected_documents_result.get('new_document_url', ''),
                'notification_pdf_url': affected_documents_result.get('notification_pdf_url', ''),
                'related_document_ids': affected_documents_result.get('related_document_ids', []),
                'pdf_links': affected_documents_result.get('pdf_links', []),
                'rbi_links': affected_documents_result.get('rbi_links', [])
            }

            logger.info(f"   ✅ Determined {len(actions)} enhanced update actions")
            logger.info(f"   📄 New document URL: {result.get('new_document_url', 'None')}")
            logger.info(f"   📋 Notification PDF URL: {result.get('notification_pdf_url', 'None')}")
            logger.info(f"   ⚠️ Manual review required: {result['requires_manual_review']}")

            return result

        except Exception as e:
            logger.error(f"❌ Error determining update actions: {e}")
            import traceback
            logger.error(f"   Stack trace: {traceback.format_exc()}")
            return self._get_fallback_update_actions()

    def _build_action_details(self, doc_action: dict) -> str:
        """Build comprehensive action details from enhanced document action metadata"""
        details_parts = [f"Action: {doc_action.get('action_type', 'NO_ACTION')}"]

        if doc_action.get('document_title'):
            details_parts.append(f"Title: {doc_action.get('document_title')}")
        if doc_action.get('reference_number'):
            details_parts.append(f"Ref: {doc_action.get('reference_number')}")
        if doc_action.get('department'):
            details_parts.append(f"Dept: {doc_action.get('department')}")
        if doc_action.get('update_location'):
            details_parts.append(f"Location: {doc_action.get('update_location')}")
        if doc_action.get('sunset_withdraw_date'):
            details_parts.append(f"Sunset: {doc_action.get('sunset_withdraw_date')}")
        if doc_action.get('reasoning'):
            details_parts.append(f"Reason: {doc_action.get('reasoning')}")

        return " | ".join(details_parts)

    def _get_fallback_update_actions(self) -> dict:
        """Return enhanced fallback when update action determination fails"""
        return {
            "actions": [],
            "processing_notes": "Enhanced LLM analysis failed",
            "requires_manual_review": True,
            "superseded_documents": [],
            "amendment_details": None,
            "effective_date": None,
            "new_document_url": "",
            "notification_pdf_url": "",
            "related_document_ids": [],
            "pdf_links": [],
            "rbi_links": []
        }


# Create global instance
notification_processor = NotificationProcessor()


# --- DOCUMENT UPDATE HISTORY TRACKING ---

class DocumentUpdateHistoryTracker:
    """Handles tracking and storing document update history in metadata"""

    def __init__(self, mongodb_uri: str):
        self.mongodb_uri = mongodb_uri

    def create_update_record(self, notification_data: dict, action_type: str, target_document: str = None) -> dict:
        """Create a standardized update history record"""
        return {
            "update_id": str(uuid.uuid4()),
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "notification_id": notification_data.get('id'),
            "notification_title": notification_data.get('title'),
            "notification_link": notification_data.get('link'),
            "notification_date": notification_data.get('published_date'),
            "action_type": action_type,  # UPDATE, REMOVE, ADD, TEMPORARY_NOTE
            "target_document": target_document,
            "llm_category": notification_data.get('llm_analysis', {}).get('category'),
            "confidence": notification_data.get('llm_analysis', {}).get('confidence'),
            "reasoning": notification_data.get('llm_analysis', {}).get('reasoning', '')
        }

    def add_update_history_to_document(self, collection_name: str, document_id: str, update_record: dict) -> bool:
        """Add update history record to a document's metadata"""
        try:
            client = MongoClient(self.mongodb_uri)
            db = client["rbi_regulation_db"]
            collection = db[collection_name]

            # Add update record to document's update_history array
            result = collection.update_one(
                {"document_id": document_id},
                {
                    "$push": {"update_history": update_record},
                    "$set": {"last_updated": update_record["timestamp"]}
                }
            )

            client.close()

            if result.modified_count > 0:
                logger.info(f"Added update history to document {document_id}")
                return True
            else:
                logger.warning(f"No document found with ID {document_id} in collection {collection_name}")
                return False

        except Exception as e:
            logger.error(f"Error adding update history to document {document_id}: {e}")
            return False

    def track_document_removal(self, notification_data: dict, removed_documents: list) -> bool:
        """Track when documents are removed due to notifications"""
        try:
            client = MongoClient(self.mongodb_uri)
            db = client["rbi_regulation_db"]

            # Create a removal history collection to track removed documents
            removal_history_collection = db["document_removal_history"]

            for doc_info in removed_documents:
                removal_record = self.create_update_record(
                    notification_data,
                    "REMOVE",
                    doc_info.get("document_id")
                )
                removal_record.update({
                    "removed_document_title": doc_info.get("title"),
                    "removed_document_number": doc_info.get("document_number"),
                    "original_collection": doc_info.get("collection")
                })

                removal_history_collection.insert_one(removal_record)
                logger.info(f"Tracked removal of document: {doc_info.get('document_id')}")

            client.close()
            return True

        except Exception as e:
            logger.error(f"Error tracking document removal: {e}")
            return False


# Create global instance
update_history_tracker = DocumentUpdateHistoryTracker(mongodb_uri)


class DocumentSearchEngine:
    """Optimized document search and matching engine for knowledge base"""

    def __init__(self, mongodb_uri: str):
        self.mongodb_uri = mongodb_uri
        self.collections = [
            "rbi_master_direction",
            "rbi_master_circular",
            "rbi_circular",
            # "rbi_notification",
            # "rbi_press_release"
        ]

    def find_documents(self, document_keywords: list, doc_id: str, doc_title:str ) -> list:
        """
        Enhanced document search using Qdrant vector database with semantic similarity
        """
        try:
            logger.info(f"🔍 STARTING QDRANT DOCUMENT SEARCH")
            logger.info(f"   🔑 Keywords ({len(document_keywords)}): {document_keywords}")
            logger.info(f"   📋 Doc Info ({len(doc_id)}): {doc_title}")
            logger.info(f"   📊 Collections to search: {self.collections}")

            found_documents = []

            # Search by reference numbers (highest priority) - exact ref number matches
            logger.info(f"   🔍 Phase 1: Searching by reference numbers...")
            ref_docs = self._search_by_reference_numbers_qdrant(doc_id)
            found_documents.extend(ref_docs)
            logger.info(f"   📊 Reference search results: {len(ref_docs)} documents")

            # Search by title (second priority) - exact title matches
            logger.info(f"   🔍 Phase 1: Searching by title...")
            title_docs = self._search_by_title_qdrant(doc_title)
            found_documents.extend(title_docs)
            logger.info(f"   📊 Title search results: {len(title_docs)} documents")


            # Search by keywords (semantic similarity)
            logger.info(f"   🔍 Phase 2: Searching by keywords...")
            keyword_docs = self._search_by_keywords_qdrant(document_keywords)
            found_documents.extend(keyword_docs)
            logger.info(f"   📊 Keyword search results: {len(keyword_docs)} documents")

            # Remove duplicates and rank results
            logger.info(f"   🔄 Phase 3: Deduplicating and ranking results...")
            unique_docs = self._deduplicate_and_rank(found_documents)

            logger.info(f"✅ QDRANT DOCUMENT SEARCH COMPLETE")
            logger.info(f"   📊 Total raw results: {len(found_documents)}")
            logger.info(f"   📊 Unique documents after deduplication: {len(unique_docs)}")

            # Log top results for debugging
            for i, doc in enumerate(unique_docs[:3]):  # Show top 3 results
                logger.info(f"   🏆 Top Result {i+1}:")
                logger.info(f"      - Collection: {doc.get('collection', 'N/A')}")
                logger.info(f"      - Document ID: {doc.get('document_id', 'N/A')}")
                logger.info(f"      - Title: {doc.get('title', 'N/A')[:50]}...")
                logger.info(f"      - Match Type: {doc.get('match_type', 'N/A')}")
                logger.info(f"      - Match Value: {doc.get('match_value', 'N/A')}")
                logger.info(f"      - Confidence: {doc.get('confidence_score', 0):.2f}")

            return unique_docs

        except Exception as e:
            logger.error(f"❌ ERROR IN QDRANT DOCUMENT SEARCH: {e}")
            logger.error(f"   🔑 Keywords: {document_keywords}")
            logger.error(f"   📋 Doc Identifies: {doc_id}, {doc_title}")
            import traceback
            logger.error(f"   📚 Stack trace: {traceback.format_exc()}")
            return []

    def _search_by_reference_numbers_qdrant(self, document_id: str) -> list:
        """
        Search for documents by exact reference number matches using Qdrant
        Filters on metadata.document_id field
        """
        found_documents = []

        ref_num = ref_num.strip()
        logger.info(f"   🔍 Searching for reference: {ref_num}")

        # Search across all collections for exact document_id matches
        for collection_name in self.collections:
            try:
                # Create exact match filter for metadata.document_id
                document_id_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.document_id",
                            match=models.MatchValue(value=ref_num)
                        )
                    ]
                )

                # Use native Qdrant scroll to get all matching documents (no vector search needed)
                scroll_result = qdrant_manager.client.scroll(
                    collection_name=collection_name,
                    scroll_filter=document_id_filter,
                    limit=100,  # Get all matches
                    with_payload=True,
                    with_vectors=False
                )

                points = scroll_result[0]  # scroll returns (points, next_offset)

                # Convert Qdrant results to expected format
                for point in points:
                    metadata = point.payload.get("metadata", {})
                    found_documents.append({
                        "collection": collection_name,
                        "document_id": point.id,
                        "title": metadata.get("title", metadata.get("document_title", "")),
                        "match_type": "reference_number",
                        "match_value": ref_num,
                        "confidence_score": 1.0,  # Exact match
                        "metadata": metadata,
                        "content": point.payload.get("page_content", "")
                    })

                logger.info(f"      📊 Found {len(points)} results in {collection_name}")

            except Exception as e:
                logger.warning(f"      ⚠️ Error searching {collection_name}: {e}")
                continue

        return found_documents

    def _search_by_title_qdrant(self, document_title: str) -> list:
        """
        Search for documents by exact title matches using Qdrant
        Filters on metadata.title and metadata.document_title fields
        """
        found_documents = []


        title = title.strip()
        logger.info(f"   🔍 Searching for title: {document_title}")

        # Search across all collections for exact title matches
        for collection_name in self.collections:
            try:
                # Search for exact match on metadata.title
                title_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.title",
                            match=models.MatchValue(value=document_title)
                        )
                    ]
                )

                scroll_result_title = qdrant_manager.client.scroll(
                    collection_name=collection_name,
                    scroll_filter=title_filter,
                    limit=100,  # Get all matches
                    with_payload=True,
                    with_vectors=False
                )

                # Search for exact match on metadata.document_title
                doc_title_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.document_title",
                            match=models.MatchValue(value=title)
                        )
                    ]
                )

                scroll_result_doc_title = qdrant_manager.client.scroll(
                    collection_name=collection_name,
                    scroll_filter=doc_title_filter,
                    limit=100,  # Get all matches
                    with_payload=True,
                    with_vectors=False
                )

                # Combine results from both searches
                points_title = scroll_result_title[0]  # scroll returns (points, next_offset)
                points_doc_title = scroll_result_doc_title[0]
                all_points = list(points_title) + list(points_doc_title)

                # Remove duplicates based on document ID
                seen_ids = set()
                unique_points = []
                for point in all_points:
                    point_id = point.id
                    if point_id not in seen_ids:
                        seen_ids.add(point_id)
                        unique_points.append(point)

                # Convert Qdrant results to expected format
                for point in unique_points:
                    metadata = point.payload.get("metadata", {})
                    found_documents.append({
                        "collection": collection_name,
                        "document_id": point.id,
                        "title": metadata.get("title", metadata.get("document_title", "")),
                        "match_type": "title",
                        "match_value": title,
                        "confidence_score": 0.95,  # Exact match almost
                        "metadata": metadata,
                        "content": point.payload.get("page_content", "")
                    })

                logger.info(f"      📊 Found {len(unique_points)} results in {collection_name}")

            except Exception as e:
                logger.warning(f"      ⚠️ Error searching {collection_name}: {e}")
                continue

        return found_documents

    def _search_by_keywords_qdrant(self, document_keywords: list) -> list:
        """
        Search for documents by keywords using Qdrant semantic similarity
        """
        found_documents = []

        if not document_keywords:
            return found_documents

        # Combine keywords into a search query
        search_query = " ".join(document_keywords)
        logger.info(f"   🔍 Semantic search query: {search_query}")

        # Search across all collections using semantic similarity
        for collection_name in self.collections:
            try:
                # Use multiple filters search with empty filters for pure semantic search
                results = search_qdrant_with_multiple_filters(
                    collection_name=collection_name,
                    query_text=search_query,
                    filters={},  # No filters, pure semantic search
                    limit=10,
                    score_threshold=0.6  # Higher threshold for semantic matches
                )

                # Convert Qdrant results to expected format
                for result in results:
                    metadata = result.get("metadata", {})
                    found_documents.append({
                        "collection": collection_name,
                        "document_id": result.get("id"),
                        "title": metadata.get("title", metadata.get("document_title", "")),
                        "match_type": "keyword_semantic",
                        "match_value": search_query,
                        "confidence_score": result.get("score", 0.0),
                        "metadata": metadata,
                        "content": result.get("content", "")
                    })

                logger.info(f"      📊 Found {len(results)} semantic results in {collection_name}")

            except Exception as e:
                logger.warning(f"      ⚠️ Error searching {collection_name}: {e}")
                continue

        return found_documents



    def _deduplicate_and_rank(self, documents: list) -> list:
        """Remove duplicates and rank by confidence score"""
        # Group by document identifier
        doc_groups = {}

        for doc in documents:
            key = (doc["collection"], doc["document_id"])

            if key not in doc_groups:
                doc_groups[key] = []
            doc_groups[key].append(doc)

        # Select best match for each document and rank
        unique_docs = []
        for group in doc_groups.values():
            # Select the match with highest confidence
            best_match = max(group, key=lambda x: x.get("confidence_score", 0))
            unique_docs.append(best_match)

        # Sort by confidence score (descending)
        unique_docs.sort(key=lambda x: x.get("confidence_score", 0), reverse=True)

        return unique_docs


# Create global instance
document_search_engine = DocumentSearchEngine(mongodb_uri)




class KnowledgeBaseUpdateExecutor:
    """Handles robust execution of knowledge base updates with comprehensive error handling"""

    def __init__(self):
        self.supported_actions = {
            "REMOVE_DOCUMENT": self._execute_remove_document,
            "ADD_DOCUMENT": self._execute_add_document,
            "UPDATE_DOCUMENT": self._execute_update_document,
            "ADD_TEMPORARY_NOTE": self._execute_add_temporary_note,
            "NO_ACTION": self._execute_no_action
        }

    def execute_updates(self, actions: list, notification_data: dict) -> list:
        """
        Execute knowledge base updates with comprehensive error handling and rollback capability
        """
        results = []
        successful_actions = []

        logger.info(f"Executing {len(actions)} knowledge base update actions")

        for i, action in enumerate(actions):
            action_type = action.get("action_type")
            target_document = action.get("target_document", "")

            logger.info(f"Executing action {i+1}/{len(actions)}: {action_type} for {target_document}")

            try:
                # Validate action
                if not self._validate_action(action):
                    results.append(self._create_error_result(action, "Invalid action format"))
                    continue

                # Execute action
                if action_type in self.supported_actions:
                    result = self.supported_actions[action_type](action, notification_data)
                    results.append(result)

                    if result["success"]:
                        successful_actions.append(action)
                        logger.info(f"✅ Successfully executed {action_type}")
                    else:
                        logger.warning(f"⚠️ Action {action_type} completed with issues: {result['details']}")

                else:
                    results.append(self._create_error_result(action, f"Unsupported action type: {action_type}"))

            except Exception as e:
                logger.error(f"❌ Critical error executing {action_type}: {e}")
                results.append(self._create_error_result(action, f"Critical error: {str(e)}"))

        # Log summary
        successful_count = sum(1 for r in results if r["success"])
        logger.info(f"Knowledge base update summary: {successful_count}/{len(actions)} actions successful")

        return results

    def _validate_action(self, action: dict) -> bool:
        """Validate action format and required fields"""
        required_fields = ["action_type"]
        return all(field in action for field in required_fields)

    def _create_error_result(self, action: dict, error_message: str) -> dict:
        """Create standardized error result"""
        return {
            "action": action.get("action_type", "UNKNOWN"),
            "target": action.get("target_document", ""),
            "success": False,
            "details": error_message,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _create_success_result(self, action: dict, details: str, additional_info: dict = None) -> dict:
        """Create standardized success result"""
        result = {
            "action": action.get("action_type"),
            "target": action.get("target_document", ""),
            "success": True,
            "details": details,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        if additional_info:
            result.update(additional_info)
        return result

    def _execute_remove_document(self, action: dict, notification_data: dict) -> dict:
        """Execute document removal with comprehensive error handling"""
        target_document = action.get("target_document")

        try:
            removed_docs, success = remove_document_from_kb_with_history(target_document, notification_data)

            if success:
                return self._create_success_result(
                    action,
                    f"Successfully removed document. Tracked {len(removed_docs)} removals.",
                    {"removed_documents_count": len(removed_docs)}
                )
            else:
                return self._create_error_result(action, "Failed to remove document - document may not exist")

        except Exception as e:
            return self._create_error_result(action, f"Error during document removal: {str(e)}")

    def _execute_add_document(self, action: dict, notification_data: dict) -> dict:
        """Execute document addition with comprehensive error handling"""
        new_doc_url = action.get("new_document_url")
        rbi_page_url = action.get("rbi_page_url")

        # If no direct PDF URL, try to extract from RBI page
        if not new_doc_url and rbi_page_url:
            logger.info(f"No direct PDF URL provided, attempting to extract from RBI page: {rbi_page_url}")
            try:
                new_doc_url = get_rbi_pdf_link(rbi_page_url)
                if new_doc_url:
                    logger.info(f"Successfully extracted PDF URL from RBI page: {new_doc_url}")
                else:
                    return self._create_error_result(action, f"Could not extract PDF URL from RBI page: {rbi_page_url}")
            except Exception as e:
                return self._create_error_result(action, f"Error extracting PDF from RBI page {rbi_page_url}: {str(e)}")

        if not new_doc_url:
            return self._create_error_result(action, "No document URL provided for ADD_DOCUMENT action and no rbi_page_url to extract from")

        try:
            success = download_and_ingest_document_with_history(new_doc_url, notification_data)

            if success:
                return self._create_success_result(
                    action,
                    "Successfully added new document to knowledge base",
                    {"document_url": new_doc_url, "extracted_from_rbi_page": bool(rbi_page_url)}
                )
            else:
                return self._create_error_result(action, "Failed to download or ingest new document")

        except Exception as e:
            return self._create_error_result(action, f"Error during document addition: {str(e)}")

    def _execute_update_document(self, action: dict, notification_data: dict) -> dict:
        """Execute document update with comprehensive error handling"""
        target_document = action.get("target_document")

        try:
            success = update_document_from_rbi_with_history(target_document, notification_data)

            if success:
                return self._create_success_result(
                    action,
                    "Successfully updated document from RBI source"
                )
            else:
                return self._create_error_result(
                    action,
                    "Failed to automatically update document - may require manual intervention"
                )

        except Exception as e:
            return self._create_error_result(action, f"Error during document update: {str(e)}")

    def _execute_add_temporary_note(self, action: dict, notification_data: dict) -> dict:
        """Execute temporary note addition with comprehensive error handling"""
        expiry_date = action.get("expiry_date")

        try:
            success = add_notification_as_temporary_note_with_history(notification_data, expiry_date)

            if success:
                return self._create_success_result(
                    action,
                    "Successfully added temporary note to knowledge base",
                    {"expiry_date": expiry_date}
                )
            else:
                return self._create_error_result(action, "Failed to add temporary note")

        except Exception as e:
            return self._create_error_result(action, f"Error during temporary note addition: {str(e)}")

    def _execute_no_action(self, action: dict, notification_data: dict) -> dict:
        """Handle NO_ACTION case"""
        return self._create_success_result(action, "No action required for this notification")


# Create global instance
kb_update_executor = KnowledgeBaseUpdateExecutor()




def remove_document_from_kb_with_history(document_identifier, notification_data):
    """
    Remove document from both Qdrant and MongoDB with history tracking
    """
    try:
        client = MongoClient(mongodb_uri)
        db = client["rbi_regulation_db"]

        # Search and remove from all collections
        collections = ["rbi_master_direction", "rbi_master_circular", "rbi_circular",
                      "rbi_notification", "rbi_press_release"]

        removed_documents = []

        for collection_name in collections:
            if collection_name in db.list_collection_names():
                collection = db[collection_name]

                # Find documents to remove (using exact match only - no $regex)
                docs_to_remove = list(collection.find({
                    "$or": [
                        {"document_id": document_identifier},
                        {"document_number": document_identifier},
                        {"title": document_identifier}  # Exact match only
                    ]
                }))

                for doc in docs_to_remove:
                    # Store document info for history tracking
                    removed_documents.append({
                        "document_id": doc.get("document_id", str(doc["_id"])),
                        "title": doc.get("title", ""),
                        "document_number": doc.get("document_number", ""),
                        "collection": collection_name
                    })

                    # Remove from MongoDB
                    collection.delete_one({"_id": doc["_id"]})

                    # Remove from Qdrant (remove all chunks of this document)
                    article_id = doc.get("article_id", doc.get("document_id"))
                    if article_id:
                        logger.info(f"Document {article_id} marked for removal from Qdrant")

        client.close()

        # Track removal history
        if removed_documents:
            update_history_tracker.track_document_removal(notification_data, removed_documents)

        logger.info(f"Removed {len(removed_documents)} documents from knowledge base with history tracking")
        return removed_documents, len(removed_documents) > 0

    except Exception as e:
        logger.error(f"Error removing document from KB: {e}")
        return [], False




def download_and_ingest_document_with_history(document_url, notification_data):
    """
    Download a new document and ingest it into the knowledge base with history tracking.
    Uses the regs_from_s3 methodology:
    - Downloads PDF and parses to HTML AST
    - Generates LLM-based summary and topics
    - Uses QdrantVectorStore.add_texts() for batch processing with rich context
    - Includes comprehensive logging and update history tracking
    """
    try:
        logger.info(f"📥 Downloading and ingesting document: {document_url}")

        # Download the document
        response = requests.get(document_url, timeout=60)
        response.raise_for_status()

        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(response.content)
            temp_path = tmp_file.name

        try:
            # Determine collection based on notification category
            collection_name = categorize_document(notification_data.get("title", ""))

            # Generate a unique article ID for this document
            article_id = str(uuid.uuid4())

            # Parse PDF into an HTML AST and then chunk it
            soup = parse_pdf_to_html_ast(temp_path)
            full_html = str(soup)
            chunks = chunk_html_ast(soup, max_chars=3000)

            logger.info(f"📊 Generated {len(chunks)} chunks for document ingestion")

            # Generate document summary and topics using LLM (regs methodology)
            logger.info(f"🤖 Generating document summary and topics using LLM...")
            try:
                # Use a subset of the document for summary generation (first 6000 chars)
                document_slice = full_html[:6000]

                summary_prompt = (
                    "Analyze the following RBI document and provide:\n"
                    "1. A concise summary (2-3 sentences) of the main content\n"
                    "2. Key topics/themes (comma-separated list)\n\n"
                    f"Document content:\n{strip_data_attributes(document_slice)}"
                )

                summary_response = openai_manager.get_completion(summary_prompt)

                # Parse the response to extract summary and topics
                lines = summary_response.strip().split('\n')
                document_summary = ""
                topics = []

                for line in lines:
                    if line.strip():
                        if not document_summary:
                            document_summary = line.strip()
                        elif ',' in line:
                            topics = [topic.strip() for topic in line.split(',')]
                            break

                if not document_summary:
                    document_summary = f"RBI document: {notification_data.get('title', '')}"
                if not topics:
                    topics = ["RBI", "Banking", "Regulation"]

                logger.info(f"📝 Generated summary: {document_summary[:100]}...")
                logger.info(f"🏷️ Generated topics: {topics}")

            except Exception as e:
                logger.warning(f"⚠️ Failed to generate LLM summary, using defaults: {e}")
                document_summary = f"RBI document: {notification_data.get('title', '')}"
                topics = ["RBI", "Banking", "Regulation"]

            # Create update history record
            update_record = update_history_tracker.create_update_record(
                notification_data,
                "ADD",
                article_id
            )

            # Prepare metadata following regs_from_s3 structure
            document_metadata = {
                "document_title": notification_data.get("title", ""),
                "document_type": collection_name,
                "published_date": notification_data.get("published_date", ""),
                "link": notification_data.get("link", ""),
                "document_url": document_url,
                "source": "document_update",
                "update_history": [update_record],
                "last_updated": update_record["timestamp"],
                "processed_date": datetime.now().isoformat()
            }

            # Use regs_from_s3 methodology for Qdrant ingestion
            success = _ingest_chunks_to_qdrant_regs_style(
                collection=collection_name,
                chunks_payload=chunks,
                document_id=article_id,
                document_summary=document_summary,
                topics=topics,
                document_metadata=document_metadata
            )

            successful_chunks = len(chunks) if success else 0
            failed_chunks = 0 if success else len(chunks)

            logger.info(f"✅ Document ingestion complete: {successful_chunks}/{len(chunks)} chunks processed successfully")
            return successful_chunks > 0

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except Exception as e:
        logger.error(f"Error downloading and ingesting document with history: {e}")
        return False




def add_notification_as_temporary_note_with_history(notification_data, expiry_date=None):
    """
    Add the notification itself as a temporary note in the knowledge base with history tracking
    """
    try:
        # Create a unique ID for this notification
        notification_id = str(uuid.uuid4())

        # Prepare the content using regs methodology structure
        title = notification_data.get('title', '')
        main_text = notification_data.get('doc_info', {}).get('main_text', '')

        # Generate summary and topics for temporary note
        document_summary = f"Temporary regulatory note: {title}"
        topics = ["RBI", "Temporary", "Regulatory Note", notification_data.get('category', 'Notification')]

        # Structure content following regs methodology
        content = (
            f"Document Summary:\n{document_summary}\n\n"
            f"Topics:\n{', '.join(topics)}\n\n"
            f"Content:\n"
            f"TEMPORARY REGULATORY NOTE\n\n"
            f"Title: {title}\n"
            f"Date: {notification_data.get('published_date', '')}\n"
            f"Category: {notification_data.get('category', '')}\n\n"
            f"Content: {main_text}\n\n"
            f"Link: {notification_data.get('link', '')}"
        )

        # Set expiry date if not provided (default 1 year)
        if not expiry_date:
            expiry_date = (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d')

        # Create update history record
        update_record = update_history_tracker.create_update_record(
            notification_data,
            "ADD_TEMPORARY_NOTE",
            notification_id
        )

        # Prepare metadata with update history - nested structure for consistency
        document_metadata = {
            "notification_id": notification_id,
            "title": notification_data.get('title', ''),
            "published_date": notification_data.get('published_date', ''),
            "category": notification_data.get('category', ''),
            "is_temporary": True,
            "expiry_date": expiry_date,
            "link": notification_data.get('link', ''),
            "type": "temporary_note",
            "update_history": [update_record],
            "last_updated": update_record["timestamp"]
        }

        # Create payload with proper metadata nesting
        payload = {
            "notification_id": notification_id,
            "metadata": document_metadata
        }

        # Use notification collection for temporary notes
        collection_name = "rbi_notification"

        # Use comprehensive logging utilities for temporary note
        log_qdrant_operation_start("TEMPORARY_NOTE_UPSERT", collection_name, notification_id, 1)
        logger.info(f"📝 Document Summary: {document_summary}")
        logger.info(f"🏷️ Topics: {topics}")
        logger.info(f"📦 Content length: {len(content)} characters")
        logger.info(f"⏰ Expiry date: {expiry_date}")

        # Ingest into Qdrant using individual upsert (appropriate for single temporary notes)
        success = qdrant_manager.upsert_point(
            collection_name=collection_name,
            point_id=notification_id,
            payload=payload,
            text_for_embedding=content
        )

        # Use comprehensive logging utilities for result
        if success:
            log_qdrant_operation_end("TEMPORARY_NOTE_UPSERT", True, collection_name, notification_id, 1)
            logger.info(f"Added temporary note with history tracking for notification: {notification_data.get('title', '')}")
        else:
            log_qdrant_operation_end("TEMPORARY_NOTE_UPSERT", False, collection_name, notification_id, 1, "Upsert operation failed")

        return success

    except Exception as e:
        logger.error(f"Error adding temporary note with history: {e}")
        return False




def get_rbi_pdf_link(url):
    """
    Extract PDF link from RBI page URL
    """
    try:
        import requests
        from bs4 import BeautifulSoup

        logger.info(f"Fetching PDF link from RBI page: {url}")
        response = requests.get(url)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, "html.parser")

        # Look for PDF links
        pdf_link = soup.find("a", href=lambda href: href and href.endswith(".PDF"))
        if pdf_link:
            pdf_url = pdf_link["href"]
            # Handle relative URLs
            if pdf_url.startswith("/"):
                pdf_url = "https://www.rbi.org.in" + pdf_url
            elif not pdf_url.startswith("http"):
                pdf_url = "https://www.rbi.org.in/" + pdf_url

            logger.info(f"Found PDF link: {pdf_url}")
            return pdf_url
        else:
            logger.warning(f"No PDF link found on page: {url}")
            return None

    except Exception as e:
        logger.error(f"Error extracting PDF link from {url}: {e}")
        return None


def extract_anchor_information(html_content):
    """
    Extract comprehensive anchor information with enhanced PDF link detection
    Filters navigation links and handles relative URLs properly
    """
    import re
    from bs4 import BeautifulSoup
    import requests

    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        anchor_info = {
            'anchors': [],
            'reference_numbers': [],
            'rbi_links': [],
            'document_links': [],
            'pdf_links': [],
            'rbi_page_urls': [],
            'navigation_links': []
        }

        # Navigation link patterns to filter out
        navigation_patterns = [
            r'home', r'about', r'contact', r'sitemap', r'search', r'login',
            r'menu', r'header', r'footer', r'nav', r'breadcrumb'
        ]

        # Extract all anchor tags with their text and href
        for link in soup.find_all('a', href=True):
            href = link['href'].strip()
            text = link.get_text(strip=True)

            # Skip empty links
            if not href or not text:
                continue

            # Filter out navigation links
            is_navigation = any(pattern in href.lower() or pattern in text.lower()
                              for pattern in navigation_patterns)

            if is_navigation:
                anchor_info['navigation_links'].append({'text': text, 'href': href})
                continue

            # Handle relative URLs
            processed_href = _process_relative_url(href)

            anchor_data = {
                'text': text,
                'href': processed_href,
                'original_href': href,
                'full_html': str(link)
            }
            anchor_info['anchors'].append(anchor_data)

            # Enhanced RBI reference patterns including FEMA, DoR, and dual references
            ref_patterns = [
                r'DoR\.[A-Z]+\.[A-Z]+\.\d+/[\d\.]+/\d{4}-\d{2}',  # DoR.MCS.REC.38/01.01.001/2025-26
                r'FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
                r'FEMA\s+\d+\([A-Z]\)/\d{4}-[A-Z]+',  # FEMA 23(R)/2015-RB
                r'[A-Z]+\.No\.[A-Za-z\.]+\d+/[\d\.]+/\d{4}-?\d*',  # DBOD.No.Leg.BC.21/09.07.007/2002-03
                r'RBI/\d{4}-\d{2}/\d+',  # RBI/2025-26/64
                r'[A-Z]+/\d{4}-\d{2}/\d+',  # DPSS/2024-25/123
                r'Notification\s+No\.?\s*RBI/\d{4}-\d{2}/\d+',  # Notification No. RBI/2025-26/64
                r'Notification\s+No\.?\s*DoR\.[A-Z]+\.[A-Z]+\.\d+/[\d\.]+/\d{4}-\d{2}',  # Full DoR pattern
                r'Notification\s+No\.\s+FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',  # Full FEMA pattern
                r'Notification\s+No\.\s+FEMA\s+\d+\([A-Z]\)/\d{4}-[A-Z]+'  # Principal FEMA pattern
            ]

            for pattern in ref_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if match not in anchor_info['reference_numbers']:
                        anchor_info['reference_numbers'].append(match)

            # Categorize links by type
            if processed_href.lower().endswith('.pdf'):
                # Direct PDF link
                anchor_info['pdf_links'].append(processed_href)
                anchor_info['document_links'].append(processed_href)
                logger.info(f"   📄 Found direct PDF link: {processed_href}")

            elif 'rbi.org.in' in processed_href:
                if 'NotificationUser.aspx' in processed_href or 'Scripts/' in processed_href:
                    # RBI notification page that likely contains PDF
                    anchor_info['rbi_page_urls'].append(processed_href)
                    logger.info(f"   📋 Found RBI page URL: {processed_href}")
                else:
                    # Other RBI links
                    anchor_info['rbi_links'].append(processed_href)

            # Check for other document types
            elif any(ext in processed_href.lower() for ext in ['.doc', '.docx', '.xls', '.xlsx']):
                anchor_info['document_links'].append(processed_href)

        # Also extract reference numbers from plain text (backup)
        text_content = soup.get_text()
        for pattern in ref_patterns:
            matches = re.findall(pattern, text_content)
            for match in matches:
                # Clean up the match (remove extra spaces)
                clean_match = re.sub(r'\s+', ' ', match.strip())
                if clean_match not in anchor_info['reference_numbers']:
                    anchor_info['reference_numbers'].append(clean_match)

        # Extract URLs from text content
        url_pattern = r'https?://(?:www\.)?rbi\.org\.in/[^\s<>"]+'
        found_urls = re.findall(url_pattern, text_content)
        for url in found_urls:
            processed_url = _process_relative_url(url)
            if processed_url not in anchor_info['rbi_links'] and processed_url not in anchor_info['rbi_page_urls']:
                anchor_info['rbi_links'].append(processed_url)

        logger.info(f"Extracted anchor info: {len(anchor_info['anchors'])} anchors, "
                   f"{len(anchor_info['reference_numbers'])} references, "
                   f"{len(anchor_info['pdf_links'])} PDF links, "
                   f"{len(anchor_info['rbi_page_urls'])} RBI pages")

        return anchor_info

    except Exception as e:
        logger.error(f"Error extracting anchor information: {e}")
        return {
            'anchors': [],
            'reference_numbers': [],
            'rbi_links': [],
            'document_links': [],
            'pdf_links': [],
            'rbi_page_urls': [],
            'navigation_links': []
        }


def _process_relative_url(href):
    """
    Process relative URLs and convert them to absolute URLs
    """
    import re

    # Already absolute URL
    if href.startswith('http'):
        return href

    # Relative URL starting with /
    if href.startswith('/'):
        if 'rdocs' in href or '.pdf' in href.lower():
            # PDF documents are typically on rbidocs subdomain
            return f"https://rbidocs.rbi.org.in{href}"
        else:
            # Other pages are on main domain
            return f"https://www.rbi.org.in{href}"

    # Relative URL starting with ../
    if href.startswith('../'):
        # Remove ../ and treat as root relative
        clean_href = href.replace('../', '/')
        if 'rdocs' in clean_href or '.pdf' in clean_href.lower():
            return f"https://rbidocs.rbi.org.in{clean_href}"
        else:
            return f"https://www.rbi.org.in{clean_href}"

    # Relative URL without leading slash
    if not href.startswith('http'):
        if 'rdocs' in href or '.pdf' in href.lower():
            # Remove leading slash to avoid double slashes
            clean_href = href.lstrip('/')
            return f"https://rbidocs.rbi.org.in/{clean_href}"
        else:
            # Remove leading slash to avoid double slashes
            clean_href = href.lstrip('/')
            return f"https://www.rbi.org.in/{clean_href}"

    return href


def extract_pdf_from_rbi_page(rbi_page_url):
    """
    Fetch an RBI notification page and extract the PDF link
    Enhanced to handle specific RBI patterns like tableheader with PDF icons
    """
    import requests
    from bs4 import BeautifulSoup
    import re

    try:
        logger.info(f"🔍 Fetching PDF link from RBI page: {rbi_page_url}")

        # Add headers to mimic browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(rbi_page_url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Enhanced PDF extraction patterns for RBI pages
        pdf_link = None
        notification_id = None

        # Extract notification ID from URL for validation
        import re
        id_match = re.search(r'Id=(\d+)', rbi_page_url)
        if id_match:
            notification_id = id_match.group(1)
            logger.info(f"   🔍 Notification ID: {notification_id}")

        # Pattern 1: Table header with PDF icon and link (most common RBI pattern)
        tableheader = soup.find('td', class_='tableheader')
        if tableheader:
            # Look for anchor tag within tableheader
            pdf_anchor = tableheader.find('a', href=True)
            if pdf_anchor and pdf_anchor.get('href'):
                href = pdf_anchor['href']
                if '.pdf' in href.lower():
                    # Simplified validation - avoid only known bad patterns
                    if 'NOTI134' in href.upper():
                        logger.warning(f"   ❌ Skipping known incorrect NOTI134 link: {href}")
                    elif 'Utkarsh30122022.pdf' in href:
                        logger.warning(f"   ❌ Skipping generic RBI vision document: {href}")
                    else:
                        pdf_link = href
                        logger.info(f"   ✅ Found PDF in tableheader: {pdf_link}")

            # Alternative: Look for href in any element within tableheader
            if not pdf_link:
                for element in tableheader.find_all(href=True):
                    href = element.get('href', '')
                    if '.pdf' in href.lower():
                        # Apply same simplified validation
                        if 'NOTI134' in href.upper():
                            logger.warning(f"   ❌ Skipping NOTI134 in tableheader element: {href}")
                        elif 'Utkarsh30122022.pdf' in href:
                            logger.warning(f"   ❌ Skipping generic vision document: {href}")
                        else:
                            pdf_link = href
                            logger.info(f"   ✅ Found PDF in tableheader element: {pdf_link}")
                            break

        # Pattern 2: Direct PDF links anywhere on page (with filtering)
        if not pdf_link:
            pdf_anchors = soup.find_all('a', href=re.compile(r'.*\.pdf$', re.IGNORECASE))
            for anchor in pdf_anchors:
                href = anchor['href']
                # Skip known generic documents
                if 'NOTI134' in href.upper() or 'Utkarsh30122022.pdf' in href:
                    continue
                # Prefer notification/PressRelease PDFs over content PDFs
                if 'notification/PDFs/' in href or 'PressRelease/PDFs/' in href:
                    pdf_link = href
                    logger.info(f"   ✅ Found priority PDF link: {pdf_link}")
                    break
                elif not pdf_link:  # Use as fallback if no priority link found
                    pdf_link = href
                    logger.info(f"   ✅ Found fallback PDF link: {pdf_link}")

        # Pattern 3: Links with size info (e.g., "(376 kb)")
        if not pdf_link:
            size_pattern = re.compile(r'\(\d+\s*kb\)', re.IGNORECASE)
            for element in soup.find_all(string=size_pattern):
                parent = element.parent
                if parent and parent.name == 'td':
                    # Look for anchor in the same cell
                    anchor = parent.find('a', href=True)
                    if anchor:
                        href = anchor.get('href', '')
                        if '.pdf' in href.lower() and 'NOTI134' not in href.upper():
                            pdf_link = href
                            logger.info(f"   ✅ Found PDF with size info: {pdf_link}")
                            break

        # Pattern 4: PDF icon images with adjacent links
        if not pdf_link:
            pdf_images = soup.find_all('img', src=re.compile(r'.*pdf\.gif', re.IGNORECASE))
            for img in pdf_images:
                # Look for anchor tag in the same parent
                parent = img.parent
                if parent:
                    anchor = parent.find('a', href=True)
                    if anchor:
                        href = anchor.get('href', '')
                        if '.pdf' in href.lower() and 'NOTI134' not in href.upper():
                            pdf_link = href
                            logger.info(f"   ✅ Found PDF via icon: {pdf_link}")
                            break

        if pdf_link:
            # Process relative URL if needed
            processed_pdf_link = _process_relative_url(pdf_link)
            logger.info(f"   📄 Final PDF URL: {processed_pdf_link}")
            return processed_pdf_link
        else:
            logger.warning(f"   ⚠️ No PDF link found on page: {rbi_page_url}")
            return None

    except requests.RequestException as e:
        logger.error(f"   ❌ Network error fetching RBI page {rbi_page_url}: {e}")
        return None
    except Exception as e:
        logger.error(f"   ❌ Error extracting PDF from RBI page {rbi_page_url}: {e}")
        return None


def download_and_extract_pdf_content(pdf_url, max_chars=8000):
    """
    Download PDF and extract text content for LLM processing
    """
    import requests
    import tempfile
    import os
    from utils.pdf_utils import parse_pdf_to_html_ast
    from bs4 import BeautifulSoup

    try:
        logger.info(f"📄 Downloading PDF for content extraction: {pdf_url}")

        # Download PDF
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(pdf_url, headers=headers, timeout=60)
        response.raise_for_status()

        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(response.content)
            temp_pdf_path = temp_file.name

        try:
            # Extract HTML content from PDF
            html_content = parse_pdf_to_html_ast(temp_pdf_path)

            # Parse and clean the content
            soup = BeautifulSoup(html_content, 'html.parser')
            text_content = soup.get_text(separator=' ', strip=True)

            # Limit content for LLM processing
            if len(text_content) > max_chars:
                text_content = text_content[:max_chars] + "... [CONTENT TRUNCATED]"

            logger.info(f"   ✅ Extracted {len(text_content)} characters from PDF")
            return text_content

        finally:
            # Clean up temporary file
            if os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)

    except Exception as e:
        logger.error(f"   ❌ Error extracting PDF content: {e}")
        return None


def scrape_all_links_from_rbi_page(rbi_page_url):
    """
    Scrape ALL links from an RBI notification page using BeautifulSoup
    Returns comprehensive link information for LLM analysis
    """
    import requests
    from bs4 import BeautifulSoup
    import re

    try:
        logger.info(f"🔍 Scraping ALL links from RBI page: {rbi_page_url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(rbi_page_url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        all_links = {
            'pdf_links': [],
            'rbi_links': [],
            'external_links': [],
            'page_text': soup.get_text()[:5000],  # First 5000 chars for context
            'page_title': soup.title.string if soup.title else '',
            'all_anchors': []
        }

        # Extract ALL anchor tags
        for link in soup.find_all('a', href=True):
            href = link['href'].strip()
            text = link.get_text(strip=True)

            if not href or not text:
                continue

            # Process relative URLs
            processed_href = _process_relative_url(href)

            anchor_data = {
                'text': text,
                'href': processed_href,
                'original_href': href
            }
            all_links['all_anchors'].append(anchor_data)

            # Categorize links
            if processed_href.lower().endswith('.pdf'):
                all_links['pdf_links'].append({
                    'url': processed_href,
                    'text': text,
                    'context': link.parent.get_text(strip=True)[:200] if link.parent else ''
                })
                logger.info(f"   📄 Found PDF: {text} -> {processed_href}")

            elif 'rbi.org.in' in processed_href:
                all_links['rbi_links'].append({
                    'url': processed_href,
                    'text': text,
                    'context': link.parent.get_text(strip=True)[:200] if link.parent else ''
                })
            else:
                all_links['external_links'].append({
                    'url': processed_href,
                    'text': text
                })

        logger.info(f"   📊 Scraped: {len(all_links['pdf_links'])} PDFs, "
                   f"{len(all_links['rbi_links'])} RBI links, "
                   f"{len(all_links['external_links'])} external links")

        return all_links

    except Exception as e:
        logger.error(f"   ❌ Error scraping RBI page {rbi_page_url}: {e}")
        return None


def enhance_document_links(anchor_info):
    """
    Enhance document links by following RBI page URLs to find PDF links
    Also identifies notification PDF for processing when no additional docs found
    """
    enhanced_info = dict(anchor_info)
    notification_pdf_url = None
    all_scraped_links = []

    # If we already have direct PDF links, prioritize those
    if enhanced_info['pdf_links']:
        logger.info(f"✅ Already have {len(enhanced_info['pdf_links'])} direct PDF links")
        # First PDF could be the notification itself
        notification_pdf_url = enhanced_info['pdf_links'][0]

    # If we have RBI page URLs, scrape ALL links from them
    if enhanced_info['rbi_page_urls']:
        logger.info(f"🔍 Scraping ALL links from {len(enhanced_info['rbi_page_urls'])} RBI pages")

        for rbi_page_url in enhanced_info['rbi_page_urls']:
            scraped_links = scrape_all_links_from_rbi_page(rbi_page_url)
            if scraped_links:
                all_scraped_links.append(scraped_links)

                # Add found PDFs to our enhanced info
                for pdf_info in scraped_links['pdf_links']:
                    pdf_url = pdf_info['url']
                    if pdf_url not in enhanced_info['pdf_links']:
                        enhanced_info['pdf_links'].append(pdf_url)
                        enhanced_info['document_links'].append(pdf_url)
                        if not notification_pdf_url:
                            notification_pdf_url = pdf_url
                        logger.info(f"   ✅ Added PDF: {pdf_url}")

    # Store the notification PDF URL and scraped links for LLM processing
    enhanced_info['notification_pdf_url'] = notification_pdf_url or ''
    enhanced_info['all_scraped_links'] = all_scraped_links

    logger.info(f"📊 Enhanced links summary: {len(enhanced_info['pdf_links'])} PDFs, "
               f"{len(enhanced_info['rbi_page_urls'])} RBI pages, "
               f"notification PDF: {bool(notification_pdf_url)}")

    return enhanced_info


def extract_rbi_links_from_description(rss_description):
    """
    Extract RBI document links from RSS description
    """
    import re
    from bs4 import BeautifulSoup

    try:
        # Parse the RSS description HTML
        soup = BeautifulSoup(rss_description, 'html.parser')

        # Look for RBI links in the description
        rbi_links = []

        # Find all links in the description
        for link in soup.find_all('a', href=True):
            href = link['href']
            if 'rbi.org.in' in href and ('ViewMasDirections' in href or 'ViewBulletin' in href or 'ViewCircular' in href):
                rbi_links.append(href)

        # Also look for links in text content using regex
        text_content = soup.get_text()
        url_pattern = r'https?://(?:www\.)?rbi\.org\.in/[^\s<>"]+'
        found_urls = re.findall(url_pattern, text_content)

        for url in found_urls:
            if url not in rbi_links:
                rbi_links.append(url)

        logger.info(f"Found {len(rbi_links)} RBI links in description")
        return rbi_links

    except Exception as e:
        logger.error(f"Error extracting RBI links from description: {e}")
        return []


def update_document_from_rbi_with_history(target_document, notification_data):
    """
    Update a document by fetching the latest version from RBI with history tracking
    """
    try:
        # Extract RBI links from the notification description
        rss_description = notification_data.get('rss_description', '')
        rbi_links = extract_rbi_links_from_description(rss_description)

        if not rbi_links:
            logger.warning(f"No RBI links found in notification for document: {target_document}")
            return False

        # Try each RBI link to find a PDF
        for rbi_link in rbi_links:
            pdf_url = get_rbi_pdf_link(rbi_link)
            if pdf_url:
                logger.info(f"Found updated document PDF: {pdf_url}")

                # First, remove the old document from knowledge base with history tracking
                removed_docs, removal_success = remove_document_from_kb_with_history(target_document, notification_data)
                if removal_success:
                    logger.info(f"Successfully removed old version of: {target_document}")

                # Download and ingest the new version with history tracking
                ingestion_success = download_and_ingest_document_with_history(pdf_url, notification_data)
                if ingestion_success:
                    logger.info(f"Successfully updated document: {target_document}")
                    return True
                else:
                    logger.error(f"Failed to ingest updated document: {target_document}")
                    return False

        logger.warning(f"No PDF found for document update: {target_document}")
        return False

    except Exception as e:
        logger.error(f"Error updating document from RBI with history: {e}")
        return False




class RSSFeedProcessor:
    """Clean and modular RSS feed processing with improved error handling"""

    def __init__(self):
        self.rss_feeds = {
            "notifications": "https://rbi.org.in/notifications_rss.xml",
            # "press_releases": "https://rbi.org.in/pressreleases_rss.xml",
            # "publications": "https://rbi.org.in/Publication_rss.xml"
        }

        # S3 Configuration
        self.s3_bucket_name = Variable.get("s3_bucket_name", default_var="rbi-notifications")
        self.s3_client = self._initialize_s3_client()

        # Processing statistics
        self.stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0
        }

    def _initialize_s3_client(self):
        """Initialize S3 client with error handling"""
        try:
            aws_access_key_id = Variable.get("aws_access_key_id")
            aws_secret_access_key = Variable.get("aws_secret_access_key")

            return boto3.client(
                "s3",
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            return None

    def parse_rbi_notification(self, data: str) -> dict:
        """Parse RBI notification XML to extract key fields"""
        try:
            soup = BeautifulSoup(data, 'html.parser')
            document = {}
            p_tags = soup.find_all('p')

            # Extract circular numbers
            document['circular_numbers'] = self._extract_circular_numbers(p_tags)

            # Extract press release number
            document['press_release_number'] = self._extract_press_release_number(soup)

            # Extract and parse date
            document['date'], document['date_iso'] = self._extract_date(soup)

            # Extract addressee
            document['addressee'] = self._extract_addressee(p_tags, document['date'])

            # Extract subject/title
            document['subject'] = self._extract_subject(soup)

            # Extract main text
            document['main_text'] = self._extract_main_text(p_tags)

            return document

        except Exception as e:
            logger.error(f"Error parsing RBI notification: {e}")
            return {}

    def _extract_circular_numbers(self, p_tags: list) -> list:
        """Extract circular numbers from paragraph tags"""
        import re
        circular_nums = []

        for p in p_tags:
            text = p.get_text()
            if re.search(r'[A-Z]+/\d{4}-\d{2}/\d+', text) or re.search(r'RBI/\d{4}-\d{2}/\d+', text):
                circular_nums.append(text.strip())

        return circular_nums

    def _extract_press_release_number(self, soup) -> str:
        """Extract press release number"""
        import re

        press_release_tags = soup.find_all('p', text=re.compile(r'Press Release: \d{4}-\d{4}/\d+'))
        for tag in press_release_tags:
            match = re.search(r'\d{4}-\d{4}/\d+', tag.get_text().strip())
            if match:
                return match.group()
        return None

    def _extract_date(self, soup) -> tuple:
        """Extract and parse publication date"""
        pub_date = soup.find('pubDate')
        date = None
        date_iso = None

        if pub_date:
            try:
                date = pub_date.get_text().strip()
                date_iso = datetime.strptime(date, '%a, %d %b %Y %H:%M:%S').isoformat()
            except ValueError as e:
                logger.warning(f"Failed to parse date: {date}, error: {e}")

        return date, date_iso

    def _extract_addressee(self, p_tags: list, date: str) -> str:
        """Extract addressee information"""
        if not date:
            return None

        for i, p in enumerate(p_tags):
            if date in p.get_text() and i + 1 < len(p_tags):
                return p_tags[i + 1].get_text().strip()
        return None

    def _extract_subject(self, soup) -> str:
        """Extract subject/title"""
        title_tags = soup.find_all('p', class_='head')
        for tag in title_tags:
            text = tag.get_text().strip()
            if text and not text.startswith('Annex'):
                return text
        return None

    def _extract_main_text(self, p_tags: list) -> str:
        """Extract main text content"""
        main_text = []
        found_subject = False

        for p in p_tags:
            if not found_subject and p.get('class') and 'head' in p.get('class'):
                found_subject = True
                continue
            if found_subject:
                main_text.append(p.get_text().strip())

        return '\n\n'.join(main_text)

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filenames for safe storage"""
        import re
        sanitized = re.sub(r"[^a-zA-Z0-9_\-]", "_", filename)
        return sanitized[:50]

    def upload_to_s3(self, file_path: str, s3_key: str) -> str:
        """Upload file to S3 with error handling"""
        if not self.s3_client:
            logger.error("S3 client not initialized")
            return None

        try:
            self.s3_client.upload_file(file_path, self.s3_bucket_name, s3_key)
            s3_url = f"https://{self.s3_bucket_name}.s3.amazonaws.com/{s3_key}"
            logger.info(f"Uploaded {file_path} to S3 as {s3_key}")
            return s3_url
        except Exception as e:
            logger.error(f"Error uploading to S3: {e}")
            return None

    def article_exists_in_s3(self, s3_key: str) -> bool:
        """Check if article exists in S3"""
        if not self.s3_client:
            return False

        try:
            self.s3_client.head_object(Bucket=self.s3_bucket_name, Key=s3_key)
            return True
        except Exception:
            return False

    def extract_pdf_link(self, article_url: str) -> str:
        """Extract PDF link from RBI article page"""
        try:
            response = requests.get(article_url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")
            table_header = soup.find("td", class_="tableheader")

            if table_header:
                import re
                pdf_anchor = table_header.find("a", href=re.compile(r".*\.pdf", re.IGNORECASE))
                if pdf_anchor:
                    return pdf_anchor["href"]

            logger.warning(f"No PDF link found on page: {article_url}")
            return None

        except Exception as e:
            logger.error(f"Error extracting PDF link from {article_url}: {e}")
            return None

    def download_pdf(self, pdf_url: str, pdf_filepath: str) -> bool:
        """Download PDF file with error handling"""
        try:
            response = requests.get(pdf_url, timeout=60)
            response.raise_for_status()

            with open(pdf_filepath, "wb") as pdf_file:
                pdf_file.write(response.content)

            logger.info(f"Downloaded PDF from {pdf_url} to {pdf_filepath}")
            return True

        except Exception as e:
            logger.error(f"Error downloading PDF from {pdf_url}: {e}")
            return False

    def convert_published_date(self, pub_date: str) -> str:
        """Convert published date to standardized format"""
        try:
            from utils.date_utils import standardize_date_for_storage
            return standardize_date_for_storage(pub_date)
        except Exception as e:
            logger.error(f"Error converting date {pub_date}: {e}")
            return pub_date

    def process_feeds(self) -> list:
        """Main method to process all RSS feeds"""
        logger.info("Starting RSS feed processing")
        articles = []

        for feed_name, feed_url in self.rss_feeds.items():
            try:
                logger.info(f"Processing feed: {feed_name}")
                feed_articles = self._process_single_feed(feed_name, feed_url)
                articles.extend(feed_articles)

                self.stats["successful"] += len(feed_articles)

            except Exception as e:
                logger.error(f"Error processing feed {feed_name}: {e}")
                self.stats["failed"] += 1
                continue

        self.stats["total_processed"] = len(articles)
        logger.info(f"RSS processing complete. Stats: {self.stats}")
        return articles

    def _process_single_feed(self, feed_name: str, feed_url: str) -> list:
        """Process a single RSS feed"""

        feed = feedparser.parse(feed_url)
        articles = []

        if not feed.entries:
            logger.warning(f"No entries found in feed: {feed_name}")
            return articles

        logger.info(f"Processing {len(feed.entries)} entries from {feed_name}")

        for entry in feed.entries:
            try:
                article = self._process_single_entry(entry, feed_name)
                if article:
                    articles.append(article)

            except Exception as e:
                logger.error(f"Error processing entry {entry.get('title', 'Unknown')}: {e}")
                self.stats["failed"] += 1
                continue

        return articles

    def _process_single_entry(self, entry, feed_name: str) -> dict:
        """Process a single RSS entry"""
        try:
            # Extract basic information
            title = entry.title
            link = entry.link
            published_date = self.convert_published_date(entry.published)
            rss_description = entry.description

            # Parse notification content
            doc_info = self.parse_rbi_notification(rss_description)

            # Categorize document
            category = categorize_document(title)

            # Extract PDF link
            pdf_link = self.extract_pdf_link(link)

            # LLM-based analysis
            logger.info(f"🤖 STARTING LLM ANALYSIS for: {title[:50]}...")
            llm_analysis = notification_processor.analyze_notification(title, rss_description, link)
            logger.info(f"✅ LLM ANALYSIS COMPLETE")
            logger.info(f"   📂 Category: {llm_analysis.get('category', 'Unknown')}")
            logger.info(f"   🎯 Confidence: {llm_analysis.get('confidence', 'Unknown')}")
            logger.info(f"   ⚡ Requires KB Update: {llm_analysis.get('requires_kb_update', False)}")
            logger.info(f"   🔍 Affects Regulations: {llm_analysis.get('affects_regulations', False)}")

            # Extract affected documents and determine actions if KB update required
            logger.info(f"🔍 STARTING KB IMPACT ANALYSIS...")
            affected_docs, update_actions = self._analyze_kb_impact(
                title, rss_description, llm_analysis
            )
            logger.info(f"✅ KB IMPACT ANALYSIS COMPLETE")

            # Enhanced logging for debugging
            logger.info(f"📊 FINAL PROCESSING RESULTS:")
            logger.info(f"   ⚡ KB Update Required: {llm_analysis.get('requires_kb_update', False)}")
            logger.info(f"   📋 Update Actions Count: {len(update_actions.get('actions', []))}")
            logger.info(f"   📄 Affected Documents Count: {len(affected_docs.get('document_actions', []))}")
            logger.info(f"   🔗 RBI Links Found: {len(affected_docs.get('rbi_links', []))}")
            logger.info(f"   ⚠️ Manual Review Required: {update_actions.get('requires_manual_review', False)}")

            # Log specific actions for debugging
            actions = update_actions.get('actions', [])
            if actions:
                logger.info(f"   📋 SPECIFIC ACTIONS:")
                for i, action in enumerate(actions):
                    logger.info(f"      Action {i+1}: {action.get('action_type', 'N/A')} for {action.get('target_document', 'N/A')}")
            else:
                logger.info(f"   📋 NO SPECIFIC ACTIONS GENERATED")


            # Create article data
            article_data = {
                "title": title,
                "link": link,
                "published_date": published_date,
                "category": category,
                "doc_info": doc_info,
                "feed_name": feed_name,
                "id": str(uuid.uuid4()),
                "ingestion_timestamp": datetime.now().strftime('%Y-%m-%d'),
                "rss_description": rss_description,
                "llm_analysis": llm_analysis,
                "affected_documents": affected_docs,
                "update_actions": update_actions,
                "requires_kb_update": llm_analysis.get('requires_kb_update', False)
            }

            # Handle PDF processing and S3 upload
            if pdf_link:
                self._handle_pdf_processing(article_data, pdf_link, title, category)

            # Log important updates
            if category != "Informational":
                self._log_important_update(article_data)

            return article_data

        except Exception as e:
            logger.error(f"Error processing entry: {e}")
            return None

    def _analyze_kb_impact(self, title: str, rss_description: str, llm_analysis: dict) -> tuple:
        """Analyze knowledge base impact and determine actions"""
        logger.info(f"🔍 ANALYZING KB IMPACT")
        logger.info(f"   📋 Title: {title[:60]}...")
        logger.info(f"   📂 LLM Category: {llm_analysis.get('category', 'Unknown')}")
        logger.info(f"   ⚡ Requires KB Update: {llm_analysis.get('requires_kb_update', False)}")
        logger.info(f"   🎯 Confidence: {llm_analysis.get('confidence', 'Unknown')}")

        affected_docs = {}
        update_actions = {}

        if llm_analysis.get('requires_kb_update', False):
            try:
                logger.info(f"   🚀 KB update required - extracting affected documents...")

                # Extract affected documents with their actions
                affected_docs = notification_processor.extract_affected_documents(
                    title, rss_description, llm_analysis.get('category', 'Informational')
                )

                logger.info(f"   📊 Affected docs extraction result:")
                logger.info(f"      - Document actions: {len(affected_docs.get('document_actions', []))}")
                logger.info(f"      - Document keywords: {affected_docs.get('document_keywords', [])}")
                logger.info(f"      - Has new document link: {affected_docs.get('has_new_document_link', False)}")
                logger.info(f"      - RBI links: {len(affected_docs.get('rbi_links', []))}")
                logger.info(f"      - Requires manual review: {affected_docs.get('requires_manual_review', False)}")

                # Use enhanced determine_update_actions function
                logger.info(f"   🔄 Determining update actions using enhanced function...")
                update_actions = notification_processor.determine_update_actions(
                    title, llm_analysis.get('category', 'Informational'), affected_docs, rss_description
                )

                logger.info(f"   ✅ KB IMPACT ANALYSIS COMPLETE")
                logger.info(f"      - Total actions created: {len(update_actions.get('actions', []))}")
                logger.info(f"      - Manual review required: {update_actions['requires_manual_review']}")
                logger.info(f"      - New document URL: {update_actions.get('new_document_url', 'None')}")
                logger.info(f"      - Notification PDF URL: {update_actions.get('notification_pdf_url', 'None')}")
                logger.info(f"      - Related document IDs: {len(update_actions.get('related_document_ids', []))}")

            except Exception as e:
                logger.error(f"   ❌ ERROR IN KB IMPACT ANALYSIS: {e}")
                logger.error(f"      📋 Title: {title[:50]}...")
                logger.error(f"      📂 Category: {llm_analysis.get('category', 'Unknown')}")
                import traceback
                logger.error(f"      📚 Stack trace: {traceback.format_exc()}")
        else:
            logger.info(f"   ⏭️ No KB update required - skipping affected documents extraction")

        return affected_docs, update_actions

    def _handle_pdf_processing(self, article_data: dict, pdf_link: str, title: str, category: str):
        """Handle PDF download and S3 upload"""
        try:
            today = datetime.now().strftime('%Y%m%d')
            pdf_filename = self.sanitize_filename(title) + ".pdf"
            s3_key = f"{today}/{category}/{pdf_filename}"

            article_data["pdf_link"] = pdf_link

            if not self.article_exists_in_s3(s3_key):
                # Download and upload new PDF
                pdf_filepath = os.path.join("/tmp", pdf_filename)

                if self.download_pdf(pdf_link, pdf_filepath):
                    s3_url = self.upload_to_s3(pdf_filepath, s3_key)
                    if s3_url:
                        article_data["s3_url"] = s3_url
                        logger.info(f"Successfully processed PDF for: {title}")

                    # Clean up local file
                    try:
                        os.remove(pdf_filepath)
                    except Exception:
                        pass
                else:
                    logger.warning(f"Failed to download PDF for: {title}")
            else:
                # PDF already exists in S3
                article_data["s3_url"] = f"https://{self.s3_bucket_name}.s3.amazonaws.com/{s3_key}"
                logger.info(f"PDF already exists in S3 for: {title}")

        except Exception as e:
            logger.error(f"Error handling PDF processing for {title}: {e}")

    def _log_important_update(self, article_data: dict):
        """Log important updates for monitoring"""
        title = article_data.get('title', '')
        category = article_data.get('category', '')
        llm_analysis = article_data.get('llm_analysis', {})

        logger.info(f"📋 Important Update Detected: {title}")
        logger.info(f"   Category: {category}")
        logger.info(f"   LLM Category: {llm_analysis.get('category', 'Unknown')}")
        logger.info(f"   Confidence: {llm_analysis.get('confidence', 'Unknown')}")
        logger.info(f"   Requires KB Update: {article_data.get('requires_kb_update', False)}")
        logger.info(f"   Link: {article_data.get('link', '')}")


# Create global instance
rss_processor = RSSFeedProcessor()


def scrape_rss_feeds():
    """Main function to scrape RSS feeds and return articles with LLM analysis"""
    dag_monitor.log_task_start("RSS Feed Scraping")

    try:
        # Use the global RSS processor instance
        articles = rss_processor.process_feeds()

        logger.info(f"Scraping completed. Articles type: {type(articles)}, count: {len(articles)}")

        # Log sample article structure for debugging
        if articles:
            sample_article = articles[0]
            logger.info(f"Sample article keys: {list(sample_article.keys())}")
            logger.info(f"Sample article LLM analysis: {sample_article.get('llm_analysis', 'Missing')}")

        dag_monitor.log_task_end("RSS Feed Scraping", True, f"Successfully processed {len(articles)} articles")
        return articles

    except Exception as e:
        dag_monitor.log_task_end("RSS Feed Scraping", False, str(e))
        raise


def store_articles_in_mongo(**context):
    """Store articles in MongoDB and return them for the next task"""
    from pymongo import MongoClient
    import requests

    task_instance = context['task_instance']
    articles = task_instance.xcom_pull(
        task_ids='scrape_and_store_group.scrape_rss_feeds')

    if not articles:
        logger.warning("No articles received from scraping task")
        return []

    logger.info(f"📊 STORING {len(articles)} ARTICLES IN MONGODB")
    logger.info(f"   📋 Sample article keys: {list(articles[0].keys()) if articles else 'No articles'}")
    logger.info(f"   🤖 Sample LLM analysis: {articles[0].get('llm_analysis', 'Missing') if articles else 'No articles'}")

    # def download_tls_ca_file():
    #     try:
    #         response = requests.get(
    #             "https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
    #         response.raise_for_status()
    #         with open("/tmp/global-bundle.pem", "wb") as file:
    #             file.write(response.content)
    #         logger.info("TLS CA file downloaded successfully")
    #     except requests.RequestException as e:
    #         logger.error(f"Failed to download TLS CA file: {e}")
    #         raise

    # download_tls_ca_file()

    client = None
    try:

        params = {
            "tls": True,
            "tlsCAFile": "/tmp/global-bundle.pem",
            "replicaSet": "rs0",
            "readPreference": "secondaryPreferred",
            "retryWrites": False
        }
        if "docker" in mongodb_uri:
            params = {}

        client = MongoClient(
            mongodb_uri,
            **params
        )

        db = client["rss_feed_db"]
        collection_name = "articles"
        if collection_name not in db.list_collection_names():
            collection = db.create_collection(collection_name)
        else:
            collection = db[collection_name]

        stored_count = 0
        for article in articles:
            collection.update_one(
                {"link": article["link"]},
                {"$set": article},
                upsert=True
            )
            stored_count += 1

        logger.info(f"✅ Successfully stored {stored_count} articles in MongoDB")
        logger.info(f"📤 RETURNING {len(articles)} ARTICLES VIA XCOM for next task")

        # CRITICAL FIX: Return articles so they can be passed to the next task via XCom
        return articles

    except Exception as e:
        logger.error(f"❌ Error storing articles in MongoDB: {e}")
        raise
    finally:
        if client:
            client.close()


# --- ACTION-BASED PROCESSING HELPERS ---
# Note: Individual document actions are now executed directly through kb_update_executor


def _process_new_document(article: dict, article_id: str, title: str) -> bool:
    """
    Process a new document using the regs_from_s3 methodology:
    - Download and parse PDF to HTML AST
    - Generate document summary and topics using LLM
    - Use QdrantVectorStore.add_texts() for batch processing with rich context
    - Include structured text: summary + topics + content in each chunk
    - Comprehensive logging for all Qdrant operations
    """
    import os, requests, tempfile
    from utils.openai_utils import strip_data_attributes

    pdf_url = article.get("pdf_link")
    if not pdf_url:
        logger.warning(f"No PDF link found for new document: {title}")
        return False

    try:
        logger.info(f"📄 Processing new document using regs methodology: {title}")

        # Step 1: Download PDF to a temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmpf:
            logger.info(f"🔽 Downloading PDF from: {pdf_url}")
            response = requests.get(pdf_url, timeout=60)
            response.raise_for_status()
            tmpf.write(response.content)
            pdf_path = tmpf.name
            logger.info(f"✅ PDF downloaded to: {pdf_path}")

        # Step 2: Parse PDF into HTML AST and chunk it
        logger.info(f"📖 Parsing PDF to HTML AST...")
        soup = parse_pdf_to_html_ast(pdf_path)
        full_html = str(soup)

        logger.info(f"✂️ Chunking HTML content...")
        chunks = chunk_html_ast(soup, max_chars=3000)
        logger.info(f"📊 Generated {len(chunks)} chunks for {title}")

        # Step 3: Generate document summary and topics using LLM
        logger.info(f"🤖 Generating document summary and topics using LLM...")
        try:
            # Use a subset of the document for summary generation (first 6000 chars)
            document_slice = full_html[:6000]

            summary_prompt = (
                "Analyze the following RBI document and provide:\n"
                "1. A concise summary (2-3 sentences) of the main content\n"
                "2. Key topics/themes (comma-separated list)\n\n"
                f"Document content:\n{strip_data_attributes(document_slice)}"
            )

            summary_response = openai_manager.get_completion(summary_prompt)

            # Parse the response to extract summary and topics
            lines = summary_response.strip().split('\n')
            document_summary = ""
            topics = []

            for line in lines:
                if line.strip():
                    if not document_summary:
                        document_summary = line.strip()
                    elif ',' in line:
                        topics = [topic.strip() for topic in line.split(',')]
                        break

            if not document_summary:
                document_summary = f"RBI document: {title}"
            if not topics:
                topics = ["RBI", "Banking", "Regulation"]

            logger.info(f"📝 Generated summary: {document_summary[:100]}...")
            logger.info(f"🏷️ Generated topics: {topics}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to generate LLM summary, using defaults: {e}")
            document_summary = f"RBI document: {title}"
            topics = ["RBI", "Banking", "Regulation"]

        # Step 4: Prepare metadata following regs_from_s3 structure
        document_metadata = {
            "document_title": title,
            "document_type": article.get("category", "notification"),
            "published_date": article.get("published_date"),
            "link": article.get("link"),
            "pdf_url": pdf_url,
            "source": "rss_feed",
            "processed_date": datetime.now().isoformat()
        }

        # Step 5: Store metadata in MongoDB following regs_from_s3 methodology
        # Add required fields that might be missing for RSS feed documents
        complete_metadata = {
            **document_metadata,
            "document_number": article_id,  # Use article_id as document_number for RSS feeds
            "document_summary": document_summary,
            "topics": topics,
            "summary": document_summary,  # Add both summary and document_summary for compatibility
        }

        # Store in MongoDB following regs_from_s3 pattern
        success_mongo = _store_metadata_in_mongodb_regs_style(complete_metadata, article.get('category', 'notification'))

        if not success_mongo:
            logger.error(f"❌ Failed to store metadata in MongoDB for: {title}")
            return False

        # Step 6: Use regs_from_s3 methodology for Qdrant ingestion
        collection_name = f"rbi_{article.get('category', 'notification')}"
        logger.info(f"🎯 Target collection: {collection_name}")

        success = _ingest_chunks_to_qdrant_regs_style(
            collection=collection_name,
            chunks_payload=chunks,
            document_id=article_id,
            document_summary=document_summary,
            topics=topics,
            document_metadata=complete_metadata
        )

        # Step 7: Clean up temporary PDF file
        os.remove(pdf_path)
        logger.info(f"🗑️ Cleaned up temporary file: {pdf_path}")

        if success:
            logger.info(f"✅ Successfully processed new document using regs methodology: {title}")
        else:
            logger.error(f"❌ Failed to process new document using regs methodology: {title}")

        return success

    except Exception as e:
        logger.error(f"❌ Error processing new document {title} with regs methodology: {e}")
        return False


def _ingest_chunks_to_qdrant_regs_style(collection: str, chunks_payload: list, document_id: str,
                                       document_summary: str, topics: list, document_metadata: dict) -> bool:
    """
    Ingest chunks to Qdrant using the exact methodology from regs_from_s3:
    - Use QdrantVectorStore with add_texts method
    - Include document summary and topics in each chunk text
    - Use proper metadata structure with document_id and chunk_index
    - Comprehensive logging for all operations
    """
    try:
        # Use comprehensive logging utilities
        log_qdrant_operation_start("BATCH_INGESTION_REGS_METHODOLOGY", collection, document_id, len(chunks_payload))
        logger.info(f"📝 Document Summary: {document_summary[:100]}...")
        logger.info(f"🏷️ Topics: {topics}")

        # Use centralized Qdrant manager for consistent connection handling
        logger.info(f"🔗 Connecting to Qdrant using centralized manager...")

        try:
            client = qdrant_manager.client
            # Test the connection
            client.get_collections()
            logger.info(f"✅ Qdrant client initialized and connected successfully via qdrant_manager")
            logger.info(f"🔗 Connected to: {qdrant_manager.config.full_url}")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Qdrant via qdrant_manager: {e}")
            logger.error(f"🔗 Attempted URL: {qdrant_manager.config.full_url}")
            raise e
        # Use centralized sparse embedder
        sparse = qdrant_manager.sparse_embedder
        client.set_sparse_model(qdrant_manager.config.sparse_model)

        logger.info(f"✅ Sparse embeddings initialized successfully using centralized manager")

        # Ensure collection exists
        logger.info(f"🔍 Checking if collection '{collection}' exists...")
        if not client.collection_exists(collection):
            logger.info(f"🆕 Collection '{collection}' does not exist, creating...")
            sparse_params = client.get_fastembed_sparse_vector_params()

            try:
                client.create_collection(
                    collection_name=collection,
                    vectors_config={"size": 3072, "distance": "Cosine"},  # OpenAI text-embedding-3-large size
                    sparse_vectors_config=sparse_params,
                    hnsw_config={"m": 32, "ef_construct": 150, "full_scan_threshold": 10000}
                )
                logger.info(f"✅ Successfully created collection '{collection}'")
                logger.info(f"   - Dense vectors: size=3072, distance=Cosine")
                logger.info(f"   - Sparse vectors: BM25 configured")
                logger.info(f"   - HNSW config: m=32, ef_construct=150, full_scan_threshold=10000")
            except Exception as e:
                logger.warning(f"⚠️ Collection creation failed (may already exist): {e}")
        else:
            logger.info(f"✅ Collection '{collection}' already exists")

        # Initialize QdrantVectorStore following regs_from_s3 methodology
        logger.info(f"🔧 Initializing QdrantVectorStore...")
        qstore = QdrantVectorStore(
            client=client,
            collection_name=collection,
            embedding=en_embeddings,  # Use OpenAI embeddings from utils
            sparse_embedding=sparse,
            retrieval_mode=RetrievalMode.HYBRID,
            vector_name="",  # Default vector name for dense embeddings
            sparse_vector_name="fast-sparse-bm25"
        )
        logger.info(f"✅ QdrantVectorStore initialized successfully")
        logger.info(f"   - Embedding model: OpenAI")
        logger.info(f"   - Sparse embedding: BM25")
        logger.info(f"   - Retrieval mode: HYBRID")
        logger.info(f"   - Sparse vector name: fast-sparse-bm25")

        # Prepare texts and metadata following regs_from_s3 format
        logger.info(f"📝 Preparing chunk texts and metadata...")
        texts, metas = [], []

        for idx, payload in enumerate(chunks_payload):
            # Extract content and strip data attributes
            html_content = payload.get("content", "")

            # Create structured chunk text following regs_from_s3 methodology
            chunk_text = (
                f"Document Summary:\n{document_summary}\n\n"
                f"Topics:\n{', '.join(topics)}\n\n"
                f"Content:\n{html_content}"
            )
            texts.append(chunk_text)

            # Create metadata following regs_from_s3 structure with proper nesting
            meta = {
                "document_id": document_id,
                "chunk_index": idx,
                "positions": payload.get("positions", []),
                "metadata": document_metadata
            }
            metas.append(meta)

            logger.debug(f"   Chunk {idx}: {len(chunk_text)} chars, {len(meta)} metadata fields")

        logger.info(f"✅ Prepared {len(texts)} chunk texts and metadata objects")

        # Perform batch ingestion using add_texts
        logger.info(f"🚀 Starting batch ingestion to Qdrant...")
        logger.info(f"   - Method: QdrantVectorStore.add_texts()")
        logger.info(f"   - Texts: {len(texts)} items")
        logger.info(f"   - Metadata: {len(metas)} items")

        qstore.add_texts(texts, metas)

        # Use comprehensive logging utilities for success
        log_qdrant_operation_end("BATCH_INGESTION_REGS_METHODOLOGY", True, collection, document_id, len(texts))
        logger.info(f"📊 Additional Details:")
        logger.info(f"   - Vector types: Dense (OpenAI 3072d) + Sparse (BM25)")
        logger.info(f"   - Metadata fields per chunk: {len(metas[0]) if metas else 0}")

        return True

    except Exception as e:
        # Use comprehensive logging utilities for failure
        log_qdrant_operation_end("BATCH_INGESTION_REGS_METHODOLOGY", False, collection, document_id, len(chunks_payload), str(e))
        return False


def _store_metadata_in_mongodb_regs_style(metadata: dict, category: str) -> bool:
    """Store metadata in MongoDB following regs_from_s3 methodology"""
    try:
        from pymongo import MongoClient

        # Get MongoDB URI from environment
        mongodb_uri = Variable.get("documentdb_uri")
        if not mongodb_uri.startswith(("mongodb://", "mongodb+srv://")):
            raise ValueError("Invalid DocumentDB URI")

        # Download TLS CA file if needed
        download_tls_ca_file()

        if "local" or "doker" in mongodb_uri:
            client = MongoClient(
                mongodb_uri,
                tls=False
            )
        else:
            client = MongoClient(
                mongodb_uri,
                tls=True,
                tlsCAFile="/tmp/global-bundle.pem",
                replicaSet="rs0",
                readPreference="secondaryPreferred",
                retryWrites=False
            )

        try:
            # Use the same database and collection naming as regs_from_s3
            db = client["rbi"]
            collection_name = f"rbi_{category}"  # e.g., rbi_notification, rbi_press_release

            if collection_name not in db.list_collection_names():
                db.create_collection(collection_name)

            collection = db[collection_name]

            # Use document_number as unique identifier, following regs_from_s3 pattern
            unique_id = metadata.get("document_number")
            if not unique_id:
                logger.error("No document_number found in metadata")
                return False

            # Store metadata nested under metadata field, following the actual regs_from_s3 pattern
            document_structure = {
                "metadata": metadata
            }
            collection.update_one(
                {"_id": unique_id},
                {"$set": document_structure},
                upsert=True
            )

            logger.info(f"✅ Stored metadata in MongoDB: {collection_name}.{unique_id}")
            return True

        finally:
            client.close()

    except Exception as e:
        logger.error(f"❌ Failed to store metadata in MongoDB: {e}")
        return False


# Note: Document processing functions removed - actions are now executed directly through kb_update_executor


# --- ENHANCED PROCESSING ---
def process_articles_and_update_knowledge_base(**context):
    """
    Action-based unified function that processes articles based on LLM analysis results.

    The function:
    1. First analyzes each article's LLM results to determine required action
    2. Based on the action type, performs appropriate Qdrant operations:
       - NEW_DOCUMENT: Add to Qdrant with vectors
       - UPDATE_DOCUMENT: Update existing points in Qdrant
       - REMOVE_DOCUMENT: Remove points from Qdrant
       - ADD_TEMPORARY_NOTE: Add notification without affecting existing docs
       - NO_ACTION: Skip Qdrant operations
    3. Eliminates the backwards approach of "add first, then decide"
    """
    dag_monitor.log_task_start("Action-Based Article Processing & KB Updates")

    try:
        task_instance = context['task_instance']

        # Try to get articles from XCom with detailed logging
        logger.info("🔍 ATTEMPTING TO RETRIEVE ARTICLES FROM XCOM...")
        logger.info("   📋 Trying to pull from: 'scrape_and_store_group.store_articles_in_mongo'")

        # CRITICAL FIX: Pull from the store_articles_in_mongo task, not scrape_rss_feeds
        articles = task_instance.xcom_pull(task_ids='scrape_and_store_group.store_articles_in_mongo')

        logger.info(f"   📊 XCom pull result: {type(articles)}, length: {len(articles) if articles else 'None'}")

        if not articles:
            logger.error("❌ NO ARTICLES RECEIVED FROM XCOM - DEBUGGING...")

            # Try pulling from scrape_rss_feeds as fallback
            logger.info("   🔄 Trying fallback: pulling from 'scrape_and_store_group.scrape_rss_feeds'")
            articles = task_instance.xcom_pull(task_ids='scrape_and_store_group.scrape_rss_feeds')
            logger.info(f"   📊 Fallback result: {type(articles)}, length: {len(articles) if articles else 'None'}")

            if not articles:
                # Debug: List all available XCom keys
                logger.error("   🔍 Checking all available XCom data...")
                all_xcom_store = task_instance.xcom_pull(task_ids='scrape_and_store_group.store_articles_in_mongo', key=None)
                all_xcom_scrape = task_instance.xcom_pull(task_ids='scrape_and_store_group.scrape_rss_feeds', key=None)
                logger.error(f"   📦 XCom from store_articles_in_mongo: {all_xcom_store}")
                logger.error(f"   📦 XCom from scrape_rss_feeds: {all_xcom_scrape}")

                dag_monitor.log_task_end("Action-Based Article Processing & KB Updates", False, "No articles received from XCom")
                return

        logger.info(f"✅ SUCCESSFULLY RETRIEVED {len(articles)} ARTICLES FROM XCOM")

        logger.info(f"🔄 Processing {len(articles)} articles using action-based approach")

        # Process each article based on its LLM analysis and determined actions
        processed_count = 0
        failed_count = 0

        for article in articles:
            article_id = article.get('id', 'unknown')
            title = article.get('title', 'Unknown')

            try:
                logger.info(f"🔄 PROCESSING ARTICLE {processed_count + 1}/{len(articles)}")
                logger.info(f"   📋 Title: {title[:60]}...")
                logger.info(f"   🆔 Article ID: {article_id}")
                logger.info(f"   🔗 Link: {article.get('link', 'N/A')}")
                logger.info(f"   📅 Published: {article.get('published_date', 'N/A')}")

                # Step 1: Extract LLM analysis results from the previous task
                llm_analysis = article.get('llm_analysis', {})
                update_actions = article.get('update_actions', {})
                affected_documents = article.get('affected_documents', {})
                requires_kb_update = article.get('requires_kb_update', False)

                category = llm_analysis.get('category', 'Informational')
                actions = update_actions.get('actions', [])
                document_actions = affected_documents.get('document_actions', [])

                logger.info(f"   📊 ARTICLE ANALYSIS SUMMARY:")
                logger.info(f"      📂 LLM Category: {category}")
                logger.info(f"      🎯 LLM Confidence: {llm_analysis.get('confidence', 'Unknown')}")
                logger.info(f"      ⚡ Requires KB Update: {requires_kb_update}")
                logger.info(f"      📋 Document Actions Count: {len(document_actions)}")
                logger.info(f"      🔧 Update Actions Count: {len(actions)}")
                logger.info(f"      ⚠️ Manual Review Required: {update_actions.get('requires_manual_review', False)}")

                # Log document actions in detail
                if document_actions:
                    logger.info(f"      📋 DOCUMENT ACTIONS DETAIL:")
                    for i, da in enumerate(document_actions):
                        logger.info(f"         {i+1}. {da.get('action_type', 'N/A')} for '{da.get('document_id', 'N/A')}' (confidence: {da.get('confidence', 'N/A')})")

                # Log update actions in detail
                if actions:
                    logger.info(f"      🔧 UPDATE ACTIONS DETAIL:")
                    for i, action in enumerate(actions):
                        logger.info(f"         {i+1}. {action.get('action_type', 'N/A')} for '{action.get('target_document', 'N/A')}' (priority: {action.get('priority', 'N/A')})")

                # Step 2: Execute document actions identified by LLM
                if not requires_kb_update:
                    logger.info(f"⏭️ No KB update required for: {title}")
                    success = True
                elif not document_actions:
                    logger.info(f"⏭️ No document actions identified for: {title}")
                    # Check if this is a new document that should be added
                    if article.get("pdf_link") and category in ['Amendment', 'New Regulation', 'Circular', 'Master Direction']:
                        logger.info(f"   📄 Processing as new document due to PDF link and category")
                        success = _process_new_document(article, article_id, title)
                    else:
                        success = True
                else:
                    # Execute each document action identified by the LLM
                    logger.info(f"   🔧 EXECUTING {len(document_actions)} DOCUMENT ACTIONS...")

                    all_results = []
                    for i, doc_action in enumerate(document_actions):
                        action_type = doc_action.get('action_type', 'NO_ACTION')
                        document_id = doc_action.get('document_id', '')
                        confidence = doc_action.get('confidence', 'medium')

                        logger.info(f"      🎯 Action {i+1}/{len(document_actions)}: {action_type} for '{document_id}' (confidence: {confidence})")

                        # Convert document action to the format expected by kb_update_executor
                        action_obj = {
                            'action_type': action_type,
                            'target_document': document_id,
                            'priority': 'high' if confidence == 'high' else 'medium',
                            'details': doc_action.get('reasoning', f"{action_type} for {document_id}"),
                            'document_metadata': {
                                'document_title': doc_action.get('document_title'),
                                'reference_number': doc_action.get('reference_number'),
                                'department': doc_action.get('department'),
                                'original_date': doc_action.get('original_date'),
                                'confidence': confidence,
                                'reasoning': doc_action.get('reasoning')
                            }
                        }

                        # Add URLs if available for ADD_DOCUMENT actions
                        if action_type == 'ADD_DOCUMENT':
                            new_doc_url = update_actions.get('new_document_url', '')
                            rbi_links = update_actions.get('rbi_links', [])
                            if new_doc_url:
                                action_obj['new_document_url'] = new_doc_url
                            elif rbi_links:
                                action_obj['rbi_page_url'] = rbi_links[0]

                        # Add expiry date for temporary notes
                        if action_type == 'ADD_TEMPORARY_NOTE':
                            effective_date = affected_documents.get('effective_date')
                            if effective_date:
                                action_obj['expiry_date'] = effective_date

                        # Execute the individual action
                        try:
                            result = kb_update_executor.execute_updates([action_obj], article)
                            all_results.extend(result)

                            if result and result[0].get('success'):
                                logger.info(f"         ✅ Successfully executed {action_type} for {document_id}")
                            else:
                                error_details = result[0].get('details', 'Unknown error') if result else 'No result returned'
                                logger.warning(f"         ⚠️ Failed to execute {action_type} for {document_id}: {error_details}")

                        except Exception as e:
                            logger.error(f"         ❌ Error executing {action_type} for {document_id}: {e}")
                            all_results.append({
                                'action': action_type,
                                'target': document_id,
                                'success': False,
                                'details': f"Exception: {str(e)}",
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })

                    # Determine overall success based on all action results
                    successful_actions = sum(1 for r in all_results if r.get('success', False))
                    total_actions = len(all_results)
                    success = successful_actions > 0  # At least one action succeeded

                    logger.info(f"   📊 DOCUMENT ACTIONS SUMMARY: {successful_actions}/{total_actions} actions successful")

                    # If no actions succeeded but we have a PDF link, try processing as new document
                    if not success and article.get("pdf_link") and category in ['Amendment', 'New Regulation', 'Circular', 'Master Direction']:
                        logger.info(f"   📄 Fallback: Processing as new document due to PDF link")
                        success = _process_new_document(article, article_id, title)

                if success:
                    processed_count += 1
                    logger.info(f"✅ Successfully processed article: {title}")
                else:
                    failed_count += 1
                    logger.error(f"❌ Failed to process article: {title}")

            except Exception as e:
                failed_count += 1
                dag_monitor.log_error_with_context(e, {"article_id": article_id, "title": title})
                logger.error(f"❌ Error processing article {title}: {e}")

        logger.info(f"📊 Action-Based Processing Summary: {processed_count}/{len(articles)} articles processed successfully")

        # Final comprehensive summary
        total_articles = len(articles)
        success_rate = (processed_count / total_articles * 100) if total_articles else 100

        summary = (f"Action-Based Processing Complete: {total_articles} total articles. "
                  f"Successfully processed: {processed_count}/{total_articles} ({success_rate:.1f}%)")

        dag_monitor.log_task_end("Action-Based Article Processing & KB Updates", True, summary)

        return {
            "processed": processed_count,
            "failed": failed_count,
            "total": total_articles,
            "success_rate": success_rate
        }

    except Exception as e:
        dag_monitor.log_error_with_context(e, {"task": "process_articles_and_update_knowledge_base"})
        dag_monitor.log_task_end("Action-Based Article Processing & KB Updates", False, str(e))
        raise





# Define default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'rss_feed_etl_dag',
    default_args=default_args,
    description='A simple RSS feed ETL DAG',
    schedule_interval=timedelta(days=1),
    catchup=False,  
)

# Define task groups
with TaskGroup("scrape_and_store_group", dag=dag) as scrape_and_store_group:
    scrape_task = PythonOperator(
        task_id='scrape_rss_feeds',
        python_callable=scrape_rss_feeds,
        dag=dag,
    )

    store_task = PythonOperator(
        task_id='store_articles_in_mongo',
        python_callable=store_articles_in_mongo,
        provide_context=True,
        dag=dag,
    )

    scrape_task >> store_task

# Unified processing task that handles both vector generation and knowledge base updates
unified_process_task = PythonOperator(
    task_id='process_articles_and_update_knowledge_base',
    python_callable=process_articles_and_update_knowledge_base,
    provide_context=True,
    dag=dag,
)

# Set task dependencies - simplified workflow
scrape_and_store_group >> unified_process_task
