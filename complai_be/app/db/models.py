# app/models.py
from pydantic import BaseModel
from datetime import datetime

# Collection names for pymongo
USER_LIMIT_COLLECTION = "user_limits"
MESSAGE_COLLECTION = "messages"
CHAT_SESSION_COLLECTION = "chat_sessions"
USER_COLLECTION = "users"
SESSION_COLLECTION = "sessions"
FEEDBACK_COLLECTION = "feedback"

# Pydantic schemas for request validation
class SignInRequest(BaseModel):
    email: str
    password: str

class UpdateUserRequest(BaseModel):
    username: str
    old_password: str
    new_password: str


class SignUpRequest(BaseModel):
    username: str
    email: str
    password: str

class QueryRequest(BaseModel):
    message: str
    session_id: str
    username: str
    origin: str

class SessionRequest(BaseModel):
    username: str

class SessionCreateRequest(BaseModel):
    username: str
    origin: str

class SessionUpdateRequest(BaseModel):
    session_id: str
    title: str
    username: str

class ChatsRequest(BaseModel):
    session_id: str
    username: str

class MetadataRequest(BaseModel):
    session_id: str
    username: str
    content: str

class FeedbackRequest(BaseModel):
    session_id: str
    message_content: str
    feedback_text: str = None
    vote: str = None
    copied: bool = None
    username: str = None
    action: str = None


