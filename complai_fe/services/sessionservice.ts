// // services/sessionservice.ts
// import {SESSION_ENDPOINT, CREATE_SESSION_ENDPOINT} from "@/app/utils/Api"

// /**
//  * Service to handle chat-related API requests
//  */
// export const chatService = {
//   /**
//    * Fetch chat sessions for a user
//    * @param username The username to fetch sessions for
//    * @returns Promise with session data
//    */
//   fetchSessions: async (username: string) => {
//     const response = await fetch(SESSION_ENDPOINT, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ username }),
//     });

//     if (!response.ok) {
//       throw new Error(`Failed to fetch sessions: ${response.status}`);
//     }

//     return response.json();
//   },

//   /**
//    * Create a new chat session
//    * @param username The username to create a session for
//    * @returns Promise with the new session_id
//    */
//   createSession: async (username: string) => {
//     const response = await fetch(CREATE_SESSION_ENDPOINT, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ username }),
//     });

//     if (!response.ok) {
//       throw new Error(`Failed to create session: ${response.status}`);
//     }

//     return response.json();
//   }
// };
// services/sessionservice.ts
import {SESSION_ENDPOINT, CREATE_SESSION_ENDPOINT, SESSION_CHATS_ENDPOINT} from "@/app/utils/Api"

/**
 * Service to handle chat-related API requests
 */
export const chatService = {
  /**
   * Fetch chat sessions for a user
   * @param username The username to fetch sessions for
   * @returns Promise with session data
   */
  fetchSessions: async (username: string) => {
    const response = await fetch(SESSION_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ username }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch sessions: ${response.status}`);
    }

    return response.json();
  },

  /**
   * Create a new chat session
   * @param username The username to create a session for
   * @returns Promise with the new session_id
   */
  createSession: async (username: string) => {
    const response = await fetch(CREATE_SESSION_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ username }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.status}`);
    }

    return response.json();
  },

  /**
   * Fetch chat messages for a specific session
   * @param sessionId The session ID to fetch messages for
   * @param username The username associated with the session
   * @returns Promise with the session's chat messages
   */
  fetchSessionChats: async (sessionId: string, username: string) => {
    const response = await fetch(SESSION_CHATS_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ 
        session_id: sessionId,
        username: username 
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch session chats: ${response.status}`);
    }

    return response.json();
  }
};