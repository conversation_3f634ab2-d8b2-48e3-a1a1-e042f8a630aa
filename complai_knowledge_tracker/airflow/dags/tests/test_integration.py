"""
Integration tests for the refactored DAGs and utilities
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# Add utils to path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "utils"))

from utils.config import config
from utils.document_pipeline import document_processor, ProcessingResult
from utils.qdrant_utils import qdrant_manager, splade_encoder
from utils.database_utils import db_manager
from utils.openai_utils import en_embeddings, openai_manager
from utils.error_handling import error_handler, validate_config

class TestConfiguration(unittest.TestCase):
    """Test configuration management"""
    
    def test_config_validation(self):
        """Test that configuration validation works"""
        # This will depend on actual Airflow Variables being set
        # In a real environment, you'd mock these
        try:
            is_valid = validate_config()
            # In test environment, this might fail due to missing variables
            # That's expected
            self.assertIsInstance(is_valid, bool)
        except Exception as e:
            # Expected in test environment without Airflow Variables
            self.assertIsInstance(e, Exception)
    
    def test_config_structure(self):
        """Test that config has expected structure"""
        self.assertTrue(hasattr(config, 'openai'))
        self.assertTrue(hasattr(config, 'qdrant'))
        self.assertTrue(hasattr(config, 'database'))
        self.assertTrue(hasattr(config, 's3'))
        self.assertTrue(hasattr(config, 'processing'))

class TestOpenAIUtils(unittest.TestCase):
    """Test OpenAI utilities"""
    
    @patch('utils.openai_utils.config')
    def test_openai_manager_initialization(self, mock_config):
        """Test OpenAI manager initialization"""
        # Mock config
        mock_openai_config = Mock()
        mock_openai_config.api_keys = ["test-key-1", "test-key-2"]
        mock_openai_config.get_next_key.return_value = "test-key-1"
        mock_config.openai = mock_openai_config
        
        # This would normally require actual OpenAI setup
        # In real tests, you'd mock the OpenAI client
        self.assertTrue(hasattr(openai_manager, 'config'))
        self.assertTrue(hasattr(openai_manager, 'client'))
    
    @patch('utils.openai_utils.OpenAI')
    def test_key_rotation(self, mock_openai_class):
        """Test key rotation functionality"""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock a function that would normally call OpenAI
        def mock_openai_call():
            return {"result": "success"}
        
        # Test that key rotation wrapper works
        result = openai_manager.with_key_rotation(mock_openai_call)
        self.assertEqual(result, {"result": "success"})

class TestQdrantUtils(unittest.TestCase):
    """Test Qdrant utilities"""
    
    @patch('utils.qdrant_utils.QdrantClient')
    def test_qdrant_manager_initialization(self, mock_client_class):
        """Test Qdrant manager initialization"""
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        self.assertTrue(hasattr(qdrant_manager, 'config'))
        self.assertTrue(hasattr(qdrant_manager, 'client'))
    
    @patch('utils.qdrant_utils.torch')
    @patch('utils.qdrant_utils.AutoTokenizer')
    @patch('utils.qdrant_utils.AutoModelForMaskedLM')
    def test_splade_encoder(self, mock_model_class, mock_tokenizer_class, mock_torch):
        """Test SPLADE encoder functionality"""
        # Mock tokenizer and model
        mock_tokenizer = Mock()
        mock_model = Mock()
        mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
        mock_model_class.from_pretrained.return_value = mock_model
        
        # Mock torch operations
        mock_tokens = {"input_ids": Mock(), "attention_mask": Mock()}
        mock_tokenizer.return_value = mock_tokens
        
        mock_logits = Mock()
        mock_model.return_value.logits = [mock_logits]
        
        # Mock torch tensor operations
        mock_torch.no_grad.return_value.__enter__ = Mock()
        mock_torch.no_grad.return_value.__exit__ = Mock()
        mock_torch.max.return_value = (Mock(), Mock())
        mock_torch.relu.return_value = Mock()
        
        # Test encoder
        try:
            result = splade_encoder.encode("test text")
            # In a real test, you'd verify the structure
            self.assertIsInstance(result, dict)
        except Exception:
            # Expected in test environment without actual models
            pass

class TestDatabaseUtils(unittest.TestCase):
    """Test database utilities"""
    
    @patch('utils.database_utils.MongoClient')
    def test_database_manager(self, mock_mongo_client):
        """Test database manager functionality"""
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client
        
        # Mock database operations
        mock_db = Mock()
        mock_collection = Mock()
        mock_client.__getitem__.return_value = mock_db
        mock_db.__getitem__.return_value = mock_collection
        mock_db.list_collection_names.return_value = ["test_collection"]
        
        # Test document operations
        test_doc = {"document_number": "TEST-001", "title": "Test Document"}
        
        # Test upsert
        result = db_manager.upsert_document(test_doc)
        # In test environment, this might fail due to connection issues
        # That's expected
        self.assertIsInstance(result, bool)

class TestDocumentPipeline(unittest.TestCase):
    """Test document processing pipeline"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
    
    @patch('utils.document_pipeline.parse_pdf_to_html_ast')
    @patch('utils.document_pipeline.extract_document_code')
    @patch('utils.document_pipeline.check_document_exists')
    def test_process_pdf_from_path(self, mock_check_exists, mock_extract_code, mock_parse_pdf):
        """Test PDF processing from path"""
        # Mock dependencies
        mock_extract_code.return_value = "TEST-001"
        mock_check_exists.return_value = False
        
        mock_soup = Mock()
        mock_soup.__str__ = Mock(return_value="<html>Test content</html>")
        mock_parse_pdf.return_value = mock_soup
        
        # Create temporary PDF file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(self.test_pdf_content)
            tmp_path = tmp_file.name
        
        try:
            # Test processing
            with patch.object(document_processor, '_extract_metadata') as mock_extract_meta:
                mock_extract_meta.return_value = {
                    "document_number": "TEST-001",
                    "document_type": "circular",
                    "is_exclusive_to_nbfc": False,
                    "is_exclusive_to_co_operative_banks": False
                }
                
                with patch.object(document_processor, '_generate_summary_and_topics') as mock_summary:
                    mock_summary.return_value = ("Test summary", ["topic1", "topic2"])
                    
                    with patch('utils.document_pipeline.store_metadata_in_documentdb') as mock_store:
                        mock_store.return_value = True
                        
                        with patch('utils.document_pipeline.ingest_chunks_to_qdrant') as mock_ingest:
                            mock_ingest.return_value = True
                            
                            result = document_processor.process_pdf_from_path(
                                pdf_path=tmp_path,
                                collection_name="test_collection"
                            )
                            
                            self.assertIsInstance(result, ProcessingResult)
                            self.assertEqual(result.document_id, "TEST-001")
        
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_processing_result_structure(self):
        """Test ProcessingResult structure"""
        result = ProcessingResult(
            success=True,
            document_id="TEST-001",
            chunks_count=5,
            obligations_count=3
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.document_id, "TEST-001")
        self.assertEqual(result.chunks_count, 5)
        self.assertEqual(result.obligations_count, 3)

class TestErrorHandling(unittest.TestCase):
    """Test error handling utilities"""
    
    def test_error_handler_logging(self):
        """Test error handler logging functionality"""
        test_error = ValueError("Test error")
        context = {"function": "test_function", "module": "test_module"}
        
        # Test error logging
        error_info = error_handler.log_error(
            error=test_error,
            context=context
        )
        
        self.assertIsInstance(error_info, dict)
        self.assertIn("timestamp", error_info)
        self.assertIn("error_type", error_info)
        self.assertIn("error_message", error_info)
        self.assertEqual(error_info["error_type"], "ValueError")
        self.assertEqual(error_info["error_message"], "Test error")
    
    def test_safe_execute(self):
        """Test safe execution wrapper"""
        from utils.error_handling import safe_execute
        
        # Test successful execution
        def success_func(x, y):
            return x + y
        
        result = safe_execute(success_func, 2, 3)
        self.assertEqual(result, 5)
        
        # Test failed execution with default return
        def fail_func():
            raise ValueError("Test error")
        
        result = safe_execute(fail_func, default_return="default")
        self.assertEqual(result, "default")

class TestIntegrationFlow(unittest.TestCase):
    """Test end-to-end integration flow"""
    
    @patch('utils.document_pipeline.boto3')
    @patch('utils.document_pipeline.requests')
    def test_pdf_processing_flow(self, mock_requests, mock_boto3):
        """Test complete PDF processing flow"""
        # Mock S3 download
        mock_s3_client = Mock()
        mock_boto3.client.return_value = mock_s3_client
        
        # Mock PDF content download
        mock_response = Mock()
        mock_response.content = self.test_pdf_content
        mock_response.raise_for_status.return_value = None
        mock_requests.get.return_value = mock_response
        
        # Test URL processing
        with patch.object(document_processor, 'process_pdf_from_path') as mock_process:
            mock_process.return_value = ProcessingResult(
                success=True,
                document_id="TEST-001",
                chunks_count=3
            )
            
            result = document_processor.process_pdf_from_url(
                pdf_url="https://example.com/test.pdf",
                collection_name="test_collection"
            )
            
            self.assertIsInstance(result, ProcessingResult)
            self.assertTrue(result.success)

if __name__ == "__main__":
    # Run tests
    unittest.main(verbosity=2)
