{"params": {"vectors": {"dense": {"size": 3072, "distance": "<PERSON><PERSON>e"}}, "shard_number": 1, "replication_factor": 1, "write_consistency_factor": 1, "on_disk_payload": true, "sparse_vectors": {"fast-sparse-bm25": {"index": {}, "modifier": "idf"}, "fast-sparse-bm25-splade": {}}}, "hnsw_config": {"m": 32, "ef_construct": 150, "full_scan_threshold": 10000, "max_indexing_threads": 0, "on_disk": false}, "optimizer_config": {"deleted_threshold": 0.2, "vacuum_min_vector_number": 1000, "default_segment_number": 0, "max_segment_size": null, "memmap_threshold": null, "indexing_threshold": 20000, "flush_interval_sec": 5, "max_optimization_threads": null}, "wal_config": {"wal_capacity_mb": 32, "wal_segments_ahead": 0}, "quantization_config": null, "strict_mode_config": {"enabled": false}, "uuid": null}