from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.hooks.base import BaseHook
from airflow.exceptions import AirflowNotFoundException
import re
import logging
import os

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,  # Increased retries for better reliability
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'qdrant_date_update_v2',  # Updated DAG name
    default_args=default_args,
    description='Standardize date metadata in Qdrant collection',
    schedule_interval=timedelta(days=1),
    start_date=datetime(2024, 4, 1),
    catchup=False,
    tags=['qdrant', 'metadata', 'date_standardization'],
)

def standardize_date_format(**kwargs):
    """
    Connect to Qdrant and update the date field format to ensure
    it follows the ISO format (YYYY-MM-DD).
    """
    from qdrant_client import QdrantClient
    from qdrant_client.http.exceptions import UnexpectedResponse
    import time

    logging.info("Starting date standardization process")

    # Get Qdrant connection details from environment variables as fallback
    try:
        conn = BaseHook.get_connection('qdrant_default')
        QDRANT_HOST = "host.docker.internal"
        QDRANT_PORT = conn.port
        QDRANT_API_KEY = conn.password or None
        logging.info(f"Successfully retrieved Qdrant connection details from Airflow. Host: {QDRANT_HOST}, Port: {QDRANT_PORT}")
    except AirflowNotFoundException:
        logging.warning("Qdrant connection not found in Airflow, using environment variables")
        QDRANT_HOST = "host.docker.internal"
        QDRANT_PORT = int(os.getenv('QDRANT_PORT', '6333'))
        QDRANT_API_KEY = os.getenv('QDRANT_API_KEY')
        logging.info(f"Using environment variables for connection. Host: {QDRANT_HOST}, Port: {QDRANT_PORT}")

    COLLECTION_NAME = "rbi_9"
    BATCH_SIZE = 100
    logging.info(f"Processing collection: {COLLECTION_NAME} with batch size: {BATCH_SIZE}")

    # Connect to Qdrant with retry mechanism
    max_retries = 5  # Increased from 3
    retry_count = 0
    client = None

    while retry_count < max_retries:
        try:
            logging.info(f"Attempting to connect to Qdrant (attempt {retry_count + 1}/{max_retries})")
            client = QdrantClient(
                host=QDRANT_HOST,
                port=QDRANT_PORT,
                api_key=QDRANT_API_KEY,
                prefer_grpc=True,  # Use more reliable gRPC protocol
                timeout=30  # Increased timeout
            )
            # Validate connection with actual API call
            client.get_collections()
            logging.info("Successfully connected to Qdrant")
            break
        except Exception as e:
            retry_count += 1
            sleep_time = 10 * retry_count  # Exponential backoff
            logging.error(f"Connection attempt {retry_count} failed: {str(e)}. Retrying in {sleep_time}s")
            time.sleep(sleep_time)
            if retry_count == max_retries:
                logging.error(f"Permanent Qdrant connection failure after {max_retries} attempts")
                raise AirflowException(f"Qdrant connection failed: {str(e)}")

    # Unified date pattern with improved hyphen handling
    date_pattern = re.compile(
        r'(?P<year>\d{4})[-/\.](?P<month>\d{1,2})[-/\.](?P<day>\d{1,2})|' # ISO/YMD formats
        r'(?P<day2>\d{1,2})[-/\.](?P<month2>\d{1,2})[-/\.](?P<year2>\d{4})'  # DMY/MDY formats
    )

    def clean_month_names(date_str):
        # Normalize common month abbreviations
        months = {month[:3].lower(): str(index).zfill(2) for index, month in enumerate(calendar.month_name) if month}
        for abbr, num in months.items():
            date_str = re.sub(rf'\b{abbr}\w*\b', num, date_str, flags=re.IGNORECASE)
        return date_str

    def normalize_date(date_str):
        """Normalize any date string to ISO format (YYYY-MM-DD)."""
        from dateutil import parser as date_parser

        if not date_str:
            return None

        date_str = str(date_str).strip().replace(',', '').replace('  ', ' ')
        try:
            parsed = date_parser.parse(date_str, dayfirst=True, fuzzy=True)
            return parsed.date().isoformat()
        except Exception as e:
            logging.warning(f"dateutil could not parse '{date_str}': {str(e)}")
            return None

    try:
        collection_info = client.get_collection(collection_name=COLLECTION_NAME)
        total_points = collection_info.points_count
        logging.info(f"Retrieved collection info. Total points to process: {total_points}")
    except UnexpectedResponse as e:
        logging.error(f"Failed to get collection info: {str(e)}")
        raise

    offset = 0
    total_updated = 0
    errors = 0

    while offset < total_points:
        try:
            logging.info(f"Processing batch starting at offset {offset}")
            search_result = client.scroll(
                collection_name=COLLECTION_NAME,
                limit=BATCH_SIZE,
                offset=offset,
            )

            points = search_result[0]
            if not points:
                logging.info("No more points to process")
                break

            logging.info(f"Retrieved {len(points)} points in current batch")

            # Update these field names to match your schema
            SOURCE_DATE_FIELD = 'date_of_issue'  # Original field name
            TARGET_DATE_FIELD = 'normalized_date_of_issue'  # New field name to store normalized date

            for point in points:
                try:
                    point_id = point.id
                    original_date = point.payload.get(SOURCE_DATE_FIELD)

                    if original_date:
                        logging.info(f"[{point_id}] Found original date: {original_date}")
                        normalized_date = normalize_date(str(original_date))

                        if normalized_date:
                            # Update both fields - keep original and add normalized
                            client.set_payload(
                                collection_name=COLLECTION_NAME,
                                points=[point_id],
                                payload={
                                    SOURCE_DATE_FIELD: original_date,  # Preserve original
                                    TARGET_DATE_FIELD: normalized_date  # Add normalized version
                                }
                            )
                            logging.info(f"[{point_id}] Added normalized date: {normalized_date}")
                            total_updated += 1
                        else:
                            logging.warning(f"[{point_id}] Could not normalize date: {original_date}")
                    else:
                        logging.debug(f"[{point_id}] No {SOURCE_DATE_FIELD} found in payload")

                except Exception as e:
                    errors += 1
                    logging.error(f"Error processing point {point_id}: {str(e)}")
                    continue

            offset += len(points)
            logging.info(f"Completed batch. Progress: {offset}/{total_points} points processed")

        except Exception as e:
            logging.error(f"Batch processing error at offset {offset}: {str(e)}")
            errors += 1
            offset += BATCH_SIZE  # Skip problematic batch
            continue

    logging.info("Process completed. Summary:")
    logging.info(f"- Total records processed: {total_points}")
    logging.info(f"- Records updated: {total_updated}")
    logging.info(f"- Errors encountered: {errors}")
    logging.info(f"- Success rate: {((total_points - errors) / total_points) * 100:.2f}%")

    return {
        "total_updated": total_updated,
        "total_errors": errors,
        "total_processed": total_points
    }

# Define the task
update_date_formats_task = PythonOperator(
    task_id='update_date_formats',
    python_callable=standardize_date_format,
    provide_context=True,
    dag=dag,
)

# Task dependencies
update_date_formats_task