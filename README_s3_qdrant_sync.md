# S3-Qdrant Sync Checker

This script compares files stored in AWS S3 with documents in Qdrant vector database to identify missing files.

## Fixed Issues

The original script had several critical issues that have been resolved:

1. **Security**: Removed hardcoded AWS credentials - now uses environment variables
2. **Logic Error**: Fixed comparison logic (was backwards - now correctly finds files in S3 but missing in Qdrant)
3. **Missing Implementation**: Implemented proper Qdrant client connection and querying
4. **Collection Mapping**: Added proper mapping between S3 folders and Qdrant collections
5. **Error Handling**: Added comprehensive error handling and validation

## Prerequisites

1. **Python Dependencies**:
   ```bash
   pip install boto3 qdrant-client pandas tqdm xlsxwriter
   ```

2. **Environment Variables**:
   ```bash
   export AWS_ACCESS_KEY_ID="your_aws_access_key"
   export AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
   export QDRANT_HOST="*************"  # Optional, defaults to this
   export QDRANT_PORT="6333"           # Optional, defaults to 6333
   ```

## Usage

```bash
python s3_qdrant_sync.py
```

## Configuration

The script is configured to check these mappings:

| S3 Folder | Qdrant Collection |
|-----------|-------------------|
| Master Directions/ | rbi_master_direction |
| Master Circulars/ | rbi_master_circular |
| Notifications/ | rbi_notification |

To add more mappings, edit the `FOLDER_COLLECTION_MAPPING` dictionary in the script.

## Output

1. **Console Output**: Real-time progress and summary
2. **Log File**: `missing_files.log` - Detailed log of all operations
3. **Excel Report**: `missing_files_report_YYYYMMDD_HHMMSS.xlsx` - Generated only if missing files are found

## How It Works

1. **S3 Scanning**: Lists all files in configured S3 folders
2. **Qdrant Querying**: Scrolls through Qdrant collections to extract `s3_url` from metadata
3. **Comparison**: Identifies files present in S3 but missing from Qdrant
4. **Reporting**: Generates detailed reports of missing files

## Security Notes

- Never commit AWS credentials to version control
- Use IAM roles or environment variables for credentials
- Ensure Qdrant access is properly secured

## Troubleshooting

- **Connection Issues**: Check network connectivity to both S3 and Qdrant
- **Permission Issues**: Verify AWS credentials have S3 read permissions
- **Collection Not Found**: Ensure Qdrant collections exist and are accessible
