# main.py
import json
import pprint
from ingestion.combine_pages import combine_pages

# Import OCR extraction and Mistralai client.
from ingestion.ocr_extractor import extract_ocr_from_pdf
from mistralai import Mistral  # Ensure the mistralai package is installed and configured
from ingestion.chunker import chunk_full_markdown
import fitz  
import re
import markdown
from bs4 import BeautifulSoup
from pydantic import BaseModel
from typing import List, Optional
from openai import OpenAI


def main():
    # Initialize the Mistralai client with your API credentials.
    client = Mistral(api_key="B2NprCHX5yZ6p2G4VKtSX9on3KHNa0EU")  # Replace with your actual API key.

    pdf_file = "/Users/<USER>/test_rbi_notification.pdf"
    
    # -------------------------------
    # STEP 0: Extract OCR from the PDF
    # -------------------------------
    ocr_response = extract_ocr_from_pdf(pdf_file, client)
    pages = ocr_response.get("pages", [])
    
    # -------------------------------
    # STEP 1: Combine markdown from all pages
    # -------------------------------
    combined_markdown, pages_sorted = combine_pages(pages)
    print(combined_markdown)
    
    # -------------------------------
    # STEP 2: Split combined markdown into chunks
    # -------------------------------
    # This attempts to split by markdown headings, preserving structure.
    chunks = chunk_full_markdown(combined_markdown, max_chars=5000)
    print(f"Total chunks created: {len(chunks)}")

    def get_text_positions(pdf_path: str, chunk_text: str, pages_sorted: list, candidate_page_indices=None) -> list:
        """
        For a given text chunk and OCR page information, search the PDF to obtain pixel locations.
        Uses PyMuPDF's search_for. If a full-chunk search fails (likely due to formatting differences),
        the chunk text is split into sentences and each sentence is searched individually.
        
        If candidate_page_indices is provided (e.g. from the last found chunk page and/or one above),
        then those pages are searched first. If no results are found, we fallback to searching all pages.
        
        Returns a list of dictionaries containing the page index and found rectangles.
        """
        doc = fitz.open(pdf_path)
        results = []
        
        # If candidate_page_indices provided, filter pages_sorted accordingly.
        if candidate_page_indices is None:
            candidate_pages = pages_sorted
        else:
            candidate_pages = [page for page in pages_sorted if page["index"] in candidate_page_indices]
        
        # Search candidate pages first.
        for page in candidate_pages:
            page_index = page["index"]
            pdf_page = doc.load_page(page_index)
            rects = pdf_page.search_for(chunk_text)
            if not rects:
                # If full-chunk search fails, split the chunk into sentences.
                sentences = re.split(r'(?<=[.!?])\s+', chunk_text)
                rects = []
                for sentence in sentences:
                    sentence = sentence.strip()
                    if sentence:
                        found = pdf_page.search_for(sentence)
                        if found:
                            rects.extend(found)
            if rects:
                rect_list = [(r.x0, r.y0, r.x1, r.y1) for r in rects]
                results.append({
                    "page_index": page_index,
                    "rectangles": rect_list
                })
        
        # Fallback: If no results found from candidate pages, search all other pages.
        if not results:
            for page in pages_sorted:
                if candidate_page_indices and page["index"] in candidate_page_indices:
                    continue  # already searched these candidate pages
                page_index = page["index"]
                pdf_page = doc.load_page(page_index)
                rects = pdf_page.search_for(chunk_text)
                if not rects:
                    sentences = re.split(r'(?<=[.!?])\s+', chunk_text)
                    rects = []
                    for sentence in sentences:
                        sentence = sentence.strip()
                        if sentence:
                            found = pdf_page.search_for(sentence)
                            if found:
                                rects.extend(found)
                if rects:
                    rect_list = [(r.x0, r.y0, r.x1, r.y1) for r in rects]
                    results.append({
                        "page_index": page_index,
                        "rectangles": rect_list
                    })
        return results

    def get_text_from_chunk(markdown_text: str) -> str:
        """
        Converts markdown text into plain text.
        """
        html = markdown.markdown(markdown_text)
        text = BeautifulSoup(html, "html.parser").get_text().replace("\n", " ")
        return text

    def process_chunks(chunks: list, pdf_file: str, pages_sorted: list) -> list:
        """
        Processes each markdown chunk:
        - Converts markdown to plain text.
        - Uses get_text_positions to obtain pixel positions from the PDF.
        - Uses candidate pages based on the last found chunk's page (and one above it).
        Returns a list of dictionaries (one per chunk) containing the original chunk and its text positions.
        """
        chunk_payloads = []
        last_found_page = None  # Track the page index where the last chunk was found.
        
        for chunk in chunks:
            text_chunk = get_text_from_chunk(chunk)
            candidate_page_indices = None
            # If we have a last_found_page, try that page and one preceding it.
            if last_found_page is not None:
                candidate_page_indices = {last_found_page}
                if last_found_page - 1 >= 0:
                    candidate_page_indices.add(last_found_page - 1)
            
            positions = get_text_positions(pdf_file, text_chunk, pages_sorted, candidate_page_indices)
            
            # Update last_found_page if any candidate yielded results.
            if positions:
                # For simplicity, we choose the first found page from the results.
                last_found_page = positions[-1]["page_index"]
            
            chunk_payloads.append({"chunk": chunk, "positions": positions})
        
        return chunk_payloads

    # -------------------------------
    # STEP 3, 4, 5 & 6: Process each chunk
    # -------------------------------
    chunk_payloads = process_chunks(chunks, pdf_file, pages_sorted)


    client = OpenAI(api_key="********************************************************************************************************************************************************************")

    class RBIMetadataExtraction(BaseModel):
        document_number: Optional[str]
        document_title: Optional[str]
        document_type: str
        date_of_issue: Optional[str]
        addressee: Optional[str]
        is_applicable_to_banks: Optional[bool]
        addressee_entities: List[str]
        addressee_person: List[str]
        applicable_departments: List[str]
        is_withdrawn: Optional[bool]
        keywords: List[str]
        effective_date: Optional[str]
        supersedes: List[str]

    def extract_rbi_metadata(document_text: str) -> RBIMetadataExtraction:
        metadata_prompt = (
            "Analyze the following RBI regulation document and generate a structured JSON output with the following fields:\n"
            "1. 'document_number' (string) - Similar patterns like RBI/YYYY-YY/NNN, DEPT.DIV.NN/NN.NN.NNN/YYYY-YY or similar reference numbers.\n"
            "2. 'document_title' (string) - The full title of the document.\n"
            "3. 'document_type' (string) - The RBI regulation/document type whether it is Master Direction, Master Circular, Notification, Press release or other.\n"
            "3. 'date_of_issue' (date) - The official issue date (string in DD/MM/YYYY format.)\n"
            "5. 'addressee' (list of strings) - The financial institutions to which the document is addressed.\n"
            "6. 'is_applicable_to_banks' (boolean) - Boolean value understood from addressee, whether it is applicable to banks not NBFCs \n"
            "7. 'addressee_entities' (list of strings) - if the regulation is directed to individual entities like Scheduled Banks (SBI etc) or NBFCs \n"
            "6. 'addressee_person' (list of strings) - If addressed to the individuals within the institution.\n"
            "7. 'applicable_departments' (array of department codes) - Departments this applies to.\n"
            "8. 'is_withdrawn' (boolean) - If the document is mentioned to be withdrawn.\n"
            "9. 'keywords' (array of strings) - Important keywords for search indexing.\n"
            "10. 'effective_date' (string in DD/MM/YYYY format) - When the circular takes effect.\n"
            "11. 'supersedes' (array of strings) - Any previous circulars this replaces.\n\n"
            "Instructions:\n"
            "- Look for department codes like DOR, DEPR, DSIM, DBR, DBS, DCM in circular number, title, authority, or signature.\n"
            "- Identify Master Directions and Notifications.\n"
            "- Extract all deadlines and reporting requirements.\n"
            "- Include penalties, technology recommendations, and deadlines.\n"
            "Return ONLY the structured JSON, no explanations.\n\n"
            "Document Content:\n"
            f"{document_text}\n"
        )

        completion = client.beta.chat.completions.parse(
            model="gpt-4.1-2025-04-14",
            messages=[
                {"role": "system", "content": "You are an expert at extracting structured data from RBI(Reserve Bank of India) regulatory documents."},
                {"role": "user", "content": metadata_prompt}
            ],
            response_format=RBIMetadataExtraction,
        )

        metadata = completion.choices[0].message.parsed
        return metadata

    metadata = extract_rbi_metadata(pages[0]['markdown'])
    # For demonstration, print the payload of the first chunk and save all payloads.
    pprint.pprint(chunk_payloads[0])
    with open("chunk_payloads.json", "w", encoding="utf-8") as out_file:
        json.dump(chunk_payloads, out_file, indent=4)

if __name__ == "__main__":
    main()
