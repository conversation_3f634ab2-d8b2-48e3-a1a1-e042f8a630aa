from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv
from pathlib import Path
import os

# Load .env file
env_path = Path('.') / '.env'
load_dotenv(dotenv_path=env_path)


class Settings(BaseSettings):
    # Google OAuth credentials
    # Other configurations
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "local")
    OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY")
    SECRET_KEY: str = os.getenv("SECRET_KEY", "default_secret_key")

    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    AZURE_OPENAI_ENDPOINT: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_API_VERSION: str = os.getenv("AZURE_OPENAI_API_VERSION")

    OPENAI_CHAT_MODEL: str = os.getenv("OPENAI_CHAT_MODEL", "gpt-4.1")
    OPENAI_STRUCTURED_OUTPUT_MODEL: str = os.getenv(
        "OPENAI_STRUCTURED_OUTPUT_MODEL", "gpt-4.1")
    OPENAI_EMBEDDING_MODEL: str = os.getenv(
        "OPENAI_EMBEDDING_MODEL", "text-embedding-3-large")
    OPENAI_ANSWER_GENERATOR_MODEL: str = os.getenv(
        "OPENAI_ANSWER_GENERATOR_MODEL", "gpt-4.1")

    MONGODB_URI: str = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
    MONGODB_COLLECTION: str = os.getenv(
        "MONGODB_COLLECTION", "rbi_bank_regulations")

    LANGFUSE_PUBLIC_KEY: str = os.getenv("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_SECRET_KEY: str = os.getenv("LANGFUSE_SECRET_KEY")
    model_config = SettingsConfigDict(env_file=env_path)

    # Redis Configuration
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")

    # Qdrant Configuration
    QDRANT_API_URL: str = os.getenv("QDRANT_API_URL", "http://localhost:6333")
    QDRANT_API_KEY: Optional[str] = os.getenv("QDRANT_API_KEY")
    QDRANT_COLLECTION: str = os.getenv(
        "QDRANT_COLLECTION", "rbi_bank_regulations")

    # Adding a custom method to validate important fields and throw errors if any are missing
    def validate(self):
        missing_values = []

        if missing_values:
            raise ValueError(
                f"Missing required configuration values: {', '.join(missing_values)}")


# Initialize settings
settings = Settings()

# Validate the settings
settings.validate()
