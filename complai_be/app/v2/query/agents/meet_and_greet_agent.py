import yaml
import logging
import re
from langfuse.decorators import observe
from ..utils.completion_client import completion_client

class MeetAndGreetAgent:
    """
    MeetAndGreetAgent handles pleasantries, greetings, and non-compliance queries.
    This agent is designed to respond to casual conversation, non-compliance questions,
    and general inquiries that don't require accessing the compliance knowledge base.
    
    Generated by Copilot
    """
    
    def __init__(self, model: str, prompt_file: str = None):
        """
        Initialize the MeetAndGreetAgent.
        
        Args:
            model (str): The LLM model to use
            prompt_file (str, optional): Path to YAML file containing the prompt. Defaults to None.
        """
        self.model = model
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Default prompt if none provided
        self.prompt = """You are an AI assistant representing the Reserve Bank of India's Compliance Department.

You should respond to greetings, pleasantries, and non-compliance related queries in a professional, helpful manner.

Please follow these guidelines:
1. Be courteous, professional, and concise in your responses
2. Identify yourself as the RBI Compliance Assistant
3. For non-compliance topics, gently steer the conversation back to banking compliance topics
4. For general greetings, respond warmly but briefly
5. For small talk, provide brief responses and suggest compliance topics the user might want to explore
6. For completely unrelated queries, politely explain that you're specifically trained to assist with RBI banking compliance matters

Remember that your primary purpose is to assist users with RBI regulatory compliance queries. 
While you can handle casual conversation, your expertise is in Indian banking regulations and compliance.

User query: {query}"""
        
        # Load custom prompt if provided
        if prompt_file:
            try:
                with open(prompt_file, "r") as f:
                    data = yaml.safe_load(f)
                    self.prompt = data.get("prompt", self.prompt)
            except Exception as e:
                self.logger.error(f"Error loading prompt file: {e}")
    
    @observe()
    def is_greeting_or_smalltalk(self, query: str) -> bool:
        """
        Determine if the query is a greeting, pleasantry, or small talk.
        
        Args:
            query (str): The user's query
            
        Returns:
            bool: True if the query is a greeting or small talk
        """
        # Common greeting patterns
        greetings = [
            r'\b(?:hi|hello|hey|greetings|good\s+(?:morning|afternoon|evening|day)|namaste)\b',
            r'\bhow\s+(?:are\s+(?:you|things)|is\s+it\s+going|have\s+you\s+been)\b',
            r'\bnice\s+to\s+(?:meet|see)\s+you\b',
            r'\bthank(?:s|\s+you)\b',
            r'^(?:hi|hello|hey)$',  # Single word greetings
            r'\bwelcome\b',
            r'\bwhat\'s\s+up\b',
            r'\bnice\s+weather\b',
            r'\bhappy\s+(?:new\s+year|birthday|holiday|weekend)\b'
        ]
        
        # Non-compliance small talk patterns
        smalltalk = [
            r'\bwho\s+(?:are\s+you|made\s+you)\b',
            r'\bwhat\s+(?:is\s+your\s+name|can\s+you\s+do|are\s+you)\b',
            r'\bhow\s+(?:is\s+(?:the\s+weather|your\s+day)|do\s+you\s+work)\b',
            r'\btell\s+(?:me\s+about\s+yourself|me\s+a\s+joke|me\s+something)\b',
            r'\bcan\s+you\s+(?:help|assist)\s+me\b',
            r'\bi\s+(?:need|want|would\s+like)\s+(?:help|assistance)\b'
        ]
        
        # Check for patterns
        for pattern in greetings + smalltalk:
            if re.search(pattern, query.lower()):
                return True
                
        # Check for very short queries (likely greetings)
        words = query.split()
        if len(words) <= 3:
            return True
            
        return False
    
    @observe()
    def run(self, query: str) -> dict:
        """
        Process the input query and generate a response for greetings, pleasantries, 
        or non-compliance queries.
        
        Args:
            query (str): The user's query
            
        Returns:
            dict: The formatted response compatible with AnswerFormatter
        """
        self.logger.info(f"Processing greeting/small talk: {query}")
        
        # Format the prompt with the query
        prompt_filled = self.prompt.format(query=query)
        
        # Generate response
        messages = [{"role": "system", "content": prompt_filled}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)
        response = response.strip()
        
        self.logger.info(f"Generated greeting response: {response[:50]}...")
        
        # Return in format expected by AnswerFormatter
        formatted_response = {
            "query": query,
            "response": response,
            "metadata": []
        }
        
        return formatted_response