import { ProcessingStep, SSEEvent } from "@/services/ssequeryservice";

export interface ActionItem {
  _id: string;
  document_number: string;
  document_title: string;
  s3_url: string;
  username: string;
  document_id?: string;
  changed_date: string; // ISO date string format like "2025-05-28T12:29:11.725000"
}
export interface CombinedHistoryItem {
  id: string;
  title: string;
  date: string;
  created_at: string;
  type: 'session' | 'action_item';
  original_data?: any;
}

export interface RegulationInfo {
  id: string;
  title: string;
  content: string;
  docCode: string;
  date: string;
  department: string;
}

export interface ReferenceItem {
 Type?: string;
  addressee?: string;
  chunk_id?: number;
  date_of_issue?: string;
  dept?: string[] | string;
  document_code?: string;
  id?: string;
  pdf_filename?: string;
  pdf_link?: string;
  // Updated positions format
  positions?: {
    page: number;
    bboxes: number[][];
  }[];
  revision_s3_url?: string;
  section_summary?: string;
  title?: string;
  document_id?: string; // For backward compatibility
  // For backward compatibility with the previous format
  content?: string;
  date?: string;
  department?: string;
  source?: string;
  _allChunks?: ReferenceItem[];
}

export interface ChatMessage {
  id: string;
  content: string;
  sender: "user" | "assistant" | "system" | "loading";
  timestamp: Date;
  fadeState?: string;
  loadingState?: string;
  metadata?: any[] | any;
  regulation?: RegulationInfo;
  feedback?: {
    vote?: "up" | "down" | null;
    text?: string;
  };
  isLoading?: boolean;
  sessionId?: string;
  sseUpdates?: ProcessingStep[];
  sseEvents?: SSEEvent[];
  uniqueKey?: string;
}

export interface GroupedChat {
  id: string;
  title: string;
  date: "Today" | "Yesterday" | "Last Week" | "Older";
  created_at: Date;
}



//component props

export interface ChatHistoryProps {
  isCollapsed: boolean;
  onToggleCollapse: (isCollapsed: boolean) => void;
  setActiveView: (view: "home" | "chat" | "action-items") => void;
  setSelectedActionItem?: (actionItem: ActionItem | null) => void;
  setShowDetailView: (ShowDetailView:boolean) => void;
}

export interface NavigationProps {
  isCollapsed: boolean;
  onToggleCollapse: (collapsed: boolean) => void;
  setActiveView: (view: "home" | "chat" | "action-items") => void;  
  setSelectedActionItem?: (actionItem: ActionItem | null) => void;  
  setShowDetailView: (ShowDetailView: boolean) => void;
}

export interface MainContentProps {
  activeView: "home" | "chat" | "action-items";
  setActiveView: (view: "home" | "chat" | "action-items") => void;
  currentRegulation: RegulationInfo | null;
  selectedDocCode: string | null;
  setselectedDocCode: (doccode: string | null) => void;
  setCurrentRegulation: (regulation: RegulationInfo | null) => void;
  onViewDetail: () => void;
  showDetailView: boolean;
  isChatHistoryCollapsed?: boolean;
  setReferences: (references: ReferenceItem[]) => void;
  setSelectedSpecificRef: (ref: ReferenceItem | null) => void;
  onActionItemsClick?: () => void;
  onClose: () => void;
  selectedActionItem?: ActionItem | null; // Add this line
  setSelectedActionItem?: (actionItem: ActionItem | null) => void; // Add this line
}

export interface ChatSectionProps {
  setActiveView: (view: "home" | "chat") => void;
  currentRegulation: RegulationInfo | null;
  selectedDocCode: string | null;
  setselectedDocCode: (doccode: string | null) => void;
  setCurrentRegulation: (regulation: RegulationInfo | null) => void;
  onViewDetail: () => void;
  initialQuery: string | null;
  setInitialQuery: (query: string | null) => void;
  showDetailView: boolean;
  onFetchReferences: (references: ReferenceItem[]) => void;
  setSelectedSpecificRef: (ref: ReferenceItem | null) => void;
  chatContainerRef: React.RefObject<HTMLDivElement>;
  onClose: () => void;
}

export interface HomeViewProps {
  inputValue: string
  setInputValue: (value: string) => void
  handleSubmit: (e: React.FormEvent) => void
  onSearch: (query: string) => void
  onActionItemsClick?: () => void
}

export interface ChatViewProps {
  messages: ChatMessage[];
  currentRegulation: RegulationInfo | null;
  selectedDocCode: string | null;
  setselectedDocCode: (doccode: string | null) => void;
  setCurrentRegulation: (regulation: RegulationInfo | null) => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  onViewDetail: () => void;
  isLoading: boolean;
  loadingPhase: "processing" | "fetching" | "analyzing" | "generating" | null;
  showDetailView?: boolean;
  onShowReferences?: (references: ReferenceItem[], messageId?: string) => void;
  chatContainerRef: React.RefObject<HTMLDivElement>;
}


export interface ChatMessageProps {
  message: ChatMessage;
  currentRegulation: RegulationInfo | null;
  selectedDocCode: string | null;
  setselectedDocCode?: (doccode: string | null) => void;
  setCurrentRegulation?: (regulation: RegulationInfo | null) => void;
  onViewDetail: () => void;
  showDetailView?: boolean;
  onShowReferences?: (references: ReferenceItem[], messageId?: string) => void;
  sessionId?: string;
}