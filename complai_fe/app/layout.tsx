// //layout.tsx
// import type React from "react"
// import type { Metadata } from "next"
// import { Figtree } from "next/font/google"
// import { ChatProvider } from '@/contexts/ChatContext'
// import "./globals.css"

// const figtree = Figtree({ subsets: ["latin"] })

// export const metadata: Metadata = {
//   title: "Complai - Regulatory Assistant",
//   description: "Your AI assistant for financial regulations",
//   generator: "v0.dev",
// }

// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode
// }>) {
//   return (
//     <html lang="en">
//       <body className={figtree.className}>
//         <ChatProvider>
//           {children}
//         </ChatProvider>
//       </body>
//     </html>
//   )
// }

import type React from "react"
import type { Metadata } from "next"
import { Figtree } from "next/font/google"
import { ChatProvider } from '@/contexts/ChatContext'
import { AuthProvider } from '@/contexts/AuthContext'
import "./globals.css"

const figtree = Figtree({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Complai - Regulatory Assistant",
  description: "Your AI assistant for financial regulations",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={figtree.className}>
        <AuthProvider>
          <ChatProvider>
            {children}
          </ChatProvider>
        </AuthProvider>
      </body>
    </html>
  )
}

