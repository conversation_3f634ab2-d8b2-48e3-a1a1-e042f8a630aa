// //Signin.tsx
// "use client"
// import { useState } from "react"
// import { useRouter } from "next/navigation"
// import { useAuth } from "@/contexts/AuthContext"
// import { SIGNIN_ENDPOINT } from "@/app/utils/Api"
// import { cn } from "@/lib/utils"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent } from "@/components/ui/card"
// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"

// export default function SignIn({ onSuccess }: { onSuccess: () => void }) {
//   const [email, setEmail] = useState("")
//   const [password, setPassword] = useState("")
//   const [error, setError] = useState("")
//   const router = useRouter()
//   const { login } = useAuth()

//   const handleSignIn = async (e: React.FormEvent) => {
//     e.preventDefault()
//     setError("") // Clear any previous error

//     try {
//       const response = await fetch(SIGNIN_ENDPOINT, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ email, password }),
//       })

//       if (response.ok) {
//         const data = await response.json()
//         const userProfile = {
//           username: data.username,
//           email: data.email,
//         }
//         login(userProfile) // Store user in context & cookie
//         localStorage.setItem("token", data.token) // Store token in localStorage
//         onSuccess()
//         router.push("/")
//       } else {
//         const errorData = await response.json()
//         setError(errorData.detail || "Invalid credentials")
//       }
//     } catch (err) {
//       console.error("Error during sign-in:", err)
//       setError("An error occurred. Please try again.")
//     }
//   }

//   return (
//     <div className="flex flex-col gap-6">
//       <Card className="overflow-hidden">
//         <CardContent className="grid p-0 md:grid-cols-2">
//           <form onSubmit={handleSignIn} className="p-6 md:p-8">
//             <div className="flex flex-col gap-6">
//               <div className="flex flex-col items-center text-center">
//                 <h1 className="text-2xl font-bold">Welcome back</h1>
//                 <p className="text-balance text-muted-foreground">
//                   Login to your Selkea AI account
//                 </p>
//               </div>
//               {error && <p className="text-red-500 text-center">{error}</p>}
//               <div className="grid gap-2">
//                 <Label htmlFor="email">Email</Label>
//                 <Input
//                   id="email"
//                   type="email"
//                   placeholder="<EMAIL>"
//                   value={email}
//                   onChange={(e) => setEmail(e.target.value)}
//                   required
//                 />
//               </div>
//               <div className="grid gap-2">
//                 <div className="flex items-center justify-between">
//                   <Label htmlFor="password">Password</Label>
//                   <a
//                     href="#"
//                     className="text-sm underline-offset-2 hover:underline"
//                   >
//                     Forgot your password?
//                   </a>
//                 </div>
//                 <Input
//                   id="password"
//                   type="password"
//                   placeholder="Enter your password"
//                   value={password}
//                   onChange={(e) => setPassword(e.target.value)}
//                   required
//                 />
//               </div>
//               <Button type="submit" className="w-full">
//                 Login
//               </Button>
//               {/* <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
//                 <span className="relative z-10 bg-background px-2 text-muted-foreground">
//                   Or continue with
//                 </span>
//               </div>
//               <div className="grid grid-cols-3 gap-4">
//                 <Button variant="outline" className="w-full">
//                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
//                     <path
//                       d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"
//                       fill="currentColor"
//                     />
//                   </svg>
//                 </Button>
//                 <Button variant="outline" className="w-full">
//                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
//                     <path
//                       d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
//                       fill="currentColor"
//                     />
//                   </svg>
//                 </Button>
//                 <Button variant="outline" className="w-full">
//                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
//                     <path
//                       d="M6.915 4.03c-1.968 0-3.683 1.28-4.871 3.113C.704 9.208 0 11.883 0 14.449c0 .706.07 1.369.21 1.973a6.624 6.624 0 0 0 .265.86 5.297 5.297 0 0 0 .371.761c.696 1.159 1.818 1.927 3.593 1.927 1.497 0 2.633-.671 3.965-2.444.76-1.012 1.144-1.626 2.663-4.32l.756-1.339.186-.325c.061.1.121.196.183.3l2.152 3.595c.724 1.21 1.665 2.556 2.47 3.314 1.046.987 1.992 1.22 3.06 1.22 1.075 0 1.876-.355 2.455-.843a3.743 3.743 0 0 0 .81-.973c.542-.939.861-2.127.861-3.745 0-2.72-.681-5.357-2.084-7.45-1.282-1.912-2.957-2.93-4.716-2.93-1.047 0-2.088.467-3.053 1.308-.652.57-1.257 1.29-1.82 2.05-.69-.875-1.335-1.547-1.958-2.056-1.182-.966-2.315-1.303-3.454-1.303z"
//                       fill="currentColor"
//                     />
//                   </svg>
//                 </Button>
//               </div> */}
//               <div className="text-center text-sm">
//                 Don&apos;t have an account?{" "}
//                 <a
//                   href="#"
//                   className="underline underline-offset-4 cursor-pointer"
//                   onClick={() => router.push("/signup")}
//                 >
//                   Sign up
//                 </a>
//               </div>
//             </div>
//           </form>
//           <div className="relative hidden bg-muted md:block">
//             <img
//               src="/logo2.png"
//               alt="Image"
//               className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
//             />
//           </div>
//         </CardContent>
//       </Card>
//       <div className="text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
//         By clicking continue, you agree to our{" "}
//         <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
//       </div>
//     </div>
//   )
// }
"use client"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { SIGNIN_ENDPOINT } from "@/app/utils/Api"
import authframe from "@/components/assets/icons/auth-frame.png"

export default function SignIn({ onSuccess }: { onSuccess: () => void }) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()
  const { login } = useAuth()

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("") // Clear any previous error

    try {
      const response = await fetch(SIGNIN_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      })

      if (response.ok) {
        const data = await response.json()
        const userProfile = {
          username: data.username,
          email: data.email,
        }
        login(userProfile) // Store user in context & cookie
        localStorage.setItem("token", data.token) // Store token in localStorage
        onSuccess()
        router.push("/")
      } else {
        const errorData = await response.json()
        setError(errorData.detail || "Invalid email or password")
      }
    } catch (err) {
      console.error("Error during sign-in:", err)
      setError("An error occurred. Please try again.")
    }
  }
  
  return (
    <div className="relative w-screen h-screen overflow-hidden">
  {/* Background Image */}
  <img
    src={authframe.src}
    alt="Background"
    className="absolute inset-0 w-full h-full object-cover z-0"
  />

  {/* Overlay Content */}
  <div className="absolute inset-0 flex flex-col justify-center px-6 md:px-[103px] z-10">
    <div className="w-full max-w-[441px]">
      {/* Logo & Tagline */}
      <h1 className="text-[#FF6B1C] text-[40px] leading-[48px] font-bold mb-1">Complai</h1>
      <h2 className="text-[#494949] text-[32px] leading-[40px] font-semibold mb-10">
        Navigate Regulations.
      </h2>

      {/* Form */}
      <form onSubmit={handleSignIn} className="flex flex-col gap-2">
        {error && (
          <div className="text-red-500 text-sm text-center mb-2">
            {error}
          </div>
        )}

        {/* Email Input */}
        <div className="flex flex-col gap-1">
          <label htmlFor="email" className="text-sm text-[#494949] pl-[4px]">
            Email
          </label>
          <input
            id="email"
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
            />
        </div>

        {/* Password Input */}
        <div className="flex flex-col gap-1 mt-2">
          <label htmlFor="password" className="text-sm text-[#494949] pl-[4px]">
            Password
          </label>
          <input
            id="password"
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
            />
        </div>

        {/* Sign In Button */}
        <button
          type="submit"
          className="mt-6 h-[63px] bg-[#FF6B1C] text-white text-base font-bold rounded-[6px] hover:bg-[#e65928] transition-colors"
        >
          Sign In
        </button>

        {/* Link to Sign Up */}
        <div className="text-sm text-[#3B3B3B] text-center mt-4">
          Don&apos;t have an account?{" "}
          <a
            href="/signup"
            className="text-[#FF6B1C] font-semibold hover:underline"
            onClick={(e) => {
              e.preventDefault();
              router.push("/signup");
            }}
          >
            Sign up
          </a>
        </div>
      </form>
    </div>
  </div>
</div>


  )
}