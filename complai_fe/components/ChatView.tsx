//chatview.tsx
"use client";

import type React from "react";
import { useRef, useEffect, useState, useCallback } from "react";
import { ChevronUp } from "lucide-react";
import InputIcon from "@/components/assets/icons/input-icon.svg";
import type { ChatMessage, RegulationInfo, ReferenceItem, ChatViewProps } from "@/types/main_types";
import { ChatMessageComponent, getAllReferencesFromMessage, groupReferencesByDocAndPage } from "./ChatMessage";

// Firefox scrollbar style is handled inline
const firefoxScrollbarStyles = {
  scrollbarWidth: "thin" /* Firefox */,
  scrollbarColor: "#CFBFAC transparent" /* Firefox */,
};



export function ChatView({
  messages,
  currentRegulation,
  selectedDocCode,
  setselectedDocCode,
  setCurrentRegulation,
  inputValue,
  setInputValue,
  handleSubmit,
  onViewDetail,
  isLoading,
  loadingPhase,
  showDetailView = false,
  onShowReferences,
  chatContainerRef,
}: ChatViewProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [fadeState, setFadeState] = useState<string>("opacity-100");
  const [loadingMessage, setLoadingMessage] = useState<ChatMessage | null>(
    null
  );
  const messageRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const [lastVisibleMessageId, setLastVisibleMessageId] = useState<
    string | null
  >(null);

  // SSE state
  const [processingSteps, setProcessingSteps] = useState<
    Map<string, ProcessingStep[]>
  >(new Map());
  const [sseEvents, setSSEEvents] = useState<Map<string, SSEEvent[]>>(
    new Map()
  );

  // console.log("messages",messages);

  // Keep track of container width for responsive sizing
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const mainContainerRef = useRef<HTMLDivElement>(null);

  // Keep track of previous loading phase
  const prevLoadingPhaseRef = useRef(loadingPhase);

  // Add scrollbar styles with !important to ensure they override other styles
  useEffect(() => {
    // Create a style element with high specificity
    const style = document.createElement("style");
    style.textContent = `
      /* Custom scrollbar for WebKit browsers */
      .custom-scrollbar::-webkit-scrollbar {
        width: 2px !important;
        height: 2px !important;
      }
      
      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent !important;
      }
      
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #CFBFAC !important;
        border-radius: 10px !important;
      }
      
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #bfad99 !important;
      }
      
      /* Reset any other scrollbar styles that might be interfering */
      .custom-scrollbar::-webkit-scrollbar-corner,
      .custom-scrollbar::-webkit-scrollbar-button {
        display: none !important;
      }
      
      /* Hide scrollbar for textarea */
      .hide-scrollbar::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
      
      /* Ensure the scrollbar container has correct overflow */
      .custom-scrollbar {
        overflow-y: auto !important;
        scrollbar-width: thin !important;
        scrollbar-color: #CFBFAC transparent !important;
      }
    `;

    // Add the style to the head with higher priority
    document.head.appendChild(style);

    // Clean up on component unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Monitor container width for responsive adjustments
  useEffect(() => {
    if (!mainContainerRef.current) return;

    const updateWidth = () => {
      if (mainContainerRef.current) {
        // console.log(mainContainerRef);
        setContainerWidth(mainContainerRef.current.offsetWidth);
        // console.log(containerWidth);
      }
    };

    // Initial width measurement
    updateWidth();

    // Create a ResizeObserver to monitor size changes
    const resizeObserver = new ResizeObserver(updateWidth);
    resizeObserver.observe(mainContainerRef.current);

    // Cleanup
    return () => {
      if (mainContainerRef.current) {
        resizeObserver.unobserve(mainContainerRef.current);
      }
      resizeObserver.disconnect();
    };
  }, [showDetailView]);

  // Generate loading message with unique ID each time
  // Update this function in your ChatView component
  const getLoadingMessage = useCallback(
    (
      phase: "processing" | "fetching" | "analyzing" | "generating"
    ): ChatMessage => {
      const loadingMessages = {
        fetching: "is Retrieving the documents",
        processing: "is Processing the information",
        analyzing: "is analysing the information",
        generating: "is generating the answer",
      };

      return {
        id: `loading-${phase}-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        content: "",
        sender: "loading",
        timestamp: new Date(),
        loadingState: loadingMessages[phase],
        fadeState: "opacity-0",
      };
    },
    []
  );

  // Handle loading state changes
  useEffect(() => {
    if (isLoading && loadingPhase) {
      // Create new loading message immediately when phase changes
      const newLoadingMessage = getLoadingMessage(loadingPhase);
      setLoadingMessage(newLoadingMessage);

      // Set fade to visible after a short delay
      setTimeout(() => setFadeState("opacity-100"), 100);
    } else if (!isLoading) {
      // Fade out and remove loading message when loading stops
      setFadeState("opacity-0");
      setTimeout(() => {
        setLoadingMessage(null);
      }, 500);
    }
  }, [isLoading, loadingPhase, getLoadingMessage]); // Add loadingPhase to dependencies

  // Update loading message fade state
  useEffect(() => {
    if (loadingMessage && loadingMessage.fadeState !== fadeState) {
      setLoadingMessage((prev) => (prev ? { ...prev, fadeState } : null));
    }
  }, [fadeState, loadingMessage]);

  // Scroll to bottom when messages or loading state changes
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, loadingMessage]);

  // Enhanced Intersection Observer to detect visible messages and update references
  useEffect(() => {
    if (!chatContainerRef.current || !onShowReferences) return;

    const observer = new IntersectionObserver(
      (entries) => {
        let mostVisibleRegulation: RegulationInfo | null = null;
        let mostVisibleMessage: ChatMessage | null = null;
        let maxIntersectionRatio = 0;

        const visibleEntries = entries
          .filter((entry) => entry.isIntersecting)
          .map((entry) => entry.target.getAttribute("data-id"));

        const messageId = visibleEntries?.pop();

        const message = messages.find((msg) => msg.id === messageId);

        if (message?.sender === "assistant") {
          // Set the most visible message with metadata
          if (message.metadata) {
            mostVisibleMessage = message;

            // If message has regulation property directly
            if (message.regulation) {
              mostVisibleRegulation = message.regulation;
            }
          }
        }

        // Only update if we have a visible message with metadata that's different from last one
        if (
          mostVisibleMessage &&
          mostVisibleMessage.id !== lastVisibleMessageId
        ) {
          setLastVisibleMessageId(mostVisibleMessage.id);

          // Update regulation if available
          if (mostVisibleRegulation) {
            setCurrentRegulation(mostVisibleRegulation);
          }

          // Update references if metadata is present and in array format
          let allReferences = getAllReferencesFromMessage(mostVisibleMessage);
          if (allReferences.length > 0 && onShowReferences) {
            const groupedRefs = groupReferencesByDocAndPage(
              allReferences,
              message
            );
            const processedRefs = groupedRefs.map((ref) => ref.matchingRef);
            onShowReferences(processedRefs, mostVisibleMessage.id);
          }
        }
      },
      {
        root: chatContainerRef.current,
        threshold: 0.3, // Multiple thresholds for smoother detection
        // rootMargin: "15%", // Adjust vertical margin to refine detection area
      }
    );

    // Observe all message elements
    messageRefs.current.forEach((element) => {
      if (element) observer.observe(element);
    });

    return () => {
      observer.disconnect();
    };
  }, [messages, setCurrentRegulation, onShowReferences, lastVisibleMessageId]);

  // Update message refs when messages change
  const setMessageRef = useCallback(
    (id: string, element: HTMLDivElement | null) => {
      if (element) {
        messageRefs.current.set(id, element);
      } else {
        messageRefs.current.delete(id);
      }
    },
    []
  );

  // Reset textarea height when inputValue changes to empty
  useEffect(() => {
    if (inputValue === "" && textareaRef.current) {
      textareaRef.current.style.height = "24px";
    }
  }, [inputValue]);

  // Custom submit handler to reset input height
  const onFormSubmit = (e: React.FormEvent) => {
    handleSubmit(e);

    // Reset textarea height after submission
    if (textareaRef.current) {
      setTimeout(() => {
        textareaRef.current!.style.height = "24px";
      }, 0);
    }
  };

  if (!currentRegulation && !isLoading && messages.length === 0) return null;

  const inputContainerHeight = showDetailView
    ? "min-h-[119px]"
    : "min-h-[119px]";

  // Ensure each message has a unique ID, even if there are duplicates in the data
  const uniqueMessages = messages.map((message, index) => ({
    ...message,
    uniqueKey: `${message.id}-${index}`,
  }));

  // Dynamic width calculation based on the actual container width
  const getInputContainerStyle = () => {
    // Get the parent container width for responsive sizing
    const parentWidth = mainContainerRef.current?.offsetWidth || 0;

    return {
      transition: "all 0.3s ease-in-out",
      bottom: 0,
      position: "fixed" as const,
      // Always use 100% of parent width, which will automatically adapt to available space
      width: "100%",
      // Set maximum width that adapts to the actual container size
      maxWidth: `${parentWidth + 8}px`, // Subtract padding
    };
  };

  // This will dynamically adjust the message width when the sidebar expands
  const getMessageWidth = () => {
    if (containerWidth > 500) {
      return "100%"; // Full width for very small screens
    } else if (showDetailView) {
      return "100%"; // Full width when detail view is shown
    } else {
      return ""; // Default width
    }
  };

  return (
    <div className="flex flex-col h-full relative" ref={mainContainerRef}>
      {/* Messages container - flex-grow to fill available space, but with bottom padding for input */}
      <div
        className="flex-grow custom-scrollbar"
        style={{
          minHeight: "0",
          paddingBottom: "calc(80px + 2rem)",
          overscrollBehavior: "contain",
          ...firefoxScrollbarStyles,
        }}
      >
        <div
          className="flex flex-col gap-4 w-full pr-2"
          style={{ maxWidth: getMessageWidth() }}
        >
          {uniqueMessages.map((message) => (
            <div
              key={message.uniqueKey}
              ref={(el) => setMessageRef(message.id, el)}
              data-id={message.id}
              className={showDetailView ? "max-w-full" : ""}
            >
              <ChatMessageComponent
                message={message}
                currentRegulation={currentRegulation}
                selectedDocCode={selectedDocCode}
                setselectedDocCode={setselectedDocCode}
                setCurrentRegulation={setCurrentRegulation}
                onViewDetail={onViewDetail}
                showDetailView={showDetailView}
                onShowReferences={onShowReferences}
                sessionId={message.sessionId}
              />
            </div>
          ))}
          {loadingMessage && (
            <div
              key={loadingMessage.id}
              ref={(el) => setMessageRef(loadingMessage.id, el)}
              data-id={loadingMessage.id}
            >
              <ChatMessageComponent
                message={loadingMessage}
                currentRegulation={currentRegulation}
                setCurrentRegulation={setCurrentRegulation}
                onViewDetail={onViewDetail}
              />
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed input container that's truly responsive to the container width */}
      <div className="fixed bottom-0" style={getInputContainerStyle()}>
        {/* First gradient layer - this creates the fade effect while blocking content */}
        <div className="absolute inset-x-0 bottom-0 h-28 bg-gradient-to-t from-[#e2d7e5] via-[#e2d7e5] to-[#e2d7e5]/60"></div>

        {/* Original container with gradient on top */}
        <div className="relative w-full mx-auto pl-0 pb-6 pt-0 bg-gradient-to-t from-[#e2d7e5] to-transparent">
          <form onSubmit={onFormSubmit} className="w-full px-4">
            <div className="relative w-full">
              <div
                className={`${inputContainerHeight} w-full bg-white rounded-2xl border border-[#D7CBBC] px-3 py-3 flex flex-col shadow-md`}
              >
                <div className="flex justify-between items-start">
                  <textarea
                    ref={textareaRef}
                    value={inputValue}
                    onChange={(e) => {
                      setInputValue(e.target.value);
                      e.target.style.height = "24px";
                      e.target.style.height = `${Math.min(
                        200,
                        e.target.scrollHeight
                      )}px`;
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        onFormSubmit(e as unknown as React.FormEvent);
                      }
                    }}
                    placeholder="Ask anything about regulations"
                    className="w-full bg-transparent outline-none text-[#7B7B7B] font-medium text-base resize-none overflow-y-auto hide-scrollbar"
                    rows={1}
                    style={{
                      height: "24px",
                    }}
                    disabled={isLoading}
                  />
                  <button
                    type="submit"
                    className={`w-[30px] h-[30px] bg-[#FF6B1C] ${
                      isLoading
                        ? "opacity-50 cursor-not-allowed"
                        : "opacity-70 hover:opacity-100 cursor-pointer"
                    } rounded-full flex items-center justify-center flex-shrink-0 transition-opacity mt-1`}
                    disabled={isLoading}
                  >
                    {InputIcon ? (
                      <img
                        src={InputIcon.src || InputIcon}
                        alt="input"
                        className="w-4 h-4 text-white"
                      />
                    ) : (
                      <ChevronUp className="h-4 w-4 text-white" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
