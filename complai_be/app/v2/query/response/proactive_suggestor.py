import yaml
from app.v2.query.utils.completion_client import completion_client

class ProactiveSuggestor:
    def __init__(self, model: str, prompt_file: str = None):
        self.model = model
        if prompt_file:
            with open(prompt_file, "r") as f:
                data = yaml.safe_load(f)
            self.prompt = data.get("prompt", "")
        else:
            self.prompt = ("Based on the following question and answer, suggest 2 follow-up questions for a compliance officer.\n"
                           "Question: {query}\nAnswer: {answer}")
    
    def run(self, query: str, answer: str, contexts: list) -> str:
        final_prompt = self.prompt.format(query=query, answer=answer)
        messages = [{"role": "user", "content": final_prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)
        suggestions = response.strip()
        lines = suggestions.splitlines()
        return "\n".join([f"- {line.strip()}" for line in lines if line.strip()])
