# import os
# from dotenv import load_dotenv
# from langfuse import Langfuse
# from langfuse.decorators import langfuse_context

# load_dotenv()

# class Settings:
#     LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY", "pk-lf-e745f169-9923-4042-b7e0")
#     LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY", "sk-lf-3051d668-90c5-4255-a20d")
#     LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com")
    
#     # Azure OpenAI Configuration
#     AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://sriha-mb07vtte-eastus2.cognitiveservices.azure.com/")
#     AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "CY7FFUOliBCU45ChvfiUNOX8PHHimXZr8OUtoT6U2CPH378b4vqMJQQJ99BEACHYHv6XJ3w3AAAAACOG6g1h")
#     AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2025-04-14-preview")
    
#     # Model Names/Deployments
#     OPENAI_EMBEDDING_MODEL = os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-large")
#     OPENAI_STRUCTURED_OUTPUT_MODEL = os.getenv("OPENAI_STRUCTURED_OUTPUT_MODEL", "gpt-4.1")
#     OPENAI_CHAT_MODEL = os.getenv("OPENAI_CHAT_MODEL", "gpt-4.1")
#     OPENAI_ANSWER_GENERATOR_MODEL = os.getenv("OPENAI_ANSWER_GENERATOR_MODEL", "gpt-4.1")

#     # Qdrant Configuration
#     QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
#     QDRANT_API_URL = os.getenv("QDRANT_API_URL", "0.0.0.0:6333")
#     QDRANT_URL = os.getenv("QDRANT_URL", "0.0.0.0:6333")
#     QDRANT_COLLECTION = os.getenv("QDRANT_COLLECTION", "rbi_bank_regulations")
#     QDRANT_EMBEDDING_MODEL = os.getenv("QDRANT_EMBEDDING_MODEL", "text-embedding-3-large")
#     QDRANT_DICT_COLLECTION = os.getenv("QDRANT_DICT_COLLECTION", "abbreviation_dictionary_banking")

#     # MongoDB Configuration
#     # MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://selkeaadmin:<EMAIL>:27017/")
#     MONGODB_DB = os.getenv("MONGODB_DB", "rbi")
#     MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "rbi_bank_regulations")
#     # MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://selkeaadmin:<EMAIL>:27017/")
#     # # MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
#     # MONGODB_DB = os.getenv("MONGODB_DB", "rbi")
#     # MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "rbi_bank_regulations")
#     # MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://selkeaadmin:<EMAIL>:27017/")
#     MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
#     MONGODB_DB = os.getenv("MONGODB_DB", "rbi_regulations_db")
#     MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "rbi_regulations_new")
#     # MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "rbi_regulations_new_1")

#     # Redis Configuration
#     REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

#     # Set environment variables
#     os.environ["LANGFUSE_PUBLIC_KEY"] = LANGFUSE_PUBLIC_KEY
#     os.environ["LANGFUSE_SECRET_KEY"] = LANGFUSE_SECRET_KEY
#     os.environ["LANGFUSE_HOST"] = LANGFUSE_HOST
    
#     def __init__(self):
#         self.langfuse = Langfuse(
#             public_key=self.LANGFUSE_PUBLIC_KEY,
#             secret_key=self.LANGFUSE_SECRET_KEY,
#             host=self.LANGFUSE_HOST
#         )
#         # Configure Langfuse context
#         langfuse_context.configure(
#             public_key=self.LANGFUSE_PUBLIC_KEY,
#             secret_key=self.LANGFUSE_SECRET_KEY,
#             host=self.LANGFUSE_HOST,
#             enabled=True,
#         )

# settings = Settings()