#!/usr/bin/env python3
"""
Test script to check and fix Qdrant collection format issues
"""

import sys
from pathlib import Path

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))

def test_collection_format_detection():
    """Test collection format detection"""
    print("🧪 Testing Collection Format Detection...")
    
    try:
        from utils.qdrant_utils import qdrant_manager
        
        # Test collection names that might exist
        test_collections = [
            "rbi_3",
            "rbi_master_direction", 
            "rbi_circular",
            "rbi_notification",
            "test_collection"
        ]
        
        print("Checking existing collections:")
        for collection_name in test_collections:
            try:
                exists = qdrant_manager.client.collection_exists(collection_name)
                if exists:
                    format_type = qdrant_manager._check_collection_format(collection_name)
                    print(f"  ✅ {collection_name}: exists, format = {format_type}")
                    
                    # Get detailed collection info
                    try:
                        collection_info = qdrant_manager.client.get_collection(collection_name)
                        vectors_config = collection_info.config.params.vectors
                        
                        if isinstance(vectors_config, dict):
                            print(f"      Vector names: {list(vectors_config.keys())}")
                        else:
                            print(f"      Single vector config: size={vectors_config.size}")
                            
                    except Exception as e:
                        print(f"      Could not get detailed info: {e}")
                        
                else:
                    print(f"  ⚪ {collection_name}: does not exist")
                    
            except Exception as e:
                print(f"  ❌ {collection_name}: error checking - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Collection format detection test failed: {e}")
        return False

def test_collection_creation():
    """Test creating a new collection with named vectors"""
    print("\n🧪 Testing Collection Creation...")
    
    try:
        from utils.qdrant_utils import qdrant_manager
        
        test_collection = "test_named_vectors"
        
        # Delete if exists
        try:
            if qdrant_manager.client.collection_exists(test_collection):
                qdrant_manager.client.delete_collection(test_collection)
                print(f"  🗑️ Deleted existing collection: {test_collection}")
        except Exception:
            pass
        
        # Create new collection
        success = qdrant_manager.ensure_collection(test_collection)
        
        if success:
            print(f"  ✅ Created collection: {test_collection}")
            
            # Check format
            format_type = qdrant_manager._check_collection_format(test_collection)
            print(f"  📊 Collection format: {format_type}")
            
            # Get collection details
            try:
                collection_info = qdrant_manager.client.get_collection(test_collection)
                vectors_config = collection_info.config.params.vectors
                sparse_vectors_config = collection_info.config.params.sparse_vectors
                
                print(f"  📝 Dense vectors: {list(vectors_config.keys()) if isinstance(vectors_config, dict) else 'single vector'}")
                print(f"  📝 Sparse vectors: {list(sparse_vectors_config.keys()) if sparse_vectors_config else 'none'}")
                
            except Exception as e:
                print(f"  ⚠️ Could not get collection details: {e}")
            
            return True
        else:
            print(f"  ❌ Failed to create collection: {test_collection}")
            return False
            
    except Exception as e:
        print(f"❌ Collection creation test failed: {e}")
        return False

def test_point_upsert():
    """Test point upsert with different collection formats"""
    print("\n🧪 Testing Point Upsert...")
    
    try:
        from utils.qdrant_utils import qdrant_manager, generate_valid_point_id
        
        test_collection = "test_named_vectors"
        
        # Ensure collection exists
        if not qdrant_manager.client.collection_exists(test_collection):
            print("  ⚠️ Test collection doesn't exist, creating...")
            qdrant_manager.ensure_collection(test_collection)
        
        # Test data
        test_text = "This is a test document about banking regulations and compliance requirements."
        test_metadata = {
            "document_id": "TEST-001",
            "title": "Test Banking Regulation",
            "type": "test"
        }
        
        # Generate valid point ID
        point_id = generate_valid_point_id("test_point_1")
        print(f"  🆔 Generated point ID: {point_id}")
        
        # Test upsert
        success = qdrant_manager.upsert_point(
            collection_name=test_collection,
            point_id=point_id,
            payload=test_metadata,
            text_for_embedding=test_text
        )
        
        if success:
            print("  ✅ Point upsert successful")
            
            # Try to retrieve the point
            try:
                points = qdrant_manager.client.retrieve(
                    collection_name=test_collection,
                    ids=[point_id]
                )
                
                if points:
                    print(f"  ✅ Point retrieved successfully: {len(points)} points")
                    point = points[0]
                    print(f"      Point ID: {point.id}")
                    print(f"      Payload keys: {list(point.payload.keys())}")
                    
                    # Check vectors
                    if hasattr(point, 'vector') and point.vector:
                        if isinstance(point.vector, dict):
                            print(f"      Named vectors: {list(point.vector.keys())}")
                        else:
                            print(f"      Single vector: dimension {len(point.vector)}")
                    
                else:
                    print("  ⚠️ Point not found after upsert")
                    
            except Exception as e:
                print(f"  ⚠️ Could not retrieve point: {e}")
            
            return True
        else:
            print("  ❌ Point upsert failed")
            return False
            
    except Exception as e:
        print(f"❌ Point upsert test failed: {e}")
        return False

def test_existing_collection_compatibility():
    """Test compatibility with existing collections"""
    print("\n🧪 Testing Existing Collection Compatibility...")
    
    try:
        from utils.qdrant_utils import qdrant_manager, generate_valid_point_id
        
        # Check if rbi_3 exists (the problematic collection)
        problem_collection = "rbi_3"
        
        if qdrant_manager.client.collection_exists(problem_collection):
            print(f"  📋 Collection {problem_collection} exists")
            
            # Check format
            format_type = qdrant_manager._check_collection_format(problem_collection)
            print(f"  📊 Format: {format_type}")
            
            # Try to upsert a test point
            test_point_id = generate_valid_point_id("compatibility_test")
            test_payload = {"test": True, "compatibility_check": True}
            
            success = qdrant_manager.upsert_point(
                collection_name=problem_collection,
                point_id=test_point_id,
                payload=test_payload,
                text_for_embedding="Test compatibility document"
            )
            
            if success:
                print(f"  ✅ Successfully upserted to existing collection")
                
                # Clean up test point
                try:
                    qdrant_manager.client.delete(
                        collection_name=problem_collection,
                        points_selector=[test_point_id]
                    )
                    print(f"  🗑️ Cleaned up test point")
                except Exception:
                    pass
                    
                return True
            else:
                print(f"  ❌ Failed to upsert to existing collection")
                return False
        else:
            print(f"  ⚪ Collection {problem_collection} does not exist")
            return True
            
    except Exception as e:
        print(f"❌ Existing collection compatibility test failed: {e}")
        return False

def main():
    """Run all collection format tests"""
    print("🚀 Starting Collection Format Tests\n")
    
    tests = [
        ("Collection Format Detection", test_collection_format_detection),
        ("Collection Creation", test_collection_creation),
        ("Point Upsert", test_point_upsert),
        ("Existing Collection Compatibility", test_existing_collection_compatibility),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 COLLECTION FORMAT TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All collection format tests passed!")
        print("\n💡 Collection Format Features:")
        print("   ✅ Automatic format detection (named vs single vectors)")
        print("   ✅ Backward compatibility with existing collections")
        print("   ✅ Proper named vector creation for new collections")
        print("   ✅ Multi-vector support (dense + sparse)")
    else:
        print("⚠️ Some tests failed. Check the Qdrant configuration.")

if __name__ == "__main__":
    main()
