from fastapi import APIRouter, Depends
from app.db.database import get_db
import logging
from datetime import datetime, timedelta
import sys
import os

# Add the airflow dags utils to the path for date utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'complai_knowledge_tracker', 'airflow', 'dags'))
from utils.date_utils import create_date_query_filter

notification_router = APIRouter()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@notification_router.get("/list_recent")
def get_documents(db=Depends(lambda: get_db(database_name="rss_feed_db"))):
    try:
        logger.info("Fetching recent documents from the database")
        articles_collection = db["articles"]
        print(articles_collection)
        # Calculate the date for one week ago from now
        one_week_ago = datetime.now() - timedelta(days=7)

        # Create date filter using standardized date format
        date_filter = create_date_query_filter(one_week_ago, "$gte")

        # Using pymongo to find and sort the results in descending order by published_date
        cursor = articles_collection.find({"published_date": date_filter}).sort("published_date", -1)
        documents = list(cursor)
        print(documents)
        print({'published_date': {"$gte": one_week_ago}})
        filtered_documents = []

        for document in documents:
            filtered_document = {
                "_id": str(document["_id"]),  # convert ObjectId to string
                "category": document.get("category"),
                "title": document.get("title"),
                "subject": document.get("subject"),
                "circular_numbers": document.get("doc_info", {}).get("circular_numbers"),
                "press_release_number": document.get("doc_info", {}).get("press_release_number"),
                "date_iso": document.get("published_date"),
                "addressee": document.get("doc_info", {}).get("addressee"),
                "s3_url": document.get("s3_url"),
                "link": document.get("link")
            }
            filtered_documents.append(filtered_document)

        logger.info(f"Fetched {len(filtered_documents)} documents")
        return filtered_documents
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise ValueError("An error occurred while fetching documents") from e

