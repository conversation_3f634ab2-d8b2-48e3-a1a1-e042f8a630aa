"use client"

import { useState } from "react"
import { Zap, Upload, ChevronUp, MessageSquare, FileText, User } from "lucide-react"
import { Input } from "@/components/ui/input"

export default function ComplianceAssistant() {
  const [inputValue, setInputValue] = useState("")

  return (
    <div className="flex h-screen bg-[#f5f0ed]">
      {/* Left Sidebar */}
      <div className="w-[320px] border-r border-gray-200 flex flex-col">
        <div className="p-6">
          <div className="bg-white rounded-lg p-3 w-12 h-12 flex items-center justify-center mb-8">
            <div className="w-8 h-8 bg-blue-700 rounded-sm flex items-center justify-center">
              <div className="w-5 h-2 bg-yellow-400 rounded-sm ml-2"></div>
            </div>
          </div>

          <div className="flex flex-col space-y-8">
            <div className="flex items-center text-gray-500 space-x-3">
              <MessageSquare className="w-6 h-6" />
              <span>Chat</span>
            </div>

            <div className="flex items-center text-gray-500 space-x-3">
              <FileText className="w-6 h-6" />
              <span>Docs</span>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto px-6">
          <div className="text-sm text-gray-500 font-medium mb-4">RECENT CHATS</div>

          <div className="mb-6">
            <div className="text-xs text-gray-500 mb-2">Today</div>
            <div className="text-sm mb-2 py-1">SGB Importance</div>
            <div className="text-sm mb-2 py-1">Repo Rate Implications</div>
          </div>

          <div className="mb-6">
            <div className="text-xs text-gray-500 mb-2">Yesterday</div>
            <div className="text-sm mb-2 py-1">Currency Note Change</div>
            <div className="text-sm mb-2 py-1">Phishing Alert</div>
            <div className="text-sm mb-2 py-1">Money Laundering</div>
          </div>

          <div className="mb-6">
            <div className="text-xs text-gray-500 mb-2">Last Week</div>
            <div className="text-sm mb-2 py-1">Home Loan PRD</div>
            <div className="text-sm mb-2 py-1">Actions for Interest</div>
            <div className="text-sm mb-2 py-1">Money Laundering</div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center text-gray-500 space-x-3">
            <User className="w-6 h-6" />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-8">
        <div className="max-w-3xl w-full">
          <h1 className="text-3xl font-medium text-gray-700 mb-2">Good Morning, Varun!</h1>

          <div className="mb-8">
            <span className="text-3xl font-medium text-[#ff7a45]">Ask Complai</span>
            <span className="text-3xl font-medium text-gray-700"> about any regulations</span>
          </div>

          <div className="bg-white rounded-xl p-4 flex items-center mb-6 relative">
            <Input
              className="border-0 shadow-none text-base focus-visible:ring-0 focus-visible:ring-offset-0"
              placeholder="Ask anything about regulations"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
            <div className="bg-[#ff7a45] rounded-full p-2 absolute right-4">
              <ChevronUp className="w-5 h-5 text-white" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-xl p-6">
              <div className="flex items-center mb-4">
                <div className="bg-blue-500 rounded-full p-2 mr-3">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-medium">Generate Action Items</span>
              </div>
              <p className="text-gray-500 text-sm">Select a regulation and generate action items</p>
            </div>

            <div className="bg-white rounded-xl p-6">
              <div className="flex items-center mb-4">
                <div className="bg-purple-500 rounded-full p-2 mr-3">
                  <Upload className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-medium">Analyse PRD</span>
              </div>
              <p className="text-gray-500 text-sm">Upload your PRD to review for regulation compliance</p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Sidebar */}
      <div className="w-[320px] border-l border-gray-200 p-6">
        <div className="text-sm text-gray-500 font-medium mb-4">LATEST</div>

        <div className="mb-6">
          <div className="text-xs text-gray-500 mb-2">Today</div>
          <div className="mb-4">
            <div className="text-sm font-medium mb-1">Treasury Bills: Full Auction Result</div>
            <div className="text-xs text-gray-500">Mar 19, 2025</div>
          </div>

          <div className="mb-4">
            <div className="text-sm font-medium mb-1">Result of the Overnight Variable Repo Rate</div>
            <div className="text-xs text-gray-500">Mar 19, 2025</div>
          </div>
        </div>

        <div className="mb-6">
          <div className="text-xs text-gray-500 mb-2">Yesterday</div>
          <div className="mb-4">
            <div className="text-sm font-medium mb-1">
              Treatment of Right-of-Use (ROU) Asset for Regulatory Capital Purposes
            </div>
            <div className="text-xs text-gray-500">Mar 19, 2025</div>
          </div>

          <div className="mb-4">
            <div className="text-sm font-medium mb-1">Result of the Overnight Variable Repo Rate</div>
            <div className="text-xs text-gray-500">Mar 19, 2025</div>
          </div>
        </div>
      </div>
    </div>
  )
}
