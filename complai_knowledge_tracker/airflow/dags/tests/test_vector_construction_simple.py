#!/usr/bin/env python3
"""
Simple test script to verify vector construction without Airflow dependencies.
Tests the core vector generation logic that will be used in the RSS feed ETL.
"""

import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_splade_model_import():
    """Test that the new SPLADE model can be imported and used"""
    print("\n🧠 Testing SPLADE Model Import...")

    # Configure HTTP backend for Hugging Face to bypass SSL issues
    try:
        import requests
        from huggingface_hub import configure_http_backend

        def create_session_factory():
            def session_factory():
                session = requests.Session()
                session.verify = False  # Bypass SSL verification
                return session
            return session_factory

        # Configure the HTTP backend
        configure_http_backend(backend_factory=create_session_factory())
        print("   Configured Hugging Face HTTP backend to bypass SSL verification")
    except ImportError:
        print("   Could not configure Hugging Face HTTP backend - SSL issues may occur")

    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForMaskedLM
        from qdrant_client import models
        
        model_name = "naver/efficient-splade-VI-BT-large-doc"
        
        print(f"   Loading tokenizer for {model_name}...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        print(f"   Loading model for {model_name}...")
        model = AutoModelForMaskedLM.from_pretrained(model_name)
        
        print(f"✅ SPLADE model loaded successfully")
        print(f"   - Model: {model_name}")
        print(f"   - Tokenizer vocab size: {tokenizer.vocab_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ SPLADE model import failed: {e}")
        return False

def test_splade_vector_generation():
    """Test SPLADE vector generation with the new model"""
    print("\n🔢 Testing SPLADE Vector Generation...")

    # Configure HTTP backend for Hugging Face to bypass SSL issues
    try:
        import requests
        from huggingface_hub import configure_http_backend

        def create_session_factory():
            def session_factory():
                session = requests.Session()
                session.verify = False  # Bypass SSL verification
                return session
            return session_factory

        # Configure the HTTP backend
        configure_http_backend(backend_factory=create_session_factory())
        print("   Configured Hugging Face HTTP backend to bypass SSL verification")
    except ImportError:
        print("   Could not configure Hugging Face HTTP backend - SSL issues may occur")

    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForMaskedLM
        from qdrant_client import models
        
        model_name = "naver/efficient-splade-VI-BT-large-doc"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForMaskedLM.from_pretrained(model_name)
        
        test_text = "Reserve Bank of India circular on prudential norms for classification and valuation of investments."
        
        with torch.no_grad():
            tokens = tokenizer(
                test_text,
                return_tensors="pt",
                truncation=True,
                max_length=512
            )
            logits = model(**tokens).logits[0]
            max_vals, _ = torch.max(torch.relu(logits), dim=0)
            nonzero = max_vals > 0
            indices = nonzero.nonzero(as_tuple=True)[0]
            values = max_vals[indices]
            
            splade_vector = models.SparseVector(
                indices=indices.cpu().tolist(),
                values=values.cpu().tolist()
            )
        
        if isinstance(splade_vector, models.SparseVector) and len(splade_vector.indices) > 0:
            print(f"✅ SPLADE vector generated successfully")
            print(f"   - Type: {type(splade_vector)}")
            print(f"   - Non-zero entries: {len(splade_vector.indices)}")
            print(f"   - Sample indices: {splade_vector.indices[:10]}")
            print(f"   - Sample values: {splade_vector.values[:10]}")
            return True
        else:
            print(f"❌ SPLADE vector generation failed - no non-zero entries")
            return False
            
    except Exception as e:
        print(f"❌ SPLADE vector generation failed: {e}")
        return False

def test_bm25_vector_generation():
    """Test BM25 vector generation using langchain_qdrant"""
    print("\n🔢 Testing BM25 Vector Generation...")
    
    try:
        from langchain_qdrant import FastEmbedSparse
        from qdrant_client import models
        
        sparse_embedder = FastEmbedSparse(model_name="Qdrant/bm25", model_kwargs={"device": "cpu"})
        test_text = "Banking regulations require compliance with risk management procedures and capital adequacy norms."
        
        bm25_vector = sparse_embedder.embed_query(test_text)
        
        # Convert to Qdrant SparseVector format
        if hasattr(bm25_vector, 'indices') and hasattr(bm25_vector, 'values'):
            qdrant_bm25_vector = models.SparseVector(
                indices=bm25_vector.indices,
                values=bm25_vector.values
            )
        else:
            print(f"❌ BM25 vector format not recognized: {type(bm25_vector)}")
            return False
        
        if isinstance(qdrant_bm25_vector, models.SparseVector) and len(qdrant_bm25_vector.indices) > 0:
            print(f"✅ BM25 sparse vector generated successfully")
            print(f"   - Type: {type(qdrant_bm25_vector)}")
            print(f"   - Non-zero entries: {len(qdrant_bm25_vector.indices)}")
            print(f"   - Sample indices: {qdrant_bm25_vector.indices[:10]}")
            print(f"   - Sample values: {qdrant_bm25_vector.values[:10]}")
            return True
        else:
            print(f"❌ BM25 vector generation failed - no non-zero entries")
            return False
            
    except Exception as e:
        print(f"❌ BM25 vector generation failed: {e}")
        return False

def test_openai_dense_vector():
    """Test OpenAI dense vector generation (mock test)"""
    print("\n🔢 Testing Dense Vector Generation (Mock)...")
    
    try:
        # Mock OpenAI embedding response
        import random
        
        # Simulate text-embedding-3-large dimensions (3072)
        embedding_size = 3072
        dense_vector = [random.uniform(-1, 1) for _ in range(embedding_size)]
        
        if isinstance(dense_vector, list) and len(dense_vector) == embedding_size:
            print(f"✅ Dense vector generated successfully (mock)")
            print(f"   - Type: {type(dense_vector)}")
            print(f"   - Length: {len(dense_vector)}")
            print(f"   - Sample values: {dense_vector[:5]}")
            return True
        else:
            print(f"❌ Dense vector generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Dense vector generation failed: {e}")
        return False

def test_vector_format_structure():
    """Test the expected multi-vector structure format"""
    print("\n📋 Testing Multi-Vector Structure Format...")
    
    try:
        import uuid
        from qdrant_client import models
        
        # Simulate the three vector types
        dense_vector = [0.32, 0.3243, 0.123] * 1024  # Mock dense vector
        
        bm25_sparse = models.SparseVector(
            indices=[1, 5, 10, 15, 20],
            values=[0.8, 0.6, 0.9, 0.7, 0.5]
        )
        
        splade_sparse = models.SparseVector(
            indices=[2, 7, 12, 18, 25],
            values=[0.95, 0.85, 0.75, 0.65, 0.55]
        )
        
        # Expected structure
        expected_structure = {
            "id": str(uuid.uuid4()),
            "vector": {
                "dense": dense_vector,
                "fast-sparse-bm25": bm25_sparse,
                "fast-sparse-bm25-splade": splade_sparse
            },
            "payload": {
                "content": "Test document content",
                "category": "test",
                "title": "Test Document"
            }
        }
        
        # Validate structure
        checks = {
            "has_id": "id" in expected_structure,
            "has_vector": "vector" in expected_structure,
            "has_payload": "payload" in expected_structure,
            "has_dense": "dense" in expected_structure["vector"],
            "has_bm25": "fast-sparse-bm25" in expected_structure["vector"],
            "has_splade": "fast-sparse-bm25-splade" in expected_structure["vector"],
            "dense_is_list": isinstance(expected_structure["vector"]["dense"], list),
            "bm25_is_sparse": isinstance(expected_structure["vector"]["fast-sparse-bm25"], models.SparseVector),
            "splade_is_sparse": isinstance(expected_structure["vector"]["fast-sparse-bm25-splade"], models.SparseVector)
        }
        
        all_passed = all(checks.values())
        
        print(f"{'✅' if all_passed else '❌'} Multi-vector structure validation:")
        for check_name, passed in checks.items():
            print(f"   {'✅' if passed else '❌'} {check_name}: {passed}")
        
        if all_passed:
            print(f"\n🎯 Expected Qdrant multi-vector structure:")
            print(f"   {{")
            print(f"     id: '{expected_structure['id'][:8]}...',")
            print(f"     vector: {{")
            print(f"       dense: [array of {len(expected_structure['vector']['dense'])} floats],")
            print(f"       'fast-sparse-bm25': SparseVector(indices={len(bm25_sparse.indices)}, values={len(bm25_sparse.values)}),")
            print(f"       'fast-sparse-bm25-splade': SparseVector(indices={len(splade_sparse.indices)}, values={len(splade_sparse.values)})")
            print(f"     }},")
            print(f"     payload: {{{', '.join(expected_structure['payload'].keys())}}}")
            print(f"   }}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Multi-vector structure test failed: {e}")
        return False

def main():
    """Run all vector construction tests"""
    print("🚀 Starting Vector Construction Tests (Simple Version)\n")
    
    tests = [
        ("SPLADE Model Import", test_splade_model_import),
        ("SPLADE Vector Generation", test_splade_vector_generation),
        ("BM25 Vector Generation", test_bm25_vector_generation),
        ("Dense Vector Generation (Mock)", test_openai_dense_vector),
        ("Multi-Vector Structure Format", test_vector_format_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        print(f"  {'✅' if result else '❌'} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All vector construction tests passed!")
        print("✅ RSS feed ETL should correctly generate all 3 vectors:")
        print("   - Dense vectors using OpenAI text-embedding-3-large")
        print("   - BM25 sparse vectors using Qdrant/bm25 FastEmbed")
        print("   - SPLADE sparse vectors using naver/efficient-splade-VI-BT-large-doc")
        print("✅ Vectors will be formatted correctly for Qdrant multi-vector structure")
    else:
        print("⚠️ Some tests failed. Please review the vector construction implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
