def generate_summarization_prompt(conversations):
    """
    Generates a detailed summarization prompt for the ChatGPT API.

    Args:
        conversations (list): List of dictionaries containing user queries and bot responses.

    Returns:
        str: The formatted prompt string.
    """
    conversation_text = "\n".join([
        f"User: {c['query']}\nBot: {c['content']}" for c in conversations
    ])

    prompt = f"""
    Summarize the following conversation between a user and a bot. 
    Focus on the following aspects:
    - Capture the user's intent and main points in each query.
    - Summarize the bot's key responses to the queries.
    - Highlight any unresolved queries or questions requiring follow-up.
    - Ensure the summary is concise and reflects the essence of the interaction.

    Conversation:
    {conversation_text}

    Provide a summary in no more than 3 concise sentences.
    """
    return prompt.strip()
