@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-figtree, Arial, Helvetica, sans-serif);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: #f5f0ed;
    --foreground: #333333;
    --primary: #ff7a45;
    --card: #ffffff;
    --border: #e2e2e2;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    @apply bg-background text-foreground;
  }
  
  /* Scrollbar Styles */
  * {
    scrollbar-width: thin !important;
    scrollbar-color: #CFBFAC transparent !important;
  }
  
  /* WebKit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 2px !important;
    height: 2px !important;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent !important;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #CFBFAC !important;
    border-radius: 10px !important;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #bfad99 !important; /* Slightly darker on hover */
  }
  
  /* Remove corner and button elements */
  ::-webkit-scrollbar-corner,
  ::-webkit-scrollbar-button {
    display: none !important;
  }
  
  /* Hide scrollbar for specific elements */
  .hide-scrollbar {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
}

/* Enhanced Loading Animations */
@layer components {
  /* Shimmer Animation */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* Fade In Scale Animation */
  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: scale(0.95) translateY(10px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Fade Out Scale Animation */
  @keyframes fadeOutScale {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
  }

  /* Floating Particles Animation */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) translateX(0px) scale(0.8);
      opacity: 0.3;
    }
    25% {
      transform: translateY(-10px) translateX(5px) scale(1);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-20px) translateX(-5px) scale(0.9);
      opacity: 0.8;
    }
    75% {
      transform: translateY(-10px) translateX(8px) scale(1.1);
      opacity: 0.4;
    }
  }

  /* Typewriter Animation */
  @keyframes typewriter {
    0% {
      width: 0;
    }
    100% {
      width: 100%;
    }
  }

  /* Blink Animation */
  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }

  /* Bounce Delay Animation for Dots */
  @keyframes bounceDelay {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Gradient Shift Animation */
  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Pulse Wave Animation */
  @keyframes pulseWave {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.4);
      opacity: 0;
    }
  }

  /* Wobble Animation */
  @keyframes wobble {
    0% {
      transform: translateX(0%);
    }
    15% {
      transform: translateX(-25%) rotate(-5deg);
    }
    30% {
      transform: translateX(20%) rotate(3deg);
    }
    45% {
      transform: translateX(-15%) rotate(-3deg);
    }
    60% {
      transform: translateX(10%) rotate(2deg);
    }
    75% {
      transform: translateX(-5%) rotate(-1deg);
    }
    100% {
      transform: translateX(0%);
    }
  }

  /* Animation Utility Classes */
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-fadeInScale {
    animation: fadeInScale 0.4s ease-out;
  }

  .animate-fadeOutScale {
    animation: fadeOutScale 0.4s ease-in;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-typewriter {
    animation: typewriter 2s steps(40, end);
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid #FF6B1C;
  }

  .animate-blink {
    animation: blink 1s infinite;
  }

  .animate-pulse-wave {
    animation: pulseWave 2s infinite;
  }

  .animate-wobble {
    animation: wobble 1s ease-in-out;
  }

  /* Enhanced Bounce Animation */
  .animate-bounce {
    animation: bounce 1s infinite;
  }

  /* Custom Bounce Delays for Dots */
  .animate-bounce-delay-1 {
    animation: bounceDelay 1.4s infinite ease-in-out both;
    animation-delay: 0.16s;
  }

  .animate-bounce-delay-2 {
    animation: bounceDelay 1.4s infinite ease-in-out both;
    animation-delay: 0.32s;
  }

  .animate-bounce-delay-3 {
    animation: bounceDelay 1.4s infinite ease-in-out both;
    animation-delay: 0.48s;
  }

  /* Gradient Text Animation */
  .text-gradient-animate {
    background: linear-gradient(-45deg, #FF6B1C, #FFA500, #FF8C42, #FF6B1C);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Loading Spinner Variants */
  .spinner-dual-ring {
    display: inline-block;
    width: 24px;
    height: 24px;
  }

  .spinner-dual-ring:after {
    content: " ";
    display: block;
    width: 20px;
    height: 20px;
    margin: 2px;
    border-radius: 50%;
    border: 2px solid #FF6B1C;
    border-color: #FF6B1C transparent #FF6B1C transparent;
    animation: spin 1.2s linear infinite;
  }

  /* Custom Gradient Backgrounds */
  .bg-loading-gradient {
    background: linear-gradient(135deg, #DDD1C5 0%, #E0D7E2 100%);
  }

  .bg-shimmer-gradient {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  /* Text Shadow Effects */
  .text-glow {
    text-shadow: 0 0 10px rgba(255, 107, 28, 0.5);
  }

  .text-glow-strong {
    text-shadow: 0 0 20px rgba(255, 107, 28, 0.8);
  }

  /* Box Shadow Effects */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(255, 107, 28, 0.3);
  }

  .shadow-loading {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* Loading States */
  .loading-dots {
    display: inline-flex;
    gap: 2px;
  }

  .loading-dots > div {
    width: 4px;
    height: 4px;
    background-color: #FF6B1C;
    border-radius: 50%;
    animation: bounceDelay 1.4s infinite ease-in-out both;
  }

  .loading-dots > div:nth-child(1) {
    animation-delay: -0.32s;
  }

  .loading-dots > div:nth-child(2) {
    animation-delay: -0.16s;
  }

  .loading-dots > div:nth-child(3) {
    animation-delay: 0s;
  }

  /* Backdrop Blur Utilities */
  .backdrop-blur-loading {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Progress Bar Styles */
  .progress-shimmer {
    position: relative;
    overflow: hidden;
  }

  .progress-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmer 2s infinite;
  }
}