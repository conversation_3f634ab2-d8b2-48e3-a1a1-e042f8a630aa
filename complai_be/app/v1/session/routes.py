from fastapi import APIRouter, Depends, HTTPException
from app.db.database import get_db
from app.v1.session.service import get_total_sessions
import uuid
from datetime import datetime
from app.db.models import SessionCreateRequest  # Pydantic model for session creation

session_router = APIRouter()

@session_router.post("/create")
async def create_session(request: SessionCreateRequest, db=Depends(lambda: get_db())):
    """Create a new session after checking limits"""
    try:
        # Get total sessions for the user from the sessions collection
        total_sessions = get_total_sessions(request.username, db)
        print("total_sessions", total_sessions)
        origin = request.origin

        # Retrieve the user's limit document from the "user_limits" collection
        user_limit = db["user_limits"].find_one({"username": request.username})
        if not user_limit:
            user_limit = {
                "username": request.username,
                "created_at": datetime.utcnow(),
                "origin": origin,
                "max_queries_per_session": 10,
                "max_sessions": 5
            }
            db["user_limits"].insert_one(user_limit)

        # Apply limits only if origin matches the specified value
        enforce_limits = origin == "https://chat.complai-genie.online/"
        if enforce_limits and total_sessions >= 10:
            return {
                "error": "No more sessions allowed",
                "total_sessions": total_sessions,
                "max_sessions": 10,
                "can_create": False
            }

        # Create a new session document
        session_id = str(uuid.uuid4())
        session_doc = {
            "session_id": session_id,
            "username": request.username,
            "title": "New Chat",
            "created_at": datetime.utcnow(),
            "messages": [],
            "query_count": 0
        }
        db["session"].insert_one(session_doc)
        total_sessions += 1

        return {
            "session_id": session_id,
            "can_create": True,
            "total_sessions": total_sessions,
            "remaining_sessions": 5 - total_sessions  # adjust as needed
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
