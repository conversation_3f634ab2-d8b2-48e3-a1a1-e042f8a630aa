"""
Enhanced database utilities with proper connection management and error handling
"""

import logging
import requests
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from pymongo import MongoClient
from pymongo.errors import PyMongoError

from .config import config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Enhanced database manager with connection pooling and error handling"""
    
    def __init__(self):
        self.config = config.database
        self._client = None
    
    def _ensure_tls_ca_file(self):
        """Download TLS CA file if needed for DocumentDB"""
        if not self.config.use_tls:
            return
        
        try:
            import os
            if os.path.exists(self.config.tls_ca_file):
                return
            
            response = requests.get(
                "https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem",
                timeout=30
            )
            response.raise_for_status()
            
            with open(self.config.tls_ca_file, "wb") as f:
                f.write(response.content)
            
            logger.info("TLS CA file downloaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to download TLS CA file: {e}")
            raise
    
    @contextmanager
    def get_client(self):
        """Get MongoDB client with proper connection parameters"""
        client = None
        try:
            # Prepare connection parameters
            connection_params = {}
            
            # Check if we need TLS (for DocumentDB)
            if self.config.use_tls and "docker" not in self.config.uri:
                self._ensure_tls_ca_file()
                connection_params.update({
                    "tls": True,
                    "tlsCAFile": self.config.tls_ca_file,
                    "replicaSet": self.config.replica_set,
                    "readPreference": self.config.read_preference,
                    "retryWrites": self.config.retry_writes
                })
            
            client = MongoClient(self.config.uri, **connection_params)
            
            # Test connection
            client.admin.command('ping')
            
            yield client
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
        finally:
            if client:
                client.close()
    
    def upsert_document(
        self,
        document: Dict[str, Any],
        db_name: Optional[str] = None,
        collection_name: Optional[str] = None,
        unique_key: str = "document_number"
    ) -> bool:
        """
        Upsert a document to MongoDB/DocumentDB
        """
        try:
            db_name = db_name or self.config.database_name
            collection_name = collection_name or self.config.collection_name
            
            with self.get_client() as client:
                db = client[db_name]
                
                # Ensure collection exists
                if collection_name not in db.list_collection_names():
                    db.create_collection(collection_name)
                
                collection = db[collection_name]
                
                # Perform upsert
                result = collection.update_one(
                    {unique_key: document[unique_key]},
                    {"$set": document},
                    upsert=True
                )
                
                logger.info(f"Upserted document {document[unique_key]} to {db_name}.{collection_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to upsert document: {e}")
            return False
    
    def find_document(
        self,
        query: Dict[str, Any],
        db_name: Optional[str] = None,
        collection_name: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Find a document in MongoDB/DocumentDB
        """
        try:
            db_name = db_name or self.config.database_name
            collection_name = collection_name or self.config.collection_name
            
            with self.get_client() as client:
                db = client[db_name]
                collection = db[collection_name]
                
                document = collection.find_one(query)
                return document
                
        except Exception as e:
            logger.error(f"Failed to find document: {e}")
            return None
    
    def find_documents(
        self,
        query: Dict[str, Any],
        db_name: Optional[str] = None,
        collection_name: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Find multiple documents in MongoDB/DocumentDB
        """
        try:
            db_name = db_name or self.config.database_name
            collection_name = collection_name or self.config.collection_name
            
            with self.get_client() as client:
                db = client[db_name]
                collection = db[collection_name]
                
                cursor = collection.find(query)
                if limit:
                    cursor = cursor.limit(limit)
                
                documents = list(cursor)
                return documents
                
        except Exception as e:
            logger.error(f"Failed to find documents: {e}")
            return []

# Global database manager instance
db_manager = DatabaseManager()



def store_metadata_in_documentdb(metadata: Dict[str, Any]) -> bool:
    """Store metadata in DocumentDB"""
    return db_manager.upsert_document(
        metadata,
        db_name=config.database.database_name,
        collection_name=config.database.collection_name,
        unique_key="document_number"
    )

def check_document_exists(document_number: str) -> bool:
    """Check if a document exists in the database"""
    document = db_manager.find_document(
        {"document_number": document_number},
        db_name=config.database.database_name,
        collection_name=config.database.collection_name
    )
    return document is not None
