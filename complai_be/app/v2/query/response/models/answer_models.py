# Generated by Co<PERSON>lot
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional


class DraftAnswerSection(BaseModel):
    """
    Represents a distinct section within the draft answer.
    Generated by Copilot
    """
    title: str = Field(..., description="The title of the section (e.g., 'Referenced Section', 'Condensed Knowledge').")
    content: str = Field(...,
                         description="The textual content of the section.")
    source_document_ids: List[str] = Field(
        default_factory=list, description="List of document IDs used to generate this section.")


class DraftAnswer(BaseModel):
    """
    Represents the draft answer generated by the AnswerGenerator agent.
    Generated by Copilot
    """
    sections: List[DraftAnswerSection] = Field(
        ..., description="The structured sections of the draft answer.")
    summary: str = Field(
        default="", description="A brief overall summary if applicable.")
    confidence_score: float = Field(
        default=0.0, description="A score indicating the generator's confidence in the answer's accuracy.")


class AnswerReference(BaseModel):
    """
    Represents a single source reference for a portion of the answer.
    """
    document_id: str = Field(
        ..., description="Document ID of the source document. Document ID is exact value from Knowledge Context")
    page_number: int = Field(...,
                             description="Page number where the fragment was found.")
    bboxes: List[List[float]] = Field(
        ..., description="List of Bounding box [[x1, y1, x2, y2],[x1, y1, x2, y2]] of all content of Knowledge Context considered in generating for answer_part")


class AnswerPart(BaseModel):
    """
    A segment of the overall answer, optionally backed by source references.
    """
    answer_part: str = Field(
        ..., description="Markdown formatted response content of this part of the answer.")
    answer_part_references: List[AnswerReference] = Field(
        default_factory=list,
        description="List of zero or more source references for this answer segment."
    )


class AnswerGeneratorOutput(BaseModel):
    """
    Represents the structured output of the AnswerGenerator agent.
    Generated by Copilot
    """
    comprehensive_response: List[AnswerPart] = Field(
        ..., description="Ordered list of brokendown markdown formatted answer")
    clarification_requests: Optional[List[str]] = Field(
        ..., description="Any unclear aspects that need user input.")
    information_gaps: Optional[List[str]] = Field(
        ..., description="Any missing information required for providing a complete answer.")
