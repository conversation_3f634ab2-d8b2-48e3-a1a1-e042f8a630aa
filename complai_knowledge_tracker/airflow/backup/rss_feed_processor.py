"""
Refactored RSS Feed Processing DAG
Clean, maintainable DAG for processing RBI RSS feeds with multi-vector Qdrant ingestion
"""

from datetime import datetime, timedelta
import logging
import os
import uuid
import tempfile
from typing import List, Dict, Any, Optional

import feedparser
import requests
import boto3
from bs4 import BeautifulSoup
from dateutil import parser
from airflow import DAG
from airflow.decorators import task
from airflow.utils.task_group import TaskGroup

# Import our refactored utilities
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "utils"))

from utils.config import config
from utils.document_pipeline import document_processor
from utils.database_utils import db_manager
from utils.qdrant_utils import ingest_single_document

logger = logging.getLogger("airflow.task")

# DAG Configuration
DAG_ID = "rss_feed_processor"
DESCRIPTION = "Process RBI RSS feeds with multi-vector Qdrant ingestion"
SCHEDULE_INTERVAL = timedelta(days=1)
MAX_ACTIVE_RUNS = 1

# RSS Feed URLs
RSS_FEEDS = {
    "notifications": "https://rbi.org.in/notifications_rss.xml",
    "press_releases": "https://rbi.org.in/pressreleases_rss.xml",
    "publications": "https://rbi.org.in/Publication_rss.xml"
}

# Default arguments
default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 1, 1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
}

def categorize_document(title: str) -> str:
    """Categorize document based on title and return collection name"""
    from utils.document_pipeline import DocumentTypeMapper
    return DocumentTypeMapper.categorize_document_title(title)

def parse_rbi_notification(data: str) -> Dict[str, Any]:
    """Parse RBI notification XML to extract key fields"""
    soup = BeautifulSoup(data, 'html.parser')
    document = {}
    
    # Extract circular numbers
    circular_nums = []
    p_tags = soup.find_all('p')
    for p in p_tags:
        import re
        if re.search(r'[A-Z]+/\d{4}-\d{2}/\d+', p.get_text()) or re.search(r'RBI/\d{4}-\d{2}/\d+', p.get_text()):
            circular_nums.append(p.get_text().strip())
    
    document['circular_numbers'] = circular_nums
    
    # Extract press release number
    press_release_number = None
    press_release_tags = soup.find_all('p', text=re.compile(r'Press Release: \d{4}-\d{4}/\d+'))
    for tag in press_release_tags:
        match = re.search(r'\d{4}-\d{4}/\d+', tag.get_text().strip())
        if match:
            press_release_number = match.group()
            break
    
    document['press_release_number'] = press_release_number
    
    # Extract date
    pub_date = soup.find('pubDate')
    date = None
    if pub_date:
        try:
            date = pub_date.get_text().strip()
            # Use standardized date format instead of ISO format
            from utils.date_utils import standardize_date_for_storage
            document['date_iso'] = standardize_date_for_storage(date)
        except ValueError:
            pass

    document['date'] = date
    
    # Extract addressee
    addressee = None
    if date:
        for i, p in enumerate(p_tags):
            if date in p.get_text():
                if i + 1 < len(p_tags):
                    addressee = p_tags[i + 1].get_text().strip()
                break
    
    document['addressee'] = addressee
    
    # Extract subject/title
    title_tags = soup.find_all('p', class_='head')
    for tag in title_tags:
        if tag.get_text().strip() and not tag.get_text().strip().startswith('Annex'):
            document['subject'] = tag.get_text().strip()
            break
    
    # Extract main text
    main_text = []
    found_subject = False
    for p in p_tags:
        if not found_subject and p.get('class') and 'head' in p.get('class'):
            found_subject = True
            continue
        if found_subject:
            main_text.append(p.get_text().strip())
    
    document['main_text'] = '\n\n'.join(main_text)
    return document

def create_dag():
    """Create the RSS feed processing DAG"""
    
    dag = DAG(
        DAG_ID,
        default_args=default_args,
        description=DESCRIPTION,
        schedule_interval=SCHEDULE_INTERVAL,
        catchup=False,
        max_active_runs=MAX_ACTIVE_RUNS,
        tags=["rbi", "rss", "feeds", "qdrant", "processing"]
    )
    
    @task(dag=dag)
    def scrape_rss_feeds() -> List[Dict[str, Any]]:
        """Scrape RSS feeds and return articles"""
        articles = []
        
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=config.s3.access_key_id,
            aws_secret_access_key=config.s3.secret_access_key
        )
        
        def sanitize_filename(filename: str) -> str:
            """Sanitize filenames for S3"""
            import re
            sanitized = re.sub(r"[^a-zA-Z0-9_\-]", "_", filename)
            return sanitized[:50]
        
        def extract_pdf_link(article_url: str) -> Optional[str]:
            """Extract PDF link from article page"""
            try:
                response = requests.get(article_url, timeout=30)
                soup = BeautifulSoup(response.content, "html.parser")
                table_header = soup.find("td", class_="tableheader")
                if table_header:
                    import re
                    pdf_anchor = table_header.find("a", href=re.compile(r".*\.pdf", re.IGNORECASE))
                    if pdf_anchor:
                        return pdf_anchor["href"]
            except Exception as e:
                logger.warning(f"Failed to extract PDF link from {article_url}: {e}")
            return None
        
        def upload_to_s3(file_path: str, s3_key: str) -> Optional[str]:
            """Upload file to S3"""
            try:
                s3_client.upload_file(file_path, config.s3.bucket_name, s3_key)
                s3_url = f"https://{config.s3.bucket_name}.s3.amazonaws.com/{s3_key}"
                logger.info(f"Uploaded {file_path} to S3 as {s3_key}")
                return s3_url
            except Exception as e:
                logger.error(f"Error uploading to S3: {e}")
                return None
        
        def article_exists_in_s3(s3_key: str) -> bool:
            """Check if article exists in S3"""
            try:
                s3_client.head_object(Bucket=config.s3.bucket_name, Key=s3_key)
                return True
            except:
                return False
        
        def convert_published_date(pub_date: str) -> str:
            """Convert published date to standardized YYYY-MM-DD format"""
            from utils.date_utils import standardize_date_for_storage
            return standardize_date_for_storage(pub_date)
        
        # Process each RSS feed
        for feed_name, feed_url in RSS_FEEDS.items():
            try:
                feed = feedparser.parse(feed_url)
                logger.info(f"Processing {len(feed.entries)} entries from {feed_name}")
                
                for entry in feed.entries:
                    title = entry.title
                    link = entry.link
                    published_date = convert_published_date(entry.published)
                    
                    # Parse article content
                    doc_info = parse_rbi_notification(entry.description)
                    
                    # Categorize document
                    category = categorize_document(title)
                    
                    # Extract PDF link if available
                    pdf_link = extract_pdf_link(link)
                    
                    article_data = {
                        "title": title,
                        "link": link,
                        "published_date": published_date,
                        "category": category,
                        "doc_info": doc_info,
                        "feed_name": feed_name,
                        "id": str(uuid.uuid4()),
                        "ingestion_timestamp": datetime.now().strftime('%Y-%m-%d')
                    }
                    
                    # Handle PDF download and upload
                    if pdf_link:
                        today = datetime.now().strftime('%Y%m%d')
                        pdf_filename = sanitize_filename(title) + ".pdf"
                        s3_key = f"{today}/{category}/{pdf_filename}"
                        
                        if not article_exists_in_s3(s3_key):
                            try:
                                # Download PDF
                                response = requests.get(pdf_link, timeout=30)
                                response.raise_for_status()
                                
                                with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
                                    tmp_file.write(response.content)
                                    tmp_path = tmp_file.name
                                
                                # Upload to S3
                                s3_url = upload_to_s3(tmp_path, s3_key)
                                
                                # Clean up
                                os.unlink(tmp_path)
                                
                                if s3_url:
                                    article_data["pdf_link"] = pdf_link
                                    article_data["s3_url"] = s3_url
                                    logger.info(f"Processed PDF for article: {title}")
                                
                            except Exception as e:
                                logger.error(f"Failed to process PDF for {title}: {e}")
                        else:
                            article_data["pdf_link"] = pdf_link
                            article_data["s3_url"] = f"https://{config.s3.bucket_name}.s3.amazonaws.com/{s3_key}"
                            logger.info(f"Article PDF already exists: {title}")
                    
                    articles.append(article_data)
                    
            except Exception as e:
                logger.error(f"Error processing feed {feed_name}: {e}")
                continue
        
        logger.info(f"Scraped {len(articles)} articles total")
        return articles
    
    return dag

# Create the DAG
dag = create_dag()
