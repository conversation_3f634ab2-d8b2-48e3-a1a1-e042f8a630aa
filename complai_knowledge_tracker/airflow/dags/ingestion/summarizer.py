# summarizer.py
from openai import OpenAI

import os

from airflow.models import Variable


openai_api_key = Variable.get("openai_api_key")
client_openai = OpenAI(api_key=openai_api_key)


def recursive_split(text: str, max_len: int = 5000) -> list:
    if len(text) <= max_len:
        return [text]
    mid = len(text) // 2
    split_point = text.rfind(". ", 0, mid)
    if split_point == -1:
        split_point = mid
    return recursive_split(text[:split_point]) + recursive_split(text[split_point:])

def summarize_text(chunk: str, max_words: int = 150) -> str:
    prompt = f"""
You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.

QUERY: Summarize the provided RBI-related content in strictly 200 words or less.
Provide concise  about Broadly covered Concepts discussed in the content.
Make sure to keep it less than 200 words.
CONTEXT: {chunk}
"""

    response = client_openai.chat.completions.create(model="gpt-4.1-2025-04-14",
    messages=[
        {"role": "system", "content": "You are a regulatory compliance summarizer for RBI guidelines."},
        {"role": "user", "content": prompt}
    ],
    temperature=0.1,
    max_tokens=300)

    return response.choices[0].message.content.strip()

def extract_topics(text: str) -> list:
    prompt = f"""
    You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.
    QUERY: Extract the main topics from the provided RBI-related content.
    CONTEXT: {text}
    """

    response = client_openai.chat.completions.create(model="gpt-4.1-mini-2025-04-14",
    messages=[
        {"role": "system", "content": "You are a regulatory compliance topic extractor for RBI guidelines."},
        {"role": "user", "content": prompt}
    ],
    temperature=0.1,
    max_tokens=700)

    return response.choices[0].message.content.strip().split(", ")