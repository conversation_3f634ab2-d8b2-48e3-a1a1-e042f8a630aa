#!/usr/bin/env python3
"""
Real pipeline test that uses actual LLM processing functions.
This bypasses Airflow but uses the real notification processing logic.
"""

import json
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add the DAGs directory to Python path
dags_path = Path(__file__).parent / "complai_knowledge_tracker" / "airflow" / "dags"
sys.path.insert(0, str(dags_path))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StandaloneConfig:
    """Standalone configuration that doesn't require Airflow Variables"""
    
    def __init__(self):
        # Set up OpenAI configuration from environment
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Mock other configurations
        self.qdrant_host = "localhost"
        self.database_uri = "mongodb://localhost:27017/"


class MockOpenAIManager:
    """OpenAI manager that works without Airflow Variables"""
    
    def __init__(self, api_key: str):
        from openai import OpenAI
        self.client = OpenAI(api_key=api_key)
    
    def get_client(self):
        return self.client
    
    def with_key_rotation(self, func):
        """Execute function with basic error handling"""
        return func()
    
    def get_completion(self, prompt: str, model: str = "gpt-4o-mini", temperature: float = 0.1) -> str:
        """Get a simple text completion from OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Completion call failed: {e}")
            raise


class RealPipelineTest:
    """Test the real notification processing pipeline"""
    
    def __init__(self, notifications_file: str = "rbi_notifications.json"):
        self.notifications_file = Path(notifications_file)
        self.notifications = []
        self.results = []
        
        # Set up standalone configuration
        self.setup_environment()
        
        # Import and initialize the real notification processor
        self.setup_notification_processor()
    
    def setup_environment(self):
        """Set up the environment for standalone testing"""
        try:
            # Check for OpenAI API key
            if not os.getenv('OPENAI_API_KEY'):
                raise ValueError(
                    "OPENAI_API_KEY environment variable is required. "
                    "Please set it with: export OPENAI_API_KEY='your-key-here'"
                )
            
            # Create standalone config
            self.config = StandaloneConfig()
            logger.info("✅ Environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            raise
    
    def setup_notification_processor(self):
        """Set up the real notification processor"""
        try:
            # Mock the config module to avoid Airflow dependency
            import sys
            from unittest.mock import MagicMock
            
            # Create mock config
            mock_config = MagicMock()
            mock_config.openai.get_next_key.return_value = self.config.openai_api_key
            
            # Replace the config import
            sys.modules['utils.config'] = MagicMock()
            sys.modules['utils.config'].config = mock_config
            
            # Create mock openai_manager
            openai_manager = MockOpenAIManager(self.config.openai_api_key)
            sys.modules['utils.openai_utils'] = MagicMock()
            sys.modules['utils.openai_utils'].openai_manager = openai_manager
            
            # Import the notification processor
            from prompts.notification_categorizer import (
                NotificationCategorizationResult,
                AffectedDocumentsResult,
                UpdateActionResult,
                NOTIFICATION_CATEGORIZER_PROMPT,
                AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT,
                UPDATE_ACTION_DETERMINER_PROMPT
            )
            
            # Create the real notification processor class
            class RealNotificationProcessor:
                def __init__(self):
                    self.client = openai_manager.get_client()
                    self.max_content_length = 2000
                
                def _make_llm_call(self, prompt: str, response_format, system_message: str = "You are an expert RBI regulatory analyst."):
                    """Make a structured LLM call with error handling"""
                    try:
                        logger.info(f"Making LLM call with model: gpt-4o-mini")
                        response = self.client.beta.chat.completions.parse(
                            model="gpt-4o-mini",
                            messages=[
                                {"role": "system", "content": system_message},
                                {"role": "user", "content": prompt}
                            ],
                            temperature=0.1,
                            response_format=response_format
                        )
                        
                        # Convert to dict
                        result = response.choices[0].message.parsed
                        return result.model_dump() if hasattr(result, 'model_dump') else result.__dict__
                        
                    except Exception as e:
                        logger.error(f"LLM call failed: {e}")
                        raise
                
                def analyze_notification(self, title: str, rss_description: str, link: str) -> dict:
                    """Categorize RBI notification and determine its impact on knowledge base"""
                    try:
                        logger.info(f"🔍 Starting notification analysis for: {title[:50]}...")
                        prompt = NOTIFICATION_CATEGORIZER_PROMPT.format(
                            title=title,
                            content=rss_description[:self.max_content_length],
                            link=link
                        )
                        
                        logger.info(f"📝 Prompt created, making LLM call...")
                        result = self._make_llm_call(prompt, NotificationCategorizationResult)
                        logger.info(f"✅ Notification categorized as: {result.get('category')} (confidence: {result.get('confidence')})")
                        return result
                        
                    except Exception as e:
                        logger.error(f"Error analyzing notification: {e}")
                        return self._get_fallback_analysis()
                
                def extract_affected_documents(self, title: str, rss_description: str, category: str) -> dict:
                    """Extract specific documents affected by the notification"""
                    try:
                        logger.info(f"🔍 Extracting affected documents for: {title[:50]}...")
                        prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                            title=title,
                            content=rss_description[:self.max_content_length],
                            category=category
                        )
                        
                        result = self._make_llm_call(
                            prompt,
                            AffectedDocumentsResult,
                            "You are an expert in RBI regulatory document analysis and knowledge base management."
                        )
                        logger.info(f"✅ Found {len(result.get('document_actions', []))} document actions")
                        return result
                        
                    except Exception as e:
                        logger.error(f"Error extracting affected documents: {e}")
                        return self._get_fallback_affected_documents()
                
                def determine_update_actions(self, title: str, category: str, affected_documents: list, rss_description: str) -> dict:
                    """Determine specific actions needed for knowledge base updates"""
                    try:
                        logger.info(f"🎯 Determining update actions for: {title[:50]}...")
                        prompt = UPDATE_ACTION_DETERMINER_PROMPT.format(
                            title=title,
                            category=category,
                            affected_documents=json.dumps(affected_documents, indent=2),
                            content=rss_description[:1500]
                        )
                        
                        result = self._make_llm_call(
                            prompt,
                            UpdateActionResult,
                            "You are an expert in knowledge base management for RBI regulations."
                        )
                        logger.info(f"✅ Generated {len(result.get('actions', []))} update actions")
                        return result
                        
                    except Exception as e:
                        logger.error(f"Error determining update actions: {e}")
                        return self._get_fallback_update_actions()
                
                def _get_fallback_analysis(self) -> dict:
                    """Return fallback when analysis fails"""
                    return {
                        "category": "Informational",
                        "confidence": "low",
                        "reasoning": "LLM analysis failed",
                        "affects_regulations": False,
                        "keywords_found": [],
                        "requires_kb_update": False
                    }
                
                def _get_fallback_affected_documents(self) -> dict:
                    """Return fallback when affected documents extraction fails"""
                    return {
                        "document_actions": [],
                        "document_keywords": [],
                        "has_new_document_link": False,
                        "new_document_url": "",
                        "rbi_links": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }
                
                def _get_fallback_update_actions(self) -> dict:
                    """Return fallback when update action determination fails"""
                    return {
                        "actions": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }
            
            # Create the processor instance
            self.notification_processor = RealNotificationProcessor()
            logger.info("✅ Real notification processor initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup notification processor: {e}")
            raise
    
    def load_notifications(self) -> bool:
        """Load notifications from JSON file"""
        try:
            if not self.notifications_file.exists():
                logger.error(f"Notifications file not found: {self.notifications_file}")
                return False
            
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                self.notifications = json.load(f)
            
            logger.info(f"📁 Loaded {len(self.notifications)} notifications")
            return True
            
        except Exception as e:
            logger.error(f"Error loading notifications: {e}")
            return False
    
    def process_notification(self, notification: Dict, index: int) -> Dict:
        """Process a single notification with real LLM analysis"""
        title = notification.get('Title', 'Unknown Title')
        pdf_link = notification.get('PDF Link', '')
        year = notification.get('Year', '')
        watermark = notification.get('Watermark', '')
        
        # Create mock RSS description
        rss_description = f"""
        <p>Section: {notification.get('Section', '')}</p>
        <p>Year: {year}</p>
        <p>Date: {notification.get('Date', '')}</p>
        <p class='head'>{title}</p>
        <p>Watermark: {watermark}</p>
        """
        
        logger.info(f"\n🔄 Processing notification {index}: {title[:60]}...")
        
        try:
            # Step 1: Analyze notification with real LLM
            analysis_result = self.notification_processor.analyze_notification(title, rss_description, pdf_link)
            
            category = analysis_result.get('category', 'Unknown')
            confidence = analysis_result.get('confidence', 'Unknown')
            requires_kb_update = analysis_result.get('requires_kb_update', False)
            
            logger.info(f"   📂 Category: {category}")
            logger.info(f"   🎯 Confidence: {confidence}")
            logger.info(f"   ⚡ Requires KB Update: {requires_kb_update}")
            
            # Step 2: Extract affected documents if KB update required
            affected_docs = {}
            update_actions = {}
            
            if requires_kb_update:
                logger.info(f"   🔍 Extracting affected documents...")
                affected_docs = self.notification_processor.extract_affected_documents(title, rss_description, category)
                
                document_actions = affected_docs.get('document_actions', [])
                logger.info(f"   📄 Found {len(document_actions)} document actions")
                
                # Step 3: Determine update actions
                logger.info(f"   🎯 Determining update actions...")
                update_actions = self.notification_processor.determine_update_actions(title, category, document_actions, rss_description)
                
                actions = update_actions.get('actions', [])
                logger.info(f"   ⚡ Generated {len(actions)} update actions")
            
            # Compile result
            result = {
                "notification_id": index,
                "title": title,
                "year": year,
                "pdf_link": pdf_link,
                "watermark": watermark,
                "llm_analysis": analysis_result,
                "affected_documents": affected_docs,
                "update_actions": update_actions,
                "processing_status": "success",
                "processing_time": datetime.now().isoformat()
            }
            
            logger.info(f"   ✅ Successfully processed notification {index}")
            return result
            
        except Exception as e:
            logger.error(f"   ❌ Error processing notification {index}: {e}")
            return {
                "notification_id": index,
                "title": title,
                "year": year,
                "pdf_link": pdf_link,
                "watermark": watermark,
                "processing_status": "failed",
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }
    
    def run_real_test(self, max_notifications: int = 5) -> bool:
        """Run real LLM processing test"""
        if not self.load_notifications():
            return False
        
        # Limit notifications for testing
        test_notifications = self.notifications[:max_notifications]
        
        logger.info(f"🚀 Running REAL LLM processing test on {len(test_notifications)} notifications")
        logger.info("⚠️  This will use OpenAI API credits!")
        
        self.results = []
        for i, notification in enumerate(test_notifications, 1):
            result = self.process_notification(notification, i)
            self.results.append(result)
        
        return True
    
    def save_results(self, output_file: str = "real_pipeline_results.json") -> bool:
        """Save results to JSON file"""
        try:
            output = {
                "metadata": {
                    "test_date": datetime.now().isoformat(),
                    "total_notifications": len(self.results),
                    "test_type": "real_llm_processing",
                    "version": "1.0"
                },
                "results": self.results
            }
            
            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Results saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            return False


def main():
    """Main function"""
    logger.info("🚀 Starting REAL Pipeline Test")
    logger.info("=" * 80)
    
    # Check for OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        logger.error("❌ OPENAI_API_KEY environment variable is required")
        logger.info("   Please set it with: export OPENAI_API_KEY='your-key-here'")
        return
    
    try:
        # Initialize tester
        tester = RealPipelineTest()
        
        # Run real test (start with 3 notifications to test)
        if not tester.run_real_test(max_notifications=10):
            logger.error("❌ Test failed")
            return
        
        # Save results
        if tester.save_results():
            logger.info("✅ Real pipeline test completed successfully!")
            
            # Display summary
            successful = sum(1 for r in tester.results if r.get('processing_status') == 'success')
            total = len(tester.results)
            logger.info(f"📊 Summary: {successful}/{total} notifications processed successfully")
        else:
            logger.error("❌ Failed to save results")
    
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
