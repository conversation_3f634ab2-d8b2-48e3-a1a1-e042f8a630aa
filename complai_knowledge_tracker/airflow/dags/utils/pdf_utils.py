"""
Enhanced PDF processing utilities with improved HTML AST generation and chunking
"""

import json
import os
import re
import time
import random
import logging
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from pydantic import BaseModel, Field
import fitz
import html2text
from bs4 import BeautifulSoup

# from .config import config
from .openai_utils import openai_manager, client_openai

logger = logging.getLogger(__name__)

def parse_pdf_to_html_ast(pdf_path: str) -> BeautifulSoup:
    """
    Enhanced PDF parsing with proper HTML AST construction
    Analyzes font sizes, boldness, and spacing to create semantic HTML structure
    """
    try:
        doc = fitz.open(pdf_path)

        # Collect font-size frequencies to determine body text and headings
        size_counts: Dict[float, int] = {}
        for page in doc:
            blocks = page.get_text("dict")["blocks"]
            for blk in blocks:
                if blk.get("type") != 0:  # Skip non-text blocks
                    continue
                for line in blk.get("lines", []):
                    for span in line.get("spans", []):
                        size = round(span.get("size", 0), 1)
                        size_counts[size] = size_counts.get(size, 0) + 1

        # Determine body text size (most common)
        if not size_counts:
            body_size = 12.0  # Default fallback
        else:
            body_size = max(size_counts, key=size_counts.get)

        # Map larger sizes to heading levels
        heading_sizes = sorted([s for s in size_counts.keys() if s > body_size], reverse=True)
        size_to_level: Dict[float, int] = {}
        for idx, size in enumerate(heading_sizes[:6]):  # Max 6 heading levels
            size_to_level[size] = idx + 1

        # Create HTML structure
        soup = BeautifulSoup("", "html.parser")
        root = soup.new_tag("div")
        soup.append(root)

        # Stack to maintain section hierarchy: (section_tag, level)
        section_stack: List[Tuple] = [(root, 0)]

        for page_num, page in enumerate(doc, start=1):
            blocks = page.get_text("dict")["blocks"]

            for blk in blocks:
                if blk.get("type") != 0:  # Skip non-text blocks
                    continue

                # Extract all spans from the block
                spans = []
                for line in blk.get("lines", []):
                    spans.extend(line.get("spans", []))

                if not spans:
                    continue

                # Determine block characteristics
                block_max_size = max(round(span.get("size", 0), 1) for span in spans)

                # Reconstruct text from spans
                lines_text = []
                for line in blk.get("lines", []):
                    line_text = ""
                    for span in line.get("spans", []):
                        line_text += span.get("text", "")
                    lines_text.append(line_text)

                text = "\n".join(lines_text).strip()
                if not text:
                    continue

                # Create appropriate HTML element
                if block_max_size > body_size:
                    # This is a heading
                    level = size_to_level.get(block_max_size, 6)  # Default to h6 if not found

                    # Create heading tag
                    heading_tag = soup.new_tag(f"h{level}")
                    heading_tag.string = text
                    heading_tag["data-page"] = str(page_num)
                    heading_tag["data-bbox"] = json.dumps(blk.get("bbox", []))

                    # Manage section hierarchy
                    while section_stack and section_stack[-1][1] >= level:
                        section_stack.pop()

                    parent = section_stack[-1][0]

                    # Create new section
                    section = soup.new_tag("section")
                    section["data-page"] = str(page_num)
                    section["data-bbox"] = json.dumps(blk.get("bbox", []))

                    parent.append(section)
                    section.append(heading_tag)
                    section_stack.append((section, level))

                else:
                    # This is body text
                    parent = section_stack[-1][0]
                    flat_text = text.replace("\n", " ").strip()

                    # Check if this looks like a list item
                    if re.match(r"^(\u2022|\*|-|\d+[\.\)])\s+", flat_text):
                        # Determine list type
                        if re.match(r"^\d+[\.\)]\s+", flat_text):
                            list_tag_name = "ol"
                        else:
                            list_tag_name = "ul"

                        # Check if we need to create a new list or use existing
                        last_child = parent.contents[-1] if parent.contents else None
                        if not (hasattr(last_child, "name") and last_child.name == list_tag_name):
                            # Create new list
                            list_tag = soup.new_tag(list_tag_name)
                            list_tag["data-page"] = str(page_num)
                            list_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                            parent.append(list_tag)
                        else:
                            list_tag = last_child

                        # Create list item
                        li_tag = soup.new_tag("li")
                        li_tag.string = flat_text
                        li_tag["data-page"] = str(page_num)
                        li_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                        list_tag.append(li_tag)

                    else:
                        # Regular paragraph
                        p_tag = soup.new_tag("p")
                        p_tag.string = flat_text
                        p_tag["data-page"] = str(page_num)
                        p_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                        parent.append(p_tag)

        doc.close()
        logger.info(f"Successfully parsed PDF {pdf_path} to HTML AST")
        return soup

    except Exception as e:
        logger.error(f"Failed to parse PDF {pdf_path}: {e}")
        raise

def chunk_html_ast(soup: BeautifulSoup, max_chars: int = None) -> List[Dict]:
    """
    Enhanced HTML AST chunking with better boundary preservation and position tracking
    """
    if max_chars is None:
        # max_chars = config.processing.max_chunk_chars
        max_chars = 6000

    chunks: List[Dict] = []
    curr_html = ""
    curr_positions: List[Dict] = []

    def flush_chunk():
        """Helper to flush current chunk"""
        nonlocal curr_html, curr_positions
        if curr_html.strip():
            chunks.append({
                "content": curr_html,
                "positions": extract_positions_from_html(curr_html)
            })
            curr_html = ""
            curr_positions.clear()

    # Find the root container
    root = soup.find("div") or soup

    # Process top-level elements (sections, paragraphs, etc.)
    for elem in root.find_all(recursive=False):
        if not hasattr(elem, 'name'):
            continue

        elem_html = str(elem)
        elem_length = len(elem_html)

        # If adding this element would exceed max_chars, flush current chunk
        if curr_html and len(curr_html) + elem_length > max_chars:
            flush_chunk()

        # If single element is too large, split it further
        if elem_length > max_chars:
            # For very large elements, try to split at paragraph boundaries
            if elem.name == "section":
                # Split section into smaller chunks
                section_chunks = _split_large_section(elem, max_chars)
                for chunk_html in section_chunks:
                    if curr_html and len(curr_html) + len(chunk_html) > max_chars:
                        flush_chunk()
                    curr_html += chunk_html
            else:
                # For other large elements, add as-is and flush
                if curr_html:
                    flush_chunk()
                curr_html = elem_html
                flush_chunk()
        else:
            # Add element to current chunk
            curr_html += elem_html

    # Flush any remaining content
    flush_chunk()

    logger.info(f"Created {len(chunks)} chunks from HTML AST")
    return chunks

def _split_large_section(section_elem: BeautifulSoup, max_chars: int) -> List[str]:
    """
    Split a large section element into smaller chunks while preserving structure
    """
    chunks = []
    current_chunk = ""

    # Keep the section header
    header = section_elem.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
    if header:
        header_html = str(header)
        current_chunk = f"<section>{header_html}"
    else:
        current_chunk = "<section>"

    # Process other elements in the section
    for elem in section_elem.find_all(recursive=False):
        if elem.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            continue  # Already handled

        elem_html = str(elem)
        if len(current_chunk) + len(elem_html) + 10 > max_chars:  # +10 for closing tag
            # Close current chunk and start new one
            current_chunk += "</section>"
            chunks.append(current_chunk)

            # Start new chunk with header if available
            if header:
                current_chunk = f"<section>{header_html}{elem_html}"
            else:
                current_chunk = f"<section>{elem_html}"
        else:
            current_chunk += elem_html

    # Close final chunk
    if current_chunk and not current_chunk.endswith("</section>"):
        current_chunk += "</section>"
        chunks.append(current_chunk)

    return chunks

def has_diagonal_withdrawn_watermark(pdf_path: str, size_threshold: float = 20.0, ratio_tolerance: float = 0.3) -> bool:
    doc = fitz.open(pdf_path)
    for page in doc:
        for block in page.get_text("dict")["blocks"]:
            if block["type"] == 0:  # text
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip().lower()
                        if "withdrawn" in text and span["size"] >= size_threshold:
                            w = span["bbox"][2] - span["bbox"][0]
                            h = span["bbox"][3] - span["bbox"][1]
                            if h == 0:
                                continue
                            ratio = w / h
                            if abs(ratio - 1.0) < ratio_tolerance:
                                return True
    return False

# -------------------- Pydantic Models for Extraction --------------------
class Position(BaseModel):
    page_number: int = Field(..., description="The page number from which this item was extracted referred to data-page")
    bboxes: List[List[float]] = Field(..., description="List of Bounding box [[x1, y1, x2, y2],[x1, y1, x2, y2]] of all content of RBI regulation HTML Fragement(data-bbox list) considered as the guideline ")

class Obligation(BaseModel):
    guideline: str = Field(..., description="Exact paragraph or sentence considered as guideline from RBI regulation HTML Fragement, this will be used to extract the obligation")
    positions: List[Position] = Field(..., description="Positional information of the guideline page and bounding boxes of the guideline text")
    obligation: str = Field(..., description="As Complaiance office, The extracted or paraphrased guideline as obligation ")
    action_item: str = Field(..., description="A concise, actionable item derived from the obligation.")

class ObligationList(BaseModel):
    obligations_list: List[Obligation] = Field(..., description="A list of all extracted obligations.")

# -------------------- Extraction Functions & Prompts --------------------
def extract_obligations(html_chunks: List[str]) -> List[dict]:
    all_obls = []
    for chunk in html_chunks:
        prompt = (
            "Role: You are an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items\n"
            "Your task is to extract all the guidelines and obligations from guidelines, action_items from obligations from the provided RBI regulation HTML fragment.\n\n"
            "Goal: Extract a JSON array where each object contains exactly these fields:\n"
            "  - 'guideline' - identified sentence or paragraph as a instruction to the bank\n "
            "  - 'positions' - poistion of the guildeline, extracted from data-page and data-bbox of the exact/complete sentence or paragraph considered as guideline\n"
            "  - 'obligation'- rephrased guideline with the context of complaince officer, usually start with 'Bank Shall\n"
            "  - 'action_item' - rephrased obligations as a list of action items to be taken by the bank'\n\n"
            """
            Below are the sample Guidelines from a RBI Regulation, and corresponding generated/rephrased Obligation
                Guideline   : Notes determined as counterfeit shall be stamped as \"COUNTERFEIT NOTE\"
                Obligation  : Bank shall ensure that the Notes determined as counterfeit shall be stamped as \"COUNTERFEIT NOTE\" and impounded in the prescribed format (Annex I).

                Guideline   : Such impounded note shall be recorded under authentication, in a separate register.
                Obligation  : Bank shall ensure that the impounded notes shall be recorded under authentication, in a separate register.

                Guideline   : \"When a banknote tendered at the counter of a bank branch / back office and currency chest or treasury is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.\"
                Obligation   : Bank shall ensure that when a banknote tendered at the counter of a bank branch is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.

                Guideline   : Detection of Counterfeit Notes - Reporting to Police and other bodies
                Obligation  : \"Bank shall ensure to report to Police and other bodies on Detection of Counterfeit Notes.\n                                The following procedure shall be followed while reporting incidence of detection of Counterfeit Note to the Police: \n                                1.  For cases of detection of Counterfeit Notes up to four (04) pieces in a single transaction, a consolidated report in the prescribed format (Annex III) shall be sent by the Nodal Bank Officer to the police authorities or the Nodal Police Station, along with the suspect Counterfeit Notes, at the end of the month.  \n                                2.  For cases of detection of Counterfeit Notes of five (05) or more pieces in a single transaction, the Counterfeit Notes shall be forwarded immediately by the Nodal Bank Officer to the local police authorities or the Nodal Police Station for investigation by filing FIR in the prescribed format (Annex IV).  \n                                3.  A copy of the monthly consolidated report / FIR shall be sent to the Forged Note Vigilance Cell constituted at the Head Office of the bank.  "
                Guideline   : Acknowledgement receipt - Notice to this effect should be displayed prominently at the offices / branches for information of the public
                Obligation  : Bank shall display a notice on availability of the Acknowledgement receipt to customers prominently at the offices / branches for information of the public.
            """

            "Instructions:\n"
            "1. Locate each paragraph/section containing guidelines (keywords: 'shall', 'must', 'required to') which can be percieved as instructions to banks\n"
            "3. Output only a JSON array of objects with those three keys, no extras.\n\n"
            "4. RBI Regulation as HTML contains position information as data-page_number and data-bbox atrributes for each guideline"
            "4. Make sure the Position information is accurately extracted for the each Guideline observed using  data-page_number and data-bbox atrributes"
            "5. Make sure understand the guideline and generate obligation similar to rephrasing pattern of the samples shared in the same context"
            "6. Use obligation to generate action items for the bank"
            "7. Always make sure to track and generate Position infromation for each Guideline using data-page_number and data-bbox atrributes "
            f"RBI Regulation as HTML:\n{chunk}"
        )
        completion = openai_manager.with_key_rotation(
            client_openai.beta.chat.completions.parse,
            model="gpt-4.1-mini-2025-04-14",
            messages=[
                {"role": "system", "content": "Extract structured obligations from RBI HTML docs as an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items"},
                {"role": "user",   "content": prompt},
            ],
            response_format=ObligationList,
        )
        obls = completion.choices[0].message.parsed.obligations_list
        for o in obls:
            all_obls.append(o.dict())
    return all_obls

# -------------------- HTML Utility Functions --------------------
def strip_data_attributes(html: str) -> str:
    """
    Remove all data-* attributes from HTML content and optionally convert to markdown
    """
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all():
        attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
        for attr in attrs_to_remove:
            del tag.attrs[attr]

    cleaned_html = str(soup)
    try:
        # Convert to markdown for better readability
        return html2text.html2text(cleaned_html)
    except ImportError:
        # Fallback to cleaned HTML if html2text not available
        return cleaned_html

def extract_positions_from_html(html: str) -> List[Dict]:
    soup = BeautifulSoup(html, "html.parser")
    positions: List[Dict] = []
    for tag in soup.find_all(attrs={"data-page": True}):
        try:
            page_num = int(tag["data-page"])
        except (ValueError, TypeError):
            page_num = None
        bbox = json.loads(tag.get("data-bbox", "[]"))
        positions.append({"page": page_num, "bbox": bbox})
    return positions

# -------------------- Recursive Split for Summarization --------------------
def recursive_split(text: str, max_len: int = 50000) -> List[str]:
    if len(text) <= max_len:
        return [text]
    mid = len(text) // 2
    split_point = text.rfind(". ", 0, mid)
    if split_point == -1:
        split_point = mid
    return recursive_split(text[:split_point], max_len) + recursive_split(text[split_point:], max_len)

# -------------------- Summarization & Topic Extraction --------------------
def summarize_and_extract(chunk: str, max_words: int = 150, max_topics: int = 5) -> Dict[str, Any]:
    class SummaryAndTopics(BaseModel):
        summary: str
        topics: List[str]
    prompt = f"""
You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.

QUERY: Provide a JSON object with two fields:
  - \"summary\": a concise overview in strictly {max_words} maximum words
  - \"topics\": up to {max_topics} key topics from the content

CONTEXT:
{chunk}
"""
    completion = openai_manager.with_key_rotation(
        client_openai.beta.chat.completions.parse,
        model="gpt-4.1-nano-2025-04-14",
        messages=[
            {"role":"system","content":"You are a regulatory compliance summarizer and topic extractor for RBI guidelines."},
            {"role":"user","content":prompt}
        ],
        response_format=SummaryAndTopics,
    )
    data: SummaryAndTopics = completion.choices[0].message.parsed
    return data.dict()

# -------------------- Document Code Extraction --------------------
def extract_document_code(pdf_path):
    def extract_text_from_pdf(pdf_path):
        doc = fitz.open(pdf_path)
        try:
            full_text = ""
            pages_to_extract = min(2, doc.page_count)
            for page_number in range(pages_to_extract):
                page = doc.load_page(page_number)
                full_text += page.get_text()
            return full_text
        finally:
            doc.close()
    text = extract_text_from_pdf(pdf_path)
    document_code_match = re.search(r"^\s*(RBI/\d{4}-\d{2}/\d+.*?Circular No\. \d+)", text, re.MULTILINE)
    document_code = document_code_match.group(1).strip() if document_code_match else "Not Found"
    return document_code
