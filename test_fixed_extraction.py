#!/usr/bin/env python3
"""
Test the fixed RBI PDF extraction function
"""

import sys
import os
sys.path.append('/Users/<USER>/selkea/complai_knowledge_tracker/airflow/dags')

from inuse_rss_feed_etl_dag import extract_pdf_from_rbi_page
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_fixed_extraction():
    """Test the fixed extraction function"""
    
    test_urls = [
        "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0",
        "https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566",
        "https://www.rbi.org.in/Scripts/BS_PressReleaseDisplay.aspx?prid=54321"
    ]
    
    print("🧪 Testing Fixed RBI PDF Extraction")
    print("=" * 80)
    
    for url in test_urls:
        print(f"\n🔍 Testing: {url}")
        print("-" * 60)
        
        try:
            pdf_url = extract_pdf_from_rbi_page(url)
            if pdf_url:
                print(f"✅ SUCCESS: {pdf_url}")
            else:
                print(f"❌ FAILED: No PDF found")
        except Exception as e:
            print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    test_fixed_extraction()
