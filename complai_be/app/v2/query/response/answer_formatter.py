from langfuse.decorators import observe
from config import settings

class AnswerFormatter:
    # @observe()
    def run(self, answer: dict) -> dict:
        # Add detailed input logging
        print(f"AnswerFormatter received type: {type(answer)}")
        print(f"Initial answer content: {str(answer)[:200]}...")
        
        if not isinstance(answer, dict):
            print(f"Unexpected input type: {type(answer)}. Full input: {str(answer)[:300]}...")
            return {
                "query": "",
                "response": str(answer).strip() if answer else "",
                "metadata": {}
            }
            
        # Log dictionary structure
        print(f"Answer keys: {list(answer.keys())}")
        print(f"Response type: {type(answer.get('response'))}")
        
        # print(f"State: {state}")
        formatted_answer = {
            "query": answer.get("query", "").strip(),
            "response": answer.get("response", ""),
            "metadata": answer.get("metadata", {})
        }
        
        
        # print(f"Formatted answer from AnswerFormatter: {formatted_answer}")
        return formatted_answer
