#!/usr/bin/env python3
"""
Debug the notification pipeline to see where NOTI134.PDF is coming from
"""

import json
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_notification_pipeline():
    """Debug the notification pipeline with a sample notification"""
    
    # Load the test results to see what's happening
    results_file = Path("complai_knowledge_tracker/airflow/dags/tests/notification_pipeline_results.json")
    
    if not results_file.exists():
        print(f"❌ Results file not found: {results_file}")
        return
    
    with open(results_file, 'r') as f:
        data = json.load(f)

    # Extract the notifications array
    results = data.get('notifications', [])

    print("🔍 Debugging Notification Pipeline Results")
    print("=" * 80)

    # Check each result for NOTI134 issues
    noti134_count = 0
    correct_url_count = 0

    for i, result in enumerate(results[:10], 1):  # Check first 10 for debugging
        print(f"\n📋 Notification {i}: {result.get('title', 'Unknown')[:60]}...")
        print("-" * 60)

        # Check if this is from the actual pipeline results or predicted
        if 'predicted_analysis' in result:
            print("⚠️ This is a predicted result, not actual pipeline output")
            continue

        # Check new_document_url
        new_doc_url = result.get('new_document_url', '')
        if 'NOTI134' in new_doc_url:
            noti134_count += 1
            print(f"❌ FOUND NOTI134 in new_document_url: {new_doc_url}")
        elif new_doc_url:
            correct_url_count += 1
            print(f"✅ Correct new_document_url: {new_doc_url}")
        else:
            print(f"⚠️ No new_document_url found")

        # Check notification_pdf_url
        notif_pdf_url = result.get('notification_pdf_url', '')
        if 'NOTI134' in notif_pdf_url:
            print(f"❌ FOUND NOTI134 in notification_pdf_url: {notif_pdf_url}")
        elif notif_pdf_url:
            print(f"✅ Correct notification_pdf_url: {notif_pdf_url}")

        # Check pdf_links array
        pdf_links = result.get('pdf_links', [])
        for link in pdf_links:
            if 'NOTI134' in link:
                print(f"❌ FOUND NOTI134 in pdf_links: {link}")
            else:
                print(f"✅ Correct pdf_link: {link}")

        # Check has_new_document_link flag
        has_link = result.get('has_new_document_link', False)
        print(f"🏷️ has_new_document_link: {has_link}")

        # Check document actions
        doc_actions = result.get('document_actions', [])
        print(f"📊 Document actions: {len(doc_actions)}")
        for j, action in enumerate(doc_actions):
            action_type = action.get('action_type', 'Unknown')
            doc_id = action.get('document_id', 'Unknown')
            print(f"   {j+1}. {action_type}: {doc_id}")

    print(f"\n📊 Summary:")
    print(f"   ❌ Notifications with NOTI134 URLs: {noti134_count}")
    print(f"   ✅ Notifications with correct URLs: {correct_url_count}")
    print(f"   📋 Total notifications checked: {min(10, len(results))}")

    # Look for patterns in the NOTI134 cases
    if noti134_count > 0:
        print(f"\n🔍 Analyzing NOTI134 cases:")
        for i, result in enumerate(results[:10], 1):
            new_doc_url = result.get('new_document_url', '')
            if 'NOTI134' in new_doc_url:
                title = result.get('title', 'Unknown')
                category = result.get('category', 'Unknown')
                print(f"   {i}. {title[:40]}... (Category: {category})")

def check_sample_notification():
    """Check a specific notification to see the processing flow"""
    
    # Sample notification data
    sample_notification = {
        "title": "Foreign Exchange Management (Export of Goods & Services) (Amendment) Regulations, 2025",
        "rss_description": """
        <p>FEMA 23(R)/(6)/2025-RB</p>
        <p>June 24, 2025</p>
        <p>All Authorised Dealers</p>
        <p class='head'>Foreign Exchange Management (Export of Goods & Services) (Amendment) Regulations, 2025</p>
        <p>In exercise of the powers conferred by clause (a) of sub-section (3) of section 6 and section 47 of the Foreign Exchange Management Act, 1999 (42 of 1999), the Reserve Bank of India hereby makes the following regulations to amend the Foreign Exchange Management (Export of Goods & Services) Regulations, 2015, namely:-</p>
        <p><a href='https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0'>View Notification</a></p>
        """,
        "link": "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12345&Mode=0"
    }
    
    print("\n🧪 Testing Sample Notification Processing")
    print("=" * 80)
    print(f"Title: {sample_notification['title']}")
    print(f"Link: {sample_notification['link']}")
    
    # Test URL extraction
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(sample_notification['rss_description'], 'html.parser')
    
    # Find RBI links
    rbi_links = []
    for link in soup.find_all('a', href=True):
        href = link['href']
        if 'rbi.org.in' in href:
            rbi_links.append(href)
            print(f"✅ Found RBI link: {href}")
    
    if rbi_links:
        # Test PDF extraction from the first RBI link
        print(f"\n🔍 Testing PDF extraction from: {rbi_links[0]}")
        
        # Import the fixed extraction function
        import sys
        sys.path.append('.')
        from test_standalone_extraction import extract_pdf_from_rbi_page_fixed
        
        pdf_url = extract_pdf_from_rbi_page_fixed(rbi_links[0])
        if pdf_url:
            print(f"✅ Successfully extracted PDF: {pdf_url}")
            if 'NOTI134' in pdf_url:
                print(f"❌ WARNING: Extracted URL contains NOTI134!")
            else:
                print(f"✅ PDF URL looks correct")
        else:
            print(f"❌ Failed to extract PDF URL")

if __name__ == "__main__":
    debug_notification_pipeline()
    check_sample_notification()
