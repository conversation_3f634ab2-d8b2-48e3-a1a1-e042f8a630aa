// SourcesButton.tsx
"use client";

import { useState, useRef, useEffect } from "react";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import type { ReferenceItem } from "@/types/types";

// Add a global style to hide scrollbars - matching ResourcesSidebar
const ScrollbarStyle = () => (
  <style jsx global>{`
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }
  `}</style>
);

interface SourcesButtonProps {
  metadata: ReferenceItem[];
  onReferenceClick: (reference: ReferenceItem) => void;
  onViewDetail: () => void;  // Function to expand the sidebar
  onCollapseChat?: () => void;  // Function to collapse chat sidebar
}

export function SourcesButton({ 
  metadata, 
  onReferenceClick, 
  onViewDetail,
  onCollapseChat 
}: SourcesButtonProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);
  
  // Number of references to show per page
  const itemsPerPage = 1;
  
  // Calculate total pages
  const totalPages = Math.ceil(metadata.length / itemsPerPage);
  
  // Get current references to display
  const getCurrentReferences = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return metadata.slice(startIndex, startIndex + itemsPerPage);
  };
  
  // Handle navigation
  const goToPreviousPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  const goToNextPage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Toggle dropdown
  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDropdownOpen(!isDropdownOpen);
  };
  
  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownRef, buttonRef]);
  
  // Reset to first page when dropdown opens
  useEffect(() => {
    if (isDropdownOpen) {
      setCurrentPage(1);
    }
  }, [isDropdownOpen]);
  
  // Handle reference selection
  const handleReferenceClick = (reference: ReferenceItem) => {
    // Close the dropdown
    setIsDropdownOpen(false);
    
    // Pass the reference to the parent component
    onReferenceClick(reference);
    
    // Expand the sidebar to show the PDF
    onViewDetail();
    
    // Collapse chat history sidebar if the function is provided
    if (onCollapseChat) {
      onCollapseChat();
    }
  };
  
  // Only show if we have metadata
  if (!metadata || metadata.length === 0) {
    return null;
  }
  
  return (
    <div className="relative inline-block">
      <ScrollbarStyle />
      
      {/* Sources Button */}
      <div 
        ref={buttonRef}
        onClick={toggleDropdown}
        className="inline-flex items-center px-2 py-1 bg-[#F5EEE0] rounded text-[16px] text-[#413A36] font-medium cursor-pointer"
      >
        <span className="flex items-center">
          <span className="w-4 h-4 mr-1 flex items-center justify-center text-[12px] bg-[#FF6B1C] text-white rounded-full">S</span>
          Sources
        </span>
        {metadata.length > 0 && (
          <span className="ml-1 text-[#FF6B1C]">+{metadata.length}</span>
        )}
      </div>
      
      {/* Dropdown - Exact match to ResourcesSidebar */}
      {isDropdownOpen && (
        <div 
          ref={dropdownRef}
          className="absolute left-0 mt-2 bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D7CBBC] rounded-xl shadow-lg z-50"
          style={{ 
            bottom: "calc(100% + 8px)", 
            width: "320px",
            maxHeight: "400px",
            overflow: "hidden"
          }}
        >
          {/* Header - Exact match to ResourcesSidebar header */}
          <div className="p-4 border-b border-[#D7CBBC] flex justify-between items-center sticky top-0 bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] z-20">
            <div>
              <h2 className="text-sm font-semibold text-[#494949]">Amendments to Master Direction</h2>
              <div className="text-xs text-[#7B7B7B]">
                {currentPage}/{totalPages}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                className={`p-1 ${currentPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-[#7B7B7B] hover:text-[#494949]'}`}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              <button 
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className={`p-1 ${currentPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-[#7B7B7B] hover:text-[#494949]'}`}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
              
              <button 
                onClick={() => setIsDropdownOpen(false)} 
                className="text-[#7B7B7B] hover:text-[#494949]"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          {/* References List - Exact match to ResourcesSidebar style */}
          <div className="h-full overflow-y-auto no-scrollbar" style={{ maxHeight: "350px" }}>
            {getCurrentReferences().map((reference, index) => (
              <div 
                key={index} 
                className="px-2 py-2 m-4 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer transition-colors duration-200"
                onClick={() => handleReferenceClick(reference)}
              >
                <h4 className="text-sm font-medium text-[#494949]">{reference.title || "Amendments to Master Direction on Counterfeit Notes"}</h4>
                <p className="text-xs text-[#7B7B7B]">{reference.date_of_issue || ""}</p>
                <div className="text-xs text-[#7B7B7B]">
                  {reference.Type || "Master Direction"} • {reference.dept || "RBI"}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// // SourcesButton.tsx
// "use client";

// import { useState, useRef, useEffect } from "react";
// import { X, ChevronLeft, ChevronRight } from "lucide-react";
// import type { ReferenceItem } from "@/types/types";

// // Add a global style to hide scrollbars
// const ScrollbarStyle = () => (
//   <style jsx global>{`
//     /* Hide scrollbar for Chrome, Safari and Opera */
//     .no-scrollbar::-webkit-scrollbar {
//       display: none;
//     }

//     /* Hide scrollbar for IE, Edge and Firefox */
//     .no-scrollbar {
//       -ms-overflow-style: none;  /* IE and Edge */
//       scrollbar-width: none;  /* Firefox */
//     }
//   `}</style>
// );

// interface SourcesButtonProps {
//   metadata: ReferenceItem[];
//   onSelectReference: (reference: ReferenceItem) => void;
//   onViewDetail: () => void;
//   onCollapseChat?: () => void;
// }

// export function SourcesButton({
//   metadata,
//   onSelectReference,
//   onViewDetail,
//   onCollapseChat
// }: SourcesButtonProps) {
//   const [isDropdownOpen, setIsDropdownOpen] = useState(false);
//   const [currentPage, setCurrentPage] = useState(1);
//   const dropdownRef = useRef<HTMLDivElement>(null);
//   const buttonRef = useRef<HTMLDivElement>(null);
  
//   // Number of references to show per page
//   const itemsPerPage = 1;
  
//   // Calculate total pages
//   const totalPages = Math.ceil(metadata.length / itemsPerPage);
  
//   // Get current references to display
//   const getCurrentReferences = () => {
//     const startIndex = (currentPage - 1) * itemsPerPage;
//     return metadata.slice(startIndex, startIndex + itemsPerPage);
//   };
  
//   // Handle navigation
//   const goToPreviousPage = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     if (currentPage > 1) {
//       setCurrentPage(currentPage - 1);
//     }
//   };
  
//   const goToNextPage = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     if (currentPage < totalPages) {
//       setCurrentPage(currentPage + 1);
//     }
//   };
  
//   // Toggle dropdown
//   const toggleDropdown = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     setIsDropdownOpen(!isDropdownOpen);
//   };
  
//   // Handle click outside to close dropdown
//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       if (
//         dropdownRef.current && 
//         !dropdownRef.current.contains(event.target as Node) &&
//         buttonRef.current &&
//         !buttonRef.current.contains(event.target as Node)
//       ) {
//         setIsDropdownOpen(false);
//       }
//     };
    
//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [dropdownRef, buttonRef]);
  
//   // Reset to first page when dropdown opens
//   useEffect(() => {
//     if (isDropdownOpen) {
//       setCurrentPage(1);
//     }
//   }, [isDropdownOpen]);
  
//   // Handle reference selection
//   const handleReferenceClick = (reference: ReferenceItem) => {
//     // Close the dropdown
//     setIsDropdownOpen(false);
    
//     // Pass the reference to the parent component which will handle showing it in ResourcesSidebar
//     onSelectReference(reference);
    
//     // Expand the sidebar
//     onViewDetail();
    
//     // Collapse chat history sidebar if needed
//     if (onCollapseChat) {
//       onCollapseChat();
//     }
//   };
  
//   // Only show if we have metadata
//   if (!metadata || metadata.length === 0) {
//     return null;
//   }
  
//   return (
//     <div className="relative inline-block">
//       <ScrollbarStyle />
      
//       {/* Sources Button */}
//       <div 
//         ref={buttonRef}
//         onClick={toggleDropdown}
//         className="inline-flex items-center px-2 py-1 bg-[#F5EEE0] rounded text-[16px] text-[#413A36] font-medium cursor-pointer"
//       >
//         <span className="flex items-center">
//           <span className="w-4 h-4 mr-1 flex items-center justify-center text-[12px] bg-[#FF6B1C] text-white rounded-full">S</span>
//           Sources
//         </span>
//         {metadata.length > 0 && (
//           <span className="ml-1 text-[#FF6B1C]">+{metadata.length}</span>
//         )}
//       </div>
      
//       {/* Dropdown */}
//       {isDropdownOpen && (
//         <div 
//           ref={dropdownRef}
//           className="absolute left-0 mt-2 bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] border-l border-[#D7CBBC] rounded-xl shadow-lg z-50"
//           style={{ 
//             bottom: "calc(100% + 8px)", 
//             width: "320px",
//             maxHeight: "400px",
//             overflow: "hidden"
//           }}
//         >
//           {/* Header */}
//           <div className="p-4 border-b border-[#D7CBBC] flex justify-between items-center sticky top-0 bg-gradient-to-b from-[#DDD1C5] to-[#E0D7E2] z-20">
//             <div>
//               <h2 className="text-sm font-semibold text-[#494949]">Amendments to Master Direction</h2>
//               <div className="text-xs text-[#7B7B7B]">
//                 {currentPage}/{totalPages}
//               </div>
//             </div>
//             <div className="flex items-center gap-2">
//               <button 
//                 onClick={goToPreviousPage}
//                 disabled={currentPage === 1}
//                 className={`p-1 ${currentPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-[#7B7B7B] hover:text-[#494949]'}`}
//               >
//                 <ChevronLeft className="h-5 w-5" />
//               </button>
              
//               <button 
//                 onClick={goToNextPage}
//                 disabled={currentPage === totalPages}
//                 className={`p-1 ${currentPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-[#7B7B7B] hover:text-[#494949]'}`}
//               >
//                 <ChevronRight className="h-5 w-5" />
//               </button>
              
//               <button 
//                 onClick={() => setIsDropdownOpen(false)} 
//                 className="text-[#7B7B7B] hover:text-[#494949]"
//               >
//                 <X className="h-5 w-5" />
//               </button>
//             </div>
//           </div>
          
//           {/* References List */}
//           <div className="h-full overflow-y-auto no-scrollbar" style={{ maxHeight: "350px" }}>
//             {getCurrentReferences().map((reference, index) => (
//               <div 
//                 key={index} 
//                 className="px-2 py-2 m-4 flex flex-col gap-1 rounded-xl hover:bg-[#F1E9E6] cursor-pointer transition-colors duration-200"
//                 onClick={() => handleReferenceClick(reference)}
//               >
//                 <h4 className="text-sm font-medium text-[#494949]">{reference.title || "Amendments to Master Direction on Counterfeit Notes"}</h4>
//                 <p className="text-xs text-[#7B7B7B]">{reference.date_of_issue || ""}</p>
//                 <div className="text-xs text-[#7B7B7B]">
//                   {reference.Type || "Master Direction"} • {reference.dept || "RBI"}
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }