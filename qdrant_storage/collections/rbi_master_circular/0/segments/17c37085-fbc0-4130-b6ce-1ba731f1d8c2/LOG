2025/06/30-11:26:13.492036 10110 RocksDB version: 9.9.3
2025/06/30-11:26:13.492119 10110 Compile date 2024-12-05 01:25:31
2025/06/30-11:26:13.492120 10110 DB SUMMARY
2025/06/30-11:26:13.492120 10110 Host name (Env):  3ed34e58cde5
2025/06/30-11:26:13.492121 10110 DB Session ID:  HLH6JCEFCE6BMGI16FO6
2025/06/30-11:26:13.492606 10110 CURRENT file:  CURRENT
2025/06/30-11:26:13.492611 10110 MANIFEST file:  MANIFEST-000061 size: 2122 Bytes
2025/06/30-11:26:13.492618 10110 SST files in ./storage/collections/rbi_master_circular/0/segments/17c37085-fbc0-4130-b6ce-1ba731f1d8c2 dir, Total Num: 18, files: 000015.sst 000017.sst 000019.sst 000021.sst 000023.sst 000025.sst 000027.sst 000029.sst 000031.sst 
2025/06/30-11:26:13.492619 10110 Write Ahead Log file in ./storage/collections/rbi_master_circular/0/segments/17c37085-fbc0-4130-b6ce-1ba731f1d8c2: 
2025/06/30-11:26:13.492620 10110                         Options.error_if_exists: 0
2025/06/30-11:26:13.492621 10110                       Options.create_if_missing: 1
2025/06/30-11:26:13.492622 10110                         Options.paranoid_checks: 1
2025/06/30-11:26:13.492622 10110             Options.flush_verify_memtable_count: 1
2025/06/30-11:26:13.492623 10110          Options.compaction_verify_record_count: 1
2025/06/30-11:26:13.492623 10110                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-11:26:13.492624 10110        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-11:26:13.492625 10110                                     Options.env: 0xffff86231620
2025/06/30-11:26:13.492625 10110                                      Options.fs: PosixFileSystem
2025/06/30-11:26:13.492626 10110                                Options.info_log: 0xffff8f676b00
2025/06/30-11:26:13.492627 10110                Options.max_file_opening_threads: 16
2025/06/30-11:26:13.492627 10110                              Options.statistics: (nil)
2025/06/30-11:26:13.492628 10110                               Options.use_fsync: 0
2025/06/30-11:26:13.492629 10110                       Options.max_log_file_size: 1048576
2025/06/30-11:26:13.492629 10110                  Options.max_manifest_file_size: 1073741824
2025/06/30-11:26:13.492630 10110                   Options.log_file_time_to_roll: 0
2025/06/30-11:26:13.492631 10110                       Options.keep_log_file_num: 1
2025/06/30-11:26:13.492632 10110                    Options.recycle_log_file_num: 0
2025/06/30-11:26:13.492633 10110                         Options.allow_fallocate: 1
2025/06/30-11:26:13.492633 10110                        Options.allow_mmap_reads: 0
2025/06/30-11:26:13.492634 10110                       Options.allow_mmap_writes: 0
2025/06/30-11:26:13.492635 10110                        Options.use_direct_reads: 0
2025/06/30-11:26:13.492635 10110                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-11:26:13.492636 10110          Options.create_missing_column_families: 1
2025/06/30-11:26:13.492637 10110                              Options.db_log_dir: 
2025/06/30-11:26:13.492637 10110                                 Options.wal_dir: 
2025/06/30-11:26:13.492638 10110                Options.table_cache_numshardbits: 6
2025/06/30-11:26:13.492638 10110                         Options.WAL_ttl_seconds: 0
2025/06/30-11:26:13.492639 10110                       Options.WAL_size_limit_MB: 0
2025/06/30-11:26:13.492640 10110                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-11:26:13.492641 10110             Options.manifest_preallocation_size: 4194304
2025/06/30-11:26:13.492641 10110                     Options.is_fd_close_on_exec: 1
2025/06/30-11:26:13.492642 10110                   Options.advise_random_on_open: 1
2025/06/30-11:26:13.492642 10110                    Options.db_write_buffer_size: 0
2025/06/30-11:26:13.492643 10110                    Options.write_buffer_manager: 0xffff8f64ad80
2025/06/30-11:26:13.492643 10110           Options.random_access_max_buffer_size: 1048576
2025/06/30-11:26:13.492644 10110                      Options.use_adaptive_mutex: 0
2025/06/30-11:26:13.492645 10110                            Options.rate_limiter: (nil)
2025/06/30-11:26:13.492647 10110     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-11:26:13.492647 10110                       Options.wal_recovery_mode: 0
2025/06/30-11:26:13.492648 10110                  Options.enable_thread_tracking: 0
2025/06/30-11:26:13.492649 10110                  Options.enable_pipelined_write: 0
2025/06/30-11:26:13.492650 10110                  Options.unordered_write: 0
2025/06/30-11:26:13.492650 10110         Options.allow_concurrent_memtable_write: 1
2025/06/30-11:26:13.492651 10110      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-11:26:13.492652 10110             Options.write_thread_max_yield_usec: 100
2025/06/30-11:26:13.492652 10110            Options.write_thread_slow_yield_usec: 3
2025/06/30-11:26:13.492653 10110                               Options.row_cache: None
2025/06/30-11:26:13.492654 10110                              Options.wal_filter: None
2025/06/30-11:26:13.492655 10110             Options.avoid_flush_during_recovery: 0
2025/06/30-11:26:13.492655 10110             Options.allow_ingest_behind: 0
2025/06/30-11:26:13.492656 10110             Options.two_write_queues: 0
2025/06/30-11:26:13.492656 10110             Options.manual_wal_flush: 0
2025/06/30-11:26:13.492657 10110             Options.wal_compression: 0
2025/06/30-11:26:13.492658 10110             Options.background_close_inactive_wals: 0
2025/06/30-11:26:13.492658 10110             Options.atomic_flush: 0
2025/06/30-11:26:13.492659 10110             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-11:26:13.492660 10110             Options.prefix_seek_opt_in_only: 0
2025/06/30-11:26:13.492660 10110                 Options.persist_stats_to_disk: 0
2025/06/30-11:26:13.492661 10110                 Options.write_dbid_to_manifest: 1
2025/06/30-11:26:13.492662 10110                 Options.write_identity_file: 1
2025/06/30-11:26:13.492662 10110                 Options.log_readahead_size: 0
2025/06/30-11:26:13.492663 10110                 Options.file_checksum_gen_factory: Unknown
2025/06/30-11:26:13.492663 10110                 Options.best_efforts_recovery: 0
2025/06/30-11:26:13.492664 10110                Options.max_bgerror_resume_count: 2147483647
2025/06/30-11:26:13.492665 10110            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-11:26:13.492666 10110             Options.allow_data_in_errors: 0
2025/06/30-11:26:13.492666 10110             Options.db_host_id: __hostname__
2025/06/30-11:26:13.492667 10110             Options.enforce_single_del_contracts: true
2025/06/30-11:26:13.492668 10110             Options.metadata_write_temperature: kUnknown
2025/06/30-11:26:13.492668 10110             Options.wal_write_temperature: kUnknown
2025/06/30-11:26:13.492669 10110             Options.max_background_jobs: 2
2025/06/30-11:26:13.492670 10110             Options.max_background_compactions: -1
2025/06/30-11:26:13.492670 10110             Options.max_subcompactions: 1
2025/06/30-11:26:13.492671 10110             Options.avoid_flush_during_shutdown: 0
2025/06/30-11:26:13.492671 10110           Options.writable_file_max_buffer_size: 1048576
2025/06/30-11:26:13.492672 10110             Options.delayed_write_rate : 16777216
2025/06/30-11:26:13.492673 10110             Options.max_total_wal_size: 0
2025/06/30-11:26:13.492673 10110             Options.delete_obsolete_files_period_micros: 180000000
2025/06/30-11:26:13.492674 10110                   Options.stats_dump_period_sec: 600
2025/06/30-11:26:13.492675 10110                 Options.stats_persist_period_sec: 600
2025/06/30-11:26:13.492675 10110                 Options.stats_history_buffer_size: 1048576
2025/06/30-11:26:13.492676 10110                          Options.max_open_files: 256
2025/06/30-11:26:13.492676 10110                          Options.bytes_per_sync: 0
2025/06/30-11:26:13.492677 10110                      Options.wal_bytes_per_sync: 0
2025/06/30-11:26:13.492678 10110                   Options.strict_bytes_per_sync: 0
2025/06/30-11:26:13.492678 10110       Options.compaction_readahead_size: 2097152
2025/06/30-11:26:13.492679 10110                  Options.max_background_flushes: -1
2025/06/30-11:26:13.492680 10110 Options.daily_offpeak_time_utc: 
2025/06/30-11:26:13.492681 10110 Compression algorithms supported:
2025/06/30-11:26:13.492681 10110 	kZSTD supported: 0
2025/06/30-11:26:13.492682 10110 	kXpressCompression supported: 0
2025/06/30-11:26:13.492683 10110 	kBZip2Compression supported: 0
2025/06/30-11:26:13.492683 10110 	kZSTDNotFinalCompression supported: 0
2025/06/30-11:26:13.492684 10110 	kLZ4Compression supported: 1
2025/06/30-11:26:13.492684 10110 	kZlibCompression supported: 0
2025/06/30-11:26:13.492685 10110 	kLZ4HCCompression supported: 1
2025/06/30-11:26:13.492685 10110 	kSnappyCompression supported: 1
2025/06/30-11:26:13.492686 10110 Fast CRC32 supported: Not supported on x86
2025/06/30-11:26:13.492687 10110 DMutex implementation: pthread_mutex_t
2025/06/30-11:26:13.492687 10110 Jemalloc supported: 0
2025/06/30-11:26:13.493621 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493622 10110           Options.merge_operator: None
2025/06/30-11:26:13.493623 10110        Options.compaction_filter: None
2025/06/30-11:26:13.493623 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493624 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493624 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493625 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493660 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493662 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493664 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493664 10110          Options.compression: LZ4
2025/06/30-11:26:13.493665 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493665 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493666 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493667 10110             Options.num_levels: 7
2025/06/30-11:26:13.493667 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493668 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493668 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493669 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493669 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493670 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493671 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493671 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493672 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493673 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493673 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493712 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493713 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493713 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493725 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493726 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493726 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493727 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493727 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493728 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493728 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493729 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493729 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493730 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493730 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493731 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493732 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493732 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493733 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493734 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493734 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493735 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493736 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493736 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493737 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493738 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493738 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493739 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493739 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493740 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493740 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493741 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493741 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493742 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493743 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493743 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493744 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493745 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493745 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493746 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493746 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493747 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493748 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493749 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493750 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493751 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493752 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493753 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493753 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493754 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.493754 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493755 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493755 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493756 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493756 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493757 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493758 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.493758 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493759 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493759 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493760 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493760 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.493761 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.493762 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493762 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493763 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493763 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493764 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493765 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493765 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493766 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493766 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.493909 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493910 10110           Options.merge_operator: None
2025/06/30-11:26:13.493912 10110        Options.compaction_filter: None
2025/06/30-11:26:13.493912 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493913 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493913 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493914 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493921 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493923 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493924 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493924 10110          Options.compression: LZ4
2025/06/30-11:26:13.493925 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493925 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493926 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493927 10110             Options.num_levels: 7
2025/06/30-11:26:13.493927 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493928 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493929 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493929 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493930 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493930 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493931 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493932 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493932 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493933 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493933 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493934 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493935 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493935 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493936 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493936 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493937 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493938 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493938 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493939 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493940 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493940 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493941 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493941 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493942 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493942 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493943 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493943 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493944 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493945 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493945 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493946 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493946 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493947 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493947 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493948 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493948 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493949 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493950 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493953 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493953 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493954 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493955 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493955 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493956 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493956 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493957 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493957 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493958 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493958 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493959 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493960 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493960 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493961 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493962 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493962 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493963 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493964 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493964 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493965 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.493965 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493966 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493966 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493967 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493967 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493968 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493968 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.493969 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493969 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493970 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493971 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493971 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.493972 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.493972 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493973 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493974 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493974 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493975 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493976 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493977 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493977 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493978 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494036 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494037 10110           Options.merge_operator: None
2025/06/30-11:26:13.494039 10110        Options.compaction_filter: None
2025/06/30-11:26:13.494041 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494042 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494043 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494044 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494050 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494052 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494052 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494053 10110          Options.compression: LZ4
2025/06/30-11:26:13.494054 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494054 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494056 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494056 10110             Options.num_levels: 7
2025/06/30-11:26:13.494057 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494058 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494058 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494059 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494060 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494060 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494061 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494061 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494062 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494062 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494063 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494063 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494064 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494065 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494065 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494066 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494066 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494067 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494067 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494068 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494068 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494069 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494070 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494071 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494071 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494072 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494073 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494073 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494074 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494074 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494075 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494076 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494076 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494077 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494078 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494078 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494079 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494079 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494080 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494080 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494081 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494082 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494082 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494083 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494084 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494084 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494085 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494085 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494086 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494087 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494087 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494088 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494089 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494090 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494091 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494091 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494092 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494093 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494093 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494094 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.494095 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494095 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494096 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494097 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494098 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494099 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494099 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.494100 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494101 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494102 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494103 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494103 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.494104 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.494104 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494105 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494106 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494106 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494107 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494110 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494110 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494111 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494112 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494160 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494162 10110           Options.merge_operator: None
2025/06/30-11:26:13.494162 10110        Options.compaction_filter: None
2025/06/30-11:26:13.494163 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494163 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494164 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494164 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494234 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494236 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494236 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494237 10110          Options.compression: LZ4
2025/06/30-11:26:13.494237 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494238 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494239 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494239 10110             Options.num_levels: 7
2025/06/30-11:26:13.494240 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494240 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494241 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494241 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494242 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494242 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494243 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494244 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494244 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494245 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494246 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494246 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494247 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494247 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494248 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494251 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494252 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494252 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494253 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494253 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494254 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494254 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494255 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494255 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494256 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494257 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494257 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494258 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494259 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494259 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494260 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494261 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494261 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494262 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494262 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494263 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494264 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494264 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494265 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494265 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494266 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494267 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494268 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494268 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494269 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494269 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494270 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494270 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494271 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494272 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494272 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494273 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494274 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494275 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494275 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494276 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494277 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494278 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494279 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494279 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.494280 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494280 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494281 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494282 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494283 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494283 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494284 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.494285 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494285 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494286 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494286 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494287 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.494288 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.494288 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494289 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494289 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494290 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494291 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494291 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494292 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494293 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494293 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494323 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494324 10110           Options.merge_operator: None
2025/06/30-11:26:13.494324 10110        Options.compaction_filter: None
2025/06/30-11:26:13.494325 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494325 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494326 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494327 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494332 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494334 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494334 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494335 10110          Options.compression: LZ4
2025/06/30-11:26:13.494336 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494336 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494337 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494337 10110             Options.num_levels: 7
2025/06/30-11:26:13.494338 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494338 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494339 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494340 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494340 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494341 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494341 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494342 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494342 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494343 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494344 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494344 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494345 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494346 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494346 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494347 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494347 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494348 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494349 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494350 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494350 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494351 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494352 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494352 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494353 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494353 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494354 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494355 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494355 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494356 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494357 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494357 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494358 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494358 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494359 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494360 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494360 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494361 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494362 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494362 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494363 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494363 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494364 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494365 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494365 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494366 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494366 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494367 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494367 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494368 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494368 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494369 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494370 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494370 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494371 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494372 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494372 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494373 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494374 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494374 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.494375 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494375 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494376 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494376 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494377 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494377 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494378 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.494378 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494379 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494379 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494380 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494380 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.494381 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.494381 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494382 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494382 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494383 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494384 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494384 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494385 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494385 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494386 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494416 10110               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494417 10110           Options.merge_operator: None
2025/06/30-11:26:13.494418 10110        Options.compaction_filter: None
2025/06/30-11:26:13.494418 10110        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494419 10110  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494419 10110         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494420 10110            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494427 10110            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8f600d80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8f64a310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494428 10110        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494429 10110  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494429 10110          Options.compression: LZ4
2025/06/30-11:26:13.494430 10110                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494430 10110       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494431 10110   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494432 10110             Options.num_levels: 7
2025/06/30-11:26:13.494432 10110        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494433 10110     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494433 10110     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494434 10110            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494434 10110                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494435 10110               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494435 10110         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494436 10110         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494436 10110         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494437 10110                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494438 10110         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494438 10110         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494439 10110            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494440 10110                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494440 10110               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494441 10110         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494441 10110         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494442 10110         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494442 10110         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494443 10110                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494443 10110         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494444 10110      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494444 10110          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494445 10110              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494446 10110                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494447 10110             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494447 10110                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494448 10110 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494448 10110          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494449 10110 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494449 10110 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494450 10110 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494450 10110 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494451 10110 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494451 10110 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494452 10110 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494452 10110       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494453 10110                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494453 10110                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494454 10110   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494454 10110   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494455 10110                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494455 10110                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494456 10110                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494457 10110 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494457 10110 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494458 10110 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494458 10110 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494459 10110 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494459 10110 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494460 10110 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494460 10110 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494461 10110 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494462 10110                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494463 10110                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494463 10110                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494464 10110               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494465 10110               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494465 10110   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494466 10110                           Options.bloom_locality: 0
2025/06/30-11:26:13.494466 10110                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494467 10110             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494467 10110                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494468 10110                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494469 10110                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494469 10110                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494470 10110                               Options.ttl: 2592000
2025/06/30-11:26:13.494480 10110          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494480 10110                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494481 10110  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494481 10110    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494482 10110                       Options.enable_blob_files: false
2025/06/30-11:26:13.494482 10110                           Options.min_blob_size: 0
2025/06/30-11:26:13.494483 10110                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494483 10110                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494484 10110          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494485 10110      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494485 10110 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494486 10110          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494486 10110                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494487 10110         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494487 10110            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.514307 10110 DB pointer 0xffff77065800
