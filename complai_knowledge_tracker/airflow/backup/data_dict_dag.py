from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
from datetime import datetime
import logging

from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct
from qdrant_client.http.models import VectorParams, Distance
from openai import OpenAI
import boto3
import csv
import time
import uuid
import subprocess
import sys

# ------------------------------------------------------------------------------
# Global Clients and Configuration
# ------------------------------------------------------------------------------
openai_api_key = Variable.get("openai_api_key")
documentdb_uri = Variable.get("documentdb_uri")
aws_access_key_id = Variable.get("aws_access_key_id")
aws_secret_access_key = Variable.get("aws_secret_access_key")
qdrant_url = "http://host.docker.internal:6333"  # Updated to use Docker's internal host
qdrant_collection = "abbreviation_dictionary_banking"

client_openai = OpenAI(api_key=openai_api_key)
logger = logging.getLogger(__name__)
logger.info("Starting the abbreviation dictionary DAG")
logger.info(f"Qdrant URL: {qdrant_url}")

# ------------------------------------------------------------------------------
# Tasks
# ------------------------------------------------------------------------------

@task
def install_dependencies():
    """Install required dependencies."""
    packages = ["boto3", "pandas", "qdrant-client", "langchain", "openai"]
    for package in packages:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
    logger.info("✅ All dependencies installed.")
    return True

@task
def test_qdrant_connection():
    """Test connection to Qdrant."""
    from qdrant_client import QdrantClient

    max_retries = 5
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info(f"Testing connection to Qdrant (attempt {retry_count + 1}/{max_retries})")
            qdrant_client = QdrantClient(url=qdrant_url)
            qdrant_client.get_collections()
            logger.info("✅ Qdrant connection test passed.")
            return True
        except Exception as e:
            retry_count += 1
            sleep_time = 10 * retry_count
            logger.error(f"Qdrant connection test attempt {retry_count} failed: {e}. Retrying in {sleep_time}s.")
            time.sleep(sleep_time)
            if retry_count == max_retries:
                logger.error(f"Permanent Qdrant connection failure after {max_retries} attempts.")
                raise

@task
def fetch_abbreviation_file_from_s3() -> str:
    """Download the abbreviation dictionary CSV from S3."""
    S3_BUCKET_NAME = "rbi-docs-storage"
    FILE_KEY = "Dictionary/abbreviation_file.csv"
    
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    
    local_path = "/tmp/abbreviation_file.csv"
    s3_client.download_file(S3_BUCKET_NAME, FILE_KEY, local_path)
    logger.info(f"✅ Downloaded abbreviation dictionary to {local_path}")
    return local_path

@task
def process_abbreviation_file(local_path: str) -> list:
    """Process the abbreviation CSV, create embeddings, and insert into Qdrant."""
    
    # Retry connection to Qdrant
    max_retries = 5
    retry_count = 0
    qdrant_client = None

    while retry_count < max_retries:
        try:
            logger.info(f"Attempting to connect to Qdrant (attempt {retry_count + 1}/{max_retries})")
            qdrant_client = QdrantClient(url=qdrant_url)
            logger.info("✅ Successfully connected to Qdrant.")
            break
        except Exception as e:
            retry_count += 1
            sleep_time = 10 * retry_count
            logger.error(f"Qdrant connection attempt {retry_count} failed: {e}. Retrying in {sleep_time}s.")
            time.sleep(sleep_time)
            if retry_count == max_retries:
                logger.error(f"Permanent Qdrant connection failure after {max_retries} attempts.")
                raise

    # Load the CSV and validate structure
    with open(local_path, mode='r') as file:
        reader = csv.DictReader(file)
        if 'Data Elements' not in reader.fieldnames or 'Definitions' not in reader.fieldnames:
            logger.error("❌ CSV file is missing required columns: 'Data Elements' and/or 'Definitions'.")
            raise ValueError("CSV file structure is invalid.")
        rows = list(reader)

    # Generate embeddings
    texts = [row['Data Elements'] for row in rows]
    try:
        batch_response = client_openai.embeddings.create(
            input=texts,
            model="text-embedding-3-large"
        )
        embeddings = [item.embedding for item in batch_response.data]
        vector_dim = len(embeddings[0])  # Dynamically determine vector dimension
        logger.info(f"✅ Generated {len(embeddings)} embeddings with dimension {vector_dim}.")
    except Exception as e:
        logger.error(f"❌ Failed to generate embeddings: {e}")
        raise

    # Ensure Qdrant collection exists with correct vector dimension
    if not qdrant_client.collection_exists(collection_name=qdrant_collection):
        logger.warning(f"⚠️ Qdrant collection '{qdrant_collection}' does not exist. Creating it now.")
        qdrant_client.create_collection(
            collection_name=qdrant_collection,
            vectors_config=VectorParams(size=vector_dim, distance=Distance.COSINE)
        )
        logger.info(f"✅ Created Qdrant collection '{qdrant_collection}' with vector dimension {vector_dim}.")
    else:
        logger.info(f"✅ Qdrant collection '{qdrant_collection}' already exists.")

    # Prepare points
    points = []
    for idx, row in enumerate(rows):
        point = PointStruct(
            id=str(uuid.uuid4()),
            vector=embeddings[idx],
            payload={
                "term": row['Data Elements'],
                "definition": row['Definitions'],
                "type": "abbreviation",
                "source": "dictionary_upload"
            }
        )
        points.append(point)

    # Batch insert into Qdrant
    batch_size = 100
    for i in range(0, len(points), batch_size):
        batch = points[i:i + batch_size]
        qdrant_client.upsert(
            collection_name=qdrant_collection,
            points=batch,
        )
        logger.info(f"✅ Inserted batch {i // batch_size + 1} into Qdrant collection '{qdrant_collection}'.")

    qdrant_client.close()
    return [p.id for p in points]

# ------------------------------------------------------------------------------
# DAG Definition
# ------------------------------------------------------------------------------

default_args = {
    "owner": "airflow",
    "start_date": datetime(2025, 1, 1),
    "retries": 0,
}

with DAG(
    dag_id="abbreviation_dictionary_dag",
    schedule_interval=None,
    default_args=default_args,
    catchup=False,
    tags=["abbreviation", "dictionary"],
) as dag:

    install_deps = install_dependencies()
    test_connection_task = test_qdrant_connection()
    download_task = fetch_abbreviation_file_from_s3()
    process_task = process_abbreviation_file(download_task)

    install_deps >> test_connection_task >> download_task >> process_task
