// //HomeView.tsx
"use client"

import { useEffect } from "react"
import InputIcon from "@/components/assets/icons/input-icon.svg"
import ActionIcon from "@/components/assets/icons/action-icon.svg"
import PrdIcon from '@/components/assets/icons/prd-icon.svg'
import { useChat } from "@/contexts/ChatContext"
import { useAuth } from "@/contexts/AuthContext"
import { HomeViewProps } from "@/types/main_types"


export function HomeView({ 
  inputValue, 
  setInputValue, 
  handleSubmit, 
  onSearch,
  onActionItemsClick 
}: HomeViewProps) {
  const { currentSessionId } = useChat();
  const {user} = useAuth();
  const username = user?.username;

  // Get time-based greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      return "Good Morning";
    } else if (hour >= 12 && hour < 17) {
      return "Good Afternoon";
    } else if (hour >= 17 && hour < 22) {
      return "Good Evening";
    } else {
      return "Hello";
    }
  };

  // If needed, we could access the current session ID here for API calls
  useEffect(() => {
    if (currentSessionId) {
      console.log("Current session ID:", currentSessionId);
    }
  }, [currentSessionId]);

  return (
    <div className="flex flex-col items-center w-full px-4">
      {/* Greeting and Title - Responsive */}
      <div className="flex flex-col items-left gap-2 mt-12 md:mt-24 lg:mt-32 w-full max-w-3xl">
        <h2 className="text-[#494949] text-xl md:text-2xl lg:text-3xl font-semibold text-left">{getGreeting()}, {username}!</h2>
        <div className="flex flex-wrap items-center gap-3 md:gap-3 lg:gap-2">
          <span className="text-[#FF6B1C] text-2xl md:text-3xl lg:text-4xl font-extrabold">Ask</span>
          <span className="text-[#FF6B1C] text-2xl md:text-3xl lg:text-4xl font-extrabold">Complai</span>
          <span className="text-[#494949] text-2xl md:text-3xl lg:text-4xl font-semibold">about any regulations</span>
        </div>
      </div>

      {/* Search Input */}
      <form onSubmit={handleSubmit} className="w-full mb-5 mt-8 md:mt-12">
        <div className="relative w-full max-w-3xl mx-auto">
          <div className="min-h-16 md:min-h-32 w-full bg-white bg-opacity-90 rounded-lg md:rounded-2xl border border-[#D7CBBC] px-3 py-3 flex flex-col">
            <div className="flex justify-between items-start w-full pt-2">
              <textarea
                value={inputValue}
                onChange={(e) => {
                  setInputValue(e.target.value);
                  // Auto resize the textarea
                  e.target.style.height = '56px';
                  e.target.style.height = `${Math.min(200, e.target.scrollHeight)}px`;
                }}
                onKeyDown={(e) => {
                  // Submit on Enter without Shift key
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e as unknown as React.FormEvent);
                  }
                }}
                placeholder="Ask anything about regulations"
                className="w-full bg-transparent outline-none text-[#7B7B7B] text-l md:text-base px-2 md:px-3 resize-none overflow-y-auto min-h-8 max-h-40"
                rows={1}
              />
              <button 
                type="submit" 
                className="w-7 h-7 md:w-8 md:h-8 bg-[#FF6B1C] opacity-70 rounded-full flex items-center justify-center flex-shrink-0 hover:opacity-100 transition-opacity mt-1"
              >
                <img src={InputIcon.src || InputIcon} alt="input" className="w-3 h-3 md:w-4 md:h-4 text-white" />
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Action Cards */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-5 w-full max-w-3xl">
        {/* Generate Action Items Card */}
        <div 
          onClick={onActionItemsClick}
          className="flex-1 bg-[#FFFFFF5C] bg-opacity-36 rounded-lg md:rounded-2xl p-3 md:p-5 flex gap-2 md:gap-3 cursor-pointer hover:bg-[#F5F3F0] transition-colors"
        >
          <div className="w-8 h-8 md:w-10 md:h-10 bg-[#3271EF] rounded-full flex items-center justify-center flex-shrink-0">
            <img src={ActionIcon.src || ActionIcon} alt="Chat" className="w-5 h-5 md:w-6 md:h-6 text-[#7C6F5F]" />
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <div className="font-semibold text-sm md:text-base text-[#494949]">Generate Action Items</div>
            </div>
            <div className="font-semibold text-xs md:text-sm text-[#7B7B7B]">Select a regulation and generate action items</div>
          </div>
        </div>

        {/* Analyse PRD Card */}
        <div className="flex-1 bg-[#FFFFFF5C] bg-opacity-36 rounded-lg md:rounded-2xl p-3 md:p-5 flex gap-2 md:gap-3">
          <div className="w-8 h-8 md:w-10 md:h-10 bg-[#9432EF] rounded-full flex items-center justify-center flex-shrink-0">
            <img src={PrdIcon.src || PrdIcon} alt="Chat" className="w-5 h-5 md:w-6 md:h-6 text-[#7C6F5F]" />
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <div className="font-semibold text-sm md:text-base text-[#494949]">Analyse PRD</div>
              <div className="text-[#FF6B1C] text-[8px] md:text-[9px] font-semibold tracking-wider">COMING SOON</div>
            </div>
            <div className="font-semibold text-xs md:text-sm text-[#7B7B7B]">Upload your PRD to review for regulation compliance</div>
          </div>
        </div>
      </div>
    </div>
  )
}