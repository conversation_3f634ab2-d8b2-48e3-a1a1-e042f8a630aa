import re
import requests
import urllib.parse

def extract_s3_urls(log_file_path):
    """Extracts and properly encodes all s3_url values from the log."""
    s3_urls = []
    with open(log_file_path, "r") as file:
        for line in file:
            match = re.search(r's3_url:\s+(https://[^\s]+)', line)
            if match:
                raw_url = match.group(1)
                # Encode spaces and special characters safely
                parsed = urllib.parse.urlparse(raw_url)
                encoded_path = urllib.parse.quote(parsed.path)
                fixed_url = urllib.parse.urlunparse((
                    parsed.scheme,
                    parsed.netloc,
                    encoded_path,
                    parsed.params,
                    parsed.query,
                    parsed.fragment
                ))
                s3_urls.append(fixed_url)
    return s3_urls

def is_valid_pdf(url):
    """Checks if the URL returns a valid PDF file (content-type or status code check)."""
    try:
        response = requests.head(url, timeout=5)
        if response.status_code != 200:
            return False, f"Status code: {response.status_code}"
        if 'application/pdf' not in response.headers.get("Content-Type", ""):
            return False, f"Invalid Content-Type: {response.headers.get('Content-Type')}"
        return True, "Valid"
    except requests.RequestException as e:
        return False, f"Request error: {e}"

def validate_urls(log_file_path, valid_output="./valid_urls.txt", invalid_output="./invalid_urls.txt"):
    urls = extract_s3_urls(log_file_path)
    total = len(urls)
    invalid_count = 0

    with open(valid_output, "w") as valid_file, open(invalid_output, "w") as invalid_file:
        for url in urls:
            valid, reason = is_valid_pdf(url)
            if valid:
                valid_file.write(f"{url}  # {reason}\n")
            else:
                invalid_count += 1
                invalid_file.write(f"{url}  # {reason}\n")
                print(f"\n❌ {url}\n   ↳ Reason: {reason}")

    print(f"\n✅ Total URLs checked: {total}")
    print(f"❌ Invalid URLs found: {invalid_count}")
    print(f"✔️ Valid URLs written to: {valid_output}")
    print(f"❌ Invalid URLs written to: {invalid_output}")

# Example usage
log_file_path = "./s3_urls.log"
validate_urls(log_file_path)
