"""
dag_rbi_regulations_processing.py
---------------------------------
Refactored Airflow DAG to process RBI regulation PDFs using PyMuPDF and HTML AST.
"""

from datetime import datetime, timedelta
import json
import os
import re
import sys
import uuid
import time
import random
import logging
from typing import List, Dict, Any
from io import BytesIO
from enum import Enum
from pydantic import BaseModel, Field

import requests
import fitz  # PyMuPDF
import html2text
import boto3
from bs4 import BeautifulSoup
from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
from openai import OpenAI
from pymongo import MongoClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode, FastEmbedSparse
from qdrant_client import QdrantClient, models

from utils.date_utils import normalize_date

# Initialize logger
logger = logging.getLogger("airflow.task")

# OpenAI client
openai_api_key = Variable.get("openai_api_key")
client_openai = OpenAI(api_key=openai_api_key)

class Position(BaseModel):
    page_number: int = Field(
        ...,
        description="The page number from which this item was extracted."
    )
    bbox: List[int] = Field(
        ...,
        description="The bounding box [x0, y0, x1, y1] for this item."
    )

class Obligation(BaseModel):
    guideline: str = Field(
        ...,
        description="The guideline as paragraph or sentence from which the obligation was extracted."
    )
    positions: List[Position] = Field(
        ...,
        description="Position of the guideline page and bounding box."
    )
    obligation: str = Field(
        ...,
        description="As Complaiance office, The full text of the extracted or paraphrased obligation from the referenced guideline"
    )
    action_item: str = Field(
        ...,
        description="A concise, actionable item derived from the obligation."
    )

class ObligationList(BaseModel):
    obligations_list: List[Obligation] = Field(
        ...,
        description="A list of all extracted obligations."
    )

def extract_obligations(html_chunks: List[str]) -> List[dict]:
    """
    For each HTML fragment, call OpenAI to pull out
    obligations (para, obligation, action_item) + positions.
    """
    all_obls = []
    for chunk in html_chunks:
        prompt = (
            "Role: You are an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items\n"
            "Your task is to extract all the guidelines and obligations from guidelines, action_items from obligations from the provided RBI regulation HTML fragment.\n\n"
            "Goal: Extract a JSON array where each object contains exactly these fields:\n"
            "  - 'guideline' - identified sentence or paragraph as a instruction to the bank\n"
            "  - 'positions' - poistion of the guildeline\n"
            "  - 'obligation'- rephrased guideline with the context of complaince officer, usually start with 'Bank Shall\n"
            "  - 'action_item' - rephrased obligations as a list of action items to be taken by the bank'\n\n"
            """
            Below are the sample Guidelines from a RBI Regulation, and corresponding generated/rephrased Obligation
                Guideline	: Notes determined as counterfeit shall be stamped as "COUNTERFEIT NOTE"
                Obligation	: Bank shall ensure that the Notes determined as counterfeit shall be stamped as "COUNTERFEIT NOTE" and impounded in the prescribed format (Annex I).

                Guideline	: Such impounded note shall be recorded under authentication, in a separate register.
                Obligation	: Bank shall ensure that the impounded notes shall be recorded under authentication, in a separate register.

                Guideline	: "When a banknote tendered at the counter of a bank branch / back office and currency chest or treasury is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer."
                Obligation	 : Bank shall ensure that when a banknote tendered at the counter of a bank branch is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.

                Guideline	: Detection of Counterfeit Notes - Reporting to Police and other bodies
                Obligation	: "Bank shall ensure to report to Police and other bodies on Detection of Counterfeit Notes.
                                The following procedure shall be followed while reporting incidence of detection of Counterfeit Note to the Police: 
                                1.  For cases of detection of Counterfeit Notes up to four (04) pieces in a single transaction, a consolidated report in the prescribed format (Annex III) shall be sent by the Nodal Bank Officer to the police authorities or the Nodal Police Station, along with the suspect Counterfeit Notes, at the end of the month.  
                                2.  For cases of detection of Counterfeit Notes of five (05) or more pieces in a single transaction, the Counterfeit Notes shall be forwarded immediately by the Nodal Bank Officer to the local police authorities or the Nodal Police Station for investigation by filing FIR in the prescribed format (Annex IV).  
                                3.  A copy of the monthly consolidated report / FIR shall be sent to the Forged Note Vigilance Cell constituted at the Head Office of the bank.  "
                Guideline	: Acknowledgement receipt - Notice to this effect should be displayed prominently at the offices / branches for information of the public
                Obligation	: Bank shall display a notice on availability of the Acknowledgement receipt to customers prominently at the offices / branches for information of the public.
            """

            "Instructions:\n"
            "1. Locate each paragraph/section containing guidelines (keywords: 'shall', 'must', 'required to') which can be percieved as instructions to banks\n"
            "3. Output only a JSON array of objects with those three keys, no extras.\n\n"
            "4. RBI Regulation as HTML contains position information as data-page_number and data-bbox atrributes for each guideline"
            "5. Make sure understand the guideline and generate obligation similar to rephrasing pattern of the samples shared in the same context"
            "6. Use obligation to generate action items for the bank"
            "7. Always make sure to track and generate Position infromation for each Guideline using data-page_number and data-bbox atrributes "
            f"RBI Regulation as HTML:\n{chunk}"
        )
        completion = client_openai.beta.chat.completions.parse(
            model="gpt-4.1-mini-2025-04-14",
            messages=[
                {"role": "system", "content": "Extract structured obligations from RBI HTML docs as an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items"},
                {"role": "user",   "content": prompt},
            ],
            response_format=ObligationList,
        )
        obls = completion.choices[0].message.parsed.obligations_list
        # convert Pydantic models → dicts
        for o in obls:
            all_obls.append(o.dict())
    return all_obls

# ----------------------------------------------------------------------------
# HTML Utility: strip data- attributes and extract positions
# ----------------------------------------------------------------------------

def strip_data_attributes(html: str) -> str:
    """
    Remove all data- attributes (e.g., data-page, data-bbox) from HTML content,
    then convert the cleaned HTML to Markdown.
    """
    # Remove data-* attributes
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all():
        attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
        for attr in attrs_to_remove:
            del tag.attrs[attr]
    cleaned_html = str(soup)

    # Convert HTML to Markdown
    markdown_text = html2text.html2text(cleaned_html)
    return markdown_text


def extract_positions_from_html(html: str) -> List[Dict]:
    """
    Parse the chunk HTML and return a list of positions for each data-page/bbox tag.
    """
    soup = BeautifulSoup(html, "html.parser")
    positions: List[Dict] = []
    for tag in soup.find_all(attrs={"data-page": True}):
        try:
            page_num = int(tag["data-page"])
        except (ValueError, TypeError):
            page_num = None
        bbox = json.loads(tag.get("data-bbox", "[]"))
        positions.append({"page": page_num, "bbox": bbox})
    return positions

# ----------------------------------------------------------------------------
# Rate-Limited Embeddings to Avoid Rate Limits
# ----------------------------------------------------------------------------
class RateLimitedOpenAIEmbeddings(OpenAIEmbeddings):

    def strip_data_attributes(self, html: str) -> str:
        soup = BeautifulSoup(html, "html.parser")
        for tag in soup.find_all():
            attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
            for attr in attrs_to_remove:
                del tag.attrs[attr]
        cleaned_html = str(soup)
        return html2text.html2text(cleaned_html)

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        # Random sleep between 0-2 seconds before each embedding call
        time.sleep(random.uniform(0, 3))
        cleaned_texts = [self.strip_data_attributes(text) for text in texts ]
        logger.info(f"Cleaned {len(cleaned_texts)} chunks.")
        return super().embed_documents(cleaned_texts)

    def embed_query(self, text: str) -> List[float]:
        # Random sleep between 0-2 seconds before querying embedding
        time.sleep(random.uniform(0, 1))
        return super().embed_query(text)

# Embeddings setup with rate limiting
en_embeddings = RateLimitedOpenAIEmbeddings(
    api_key=openai_api_key,
    model="text-embedding-3-large",
    max_retries=5,
    retry_min_seconds=2,
    retry_max_seconds=5
)

embedding_size = len(en_embeddings.embed_query("a"))

# Qdrant host
tvector_host = Variable.get("vector_db_host")

# DocumentDB
documentdb_uri = Variable.get("documentdb_uri")


# ----------------------------------------------------------------------------
# Check if Withdrawn
# ----------------------------------------------------------------------------

def is_diagonal(bbox: fitz.Rect, tol: float = 0.3) -> bool:
    """
    Returns True if the bbox is roughly square (i.e. width ≈ height),
    which is a good heuristic for text rotated ~45° across the page.
    """
    w = bbox.x1 - bbox.x0
    h = bbox.y1 - bbox.y0
    if h == 0:
        return False
    ratio = w / h
    return abs(ratio - 1.0) < tol

def has_diagonal_withdrawn_watermark(
    pdf_path: str,
    size_threshold: float = 20.0,
    ratio_tolerance: float = 0.3
) -> bool:
    """
    Open the PDF at pdf_path and scan every text‐span in rawdict mode.
    Return True only if:
      1) the span's text, when stripped of whitespace, contains "withdrawn"
      2) the span's font size is >= size_threshold
      3) the span's bbox is roughly square (diagonal orientation)
    """
    doc = fitz.open(pdf_path)
    for pno, page in enumerate(doc, start=1):
        raw = page.get_text("rawdict")
        for block in raw["blocks"]:
            if block["type"] != 0:
                continue
            for line in block["lines"]:
                for span in line["spans"]:
                    # reconstruct the span text from per‐char data
                    text = "".join(ch["c"] for ch in span["chars"])
                    norm = text.lower().replace(" ", "").replace("\n", "")
                    if "withdrawn" not in norm:
                        continue

                    size = span["size"]
                    if size < size_threshold:
                        # too small (likely header/footer or normal text)
                        continue

                    if not is_diagonal(fitz.Rect(span["bbox"]), tol=ratio_tolerance):
                        # it's horizontal or near‐horizontal
                        continue

                    # if we made it here, it's a large, diagonal "withdrawn"
                    print(f"→ Found diagonal ‘Withdrawn’ on page {pno} "
                          f"(size={size:.1f}, bbox={span['bbox']})")
                    return True

    return False


# ----------------------------------------------------------------------------
# PDF Parsing and HTML AST Construction
# ----------------------------------------------------------------------------

def parse_pdf_to_html_ast(pdf_path: str) -> BeautifulSoup:
    """
    Parse PDF and build HTML AST (<section>, <h1>-<h6>, <p>, <ul>/<ol>, <li>)
    using font-size, boldness, spacing. Attach data-page/bbox attributes.
    """
    doc = fitz.open(pdf_path)
    # Collect font-size frequencies
    size_counts: Dict[float,int] = {}
    for page in doc:
        blocks = page.get_text("dict")["blocks"]
        for blk in blocks:
            if blk.get("type") != 0:
                continue
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    size = round(span.get("size",0),1)
                    size_counts[size] = size_counts.get(size,0) + 1
    # Determine body text size (most common)
    if not size_counts:
        body_size = 0
    else:
        body_size = max(size_counts, key=size_counts.get)
    # Map larger sizes to heading levels
    heading_sizes = sorted([s for s in size_counts.keys() if s > body_size], reverse=True)
    size_to_level: Dict[float,int] = {}
    for idx,size in enumerate(heading_sizes[:6]):
        size_to_level[size] = idx+1
    max_heading = 6

    soup = BeautifulSoup("", "html.parser")
    root = soup.new_tag("div")
    soup.append(root)

    # Stack: (section_tag, level)
    section_stack: List[tuple] = [(root, 0)]

    for page_num, page in enumerate(doc, start=1):
        for blk in page.get_text("dict")["blocks"]:
            if blk.get("type") != 0:
                continue
            spans = [span for line in blk.get("lines",[]) for span in line.get("spans",[])]
            if not spans:
                continue
            block_max = max(round(span.get("size",0),1) for span in spans)
            lines_text = []
            for line in blk.get("lines",[]):
                for span in line.get("spans",[]):
                    lines_text.append(span.get("text",""))
                lines_text.append("\n")
            text = "".join(lines_text).strip()
            if not text:
                continue

            # Heading detection
            if block_max > body_size:
                level = size_to_level.get(round(block_max,1), max_heading)
                tag = soup.new_tag(f"h{level}")
                tag.string = text
                tag["data-page"] = str(page_num)
                tag["data-bbox"] = json.dumps(blk.get("bbox"))
                # Pop stack until parent level < this level
                while section_stack and section_stack[-1][1] >= level:
                    section_stack.pop()
                parent = section_stack[-1][0]
                sec = soup.new_tag("section")
                sec["data-page"] = str(page_num)
                sec["data-bbox"] = json.dumps(blk.get("bbox"))
                parent.append(sec)
                sec.append(tag)
                section_stack.append((sec, level))
            else:
                parent = section_stack[-1][0]
                flat = text.replace("\n"," ").strip()
                # List detection
                if re.match(r"^(\u2022|\*|-|\d+[\.\)])\s+", flat):
                    if re.match(r"^\d+[\.\)]\s+", flat):
                        lst_name = "ol"
                    else:
                        lst_name = "ul"
                    last = parent.contents[-1] if parent.contents else None
                    if not (hasattr(last,"name") and last.name==lst_name):
                        lst = soup.new_tag(lst_name)
                        lst["data-page"] = str(page_num)
                        lst["data-bbox"] = json.dumps(blk.get("bbox"))
                        parent.append(lst)
                    else:
                        lst = last
                    li = soup.new_tag("li")
                    li.string = flat
                    li["data-page"] = str(page_num)
                    li["data-bbox"] = json.dumps(blk.get("bbox"))
                    lst.append(li)
                else:
                    p = soup.new_tag("p")
                    p.string = flat
                    p["data-page"] = str(page_num)
                    p["data-bbox"] = json.dumps(blk.get("bbox"))
                    parent.append(p)
    return soup


def chunk_html_ast(soup: BeautifulSoup, max_chars: int=3000) -> List[Dict]:
    """
    Split HTML AST into ~max_chars chunks, preserving boundaries.
    Returns list of {content: html_str, positions: [...]}
    """
    chunks: List[Dict] = []
    curr_html = ""
    curr_pos: List[Dict] = []

    def flush():
        nonlocal curr_html, curr_pos
        if curr_html:
            chunks.append({"content": curr_html, "positions": curr_pos.copy()})
            curr_html = ""
            curr_pos.clear()

    # Determine root container for top-level elements
    root = soup.find("div") or soup

    # Iterate direct children of the root
    for elem in root.find_all(recursive=False):
        if not hasattr(elem, 'name'):
            continue
        e_html = str(elem)
        # Collect positions metadata
        pos_list: List[Dict] = []
        for tag in elem.find_all(attrs={"data-page": True}):
            try:
                page_num = int(tag["data-page"])
            except ValueError:
                page_num = None
            bbox = json.loads(tag.get("data-bbox", "[]"))
            pos_list.append({"page": page_num, "bbox": bbox})

        # Flush if adding this element exceeds max_chars
        if curr_html and len(curr_html) + len(e_html) > max_chars:
            flush()
        # Append element HTML and positions
        curr_html += e_html
        curr_pos.extend(pos_list)

    # Flush any remaining content
    flush()
    return chunks


# ----------------------------------------------------------------------------
# In-line summarizer + topic extractor with JSON parse fallback
# ----------------------------------------------------------------------------
def recursive_split(text: str, max_len: int = 50000) -> List[str]:
    if len(text) <= max_len:
        return [text]
    mid = len(text) // 2
    split_point = text.rfind(". ", 0, mid)
    if split_point == -1:
        split_point = mid
    return recursive_split(text[:split_point], max_len) + recursive_split(text[split_point:], max_len)




def summarize_and_extract(chunk: str, max_words: int = 150, max_topics: int = 5) -> Dict[str, Any]:

    class SummaryAndTopics(BaseModel):
        summary: str
        topics: List[str]

    prompt = f"""
You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.

QUERY: Provide a JSON object with two fields:
  - "summary": a concise overview in strictly {max_words} maximum words
  - "topics": up to {max_topics} key topics from the content

CONTEXT:
{chunk}
"""
    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-mini-2025-04-14",
        messages=[
            {"role":"system","content":"You are a regulatory compliance summarizer and topic extractor for RBI guidelines."},
            {"role":"user","content":prompt}
        ],
        response_format=SummaryAndTopics,
    )
    data: SummaryAndTopics = completion.choices[0].message.parsed
    return data.dict()

# ------------------------------------------------------------------------------

# ------------------------------------------------------------------------------
# OpenAI Structured Extraction for RBI Metadata
class DocumentType(str, Enum):
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    CIRCULAR = "circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    TENDER = "tender"
    PUBLICATION = "publication"
    OTHER = "other"

class RBIMetadataExtraction(BaseModel):
    document_number: str
    document_title: str
    document_type: DocumentType
    date_of_issue: str
    addressee: str
    is_applicable_to_banks: bool
    is_exclusive_to_nbfc: bool
    is_exclusive_to_co_operative_banks: bool
    addressee_entities: list[str]
    addressee_person: list[str]
    applicable_departments: list[str]
    is_withdrawn: bool
    keywords: list[str]
    effective_date: str
    supersedes: list[str]
    summary:str
    short_summary:str
    topics:List[str]

def extract_rbi_metadata(document_text: str) -> RBIMetadataExtraction:
    # 3) metadata extraction on first 2000 chars
    slice_ = document_text[:3000]
    metadata_prompt = (
        "As an expert Complaince officer working in a Bank, Analyze the following RBI regulation provided in Document Content as HTML and generate a structured JSON output with the following fields:\n"
        "1. 'document_number' (string) - Similar patterns like RBI/YYYY-YY/NNN, DEPT.DIV.NN/NN.NN.NNN/YYYY-YY or similar reference numbers.\n"
        "2. 'document_title' (string) - The full title of the document.\n"
        "3. 'document_type' (string) - The RBI regulation/document type (value can be master_direction, master_circular, notification, press_release, speech, tender, publication or other).\n"
        "4. 'date_of_issue' (string in DD/MM/YYYY format) - The official issue date.\n"
        "5. 'addressee' (list of strings) - The financial institutions to which the document is addressed.\n"
        "6. 'is_applicable_to_banks' (boolean) - Whether the Regulation is applicable to All Kinds of Banks except NBFC and Co-operative(true/false).\n"
        "6. 'is_exclusive_to_nbfc' (boolean) - Whether the Regulation is exclusive for NBFCs.\n"
        "6. 'is_exclusive_to_co_operative_banks' (boolean) - Whether the Regulation is exclusive for Co-operative banks (true/false).\n"
        "7. 'addressee_entities' (list of strings) - Entities such as Scheduled Banks, NBFCs, etc.\n"
        "8. 'addressee_person' (list of strings) - If addressed to individuals within the institution.\n"
        "9. 'applicable_departments' (array of department codes) - Departments this applies to.\n"
        "10. 'is_withdrawn' (boolean) - If the document is mentioned to be withdrawn.\n"
        "11. 'keywords' (array of strings) - Important keywords for search indexing.\n"
        "12. 'effective_date' (string in DD/MM/YYYY format) - When the circular takes effect.\n"
        "13. 'supersedes' (array of strings) - Any previous circulars this replaces.\n\n"
        "Instructions:\n"
        "- Think like an Experience and Expert Complaince officer "
        "- Look for department codes like DOR, DEPR, DSIM, DBR, DBS, DCM in the circular number, title, authority, or signature.\n"
        "- Identify the type of regulation Master Direction, Master Circular, Circular, Notification, Press Relase or Other\n"
        "- Extract key points, updates, repeals, deadlines, penalties, and technology recommendations.\n"
        "Regulation Content:\n"
        f"{document_text}\n"
    )

    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-mini-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert at extracting structured data from RBI regulatory documents."},
            {"role": "user", "content": metadata_prompt}
        ],
        response_format=RBIMetadataExtraction,
    )
    metadata: RBIMetadataExtraction = completion.choices[0].message.parsed

    # normalize dates
    metadata.date_of_issue  = normalize_date(metadata.date_of_issue)
    metadata.effective_date = normalize_date(metadata.effective_date)

    if metadata.is_exclusive_to_nbfc or metadata.is_exclusive_to_co_operative_banks:
            return metadata

    # inject summaries & topics
    # 1) summary + topics via recursive chunking
    pieces = recursive_split(document_text)
    mini = [summarize_and_extract(strip_data_attributes(piece), max_words=100) for piece in pieces]

    # Combine all miniblock summaries into a raw long summary
    full_summary = "".join([m.get("summary", "") for m in mini if m.get("summary")])
    # Deduplicate topics while preserving order
    seen = set()
    aggregated_topics = []
    for m in mini:
        for t in m.get("topics", []):
            if t not in seen:
                seen.add(t)
                aggregated_topics.append(t)

    # 2) Generate a concise final summary and prune topic list
    # Re-summarize the long summary down to ~100 words and extract top 5 topics
    final_block = summarize_and_extract(full_summary+"Topics : "+"\n".join(aggregated_topics), max_words=170, max_topics=10)
    concise_summary = final_block.get("summary", "")
    final_topics = final_block.get("topics", [])

    metadata.summary = concise_summary
    metadata.topics  = final_topics
    return metadata


# ----------------------------------------------------------------------------
# DocumentDB Storage
# ----------------------------------------------------------------------------

def download_tls_ca_file():
    try:
        resp = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
        resp.raise_for_status()
        with open("/tmp/global-bundle.pem","wb") as f:
            f.write(resp.content)
        logger.info("TLS CA file downloaded.")
    except Exception as e:
        logger.error(f"Failed to download TLS CA: {e}")
        raise


def store_metadata_in_documentdb(metadata:dict):
    if not documentdb_uri.startswith(("mongodb://","mongodb+srv://")):
        raise ValueError("Invalid DocumentDB URI")
    # download_tls_ca_file()
    client = MongoClient(
        documentdb_uri,
        tls=True,
        tlsCAFile="/tmp/global-bundle.pem",
        replicaSet="rs0",
        readPreference="secondaryPreferred",
        retryWrites=False
    )
    try:
        db = client["rbi"]
        coll_name = "rbi_bank_regulations"
        if coll_name not in db.list_collection_names():
            db.create_collection(coll_name)
        coll = db[coll_name]
        unique_id = metadata.get("document_number")
        coll.update_one({"_id": unique_id}, {"$set": metadata}, upsert=True)
        logger.info("Stored metadata in DocumentDB.")
    finally:
        client.close()

def check_if_exists(metadata:dict):
    if not documentdb_uri.startswith(("mongodb://","mongodb+srv://")):
        raise ValueError("Invalid DocumentDB URI")
    client = MongoClient(
        documentdb_uri,
        tls=True,
        tlsCAFile="/tmp/global-bundle.pem",
        replicaSet="rs0",
        readPreference="secondaryPreferred",
        retryWrites=False
    )
    try:
        db = client["rbi"]
        coll_name = "rbi_bank_regulations"
        if coll_name not in db.list_collection_names():
            db.create_collection(coll_name)
        coll = db[coll_name]
        document = coll.findOne({"_id": unique_id})
        client.close()
        return document is not None
    except:
        return False
   

# ----------------------------------------------------------------------------
# Qdrant Ingestion
# ----------------------------------------------------------------------------

def ingest_chunks_to_qdrant(collection:str, chunks_payload:List[Dict], document_id:str,
                            document_summary:str, topics:List[str], document_metadata:dict):
    client = QdrantClient(host=tvector_host, port=6333)
    sparse = FastEmbedSparse(model_name="Qdrant/bm25",model_kwargs={"device":"cpu"})
    client.set_sparse_model("Qdrant/bm25")
    # ensure collection
    if not client.collection_exists(collection):
        sparse_params = client.get_fastembed_sparse_vector_params()
        try:
            client.create_collection(
                collection_name=collection,
                vectors_config={"size":embedding_size,"distance":"Cosine"},
                sparse_vectors_config=sparse_params,
                hnsw_config = models.HnswConfigDiff(m=32, ef_construct=150, full_scan_threshold=10000)
            )
        except:
            pass
    qstore = QdrantVectorStore(
        client=client,
        collection_name=collection,
        embedding=en_embeddings,
        sparse_embedding=sparse,
        retrieval_mode=RetrievalMode.HYBRID,
        sparse_vector_name="fast-sparse-bm25"
    )
    texts, metas = [], []
    for idx,payload in enumerate(chunks_payload):
        # html = strip_data_attributes(payload["content"])
        html = payload["content"]
        chunk_text = (
            f"Document Summary:\n{document_summary}\n\n"
            f"Topics:\n{', '.join(topics)}\n\n"
            f"Content:\n{html}"
        )
        texts.append(chunk_text)
        meta = {
            "document_id": document_id,
            "chunk_index": idx,
            "positions": payload["positions"],
            **document_metadata
        }
        metas.append(meta)
    qstore.add_texts(texts, metas)
    logger.info(f"Ingested {len(texts)} chunks of '{document_id}' into Qdrant.")

# ----------------------------------------------------------------------------
# Airflow DAG Definition
# ----------------------------------------------------------------------------

def _fetch_files_from_s3() -> List[str]:
    bucket = Variable.get("s3_bucket_name")
    client = boto3.client(
        "s3",
        aws_access_key_id=Variable.get("aws_access_key_id"),
        aws_secret_access_key=Variable.get("aws_secret_access_key")
    )
    paginator = client.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket)
    keys = []
    for page in pages:
        for obj in page.get("Contents",[]):
            k = obj.get("Key")
            if k.startswith("Circulars/") and k.lower().endswith(".pdf"):
                keys.append(k)
    logger.info(f"Found {len(keys)} files in S3.")
    return keys

with DAG(
    "regulations_processor_2",
    default_args={"owner":"airflow","start_date":datetime(2025,1,1),"retries":0},
    schedule_interval="@daily",
    catchup=False,
    max_active_runs=8,
    # concurrency=8,
    description="Process RBI regs via PDF->HTML->chunks->Qdrant",
) as dag:

    @task
    def process_file(file_key:str) -> Dict:

        local = f"/tmp/{os.path.basename(file_key)}"
        if os.path.exists(local):
            print(f"The file '{local}' exists.")
            return {local: "exists"}

        s3 = boto3.client(
            "s3",
            aws_access_key_id=Variable.get("aws_access_key_id"),
            aws_secret_access_key=Variable.get("aws_secret_access_key")
        )
        
        s3.download_file(Variable.get("s3_bucket_name"), file_key, local)
        logger.info(f"Downloaded {file_key} -> {local}")

        # Parse PDF -> HTML AST
        soup = parse_pdf_to_html_ast(local)
        full_html = str(soup)

        # Metadata extraction
        text_plain = soup.get_text(separator="\n")
        metadata = extract_rbi_metadata(full_html)
        metadata.is_withdrawn = has_diagonal_withdrawn_watermark(local_path)
        m_dict = metadata.dict()
        m_dict.update({
            "s3_url": f"https://{Variable.get('s3_bucket_name')}.s3.amazonaws.com/{file_key}"
        })

        if check_if_exists(m_dict):
            print("Processed already")
            return {"metadata": m_dict}

        if metadata.is_exclusive_to_nbfc or metadata.is_exclusive_to_co_operative_banks:
            print(f"{file_key} is not applicable to Banks")
            return

        if metadata.document_type == DocumentType.MASTER_DIRECTION :
            # ── NEW: extract obligations from the raw HTML chunks ───────────────────
            #   - use a larger chunk size so we don't split obligations in half
            raw_chunks = [c["content"] for c in chunk_html_ast(soup, max_chars=30000)]
            #   - strip any lingering data-* tags so the model sees clean HTML
            clean_chunks = [strip_data_attributes(c) for c in raw_chunks]
            obligations = extract_obligations(clean_chunks)
            m_dict["obligations"] = obligations

        store_metadata_in_documentdb(m_dict)

        if metadata.document_type == DocumentType.MASTER_DIRECTION :
            del m_dict["obligations"]

        if not metadata.is_applicable_to_banks and not metadata.is_withdrawn:
            print(f"{file_key} is not applicable to Banks")
            return

         # Chunk HTML AST
        chunks = chunk_html_ast(soup, max_chars=8000)

        # 1) Merge too-small initial chunks (<500 chars) into combined_chunks, with 100-char overlap
        combined_chunks: List[Dict] = []
        overlap_chars = 800
        buffer_content = ""
        # We'll recalc positions after merging
        for ch in chunks:
            ch_content = ch["content"]
            if len(buffer_content) + len(ch_content) <= 8000:
                buffer_content += ch_content
            else:
                if buffer_content:
                    # recalc positions for merged piece
                    combined_chunks.append({
                        "content": buffer_content,
                        "positions": extract_positions_from_html(buffer_content)
                    })
                overlap_text = buffer_content[-overlap_chars:] if len(buffer_content) > overlap_chars else buffer_content
                buffer_content = overlap_text + ch_content
        if buffer_content:
            combined_chunks.append({
                "content": buffer_content,
                "positions": extract_positions_from_html(buffer_content)
            })

        # 2) Subdivide only the large combined_chunks (>1000 chars) into overlapping smaller chunks
        small_chunks: List[Dict] = []
        for ch in combined_chunks:
            content = ch["content"]
            if len(content) > 12000:
                words = content.split()
                overlap = 600
                max_words = 6000  # desired words per sub-chunk
                start = 0
                while start < len(words):
                    segment = words[start:start+max_words]
                    seg_text = " ".join(segment)
                    # recalc positions for this sub-chunk
                    small_chunks.append({
                        "content": seg_text,
                        "positions": extract_positions_from_html(seg_text)
                    })
                    if start + max_words >= len(words):
                        break
                    start += max_words - overlap
            else:
                small_chunks.append({
                    "content": content,
                    "positions": extract_positions_from_html(content)
                })

        logger.info(f"Created {len(chunks)} original chunks.")
        logger.info(f"Created {len(small_chunks)} small chunks.")
        logger.info(f"Position is missing in {len([position_missing_chunk for position_missing_chunk in (combined_chunks+small_chunks) if position_missing_chunk['positions'] == []])} chunks")
        
        doc_id = metadata.document_number or metadata.document_title or file_key
        # ingest_chunks_to_qdrant(f"rbi_bank_{metadata.document_type.value}", combined_chunks, doc_id, metadata.summary, metadata.topics, m_dict)
        ingest_chunks_to_qdrant(f"rbi_bank_{metadata.document_type.value}", small_chunks, doc_id, metadata.short_summary, metadata.topics, m_dict)

        return {"metadata": metadata.dict(), "chunks": len(chunks)}

    
    files = _fetch_files_from_s3()[5000:]
    logger.info(f"Files {len(files)}.")
    # define your batch size
    batch_size = 1024

    # build list of batches
    batches = [
        files[i : i + batch_size]
        for i in range(0, len(files), batch_size)
    ]

    for batch in batches:
        results = process_file.expand(file_key=batch)
