#!/usr/bin/env python3
"""
Test notification processing with enhanced link scraping and LLM integration
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/selkea')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_notification_with_enhanced_scraping():
    """Test notification processing with enhanced scraping and LLM"""
    
    print("🧪 Testing Notification Processing with Enhanced Link Scraping")
    print("=" * 80)
    
    # Import required modules
    from complai_knowledge_tracker.airflow.dags.utils.openai_manager import OpenAIManager
    from prompts.notification_categorizer import (
        AffectedDocumentsResult,
        AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
    )
    
    # Initialize OpenAI manager
    openai_manager = OpenAIManager()
    client = openai_manager.get_client()
    
    # Test with a sample notification that contains real RBI patterns
    sample_notification = {
        "title": "Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025 - Amendment to FEMA 23(R)/2015-RB",
        "rss_description": """
        <p>Notification No. FEMA 23(R)/(6)/2025-RB dated 24 June 2025</p>
        <p>Amendment to Foreign Exchange Management (Remittance of Assets) Regulations, 2015</p>
        <p>In exercise of the powers conferred by clause (a) of sub-section (3) of section 46 of the Foreign Exchange Management Act, 1999 (42 of 1999), the Reserve Bank of India hereby makes the following amendment to the Foreign Exchange Management (Remittance of Assets) Regulations, 2015 (Notification No. FEMA 23(R)/2015-RB dated 1 May 2015):</p>
        <p>In Regulation 4 of the principal regulations, the following sub-regulation shall be inserted after sub-regulation (3):</p>
        <p>"(4) A person resident in India may remit up to USD 250,000 per financial year for investment in overseas securities."</p>
        <p><a href='https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF'>Download PDF</a></p>
        <p><a href='https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12567&Mode=0'>View Notification</a></p>
        """,
        "link": "https://www.rbi.org.in/Scripts/NotificationUser.aspx?Id=12567&Mode=0"
    }
    
    print(f"📋 Testing notification: {sample_notification['title'][:60]}...")
    
    # Import the enhanced functions (standalone versions)
    import requests
    from bs4 import BeautifulSoup
    import re
    
    def _process_relative_url(href):
        """Process relative URLs to absolute URLs"""
        if href.startswith('http'):
            return href
        if not href.startswith('http'):
            if 'rdocs' in href or '.pdf' in href.lower():
                return f"https://rbidocs.rbi.org.in/{href}"
            else:
                return f"https://www.rbi.org.in/{href}"
        return href
    
    def extract_anchor_information(html_content):
        """Extract comprehensive anchor information"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            anchor_info = {
                'anchors': [],
                'reference_numbers': [],
                'rbi_links': [],
                'document_links': [],
                'pdf_links': [],
                'rbi_page_urls': [],
                'navigation_links': []
            }
            
            # Extract all anchor tags
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                text = link.get_text(strip=True)
                
                if not href or not text:
                    continue
                
                processed_href = _process_relative_url(href)
                
                anchor_data = {
                    'text': text,
                    'href': processed_href,
                    'original_href': href
                }
                anchor_info['anchors'].append(anchor_data)
                
                # Categorize links
                if processed_href.lower().endswith('.pdf'):
                    anchor_info['pdf_links'].append(processed_href)
                    anchor_info['document_links'].append(processed_href)
                    logger.info(f"   📄 Found direct PDF link: {processed_href}")
                
                elif 'rbi.org.in' in processed_href:
                    if 'NotificationUser.aspx' in processed_href or 'Scripts/' in processed_href:
                        anchor_info['rbi_page_urls'].append(processed_href)
                        logger.info(f"   📋 Found RBI page URL: {processed_href}")
                    else:
                        anchor_info['rbi_links'].append(processed_href)
            
            # Extract reference numbers from text
            text_content = soup.get_text()
            ref_patterns = [
                r'FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
                r'FEMA\s+\d+\([A-Z]\)/\d{4}-[A-Z]+',  # FEMA 23(R)/2015-RB
                r'RBI/\d{4}-\d{2}/\d+',  # RBI/2025-26/64
                r'[A-Z]+\.No\.[A-Za-z\.]+\d+/[\d\.]+/\d{4}-?\d*'  # DBOD.No.Leg.BC.21/09.07.007/2002-03
            ]
            
            for pattern in ref_patterns:
                matches = re.findall(pattern, text_content)
                for match in matches:
                    clean_match = re.sub(r'\s+', ' ', match.strip())
                    if clean_match not in anchor_info['reference_numbers']:
                        anchor_info['reference_numbers'].append(clean_match)
            
            return anchor_info
            
        except Exception as e:
            logger.error(f"Error extracting anchor information: {e}")
            return {
                'anchors': [], 'reference_numbers': [], 'rbi_links': [],
                'document_links': [], 'pdf_links': [], 'rbi_page_urls': [], 'navigation_links': []
            }
    
    # Step 1: Extract anchor information
    print("\n🔍 Step 1: Extracting anchor information")
    print("-" * 60)
    
    anchor_info = extract_anchor_information(sample_notification['rss_description'])
    
    print(f"✅ Extracted anchor info:")
    print(f"   📄 PDF links: {len(anchor_info['pdf_links'])}")
    print(f"   🔗 RBI page URLs: {len(anchor_info['rbi_page_urls'])}")
    print(f"   📋 Reference numbers: {len(anchor_info['reference_numbers'])}")
    
    if anchor_info['pdf_links']:
        print(f"   📄 PDF links found:")
        for url in anchor_info['pdf_links']:
            print(f"      • {url}")
    
    if anchor_info['reference_numbers']:
        print(f"   📋 Reference numbers found:")
        for ref in anchor_info['reference_numbers']:
            print(f"      • {ref}")
    
    # Step 2: Create enhanced content for LLM
    print(f"\n🔍 Step 2: Creating enhanced content for LLM")
    print("-" * 60)
    
    enhanced_content = sample_notification['rss_description'][:2000]
    enhanced_content += f"\n\n=== EXTRACTED INFORMATION FOR PROCESSING ==="
    
    if anchor_info['reference_numbers']:
        enhanced_content += f"\nEXTRACTED_REFERENCE_NUMBERS: {', '.join(anchor_info['reference_numbers'])}"
    
    if anchor_info['pdf_links']:
        enhanced_content += f"\nEXTRACTED_PDF_LINKS: {', '.join(anchor_info['pdf_links'][:3])}"
        enhanced_content += f"\n*** USE THESE EXACT URLs FOR new_document_url - DO NOT MODIFY ***"
    else:
        enhanced_content += f"\nEXTRACTED_PDF_LINKS: (none found)"
        enhanced_content += f"\n*** NO PDF LINKS AVAILABLE - LEAVE new_document_url EMPTY ***"
    
    enhanced_content += f"\n=== END EXTRACTED INFORMATION ==="
    
    print(f"✅ Enhanced content created ({len(enhanced_content)} characters)")
    
    # Step 3: Test LLM call with enhanced content
    print(f"\n🔍 Step 3: Testing LLM call with enhanced content")
    print("-" * 60)
    
    try:
        prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
            title=sample_notification['title'],
            content=enhanced_content,
            category="Amendment"
        )
        
        print(f"📝 Prompt created ({len(prompt)} characters)")
        print(f"🤖 Making LLM call...")
        
        response = client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert in RBI regulatory document analysis and knowledge base management."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            response_format=AffectedDocumentsResult,
            temperature=0.1
        )
        
        result = response.choices[0].message.parsed.dict()
        
        print(f"✅ LLM call successful!")
        print(f"   📊 Document actions: {len(result.get('document_actions', []))}")
        print(f"   🔗 Has new document link: {result.get('has_new_document_link', False)}")
        print(f"   📄 New document URL: {result.get('new_document_url', 'None')}")
        
        # Display document actions
        if result.get('document_actions'):
            print(f"   📋 Document actions:")
            for i, action in enumerate(result['document_actions'], 1):
                print(f"      {i}. {action.get('action_type')} - {action.get('document_id')}")
                print(f"         Reference: {action.get('reference_number', 'N/A')}")
                print(f"         Confidence: {action.get('confidence', 'N/A')}")
        
        # Check if the LLM used the correct PDF URL
        expected_pdf_url = "https://rbidocs.rbi.org.in/rdocs/notification/PDFs/FEMA23R04072025.PDF"
        actual_pdf_url = result.get('new_document_url', '')
        
        if actual_pdf_url == expected_pdf_url:
            print(f"   ✅ LLM correctly used the extracted PDF URL!")
        elif actual_pdf_url:
            print(f"   ⚠️ LLM used different URL: {actual_pdf_url}")
            print(f"   📌 Expected: {expected_pdf_url}")
        else:
            print(f"   ❌ LLM did not provide a PDF URL")
        
        # Save results
        test_results = {
            "test_timestamp": datetime.now().isoformat(),
            "sample_notification": sample_notification,
            "anchor_info": anchor_info,
            "enhanced_content_length": len(enhanced_content),
            "llm_result": result,
            "pdf_url_check": {
                "expected": expected_pdf_url,
                "actual": actual_pdf_url,
                "correct": actual_pdf_url == expected_pdf_url
            }
        }
        
        with open('notification_enhanced_scraping_test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Test Results Summary:")
        print(f"   ✅ Anchor extraction: {len(anchor_info['pdf_links'])} PDFs, {len(anchor_info['reference_numbers'])} references")
        print(f"   ✅ LLM processing: {len(result.get('document_actions', []))} actions identified")
        print(f"   ✅ PDF URL accuracy: {'✅ Correct' if test_results['pdf_url_check']['correct'] else '❌ Incorrect'}")
        print(f"   💾 Results saved to: notification_enhanced_scraping_test_results.json")
        
        return test_results
        
    except Exception as e:
        print(f"❌ LLM call failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    try:
        results = test_notification_with_enhanced_scraping()
        if results:
            print(f"\n🎉 Enhanced notification processing test completed successfully!")
        else:
            print(f"\n❌ Test failed")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
