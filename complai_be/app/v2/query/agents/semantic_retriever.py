from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from enum import Enum
import logging
import torch
from typing import Dict, List, Any, Optional
# from config import settings
# Azure-specific LangChain imports
from langchain_openai.chat_models.azure import AzureChatOpenAI
from langchain_openai.embeddings.azure import AzureOpenAIEmbeddings

from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain.retrievers.self_query.base import SelfQueryRetriever
from langchain.chains.query_constructor.schema import AttributeInfo
from langchain.schema import Document

from langchain_qdrant import QdrantVectorStore, FastEmbedSparse, RetrievalMode

from app.v2.query.utils.completion_client import completion_client

# Configure HTTP backend for Hugging Face to bypass SSL issues
try:
    import requests
    from huggingface_hub import configure_http_backend

    def create_session_factory():
        def session_factory():
            session = requests.Session()
            session.verify = False  # Bypass SSL verification
            return session
        return session_factory

    # Configure the HTTP backend
    configure_http_backend(backend_factory=create_session_factory())
    logging.getLogger(__name__).info("Configured Hugging Face HTTP backend to bypass SSL verification")
except ImportError:
    logging.getLogger(__name__).warning("Could not configure Hugging Face HTTP backend - SSL issues may occur")

from qdrant_client import QdrantClient, models
from transformers import AutoTokenizer, AutoModelForMaskedLM

logger = logging.getLogger(__name__)


class DocumentType(str, Enum):
    MASTER_DIRECTION = "master_direction"
    MASTER_CIRCULAR = "master_circular"
    CIRCULAR = "circular"
    NOTIFICATION = "notification"
    PRESS_RELEASE = "press_release"
    SPEECHES = "speech"
    PUBLICATION = "publication"
    OTHER = "other"


class SpladeEncoder:
    """SPLADE sparse vector encoder for enhanced semantic search"""

    def __init__(self, model_name: str = "naver/efficient-splade-VI-BT-large-doc"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self._load_model()

    def _load_model(self):
        """Lazy load the SPLADE model"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForMaskedLM.from_pretrained(self.model_name)
            logger.info(f"Loaded SPLADE model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to load SPLADE model: {e}")
            raise

    def encode(self, text: str) -> models.SparseVector:
        """Generate SPLADE sparse vector for text"""
        if not self.model or not self.tokenizer:
            self._load_model()

        with torch.no_grad():
            tokens = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                max_length=512
            )
            logits = self.model(**tokens).logits[0]
            max_vals, _ = torch.max(torch.relu(logits), dim=0)
            nonzero = max_vals > 0
            indices = nonzero.nonzero(as_tuple=True)[0]
            values = max_vals[indices]

            return models.SparseVector(
                indices=indices.cpu().tolist(),
                values=values.cpu().tolist()
            )


class SemanticRetriever:
    def __init__(
        self,
        client: QdrantClient,
        collection: str,
        embed_model: str,   # this should match your Azure embedding deployment name
        chat_model: str     # this should match your Azure chat deployment name
    ):
        self.client = client
        self.collection = collection

        # Use AzureChatOpenAI instead of ChatOpenAI
        self.llm = completion_client.chat_client

        # Try to initialize Azure OpenAI embeddings with proper configuration
        from config import settings
        self.embeddings = None
        self.embeddings_available = False

        try:
            self.embeddings = AzureOpenAIEmbeddings(
                model="text-embedding-3-large",
                azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
                api_key=settings.AZURE_OPENAI_API_KEY,
                api_version=settings.AZURE_OPENAI_API_VERSION,
                azure_deployment=settings.OPENAI_EMBEDDING_MODEL
            )
            # Test the embeddings with a simple query
            test_embedding = self.embeddings.embed_query("test")
            self.embeddings_available = True
            logger.info("Azure OpenAI embeddings initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Azure OpenAI embeddings: {e}")
            logger.info("Continuing without dense embeddings - only sparse search will be available")
            self.embeddings = None
            self.embeddings_available = False
        self.sparse_embeddings = FastEmbedSparse(model_name="Qdrant/bm25")

        # Initialize SPLADE encoder for enhanced sparse search
        try:
            self.splade_encoder = SpladeEncoder()
            self.enable_splade_search = True
            self.splade_vector_name = "fast-sparse-bm25-splade"
            logger.info("SPLADE encoder initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize SPLADE encoder: {e}")
            self.splade_encoder = None
            self.enable_splade_search = False

        # Build the list of collection names
        self.top_k = {
            "master_direction": 10,
            "master_circular": 8,
            "circular": 8,
            "notification": 6,
            "press_release": 6,
            "speech": 2,
            "publication": 2,
            "other": 1,
        }

        self.score_thresholds = {
            "master_direction": 0.1,  # Lower threshold for testing
            "master_circular": 0.1,
            "circular": 0.1,
            "notification": 0.1,
            "press_release": 0.1,
            "speech": 0.1,
            "publication": 0.1,
            "other": 0.1,
        }

        # Check which collections actually exist in Qdrant and their configurations
        try:
            existing_collections = {c.name for c in self.client.get_collections().collections}
            logger.info(f"Found existing Qdrant collections: {existing_collections}")

            # Check which collections have SPLADE vectors
            self.collections_with_splade = set()
            for collection_name in existing_collections:
                try:
                    collection_info = self.client.get_collection(collection_name)
                    sparse_vectors = collection_info.config.params.sparse_vectors or {}
                    if "fast-sparse-bm25-splade" in sparse_vectors:
                        self.collections_with_splade.add(collection_name)
                        logger.info(f"Collection {collection_name} has SPLADE vectors")
                except Exception as e:
                    logger.warning(f"Could not check SPLADE vectors for {collection_name}: {e}")

        except Exception as e:
            logger.warning(f"Could not retrieve collection list: {e}")
            existing_collections = set()
            self.collections_with_splade = set()

        self.vector_stores = {}
        for document_type in DocumentType:
            collection_name = f"rbi_{document_type.value}"

            # Only initialize vector stores for collections that exist
            if collection_name not in existing_collections:
                logger.warning(f"Skipping collection {collection_name} - does not exist in Qdrant")
                continue

            try:
                logger.info(f"Initializing vector store for collection: {collection_name}")

                # Determine retrieval mode based on available embeddings
                if self.embeddings_available:
                    retrieval_mode = RetrievalMode.HYBRID
                    embedding = self.embeddings
                else:
                    retrieval_mode = RetrievalMode.SPARSE
                    embedding = None
                    logger.info(f"Using sparse-only mode for {collection_name} (no dense embeddings available)")

                # Try with named vectors first, fallback to unnamed if needed
                vector_store_kwargs = {
                    "client": self.client,
                    "collection_name": collection_name,
                    "sparse_embedding": self.sparse_embeddings,
                    "retrieval_mode": retrieval_mode,
                    "sparse_vector_name": "fast-sparse-bm25",
                }

                # Only add embedding and vector_name if embeddings are available
                if self.embeddings_available:
                    vector_store_kwargs["embedding"] = embedding
                    vector_store_kwargs["vector_name"] = "dense"

                try:
                    self.vector_stores[document_type.value] = QdrantVectorStore(**vector_store_kwargs)
                except Exception as vector_error:
                    # If named vectors fail, try with unnamed vectors (legacy collections)
                    logger.warning(f"Named vectors failed for {collection_name}, trying unnamed vectors: {vector_error}")
                    vector_store_kwargs["vector_name"] = ""  # Empty string for unnamed vectors
                    self.vector_stores[document_type.value] = QdrantVectorStore(**vector_store_kwargs)
                logger.info(f"Successfully initialized vector store for {collection_name}")
            except Exception as e:
                logger.warning(f"Error initializing collection {document_type.value}: {e}")

    def get_self_query_retriever(self, vector_store) -> MultiQueryRetriever:
        metadata_fields = [
            AttributeInfo(name="document_id",
                          description="Document identifier", type="string"),
            AttributeInfo(name="source",
                          description="Source file or URL",   type="string"),
        ]
        return MultiQueryRetriever.from_llm(
            llm=self.llm,
            vectorstore=vector_store,
            document_content_description="RBI Regulations",
            metadata_field_info=metadata_fields
        )

    def run(self, query: str, use_metadata_filter: bool = False) -> tuple[list[dict], dict]:
        seen = set()
        unique_retrieved = {}
        position_info = {}

        # Run multiple search strategies in parallel
        search_futures = []

        with ThreadPoolExecutor(max_workers=4) as executor:
            # 1. Hybrid search (dense + BM25 sparse) or sparse-only search
            if self.embeddings_available:
                search_futures.append(
                    executor.submit(self._run_hybrid_search, query)
                )
            else:
                # Use pure sparse search when no embeddings available
                search_futures.append(
                    executor.submit(self._run_sparse_search, query)
                )

            # 2. SPLADE sparse search (if available and collections support it)
            if self.splade_encoder and self.collections_with_splade:
                search_futures.append(
                    executor.submit(self._run_splade_search, query)
                )

            # 3. Multi-query retrieval (only if embeddings available)
            if self.embeddings_available:
                search_futures.append(
                    executor.submit(self._run_multi_query_search, query)
                )

            # Collect all results
            docs = []
            for future in as_completed(search_futures):
                try:
                    docs.extend(future.result())
                except Exception as e:
                    logger.warning(f"Error in search strategy: {e}")

        # Deduplicate and process results
        for doc in docs:
            metadata = doc.metadata
            doc_id = metadata.get("document_id")
            if not doc_id or doc_id in seen:
                continue
            seen.add(doc_id)
            position_info[doc_id] = metadata.pop("positions", [])
            unique_retrieved[doc_id] = {
                "page_content": doc.page_content,
                "metadata": metadata,
            }

        logger.info(f"Retrieved {len(unique_retrieved)} unique documents from {len(docs)} total results")
        return list(unique_retrieved.values()), position_info

    def _run_hybrid_search(self, query: str) -> List[Any]:
        """Run hybrid dense + BM25 sparse search (or sparse-only if no embeddings)"""
        docs = []

        # Skip hybrid search if no embeddings are available
        if not self.embeddings_available:
            logger.debug("Skipping hybrid search - no dense embeddings available")
            return docs

        for dt, vs in self.vector_stores.items():
            try:
                # Use default values if document type not in configuration
                k = self.top_k.get(dt, 5)
                score_threshold = self.score_thresholds.get(dt, 0.6)

                retriever = vs.as_retriever(
                    search_kwargs={
                        "k": int(k),
                        "score_threshold": score_threshold
                    }
                )
                results = retriever.invoke(query)
                docs.extend(results)
                logger.debug(f"Hybrid search for {dt}: {len(results)} results")
            except Exception as e:
                logger.warning(f"Error in hybrid search for {dt}: {e}")

        return docs

    def _run_sparse_search(self, query: str) -> List[Any]:
        """Run pure BM25 sparse search using direct Qdrant client"""
        docs = []

        try:
            # Generate sparse vector using FastEmbedSparse
            sparse_vector = self.sparse_embeddings.embed_query(query)

            # Search each collection with sparse vectors
            for dt in self.vector_stores.keys():
                try:
                    collection_name = f"rbi_{dt}"
                    k = self.top_k.get(dt, 5)
                    score_threshold = self.score_thresholds.get(dt, 0.6)

                    # Use direct client search with sparse vectors
                    # Convert the sparse vector to the correct format
                    qdrant_sparse_vector = models.SparseVector(
                        indices=sparse_vector.indices,
                        values=sparse_vector.values
                    )

                    search_results = self.client.search(
                        collection_name=collection_name,
                        query_vector=models.NamedSparseVector(
                            name="fast-sparse-bm25",
                            vector=qdrant_sparse_vector
                        ),
                        limit=int(k),
                        score_threshold=score_threshold
                    )

                    # Convert Qdrant results to LangChain Document format
                    for result in search_results:
                        doc = Document(
                            page_content=result.payload.get("page_content", ""),
                            metadata=result.payload
                        )
                        docs.append(doc)

                    logger.debug(f"Sparse search for {dt}: {len(search_results)} results")

                except Exception as e:
                    logger.warning(f"Error in sparse search for {dt}: {e}")

        except Exception as e:
            logger.warning(f"Error generating sparse vector: {e}")

        return docs

    def _run_splade_search(self, query: str) -> List[Any]:
        """Run SPLADE sparse vector search"""
        docs = []

        if not self.splade_encoder:
            return docs

        try:
            # Generate SPLADE vector for query
            splade_vector = self.splade_encoder.encode(query)

            # Search only collections that have SPLADE vectors
            for dt in self.vector_stores.keys():
                collection_name = f"rbi_{dt}"

                # Skip collections that don't have SPLADE vectors
                if collection_name not in self.collections_with_splade:
                    logger.debug(f"Skipping SPLADE search for {collection_name} - no SPLADE vectors")
                    continue

                try:
                    # Use default values if document type not in configuration
                    k = self.top_k.get(dt, 5)
                    score_threshold = self.score_thresholds.get(dt, 0.6)

                    # Use direct client search with SPLADE vectors
                    search_results = self.client.search(
                        collection_name=collection_name,
                        query_vector=models.NamedSparseVector(
                            name=self.splade_vector_name,
                            vector=splade_vector
                        ),
                        limit=int(k),
                        score_threshold=score_threshold
                    )

                    # Convert Qdrant results to LangChain Document format
                    for result in search_results:
                        doc = Document(
                            page_content=result.payload.get("page_content", ""),
                            metadata=result.payload
                        )
                        docs.append(doc)

                    logger.debug(f"SPLADE search for {dt}: {len(search_results)} results")

                except Exception as e:
                    logger.warning(f"Error in SPLADE search for {dt}: {e}")

        except Exception as e:
            logger.warning(f"Error generating SPLADE vector: {e}")

        return docs

    def _run_multi_query_search(self, query: str) -> List[Any]:
        """Run multi-query retrieval"""
        docs = []

        try:
            base_retrievers = []
            for dt, vs in self.vector_stores.items():
                # Use default values if document type not in configuration
                k = self.top_k.get(dt, 5)
                score_threshold = self.score_thresholds.get(dt, 0.6)

                retriever = vs.as_retriever(
                    search_kwargs={
                        "k": int(k),
                        "score_threshold": score_threshold
                    }
                )
                base_retrievers.append(retriever)

            multi_query_retrievers = [
                MultiQueryRetriever.from_llm(retriever=br, llm=self.llm)
                for br in base_retrievers
            ]

            def _run_retriever(mqr, q):
                result = mqr.invoke(q)
                logger.debug(f"Multi-query result: {len(result)} docs")
                return result

            with ThreadPoolExecutor(max_workers=len(multi_query_retrievers) or 1) as executor:
                futures = {
                    executor.submit(_run_retriever, mqr, query): mqr
                    for mqr in multi_query_retrievers
                }
                for fut in as_completed(futures):
                    try:
                        docs.extend(fut.result())
                    except Exception as e:
                        logger.warning(f"Error in multi-query retrieval: {e}")

        except Exception as e:
            logger.warning(f"Error in multi-query search: {e}")

        return docs

    def enable_splade(self, enabled: bool = True):
        """Enable or disable SPLADE search"""
        self.enable_splade_search = enabled and self.splade_encoder is not None
        logger.info(f"SPLADE search {'enabled' if self.enable_splade_search else 'disabled'}")

    def get_search_stats(self) -> Dict[str, Any]:
        """Get statistics about available search methods"""
        return {
            "hybrid_search": True,
            "splade_search": self.splade_encoder is not None,
            "splade_enabled": self.enable_splade_search,
            "multi_query_search": True,
            "collections": list(self.vector_stores.keys()),
            "splade_vector_name": getattr(self, 'splade_vector_name', 'fast-sparse-bm25-splade')
        }

    def test_splade_encoding(self, text: str) -> Optional[models.SparseVector]:
        """Test SPLADE encoding for debugging"""
        if not self.splade_encoder:
            return None

        try:
            return self.splade_encoder.encode(text)
        except Exception as e:
            logger.error(f"SPLADE encoding test failed: {e}")
            return None
