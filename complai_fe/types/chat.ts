// types/chat.ts
import { ReferenceItem } from "./types";
// API response type from the server
export interface SessionData {
    id: string;
    title: string;
    created_at: string;
  }
  
  // Grouped chat data for UI representation
  export interface GroupedChat {
    id: string;
    title: string;
    date: "Today" | "Yesterday" | "Last Week" | "Older";
  }
  
  // Props for ChatHistory component
  export interface ChatHistoryProps {
    isCollapsed: boolean;
    onToggleCollapse: (collapsed: boolean) => void;
  }

  export interface UserProfile {
    username: string;
    email: string;
  }
  // types/chat.ts
export interface GroupedChat {
  id: string;
  title: string;
  date: "Today" | "Yesterday" | "Last Week" | "Older";
  created_at: Date;
}



export interface ChatMessage {
  id: string;
  content: string;
  sender: "user" | "assistant" | "loading";
  timestamp: Date;
  metadata?: any;
  loadingState?: string;
  fadeState?: string;
  regulation?: RegulationInfo | null;
}

export interface RegulationInfo {
  id: string;
  title: string;
  content: string;
  date: string;
  department: string;
  source: string;
}

export interface ResourcesSidebarProps {
  showDetailView: boolean;
  setShowDetailView: (showDetailView:boolean) => void;
  selectedDocCode: string | null;
  currentRegulation: any;
  onClose: () => void;
  references: ReferenceItem[];
  onCollapseChat?: () => void;
  selectedSpecificRef?: ReferenceItem | null; // Add this line
}

export interface Notification {
  _id: string;
  title: string;
  date_iso: string;
  category?: string; // This will be calculated based on date
  s3_url: string;
  circular_numbers?: string[];
  press_release_number?: string | null;
  addressee?: string | null;
}