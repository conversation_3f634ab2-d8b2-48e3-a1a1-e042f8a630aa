"""
Configuration module for Airflow DAGs
Centralizes all configuration, API keys, and environment variables
"""

import os
import itertools
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from airflow.models import Variable
import logging

logger = logging.getLogger(__name__)

@dataclass
class OpenAIConfig:
    """OpenAI configuration settings"""
    api_keys: List[str]
    embedding_model: str = "text-embedding-3-large"
    chat_model: str = "gpt-4.1-mini-2025-04-14"
    nano_model: str = "gpt-4.1-nano-2025-04-14"
    max_retries: int = 5
    retry_min_seconds: int = 2
    retry_max_seconds: int = 4
    
    def __post_init__(self):
        self.key_cycle = itertools.cycle(self.api_keys)
    
    def get_next_key(self) -> str:
        """Get the next API key in rotation"""
        return next(self.key_cycle)

@dataclass
class QdrantConfig:
    """Qdrant vector database configuration"""
    host: str
    port: int = 6333
    url: Optional[str] = None
    collection_prefix: str = "rbi"
    embedding_size: int = 3072  # text-embedding-3-large dimension
    sparse_model: str = "Qdrant/bm25"
    splade_model: str = "naver/efficient-splade-VI-BT-large-doc"
    sparse_vector_name: str = "fast-sparse-bm25"
    splade_vector_name: str = "fast-sparse-bm25-splade"
    
    @property
    def full_url(self) -> str:
        """Get the full Qdrant URL"""
        if self.url:
            return self.url

        # Construct proper URL from host
        if self.host.startswith(('http://', 'https://')):
            return self.host
        else:
            return f"http://{self.host}:{self.port}"

@dataclass
class DatabaseConfig:
    """Database configuration for MongoDB/DocumentDB"""
    uri: str
    database_name: str = "rbi"
    collection_name: str = "rbi_regulations"
    rss_database_name: str = "rss_feed_db"
    rss_collection_name: str = "articles"
    use_tls: bool = True
    tls_ca_file: str = "/tmp/global-bundle.pem"
    replica_set: str = "rs0"
    read_preference: str = "secondaryPreferred"
    retry_writes: bool = False

@dataclass
class S3Config:
    """S3 configuration for file storage"""
    bucket_name: str
    access_key_id: str
    secret_access_key: str
    region: str = "us-east-1"

@dataclass
class ProcessingConfig:
    """Document processing configuration"""
    max_chunk_chars: int = 6000
    overlap_chars: int = 800
    max_summary_words: int = 150
    max_topics: int = 5
    batch_size: int = 1024
    max_concurrent_tasks: int = 8

class Config:
    """Main configuration class that loads all settings"""
    
    def __init__(self):
        self._load_config()
    
    def _load_config(self):
        """Load configuration from Airflow Variables"""
        try:
            # OpenAI Configuration
            openai_keys_raw = Variable.get("openai_api_keys", default_var="")
            openai_keys = [k.strip() for k in openai_keys_raw.split("|") if k.strip()]
            if not openai_keys:
                raise ValueError("No OpenAI API keys configured")
            
            self.openai = OpenAIConfig(api_keys=openai_keys)
            
            # Qdrant Configuration
            qdrant_host = Variable.get("vector_db_host", default_var="localhost")
            qdrant_url = Variable.get("qdrant_url", default_var=None)

            # Clean up host value (remove any carriage returns or extra characters)
            if qdrant_host:
                qdrant_host = qdrant_host.strip().replace('\r', '').replace('\n', '')

            self.qdrant = QdrantConfig(
                host=qdrant_host,
                url=qdrant_url
            )
            
            # Database Configuration
            db_uri = Variable.get("documentdb_uri", default_var="mongodb://localhost:27017/")
            self.database = DatabaseConfig(uri=db_uri)
            
            # S3 Configuration
            s3_bucket = Variable.get("s3_bucket_name", default_var="")
            s3_access_key = Variable.get("aws_access_key_id", default_var="")
            s3_secret_key = Variable.get("aws_secret_access_key", default_var="")
            
            self.s3 = S3Config(
                bucket_name=s3_bucket,
                access_key_id=s3_access_key,
                secret_access_key=s3_secret_key
            )
            
            # Processing Configuration
            self.processing = ProcessingConfig()
            
            logger.info("Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

    def validate(self) -> bool:
        """Validate that all required configuration is present"""
        errors = []
        
        if not self.openai.api_keys:
            errors.append("OpenAI API keys not configured")
        
        if not self.s3.bucket_name:
            errors.append("S3 bucket name not configured")
        
        if not self.s3.access_key_id or not self.s3.secret_access_key:
            errors.append("S3 credentials not configured")
        
        if not self.database.uri:
            errors.append("Database URI not configured")
        
        if errors:
            logger.error(f"Configuration validation failed: {', '.join(errors)}")
            return False
        
        return True

# Global configuration instance
config = Config()
