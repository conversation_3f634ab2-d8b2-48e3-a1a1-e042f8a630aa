//page.tsx
"use client"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { useChat } from "@/contexts/ChatContext"
import { Navigation } from "@/components/Navigation"
import { MainContent } from "@/components/MainContent"
import { RightSidebar } from "@/components/RightSidebar"
import type { RegulationInfo, ReferenceItem, ActionItem } from "@/types/main_types"


export default function Home() {
  const [activeView, setActiveView] = useState<"home" | "chat" | "action-items">("home")
  const [currentRegulation, setCurrentRegulation] = useState<RegulationInfo | null>(null)
  const [selectedDocCode, setselectedDocCode] = useState<string | null>(null)
  const [showDetailView, setShowDetailView] = useState(false)
  const [isChatHistoryCollapsed, setIsChatHistoryCollapsed] = useState(false)
  const [references, setReferences] = useState<ReferenceItem[]>([])
  const [selectedSpecificRef, setSelectedSpecificRef] = useState<ReferenceItem | null>(null)
  const [selectedActionItem, setSelectedActionItem] = useState<ActionItem | null>(null) // Add this line
  const [isInitialized, setIsInitialized] = useState(false)
  const router = useRouter()
  const { user } = useAuth()
  const { currentSessionId, messages, loadSession } = useChat()

  
  // console.log("isChatHistoryCollapsed:",isChatHistoryCollapsed);
  
  const handleSetActiveView = (view: "home" | "chat" | "action-items") => {
    setActiveView(view);
    if (view !== "action-items") {
      setSelectedActionItem(null);
    }
    if (typeof window !== 'undefined') {
      localStorage.setItem('activeView', view);
    }
  }
 
  // Check if user is authenticated
  useEffect(() => {
    if (!user) {
      router.push("/signin")
    }
  }, [user, router])

  // Restore session state on page load
  useEffect(() => {
    if (!user || isInitialized) return;
    
    const initializeApp = async () => {
      if (typeof window === 'undefined' || isInitialized) return;
      try {
        const savedSessionId = localStorage.getItem('currentSessionId');
        const savedView = localStorage.getItem('activeView');
        if (savedSessionId && savedView === 'chat') {
          await loadSession(savedSessionId);
          setActiveView('chat');
        } else if (savedView === 'action-items') {
          setActiveView('action-items');
          setIsChatHistoryCollapsed(true);
        }
        setIsInitialized(true);
      } catch (error) {
        console.error("Error restoring session:", error);
        setIsInitialized(true);
      }
    };
    initializeApp();
  }, [user, isInitialized, loadSession]);



  useEffect(() => {
    if (currentSessionId && typeof window !== 'undefined') {
      localStorage.setItem('currentSessionId', currentSessionId);
    }
  }, [currentSessionId]);



  useEffect(() => {
    if (showDetailView) {
      // console.log("entered");
      setIsChatHistoryCollapsed(true);
    }
  }, [showDetailView])



  useEffect(() => {
    if (activeView === "action-items") {
      setIsChatHistoryCollapsed(true);
    }
  }, [activeView]);



  const handleViewDetail = () => {
    setShowDetailView(true);
    setIsChatHistoryCollapsed(true);
  }


  const handleCloseDetail = () => {
    // console.log(showDetailView);
    // console.log("onclose is called at page");
    setShowDetailView(false);
    // console.log(showDetailView);
  }

  const handleCollapseChat = () => {
    setIsChatHistoryCollapsed(true);
  }

  // Handle action items click from anywhere in the app
  const handleActionItemsClick = () => {
    handleSetActiveView("action-items");
    setIsChatHistoryCollapsed(true);
    setShowDetailView(false);
    setReferences([]);
  }

  // If not authenticated, show loading state while redirecting
  if (!user) {
    return <div className="flex h-screen w-screen items-center justify-center bg-gradient-to-b from-[#D9CCB6] to-[#E2D7E5]">
      <div className="text-lg">Redirecting to login...</div>
    </div>
  }

  return (
    <div className="flex h-screen w-screen bg-gradient-to-b from-[#D9CCB6] to-[#E2D7E5] overflow-hidden">
      {/* Navigation with collapsible chat history */}
      <div className={`transition-all duration-300 ease-in-out ${isChatHistoryCollapsed ? 'navigation-collapsed' : 'navigation-expanded'}`}>
        <Navigation
          isCollapsed={isChatHistoryCollapsed}
          onToggleCollapse={(collapsed) => setIsChatHistoryCollapsed(collapsed)}
          setActiveView={handleSetActiveView}
          setSelectedActionItem={setSelectedActionItem} // Add this line
          setShowDetailView={setShowDetailView}
        />
      </div>
     
      {/* Main content area - expands when detail view is shown */}
      <MainContent
        activeView={activeView}
        setActiveView={handleSetActiveView}
        currentRegulation={currentRegulation}
        selectedDocCode={selectedDocCode}
        setselectedDocCode={setselectedDocCode}
        setCurrentRegulation={setCurrentRegulation}
        onViewDetail={handleViewDetail}
        showDetailView={showDetailView}
        isChatHistoryCollapsed={isChatHistoryCollapsed}
        setReferences={setReferences}
        setSelectedSpecificRef={setSelectedSpecificRef}
        onActionItemsClick={handleActionItemsClick}
        onClose={handleCloseDetail}
        selectedActionItem={selectedActionItem} // Add this line
        setSelectedActionItem={setSelectedActionItem} // Add this line
      />
     
      {/* Right sidebar - shows either notifications or resource details */}
      <RightSidebar
        activeView={activeView}
        selectedDocCode={selectedDocCode}
        showDetailView={showDetailView}
        setShowDetailView={setShowDetailView}
        currentRegulation={currentRegulation}
        onClose={handleCloseDetail}
        references={references}
        selectedSpecificRef={selectedSpecificRef}
        onCollapseChat={handleCollapseChat}
      />
    </div>
  )
}