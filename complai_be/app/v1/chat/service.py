# app/v1/chat/service.py
from datetime import datetime
import uuid
from fastapi import HTTPException, Request
import openai
import os
import json
from typing import Union, Dict, Any
openai.api_key = os.getenv("OPENAI_API_KEY")

# ----------------------------
# Retrieve user sessions
# ----------------------------
def retrive_user_sessions(db, username: str):
    sessions_cursor = db["session"].find({"username": username})
    session_list = []
    for session in sessions_cursor:
        session_list.append({
            "id": session.get("session_id"),
            "title": session.get("title", ""),
            "created_at": session.get("created_at")
        })
    return session_list

# ----------------------------
# Update user session title
# ----------------------------
import os
from pymongo.database import Database
from langchain_openai import AzureChatOpenAI
from langchain.schema.messages import SystemMessage, HumanMessage


def generate_session_title(first_user_message: str) -> str:
    """
    Generates a concise session title from the user's first message using LangChain's AzureChatOpenAI.
    """

    os.environ["AZURE_OPENAI_API_KEY"] = "CY7FFUOliBCU45ChvfiUNOX8PHHimXZr8OUtoT6U2CPH378b4vqMJQQJ99BEACHYHv6XJ3w3AAAAACOG6g1h"

    chat = AzureChatOpenAI(
        azure_deployment="gpt-4.1",
        azure_endpoint="https://sriha-mb07vtte-eastus2.cognitiveservices.azure.com/",
        model="gpt-4.1",
        openai_api_version="2024-12-01-preview",
        temperature=0.0,
    )

    prompt = (
        "You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.\n\n"
        "Your only task is to extract a short and clear session title from the user's first message related to RBI compliance.\n\n"
        "**MANDATORY INSTRUCTIONS:**\n"
        "- Respond with ONLY the title.\n"
        "- Title must be a concise summary (max 10 words).\n"
        "- DO NOT include any explanations, labels, prefixes, suffixes, or comments.\n"
        "- DO NOT use markdown, JSON, or any formatting.\n"
        "- DO NOT add quotation marks or punctuation unless required in the title.\n"
        "- DO NOT use escape characters or newline characters.\n"
        "- Ensure the response is ONLY the clean title text with nothing else.\n\n"
        f"User's message: {first_user_message}"
    )

    messages = [
        SystemMessage(content="You are a title generator that returns short, clear compliance-related titles."),
        HumanMessage(content=prompt)
    ]

    response = chat.invoke(messages)
    return response.content.strip()


def update_user_session(session_id: str, username: str, first_message: str, db: Database) -> dict:
    """
    Updates the session document in MongoDB with the AI-generated session title.
    """
    print(f"Generating title for message: {first_message}")
    generated_title = generate_session_title(first_message)

    result = db["session"].update_one(
        {"session_id": session_id, "username": username},
        {"$set": {"title": generated_title}}
    )

    if result.matched_count:
        return {"status": "success", "title": generated_title}
    else:
        return {"status": "error", "message": "Session not found."}



def update_user_session(session_id: str, username: str, title: str, db):
    query = title
    print(query)
    updatetitle = generate_session_title(query)
    
    result = db["session"].update_one(
        {"session_id": session_id, "username": username},
        {"$set": {"title": updatetitle}}
    )
    
    if result.matched_count:
        return {"status": "success", "title": updatetitle}
    else:
        return {"status": "error", "message": "Session not found."}
    

# ----------------------------
# Create a new user session
# ----------------------------
def create_user_session(username: str, db):
    session_id = str(uuid.uuid4())
    # Incredibly unlikely but we check for session_id collision
    while db["session"].find_one({"session_id": session_id}):
        session_id = str(uuid.uuid4())
    session_doc = {
        "session_id": session_id,
        "username": username,
        "created_at": datetime.utcnow(),
        "messages": [],
        "query_count": 0,
        "title": "New Chat"
    }
    db["session"].insert_one(session_doc)
    return session_id

# ----------------------------
# Retrieve chat messages for a session
# ----------------------------
def retrive_user_session_chat(session_id: str, username: str, db):
    try:
        # Query the database for the session
        session = db["session"].find_one({"username": username, "session_id": session_id})
        if not session:
            print("Session not found.")
            return []
        formatted_messages = []
        for message in session.get("messages", []):
            ts = message.get("timestamp")
            timestamp_str = None
            if ts:
                if isinstance(ts, dict) and "$date" in ts:
                    timestamp_str = ts["$date"]
                elif hasattr(ts, "isoformat"):
                    timestamp_str = ts.isoformat() + "Z"
                else:
                    # fallback if ts is already a string or in an unexpected format
                    timestamp_str = str(ts)
            formatted_messages.append({
                "query": message.get("query"),
                "content": message.get("content"),
                "metadata": message.get("metadata", []),
                "timestamp": {"$date": timestamp_str} if timestamp_str else None
            })
        return formatted_messages
    except Exception:
        print("Session not found.")
        return []


# ----------------------------
# Retrieve metadata from a specific chat message in a session
# ----------------------------
def retrive_user_session_chat_metadata(session_id: str, username: str, content: str, db):
    session = db["session"].find_one({"username": username, "session_id": session_id})
    if not session:
        print("Session not found.")
        return []
    metadata = None
    for msg in session.get("messages", []):
        if msg.get("content") == content:
            metadata = msg.get("metadata", [])
            break
    print(metadata)
    return metadata if metadata is not None else []

# ----------------------------
# Save or update feedback for a message
# ----------------------------
def save_feedback(session_id: str, message_content: Union[Dict[str, Any], str], feedback_text: str = None,
                  vote: str = None, username: str = None, db=None):
    """
    Save feedback for a message.
    
    Args:
        session_id: Session identifier
        message_content: Message content as dict/JSON object or string
        feedback_text: Optional feedback text
        vote: Optional vote (thumbs up/down)
        username: Optional username
        db: Database connection
    """
    try:
        # Convert message_content to JSON string for consistent storage and querying
        if isinstance(message_content, dict):
            message_content_str = json.dumps(message_content, sort_keys=True)
        elif isinstance(message_content, str):
            # If it's already a string, try to parse and re-stringify for consistency
            try:
                parsed = json.loads(message_content)
                message_content_str = json.dumps(parsed, sort_keys=True)
            except json.JSONDecodeError:
                # If it's not valid JSON, use as-is
                message_content_str = message_content
        else:
            # For any other type, convert to JSON string
            message_content_str = json.dumps(message_content, sort_keys=True)
        
        query = {
            "session_id": session_id, 
            "message_content": message_content_str, 
            "username": username
        }
        
        existing_feedback = db["feedback"].find_one(query)
        
        if existing_feedback:
            update_fields = {}
            if feedback_text is not None:
                update_fields["feedback_text"] = feedback_text
            if vote is not None:
                update_fields["vote"] = vote
            update_fields["updated_at"] = datetime.utcnow()
            
            if update_fields:
                db["feedback"].update_one(query, {"$set": update_fields})
        else:
            feedback_doc = {
                "session_id": session_id,
                "message_content": message_content_str,  # Store as JSON string
                "message_content_json": message_content if isinstance(message_content, dict) else None,  # Store original dict for easier querying
                "feedback_text": feedback_text,
                "vote": vote,
                "username": username,
                "copied": False,
                "copy_count": 0,
                "created_at": datetime.utcnow()
            }
            db["feedback"].insert_one(feedback_doc)
            
        return {"status": "success", "message": "Feedback saved successfully"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# ----------------------------
# Get all feedback for a session
# ----------------------------
def get_session_feedback(session_id: str, db):
    try:
        feedback_cursor = db["feedback"].find({"session_id": session_id})
        feedback_list = []
        for f in feedback_cursor:
            feedback_list.append({
                "message_content": f.get("message_content"),
                "feedback_text": f.get("feedback_text"),
                "vote": f.get("vote"),
                "username": f.get("username"),
                "created_at": f.get("created_at")
            })
        return feedback_list
    except Exception as e:
        return {"status": "error", "message": str(e)}

# ----------------------------
# Get aggregated feedback statistics for a session
# ----------------------------
def get_feedback_stats(session_id: str, db):
    try:
        feedback_cursor = list(db["feedback"].find({"session_id": session_id}))
        stats = {
            "upvotes": sum(1 for f in feedback_cursor if f.get("vote") == "up"),
            "downvotes": sum(1 for f in feedback_cursor if f.get("vote") == "down"),
            "text_feedback_count": sum(1 for f in feedback_cursor if f.get("feedback_text")),
            "total_feedback": len(feedback_cursor)
        }
        return stats
    except Exception as e:
        return {"status": "error", "message": str(e)}

# ----------------------------
# Get total sessions for a user
# ----------------------------
def get_total_sessions(username: str, db) -> int:
    """Get total number of sessions for a user"""
    return db["session"].count_documents({"username": username})

# ----------------------------
# Check session limits and increment query count if needed
# ----------------------------
def check_session_limits(username: str, session_id: str = None, request: Request = None, db=None):
    """Check if user has exceeded their session limits based on origin"""
    print("request", request)
    origin = request.origin

    # Retrieve or create the user's limit document
    user_limit = db["user_limits"].find_one({"username": username})
    if not user_limit:
        user_limit = {
            "username": username,
            "created_at": datetime.utcnow(),
            "origin": origin,
            "query_count": 0,
            "session_count": 0,
            "is_trial": True,
            "max_queries_per_session": 10,  # Default max queries per session
            "max_sessions": 5               # Default max sessions
        }
        db["user_limits"].insert_one(user_limit)

    # Determine whether to enforce limits (based on origin)
    enforce_limits = origin == "https://chat.complai-genie.online/"
    total_sessions = db["session"].count_documents({"username": username})

    # If creating a new session
    if not session_id:
        if enforce_limits and total_sessions >= user_limit.get("max_sessions", 5):
            raise HTTPException(
                status_code=403,
                detail={
                    "message": "Free trial ended - Maximum sessions reached",
                    "session_count": total_sessions,
                    "max_sessions": user_limit.get("max_sessions", 5),
                    "can_create_new_session": False
                }
            )
        return {
            "remaining_sessions": user_limit.get("max_sessions", 5) - total_sessions if enforce_limits else "Unlimited",
            "total_sessions": total_sessions,
            "max_sessions": user_limit.get("max_sessions", 5) if enforce_limits else "Unlimited",
            "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5) if enforce_limits else True
        }

    # For an existing session, check query limits
    session = db["session"].find_one({"session_id": session_id})
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    if enforce_limits and session.get("query_count", 0) >= user_limit.get("max_queries_per_session", 10):
        raise HTTPException(
            status_code=403,
            detail={
                "message": "Session limit reached",
                "query_count": session.get("query_count", 0),
                "max_queries_per_session": user_limit.get("max_queries_per_session", 10),
                "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5)
            }
        )

    # Increment query count if enforcing limits
    if enforce_limits:
        db["session"].update_one(
            {"session_id": session_id},
            {"$inc": {"query_count": 1}}
        )
        # Optionally, update local variable for returning stats:
        session["query_count"] = session.get("query_count", 0) + 1

    return {
        "remaining_queries": user_limit.get("max_queries_per_session", 10) - session.get("query_count", 0) if enforce_limits else "Unlimited",
        "total_queries": session.get("query_count", 0),
        "max_queries_per_session": user_limit.get("max_queries_per_session", 10) if enforce_limits else "Unlimited",
        "remaining_sessions": user_limit.get("max_sessions", 5) - total_sessions if enforce_limits else "Unlimited",
        "total_sessions": total_sessions,
        "max_sessions": user_limit.get("max_sessions", 5) if enforce_limits else "Unlimited",
        "can_create_new_session": total_sessions < user_limit.get("max_sessions", 5) if enforce_limits else True
    }
