import type { RecentChat, LatestUpdate } from "@/types"

export const recentChats: RecentChat[] = [
  {
    id: "1",
    title: "SGB Importance",
    date: "Today",
  },
  {
    id: "2",
    title: "Repo Rate Implications",
    date: "Today",
  },
  {
    id: "3",
    title: "Currency Note Change",
    date: "Yesterday",
  },
  {
    id: "4",
    title: "Phishing Alert",
    date: "Yesterday",
  },
  {
    id: "5",
    title: "Money Laundering",
    date: "Yesterday",
  },
  {
    id: "6",
    title: "Home Loan PRD",
    date: "Last Week",
  },
  {
    id: "7",
    title: "Actions for Interest",
    date: "Last Week",
  },
  {
    id: "8",
    title: "Money Laundering",
    date: "Last Week",
  },
]

export const latestUpdates: LatestUpdate[] = [
  {
    id: "1",
    title: "Treasury Bills: Full Auction Result",
    date: "Mar 19, 2025",
    category: "Today",
  },
  {
    id: "2",
    title: "Result of the Overnight Variable Repo Rate",
    date: "Mar 19, 2025",
    category: "Today",
  },
  {
    id: "3",
    title: "Treatment of Right-of-Use (ROU) Asset for Regulatory Capital Purposes",
    date: "Mar 19, 2025",
    category: "Yesterday",
  },
  {
    id: "4",
    title: "Result of the Overnight Variable Repo Rate",
    date: "Mar 19, 2025",
    category: "Yesterday",
  },
]
