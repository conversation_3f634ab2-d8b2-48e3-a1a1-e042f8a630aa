import re
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.utils.log.logging_mixin import LoggingMixin

import requests, boto3, os
from datetime import datetime
from bs4 import BeautifulSoup

BUCKET = "airflowdump"
BASE_FOLDER = "npci"
PAGES = {
    "upi": "https://www.npci.org.in/what-we-do/upi/circular",
    "aeps": "https://www.npci.org.in/what-we-do/aeps/circulars",
    "rupay": "https://www.npci.org.in/what-we-do/rupay/circulars",
    "nfs": "https://www.npci.org.in/what-we-do/nfs/circulars",
    "imps": "https://www.npci.org.in/what-we-do/imps/circular",
    "netc_fastag": "https://www.npci.org.in/what-we-do/netc-fastag/circulars",
    "nach": "https://www.npci.org.in/what-we-do/nach/circulars",
    "cts": "https://www.npci.org.in/what-we-do/cts/circulars",
}

aws_access_key_id = Variable.get("aws_access_key_id")
aws_secret_access_key = Variable.get("aws_secret_access_key")
s3 = boto3.client(
    "s3",
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key
)

logger = LoggingMixin().log

MONTHS_MAP = {
    'january': '01', 'february': '02', 'march': '03', 'april': '04',
    'may': '05', 'june': '06', 'july': '07', 'august': '08',
    'september': '09', 'october': '10', 'november': '11', 'december': '12'
}

def extract_month_year(text, year):
    """
    Tries to find month and year in the given text.
    Returns (yyyy, mm) if found, else (year, None)
    """
    text_l = text.lower()
    for m_name, m_num in MONTHS_MAP.items():
        if m_name in text_l:
            # Try to extract year near the month
            year_match = re.search(r'(\d{4})', text_l)
            y = year_match.group(1) if year_match else year
            return (y, m_num)
    # If month not found, try FY as before
    fy_match = re.search(r'FY\s?(\d{2})[^\d]?(\d{2})', text)
    if fy_match:
        fy_start = fy_match.group(1)
        fy_year = "20" + fy_start  # e.g., FY 24-25 → 2024
        return (fy_year, None)
    # Default fallback
    return (year, None)

def parse_and_upload(**context):
    for product, url in PAGES.items():
        logger.info(f"Processing {product}: {url}")
        try:
            resp = requests.get(url, timeout=30)
            resp.raise_for_status()
        except Exception as e:
            logger.error(f"Failed to fetch listing page {url}: {e}")
            continue

        soup = BeautifulSoup(resp.text, "html.parser")

        # Instead of relying on dropdown, get all year divs directly
        year_divs = soup.find_all('div', id=re.compile(r'^year\d{4}$'))

        for year_div in year_divs:
            # Extract year from id
            year = year_div.get('id').replace('year', '')

            # Each circular/PDF is in a div with class 'pdf-item'
            for pdf_card in year_div.find_all('div', class_='pdf-item'):
                # Usually title is in a <p> inside pdf_card or its previous sibling
                title_text = ""
                title_p = pdf_card.find('p')
                if title_p and title_p.text.strip():
                    title_text = title_p.text.strip()
                else:
                    # Try previous sibling p (sometimes the HTML is inconsistent)
                    prev_p = pdf_card.find_previous_sibling('p')
                    if prev_p and prev_p.text.strip():
                        title_text = prev_p.text.strip()
                
                # Extract the PDF link
                link = pdf_card.find('a', href=re.compile(r'\.pdf$'))
                if not link:
                    logger.warning(f"No PDF link found in {year} for product {product}, title: {title_text}")
                    continue
                pdf_url = requests.compat.urljoin(url, link['href'])
                fname = os.path.basename(pdf_url)

                # Try to extract FY and month from the title
                fy_str = None
                mm = None
                yyyy = year
                fy_match = re.search(r'FY\s?(\d{2})[^\d]?(\d{2})', title_text)
                if fy_match:
                    fy_str = fy_match.group(1) + fy_match.group(2)
                    yyyy = "20" + fy_match.group(1)
                # Try to find month
                for m_name, m_num in MONTHS_MAP.items():
                    if m_name in title_text.lower():
                        mm = m_num
                        break

                # Build S3 key
                if mm:
                    key = f"{BASE_FOLDER}/{product}/{yyyy}/{mm}/{fname}"
                elif fy_str:
                    key = f"{BASE_FOLDER}/{product}/FY{fy_str}/{fname}"
                else:
                    key = f"{BASE_FOLDER}/{product}/{year}/{fname}"

                # S3 and download logic
                try:
                    s3.head_object(Bucket=BUCKET, Key=key)
                    logger.info(f"Already uploaded: {key}")
                    continue
                except s3.exceptions.ClientError as e:
                    if int(e.response['Error']['Code']) != 404:
                        logger.error(f"S3 check error for {key}: {e}")
                        continue
                try:
                    pdf = requests.get(pdf_url, timeout=30)
                    pdf.raise_for_status()
                except Exception as e:
                    logger.error(f"Error downloading PDF {pdf_url}: {e}")
                    continue
                try:
                    s3.put_object(Bucket=BUCKET, Key=key, Body=pdf.content)
                    logger.info(f"Uploaded {key}")
                except Exception as e:
                    logger.error(f"Error uploading to S3 {key}: {e}")

with DAG(
    dag_id="npciofficial_scraper_to_s3",
    schedule_interval="0 9,15 * * *",
    start_date=days_ago(1),
    catchup=False,
    max_active_runs=1,
    tags=["npcirc", "scraper", "s3"],
) as dag:
    scrape_task = PythonOperator(
        task_id="scrape_and_upload",
        python_callable=parse_and_upload,
        provide_context=True
    )
