// import React from 'react';
// import ReactMarkdown from 'react-markdown';
// import remarkGfm from 'remark-gfm';

// interface MarkdownRendererProps {
//   content: string;
//   inline?: boolean; // Add this prop to control inline rendering
// }

// const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, inline = false }) => {
//   if (inline) {
//     // For inline rendering, use a span wrapper and modify component behavior
//     return (
//       <span className="markdown-content-inline text-gray-800">
//         <ReactMarkdown 
//           remarkPlugins={[remarkGfm]}
//           components={{
//             // Convert block elements to inline for inline mode
//             h1: ({children}) => <h1 className="text-3xl font-bold mb-4 text-gray-900">{children}</h1>,
//             h2: ({children}) => <h2 className="text-2xl font-bold mb-4 text-gray-900">{children}</h2>,
//             h3: ({children}) => <h3 className="text-xl font-bold mb-4 text-gray-900">{children}</h3>,
//             p: ({children}) => <span className="leading-relaxed">{children}</span>,
//             ul: ({children}) => <span>{children}</span>,
//             ol: ({children}) => <span>{children}</span>,
//             li: ({children}) => <span className="inline">{children} </span>,
//             strong: ({children}) => <strong className="font-semibold">{children}</strong>,
//             em: ({children}) => <em className="italic">{children}</em>,
//             // Tables remain as block elements even in inline mode
//             table: ({children}) => (
//               <div className="overflow-x-auto mb-4">
//                 <table className="min-w-full border-collapse border border-gray-300">
//                   {children}
//                 </table>
//               </div>
//             ),
//             thead: ({children}) => (
//               <thead className="bg-transparent">
//                 {children}
//               </thead>
//             ),
//             tbody: ({children}) => (
//               <tbody className="bg-transparent">
//                 {children}
//               </tbody>
//             ),
//             tr: ({children}) => (
//               <tr className="border-b border-black">
//                 {children}
//               </tr>
//             ),
//             th: ({children}) => (
//               <th className="px-4 py-3 text-center text-m font-medium text-gray-900 border border-black bg-transparent">
//                 {children}
//               </th>
//             ),
//             td: ({children}) => (
//               <td className="px-4 py-3 text-sm text-gray-700 border border-black">
//                 {children}
//               </td>
//             ),
//           }}
//         >
//           {content}
//         </ReactMarkdown>
//       </span>
//     );
//   }

//   // Default block rendering
//   return (
//     <div className="markdown-content max-w-full text-gray-800">
//       <ReactMarkdown 
//         remarkPlugins={[remarkGfm]}
//         components={{
//           h1: ({children}) => <h1 className="text-3xl font-bold mb-4 text-gray-900">{children}</h1>,
//           h2: ({children}) => <h2 className="text-2xl font-bold mb-3 text-gray-900">{children}</h2>,
//           h3: ({children}) => <h3 className="text-xl font-bold mb-2 text-gray-900">{children}</h3>,
//           p: ({children}) => <p className="mb-4 leading-relaxed">{children}</p>,
//           ul: ({children}) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
//           ol: ({children}) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
//           li: ({children}) => <li className="mb-1">{children}</li>,
//           strong: ({children}) => <strong className="font-semibold">{children}</strong>,
//           em: ({children}) => <em className="italic">{children}</em>,
//           // Table components
//           table: ({children}) => (
//             <div className="overflow-x-auto mb-4">
//               <table className="min-w-full border-collapse border border-gray-300">
//                 {children}
//               </table>
//             </div>
//           ),
//           thead: ({children}) => (
//             <thead className="bg-transparent">
//               {children}
//             </thead>
//           ),
//           tbody: ({children}) => (
//             <tbody className="bg-transparent">
//               {children}
//             </tbody>
//           ),
//           tr: ({children}) => (
//             <tr className="border-b border-black">
//               {children}
//             </tr>
//           ),
//           th: ({children}) => (
//             <th className="px-4 py-3 text-center text-m font-medium text-gray-900 border border-black bg-transparent">
//               {children}
//             </th>
//           ),
//           td: ({children}) => (
//             <td className="px-4 py-3 text-sm text-gray-700 border border-black">
//               {children}
//             </td>
//           ),
//         }}
//       >
//         {content}
//       </ReactMarkdown>
//     </div>
//   );
// };

// export default MarkdownRenderer;

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
  content: string;
  inline?: boolean; // Add this prop to control inline rendering
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, inline = false }) => {
  if (inline) {
    // For inline rendering, use a div wrapper but modify specific components
    return (
      <div className="markdown-content-inline text-gray-800">
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]}
          components={{
            // Convert headers to inline strong elements
            h1: ({children}) => <strong className="text-lg font-bold text-gray-900 block mb-2">{children}</strong>,
            h2: ({children}) => <strong className="text-base font-bold text-gray-900 block mb-2">{children}</strong>,
            h3: ({children}) => <strong className="text-base font-bold text-gray-900 block mb-2">{children}</strong>,
            h4: ({children}) => <strong className="text-base font-bold text-gray-900 block mb-2">{children}</strong>,
            // Remove bottom margin from last paragraph only
            p: ({children, node}) => {
              // Check if this is the last paragraph
              const parent = node?.parent;
              const isLastParagraph = parent && 
                parent.children[parent.children.length - 1] === node;
              
              return (
                <p className={`leading-relaxed ${isLastParagraph ? 'mb-0' : 'mb-4'}`}>
                  {children}
                </p>
              );
            },
            // Keep lists as block elements with proper spacing
            ul: ({children}) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
            ol: ({children}) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
            li: ({children}) => <li className="mb-1">{children}</li>,
            strong: ({children}) => <strong className="font-semibold">{children}</strong>,
            em: ({children}) => <em className="italic">{children}</em>,
            // Handle line breaks properly
            br: () => <br />,
            // Tables remain as block elements
            table: ({children}) => (
              <div className="overflow-x-auto mb-4">
                <table className="min-w-full border-collapse border border-gray-300">
                  {children}
                </table>
              </div>
            ),
            thead: ({children}) => (
              <thead className="bg-transparent">
                {children}
              </thead>
            ),
            tbody: ({children}) => (
              <tbody className="bg-transparent">
                {children}
              </tbody>
            ),
            tr: ({children}) => (
              <tr className="border-b border-black">
                {children}
              </tr>
            ),
            th: ({children}) => (
              <th className="px-4 py-3 text-center text-m font-medium text-gray-900 border border-black bg-transparent">
                {children}
              </th>
            ),
            td: ({children}) => (
              <td className="px-4 py-3 text-sm text-gray-700 border border-black">
                {children}
              </td>
            ),
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  }

  // Default block rendering
  return (
    <div className="markdown-content max-w-full text-gray-800">
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        components={{
          h1: ({children}) => <h1 className="text-3xl font-bold mb-4 text-gray-900">{children}</h1>,
          h2: ({children}) => <h2 className="text-2xl font-bold mb-3 text-gray-900">{children}</h2>,
          h3: ({children}) => <h3 className="text-xl font-bold mb-2 text-gray-900">{children}</h3>,
          h4: ({children}) => <h4 className="text-lg font-bold mb-2 text-gray-900">{children}</h4>,
          p: ({children}) => <p className="mb-4 leading-relaxed">{children}</p>,
          ul: ({children}) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
          ol: ({children}) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
          li: ({children}) => <li className="mb-1">{children}</li>,
          strong: ({children}) => <strong className="font-semibold">{children}</strong>,
          em: ({children}) => <em className="italic">{children}</em>,
          // Table components
          table: ({children}) => (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full border-collapse border border-gray-300">
                {children}
              </table>
            </div>
          ),
          thead: ({children}) => (
            <thead className="bg-transparent">
              {children}
            </thead>
          ),
          tbody: ({children}) => (
            <tbody className="bg-transparent">
              {children}
            </tbody>
          ),
          tr: ({children}) => (
            <tr className="border-b border-black">
              {children}
            </tr>
          ),
          th: ({children}) => (
            <th className="px-4 py-3 text-center text-m font-medium text-gray-900 border border-black bg-transparent">
              {children}
            </th>
          ),
          td: ({children}) => (
            <td className="px-4 py-3 text-sm text-gray-700 border border-black">
              {children}
            </td>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;