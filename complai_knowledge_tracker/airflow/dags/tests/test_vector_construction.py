#!/usr/bin/env python3
"""
Test script to verify that all 3 vectors (dense, BM25 sparse, SPLADE) are properly 
constructed and formatted according to the required Qdrant multi-vector structure.

This test ensures the RSS feed ETL produces vectors in the correct format:
{
  id: uuid(),
  vector: {
    dense: [0.32, 0.3243, 0.123, ...],
    "fast-sparse-bm25": SparseVector(indices=[...], values=[...]),
    "fast-sparse-bm25-splade": SparseVector(indices=[...], values=[...])
  },
  payload: {...}
}
"""

import sys
import logging
from pathlib import Path

# Add utils to path
sys.path.append(str(Path(__file__).parent / "utils"))

from utils.qdrant_utils import qdrant_manager, splade_encoder
from utils.openai_utils import en_embeddings
from utils.config import config
from qdrant_client import models
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dense_vector_generation():
    """Test OpenAI dense vector generation"""
    print("\n🔢 Testing Dense Vector Generation...")
    
    try:
        test_text = "This is a test document for vector generation. It contains banking regulations and compliance requirements."
        dense_vector = en_embeddings.embed_query(test_text)
        
        if isinstance(dense_vector, list) and len(dense_vector) == config.qdrant.embedding_size:
            print(f"✅ Dense vector generated successfully")
            print(f"   - Type: {type(dense_vector)}")
            print(f"   - Length: {len(dense_vector)}")
            print(f"   - Sample values: {dense_vector[:5]}")
            return True
        else:
            print(f"❌ Dense vector generation failed - incorrect format or size")
            print(f"   - Type: {type(dense_vector)}")
            print(f"   - Length: {len(dense_vector) if hasattr(dense_vector, '__len__') else 'N/A'}")
            return False
            
    except Exception as e:
        print(f"❌ Dense vector generation failed: {e}")
        return False

def test_bm25_sparse_vector_generation():
    """Test BM25 sparse vector generation"""
    print("\n🔢 Testing BM25 Sparse Vector Generation...")
    
    try:
        test_text = "Banking regulations require compliance with risk management procedures and capital adequacy norms."
        bm25_vector = qdrant_manager.sparse_embedder.embed_query(test_text)
        
        # Convert to Qdrant format
        from utils.qdrant_utils import convert_to_qdrant_sparse_vector
        qdrant_bm25_vector = convert_to_qdrant_sparse_vector(bm25_vector)
        
        if isinstance(qdrant_bm25_vector, models.SparseVector):
            print(f"✅ BM25 sparse vector generated successfully")
            print(f"   - Type: {type(qdrant_bm25_vector)}")
            print(f"   - Indices count: {len(qdrant_bm25_vector.indices)}")
            print(f"   - Values count: {len(qdrant_bm25_vector.values)}")
            print(f"   - Sample indices: {qdrant_bm25_vector.indices[:10]}")
            print(f"   - Sample values: {qdrant_bm25_vector.values[:10]}")
            return True
        else:
            print(f"❌ BM25 sparse vector generation failed - incorrect format")
            print(f"   - Type: {type(qdrant_bm25_vector)}")
            return False
            
    except Exception as e:
        print(f"❌ BM25 sparse vector generation failed: {e}")
        return False

def test_splade_vector_generation():
    """Test SPLADE sparse vector generation"""
    print("\n🔢 Testing SPLADE Vector Generation...")
    
    try:
        test_text = "Reserve Bank of India circular on prudential norms for classification and valuation of investments."
        splade_vector = splade_encoder.encode(test_text)
        
        if isinstance(splade_vector, models.SparseVector):
            print(f"✅ SPLADE vector generated successfully")
            print(f"   - Type: {type(splade_vector)}")
            print(f"   - Indices count: {len(splade_vector.indices)}")
            print(f"   - Values count: {len(splade_vector.values)}")
            print(f"   - Sample indices: {splade_vector.indices[:10]}")
            print(f"   - Sample values: {splade_vector.values[:10]}")
            return True
        else:
            print(f"❌ SPLADE vector generation failed - incorrect format")
            print(f"   - Type: {type(splade_vector)}")
            return False
            
    except Exception as e:
        print(f"❌ SPLADE vector generation failed: {e}")
        return False

def test_multi_vector_point_creation():
    """Test complete multi-vector point creation using QdrantManager"""
    print("\n🏗️ Testing Multi-Vector Point Creation...")
    
    try:
        test_text = "RBI Master Direction on Know Your Customer (KYC) norms and Anti-Money Laundering (AML) standards for banks."
        test_collection = "test_multi_vector"
        test_point_id = str(uuid.uuid4())
        
        test_payload = {
            "content": test_text,
            "document_type": "master_direction",
            "category": "compliance",
            "test_point": True
        }
        
        # Use QdrantManager to create point with all vectors
        success = qdrant_manager.upsert_point(
            collection_name=test_collection,
            point_id=test_point_id,
            payload=test_payload,
            text_for_embedding=test_text
        )
        
        if success:
            print(f"✅ Multi-vector point created successfully")
            print(f"   - Collection: {test_collection}")
            print(f"   - Point ID: {test_point_id}")
            print(f"   - Payload keys: {list(test_payload.keys())}")
            
            # Try to retrieve the point to verify structure
            try:
                point = qdrant_manager.client.retrieve(
                    collection_name=test_collection,
                    ids=[test_point_id],
                    with_vectors=True
                )[0]
                
                print(f"   - Retrieved point structure:")
                if hasattr(point, 'vector') and point.vector:
                    if isinstance(point.vector, dict):
                        print(f"     * Multi-vector format detected")
                        for vector_name, vector_data in point.vector.items():
                            print(f"       - {vector_name}: {type(vector_data)}")
                    else:
                        print(f"     * Single vector format: {type(point.vector)}")
                
                return True
                
            except Exception as e:
                print(f"   ⚠️ Could not retrieve point for verification: {e}")
                return success
                
        else:
            print(f"❌ Multi-vector point creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Multi-vector point creation failed: {e}")
        return False

def test_vector_format_compliance():
    """Test that vectors match the required JavaScript format structure"""
    print("\n📋 Testing Vector Format Compliance...")
    
    try:
        test_text = "Banking supervision guidelines for operational risk management in commercial banks."
        
        # Generate all vectors
        dense_vector = en_embeddings.embed_query(test_text)
        bm25_vector = qdrant_manager.sparse_embedder.embed_query(test_text)
        splade_vector = splade_encoder.encode(test_text)
        
        # Convert to proper formats
        from utils.qdrant_utils import convert_to_qdrant_sparse_vector
        qdrant_bm25_vector = convert_to_qdrant_sparse_vector(bm25_vector)
        
        # Verify format compliance
        format_checks = {
            "dense_is_list": isinstance(dense_vector, list),
            "dense_correct_size": len(dense_vector) == config.qdrant.embedding_size,
            "bm25_is_sparse_vector": isinstance(qdrant_bm25_vector, models.SparseVector),
            "bm25_has_indices": hasattr(qdrant_bm25_vector, 'indices') and len(qdrant_bm25_vector.indices) > 0,
            "bm25_has_values": hasattr(qdrant_bm25_vector, 'values') and len(qdrant_bm25_vector.values) > 0,
            "splade_is_sparse_vector": isinstance(splade_vector, models.SparseVector),
            "splade_has_indices": hasattr(splade_vector, 'indices') and len(splade_vector.indices) > 0,
            "splade_has_values": hasattr(splade_vector, 'values') and len(splade_vector.values) > 0,
        }
        
        all_passed = all(format_checks.values())
        
        print(f"{'✅' if all_passed else '❌'} Vector format compliance check:")
        for check_name, passed in format_checks.items():
            print(f"   {'✅' if passed else '❌'} {check_name}: {passed}")
        
        if all_passed:
            print(f"\n🎯 Expected Qdrant structure:")
            print(f"   {{")
            print(f"     id: '{uuid.uuid4()}',")
            print(f"     vector: {{")
            print(f"       dense: [float array of {len(dense_vector)} elements],")
            print(f"       '{config.qdrant.sparse_vector_name}': SparseVector(indices={len(qdrant_bm25_vector.indices)}, values={len(qdrant_bm25_vector.values)}),")
            print(f"       '{config.qdrant.splade_vector_name}': SparseVector(indices={len(splade_vector.indices)}, values={len(splade_vector.values)})")
            print(f"     }},")
            print(f"     payload: {{...}}")
            print(f"   }}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Vector format compliance test failed: {e}")
        return False

def main():
    """Run all vector construction tests"""
    print("🚀 Starting Vector Construction Tests\n")
    print(f"Configuration:")
    print(f"  - Dense vector size: {config.qdrant.embedding_size}")
    print(f"  - BM25 vector name: {config.qdrant.sparse_vector_name}")
    print(f"  - SPLADE vector name: {config.qdrant.splade_vector_name}")
    print(f"  - SPLADE model: {config.qdrant.splade_model}")
    
    tests = [
        ("Dense Vector Generation", test_dense_vector_generation),
        ("BM25 Sparse Vector Generation", test_bm25_sparse_vector_generation),
        ("SPLADE Vector Generation", test_splade_vector_generation),
        ("Multi-Vector Point Creation", test_multi_vector_point_creation),
        ("Vector Format Compliance", test_vector_format_compliance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        print(f"  {'✅' if result else '❌'} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All vector construction tests passed! RSS feed ETL should work correctly.")
    else:
        print("⚠️ Some tests failed. Please review the vector construction implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
