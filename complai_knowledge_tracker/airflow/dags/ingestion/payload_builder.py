# payload_builder.py
from ingestion.bounding_box_calculator import compute_chunk_bounding_boxes
from ingestion.pdf_position_finder import get_text_positions

def build_chunk_payload(chunk_text: str, elements: list, internal_references: list,
                          pages_sorted: list, pdf_path: str) -> dict:
    """
    Builds a structured payload for a markdown chunk.
    
    Args:
        chunk_text (str): The markdown chunk text.
        elements (list): Extracted structured elements.
        internal_references (list): List of internal reference strings.
        pages_sorted (list): Sorted OCR pages.
        pdf_path (str): Path to the PDF file.
    
    Returns:
        dict: Structured payload ready for embedding/upsert into Qdrant.
    """
    positional_info = compute_chunk_bounding_boxes(elements)
    text_positions = get_text_positions(pdf_path, chunk_text, pages_sorted)
    payload = {
        "chunk_text": chunk_text,
        "elements": elements,
        "internal_references": internal_references,
        "positional_info": positional_info,
        "text_positions": text_positions
    }
    return payload
