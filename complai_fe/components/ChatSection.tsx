// //chatsection.tsx

"use client";

// import { useState, useEffect, useRef } from "react";
// import { ChatView } from "./ChatView";
// import type { ChatMessage, ReferenceItem, RegulationInfo } from "@/types/types";
// import { regulationsData } from "@/data/regulations";
// import {
//   fetchQuerySSE,
//   ProcessingStep,
//   SSEEvent,
// } from "@/services/ssequeryservice";
// import { UPDAET_SESSION_ENDPOINT } from "@/app/utils/Api";
// import { useAuth } from "@/contexts/AuthContext";
// import { useChat } from "@/contexts/ChatContext";

// interface ChatSectionProps {
//   setActiveView: (view: "home" | "chat") => void;
//   currentRegulation: RegulationInfo | null;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   initialQuery: string | null;
//   setInitialQuery: (query: string | null) => void;
//   showDetailView: boolean;
//   onFetchReferences: (references: ReferenceItem[]) => void;
// }

// export function ChatSection({
//   setActiveView,
//   currentRegulation,
//   setCurrentRegulation,
//   onViewDetail,
//   initialQuery,
//   setInitialQuery,
//   showDetailView,
//   onFetchReferences,
// }: ChatSectionProps) {
//   const { user } = useAuth();
//   const {
//     currentSessionId,
//     messages: contextMessages,
//     setMessages: setContextMessages,
//     isLoading: isContextLoading
//   } = useChat();

//   const [inputValue, setInputValue] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [loadingPhase, setLoadingPhase] = useState<"analyzing" | "summarizing" | null>(null);

//   const isProcessingRef = useRef(false);
//   const processedQueriesRef = useRef<Set<string>>(new Set());
//   const mountedRef = useRef(false);
//   const messagesEndRef = useRef<HTMLDivElement>(null);

//   const username = user?.username;

//   // Initial setup
//   useEffect(() => {
//     mountedRef.current = true;

//     // Set the active session in local storage when ChatSection mounts
//     if (currentSessionId && typeof window !== 'undefined') {
//       localStorage.setItem('currentSessionId', currentSessionId);
//       localStorage.setItem('activeView', 'chat');
//     }

//     return () => {
//       mountedRef.current = false;
//       isProcessingRef.current = false;
//     };
//   }, [currentSessionId]);

//   // When messages load, set initial regulation if available
//   useEffect(() => {
//     if (contextMessages.length > 0 && !currentRegulation) {
//       // Look for the latest assistant message with metadata
//       const latestAssistantMsg = [...contextMessages]
//         .reverse()
//         .find(msg => msg.sender === 'assistant' && msg.metadata);

//       if (latestAssistantMsg && latestAssistantMsg.metadata) {
//         // If the metadata is a RegulationInfo, use it directly
//         if (latestAssistantMsg.regulation) {
//           setCurrentRegulation(latestAssistantMsg.regulation);
//         }
//         // If metadata is array of ReferenceItems, use it for references
//         else if (Array.isArray(latestAssistantMsg.metadata)) {
//           onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);

//           // Create a regulation from the first reference
//           if (latestAssistantMsg.metadata.length > 0) {
//             const meta = latestAssistantMsg.metadata[0];
//             setCurrentRegulation({
//               id: meta.document_code || "unknown",
//               title: meta.title || "Regulation",
//               content: latestAssistantMsg.content,
//               date: meta.date_of_issue || new Date().toDateString(),
//               department: typeof meta.dept === 'string' ? meta.dept :
//                 Array.isArray(meta.dept) && meta.dept.length > 0 ? meta.dept[0] : "RBI",
//               source: meta.Type || "Master Direction",
//             });
//           }
//         }
//       }
//     }
//   }, [contextMessages, currentRegulation, setCurrentRegulation, onFetchReferences]);

//   // Process initial query if provided
//   useEffect(() => {
//     if (
//       initialQuery &&
//       !isProcessingRef.current &&
//       !processedQueriesRef.current.has(initialQuery)
//     ) {
//       isProcessingRef.current = true;
//       processedQueriesRef.current.add(initialQuery);
//       const queryToProcess = initialQuery;
//       setInitialQuery(null);
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing initialQuery:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   }, [initialQuery]);

//   // Scroll to bottom when messages change
//   useEffect(() => {
//     if (messagesEndRef.current) {
//       messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
//     }
//   }, [contextMessages]);

//   // Track the last user-selected message for references
//   const [userSelectedMessageId, setUserSelectedMessageId] = useState<string | null>(null);

//   // Update references only for new messages being added, not when clicking past messages
//   useEffect(() => {
//     // Only automatically update references for the latest message if user hasn't manually selected one
//     if (contextMessages.length > 0 && !userSelectedMessageId) {
//       // Find the latest assistant message with metadata
//       const latestAssistantMsg = [...contextMessages]
//         .reverse()
//         .find(msg => msg.sender === 'assistant' && msg.metadata && Array.isArray(msg.metadata));

//       if (latestAssistantMsg && latestAssistantMsg.metadata) {
//         onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);
//       }
//     }
//   }, [contextMessages, onFetchReferences, userSelectedMessageId]);

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim() && !isProcessingRef.current) {
//       isProcessingRef.current = true;
//       const queryToProcess = inputValue.trim();
//       setInputValue("");
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing query:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   };

//   const handleShowReferences = (references: ReferenceItem[], messageId?: string) => {
//     // Check if references is an array
//     if (references && Array.isArray(references)) {
//       // Set the user-selected message ID to prevent auto-updates
//       if (messageId) {
//         setUserSelectedMessageId(messageId);
//       }

//       // Update the references in the sidebar
//       onFetchReferences(references);
//     } else {
//       console.warn("References data is not in expected format:", references);
//     }
//   };

//   const processQuery = async (query: string) => {
//     if (!query.trim() || !mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     // Reset user-selected message ID when a new query is processed
//     setUserSelectedMessageId(null);

//     const now = new Date();
//     const isDuplicate = contextMessages.some(
//       (msg) =>
//         msg.sender === "user" &&
//         msg.content === query &&
//         now.getTime() - msg.timestamp.getTime() < 500
//     );

//     // Check if this is the initial query for the session
//     const isInitialQuery = contextMessages.length === 0;

//     if (!isDuplicate) {
//       // Generate unique IDs for user message
//       const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

//       const userMessage: ChatMessage = {
//         id: userMessageId,
//         content: query,
//         sender: "user",
//         timestamp: new Date(),
//       };

//       // Update the UI immediately to show the user message
//       setContextMessages([...contextMessages, userMessage]);

//       // Update session title if this is the initial query
//       if (isInitialQuery && currentSessionId && username) {
//         try {
//           // Create a title from the first 15 characters of the query
//           const sessionTitle = query.length > 15
//             ? `${query.substring(0, 15)}...`
//             : query;

//           // Call the update session endpoint
//           await fetch(UPDAET_SESSION_ENDPOINT, {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({
//               session_id: currentSessionId,
//               title: sessionTitle,
//               username: username
//             }),
//           });

//           console.log("Session title updated successfully");
//         } catch (err) {
//           console.error("Error updating session title:", err);
//           // Continue with query processing even if title update fails
//         }
//       }
//     }

//     setIsLoading(true);
//     setLoadingPhase("analyzing");

//     await new Promise((resolve) => setTimeout(resolve, 2000));
//     if (!mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     setLoadingPhase("summarizing");

//     try {
//       const data = await fetchQueryResponse(query, username, currentSessionId);

//       const metadata = data.metadata
//         .sort((a: any, b: any) => a.rank - b.rank)
//         .slice(0, 5)
//         .map((meta: any) => ({
//           date_of_issue: meta["revision_date_of_issue"],
//           dept: meta["departments"],
//           document_code: meta["doc_code"],
//           section_summary: meta["section_summary"],
//           pdf_filename: meta["revision_pdf_filename"],
//           pdf_link: meta["revision_pdf_link"],
//           revision_s3_url: meta["revision_s3_url"],
//           Type: meta["type"],
//           title: meta["title"],
//           collection_name: meta["_collection_name"],
//           id: meta["id"],
//           rects: meta["rects"],
//           page_numbers: meta["page_numbers"],
//         }));

//       // Pass the metadata to the parent component to update the sidebar
//       onFetchReferences(metadata);

//       // Generate a unique ID for the assistant message
//       const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

//       const assistantMessage: ChatMessage = {
//         id: assistantMessageId,
//         content: data.response,
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: metadata,
//       };

//       setContextMessages(prevMessages => [...prevMessages, assistantMessage]);
//       const relevantRegulation = createOrFindRegulation(data);
//       setCurrentRegulation(relevantRegulation);
//     } catch (error) {
//       console.error("Error fetching response from API:", error);
//       const fallbackRegulation = regulationsData[0];

//       // Generate a unique ID for the fallback message
//       const fallbackMessageId = `assistant-fallback-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

//       const fallbackMessage: ChatMessage = {
//         id: fallbackMessageId,
//         content: "I'm sorry, I couldn't process your request at the moment. Please try again later.",
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: fallbackRegulation,
//       };

//       setContextMessages(prevMessages => [...prevMessages, fallbackMessage]);
//       onFetchReferences([]);
//     } finally {
//       if (mountedRef.current) {
//         setIsLoading(false);
//         setLoadingPhase(null);
//         isProcessingRef.current = false;
//       }
//     }
//   };

//   const createOrFindRegulation = (apiResponse: any): RegulationInfo => {
//     if (apiResponse.metadata && apiResponse.metadata.length > 0) {
//       const topMetadata = apiResponse.metadata[0];
//       return {
//         id: topMetadata.doc_code || "unknown",
//         title: topMetadata.title || "Regulation",
//         content: apiResponse.response,
//         date: topMetadata.revision_date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.departments)
//           ? topMetadata.departments[0].replace(/[\[\]"]/g, "")
//           : "RBI",
//         source: topMetadata.type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: apiResponse.response,
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   const chatStyle = showDetailView ? { maxWidth: "100%" } : { maxWidth: "800px" };

//   return (
//     <div style={chatStyle} className="w-full mx-auto">
//       <ChatView
//         messages={contextMessages}
//         currentRegulation={currentRegulation}
//         setCurrentRegulation={setCurrentRegulation}
//         inputValue={inputValue}
//         setInputValue={setInputValue}
//         handleSubmit={handleSubmit}
//         onViewDetail={onViewDetail}
//         isLoading={isLoading || isContextLoading}
//         loadingPhase={loadingPhase}
//         showDetailView={showDetailView}
//         onShowReferences={handleShowReferences}
//       />
//       <div ref={messagesEndRef} />
//     </div>
//   );
// }

//chatsection.tsx

// import { useState, useEffect, useRef } from "react";
// import { ChatView } from "./ChatView";
// import type { ChatMessage, ReferenceItem, RegulationInfo } from "@/types/types";
// import { regulationsData } from "@/data/regulations";
// import { fetchQueryResponse } from "@/services/queryservice";
// import { UPDAET_SESSION_ENDPOINT } from "@/app/utils/Api";
// import { useAuth } from "@/contexts/AuthContext";
// import { useChat } from "@/contexts/ChatContext";

// interface ChatSectionProps {
//   setActiveView: (view: "home" | "chat") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   initialQuery: string | null;
//   setInitialQuery: (query: string | null) => void;
//   showDetailView: boolean;
//   onFetchReferences: (references: ReferenceItem[]) => void;
// }

// export function ChatSection({
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   initialQuery,
//   setInitialQuery,
//   showDetailView,
//   onFetchReferences,
// }: ChatSectionProps) {
//   const { user } = useAuth();
//   const {
//     currentSessionId,
//     messages: contextMessages,
//     setMessages: setContextMessages,
//     isLoading: isContextLoading,
//     updateSessionTitle,
//   } = useChat();

//   const [inputValue, setInputValue] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [loadingPhase, setLoadingPhase] = useState<
//     "processing" | "fetching" | "analyzing" | "generating" | null
//   >(null);
//   const [sseProcessingSteps, setSSEProcessingSteps] = useState<
//     Map<string, ProcessingStep[]>
//   >(new Map());
//   const [sseEvents, setSSEEvents] = useState<Map<string, SSEEvent[]>>(
//     new Map()
//   );

//   const isProcessingRef = useRef(false);
//   const processedQueriesRef = useRef<Set<string>>(new Set());
//   const mountedRef = useRef(false);
//   const messagesEndRef = useRef<HTMLDivElement>(null);

//   const username = user?.username;

//   // Initial setup
//   useEffect(() => {
//     mountedRef.current = true;

//     // Set the active session in local storage when ChatSection mounts
//     if (currentSessionId && typeof window !== "undefined") {
//       localStorage.setItem("currentSessionId", currentSessionId);
//       localStorage.setItem("activeView", "chat");
//     }

//     return () => {
//       mountedRef.current = false;
//       isProcessingRef.current = false;
//     };
//   }, [currentSessionId]);

//   // When messages load, set initial regulation if available
//   useEffect(() => {
//     if (contextMessages.length > 0 && !currentRegulation) {
//       // Look for the latest assistant message with metadata
//       const latestAssistantMsg = [...contextMessages]
//         .reverse()
//         .find((msg) => msg.sender === "assistant" && msg.metadata);

//       if (latestAssistantMsg && latestAssistantMsg.metadata) {
//         // If the metadata is a RegulationInfo, use it directly
//         if (latestAssistantMsg.regulation) {
//           setCurrentRegulation(latestAssistantMsg.regulation);
//         }
//         // If metadata is array of ReferenceItems, use it for references
//         else if (Array.isArray(latestAssistantMsg.metadata)) {
//           onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);

//           // Create a regulation from the first reference
//           if (latestAssistantMsg.metadata.length > 0) {
//             const meta = latestAssistantMsg.metadata[0];
//             setCurrentRegulation({
//               id: meta.document_code || "unknown",
//               title: meta.title || "Regulation",
//               content: latestAssistantMsg.content,
//               date: meta.date_of_issue || new Date().toDateString(),
//               department:
//                 typeof meta.dept === "string"
//                   ? meta.dept
//                   : Array.isArray(meta.dept) && meta.dept.length > 0
//                   ? meta.dept[0]
//                   : "RBI",
//               source: meta.Type || "Master Direction",
//             });
//           }
//         }
//       }
//     }
//   }, [
//     contextMessages,
//     currentRegulation,
//     setCurrentRegulation,
//     onFetchReferences,
//   ]);

//   // Process initial query if provided
//   useEffect(() => {
//     if (
//       initialQuery &&
//       !isProcessingRef.current &&
//       !processedQueriesRef.current.has(initialQuery)
//     ) {
//       isProcessingRef.current = true;
//       processedQueriesRef.current.add(initialQuery);
//       const queryToProcess = initialQuery;
//       setInitialQuery(null);
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing initialQuery:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   }, [initialQuery]);

//   // Scroll to bottom when messages change
//   useEffect(() => {
//     if (messagesEndRef.current) {
//       messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
//     }
//   }, [contextMessages]);

//   // State to track the currently visible message ID (from both scroll and click)
//   const [currentVisibleMessageId, setCurrentVisibleMessageId] = useState<
//     string | null
//   >(null);

//   // Function to handle reference updates
//   const handleShowReferences = (
//     references: ReferenceItem[],
//     messageId?: string
//   ) => {
//     // Check if references is an array
//     // console.log("reference passed to chatsection");
//     // console.log("refrences at chatsection:",references)
//     if (references && Array.isArray(references)) {
//       // Update the current visible message ID
//       if (messageId) {
//         setCurrentVisibleMessageId(messageId);
//       }

//       // Update the references in the sidebar
//       onFetchReferences(references);
//     } else {
//       console.warn("References data is not in expected format:", references);
//     }
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim() && !isProcessingRef.current) {
//       isProcessingRef.current = true;
//       const queryToProcess = inputValue.trim();
//       setInputValue("");
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing query:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   };

//   const processQuery = async (query: string) => {
//     if (!query.trim() || !mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     // Reset visible message ID when a new query is processed
//     setCurrentVisibleMessageId(null);

//     onFetchReferences([]);

//     const now = new Date();
//     const isDuplicate = contextMessages.some(
//       (msg) =>
//         msg.sender === "user" &&
//         msg.content === query &&
//         now.getTime() - msg.timestamp.getTime() < 500
//     );

//     // Check if this is the initial query for the session
//     const isInitialQuery = contextMessages.length === 0;

//     if (!isDuplicate) {
//       // Generate unique IDs for user message
//       const userMessageId = `user-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const userMessage: ChatMessage = {
//         id: userMessageId,
//         content: query,
//         sender: "user",
//         timestamp: new Date(),
//       };

//       // Update the UI immediately to show the user message
//       setContextMessages([...contextMessages, userMessage]);

//       // Update session title if this is the initial query
//       if (isInitialQuery && currentSessionId && username) {
//         try {
//           // Create a title from the first 15 characters of the query
//           const sessionTitle = query;
//           // updateSessionTitle(currentSessionId, sessionTitle);
//           // Call the update session endpoint
//           const response = await fetch(UPDAET_SESSION_ENDPOINT, {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({
//               session_id: currentSessionId,
//               title: sessionTitle,
//               username: username,
//             }),
//           });
//           // console.log("response", response);
//           const data = await response.json();
//           // console.log(data.title);
//           updateSessionTitle(currentSessionId, data.title);
//           // console.log("Session title updated successfully");
//         } catch (err) {
//           console.error("Error updating session title:", err);
//           // Continue with query processing even if title update fails
//         }
//       }
//     }

//     setIsLoading(true);
//     setLoadingPhase("processing");
//     await new Promise((resolve) => setTimeout(resolve, 5000));
//     if (!mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     setLoadingPhase("fetching");

//     await new Promise((resolve) => setTimeout(resolve, 5000));
//     if (!mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     setLoadingPhase("analyzing");

//     await new Promise((resolve) => setTimeout(resolve, 5000));
//     if (!mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     setLoadingPhase("generating");

//     try {
//       // Use SSE for querying instead of regular fetch
//       const assistantMessageId = `assistant-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       // Clear existing SSE data for the new response
//       setSSEProcessingSteps((prev) => {
//         const newMap = new Map(prev);
//         newMap.delete(assistantMessageId);
//         return newMap;
//       });

//       setSSEEvents((prev) => {
//         const newMap = new Map(prev);
//         newMap.delete(assistantMessageId);
//         return newMap;
//       });

//       // Create a placeholder for assistant response
//       const placeholderMessage: ChatMessage = {
//         id: assistantMessageId,
//         content: "I'm processing your request...",
//         sender: "assistant",
//         timestamp: new Date(),
//         sessionId: currentSessionId,
//         isLoading: true,
//         sseUpdates: [], // Initialize with empty arrays
//         sseEvents: [],
//       };

//       // Add the placeholder message to the chat
//       setContextMessages((prevMessages) => [
//         ...prevMessages,
//         placeholderMessage,
//       ]);
//       // Set up SSE event handlers
//       const abortSSE = fetchQuerySSE(
//         query,
//         currentSessionId,
//         username || "anonymous",
//         {
//           onPipelineStarted: (event) => {
//             console.log("Pipeline started:", event);
//             // First store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               newMap.set(assistantMessageId, [...events, event]);
//               return newMap;
//             });

//             // Then update the message with both the new content and the updated events
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                         ...msg,
//                         content: "Processing your question...",
//                         sseEvents: [event], // Use the event directly
//                       }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onPlanCreated: (event) => {
//             console.log("Plan created:", event);

//             // Store the processing steps
//             const steps = event.plan.steps;
//             setSSEProcessingSteps((prev) => {
//               const newMap = new Map(prev);
//               newMap.set(assistantMessageId, steps);
//               return newMap;
//             });

//             // Store the event
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update loading phase
//             setLoadingPhase("analyzing");

//             // Update the placeholder message with the direct references to the new data
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                         ...msg,
//                         content: "Analyzing relevant regulations...",
//                         sseUpdates: steps, // Use steps directly
//                         sseEvents: [...(msg.sseEvents || []), event], // Combine existing events with new event
//                       }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeStarted: (event) => {
//             console.log("Node started:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update the message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                         ...msg,
//                         sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                       }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeCompleted: (event) => {
//             console.log("Node completed:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update the message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                         ...msg,
//                         sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                       }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeOutput: (event) => {
//             console.log("Node output:", event);

//             // Store the event
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update loading phase based on node status
//             setLoadingPhase("generating");

//             // Update the placeholder message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                         ...msg,
//                         content: "Generating your answer...",
//                         sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                       }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onExecutionCompleted: (event) => {
//             console.log("Execution completed:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Get current SSE processing steps
//             const currentSteps =
//               sseProcessingSteps.get(assistantMessageId) || [];

//             // Get all events including the new completion event
//             const allEvents = [
//               ...(sseEvents.get(assistantMessageId) || []),
//               event,
//             ];

//             // Process the completed response with explicit SSE data
//             const data = {
//               answer: event.answer,
//               sources: event.sources,
//               suggestions: event.suggestions,
//               // Pass explicit SSE data
//               sseData: {
//                 steps: currentSteps,
//                 events: allEvents,
//               },
//             };

//             processSSEResponse(data, assistantMessageId);
//           },
//           onError: (error) => {
//             console.error("SSE error:", error);
//             handleQueryError(assistantMessageId);
//           },
//         }
//       );
//     } catch (error) {
//       console.error("Error fetching response from API:", error);
//       const fallbackRegulation = regulationsData[0];

//       // Generate a unique ID for the fallback message
//       const fallbackMessageId = `assistant-fallback-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const fallbackMessage: ChatMessage = {
//         id: fallbackMessageId,
//         content:
//           "I'm sorry, I couldn't process your request at the moment. Please try again later.",
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: fallbackRegulation,
//       };

//       setContextMessages((prevMessages) => [...prevMessages, fallbackMessage]);
//       onFetchReferences([]);
//     } finally {
//       if (mountedRef.current) {
//         setIsLoading(false);
//         setLoadingPhase(null);
//         isProcessingRef.current = false;
//       }
//     }
//   };

//   // Process SSE response data
//   const processSSEResponse = (data: any, messageId: string) => {
//     try {
//       const metadata = data.answer.metadata
//         .sort((a: any, b: any) => a.rank - b.rank)
//         .slice(0, 5)
//         .map((meta: any) => ({
//           date_of_issue: meta["revision_date_of_issue"],
//           dept: meta["applicable_departments"],
//           document_code: meta["document_id"],
//           chunk_id: meta["chunk_index"],
//           section_summary: meta["document_summary"],
//           pdf_filename: meta["document_title"],
//           pdf_link: meta["revision_pdf_link"],
//           revision_s3_url: meta["s3_url"],
//           Type: meta["document_type"],
//           title: meta["document_title"],
//           id: meta["document_id"],
//           addressee: meta["addressee"],
//           positions: meta["positions"],
//         }));

//       // Pass the metadata to the parent component to update the sidebar
//       onFetchReferences(metadata);

//       // Get SSE data - either from the passed data object or from state
//       const sseSteps =
//         data.sseData?.steps || sseProcessingSteps.get(messageId) || [];
//       const sseAllEvents =
//         data.sseData?.events || sseEvents.get(messageId) || [];

//       console.log("Updating final message with SSE data:", {
//         steps: sseSteps.length,
//         events: sseAllEvents.length,
//       });

//       // Update the existing placeholder message instead of creating a new one
//       setContextMessages((prevMessages) =>
//         prevMessages.map((msg) =>
//           msg.id === messageId
//             ? {
//                 ...msg,
//                 content: data.answer.response,
//                 metadata: metadata,
//                 isLoading: false,
//                 sseUpdates: sseSteps,
//                 sseEvents: sseAllEvents,
//               }
//             : msg
//         )
//       );

//       const relevantRegulation = createOrFindRegulation(data);
//       setCurrentRegulation(relevantRegulation);

//       // Set the new message as the visible one
//       setCurrentVisibleMessageId(messageId);

//       // Reset loading state
//       setIsLoading(false);
//       setLoadingPhase(null);
//       isProcessingRef.current = false;
//     } catch (error) {
//       console.error("Error processing SSE response:", error);
//       handleQueryError(messageId);
//     }
//   };

//   // Handle query errors
//   const handleQueryError = (messageId?: string) => {
//     const fallbackRegulation = regulationsData[0];
//     const errorMessage =
//       "I'm sorry, I couldn't process your request at the moment. Please try again later.";

//     if (messageId && messageId.includes("assistant")) {
//       // Update the existing message with the error
//       setContextMessages((prevMessages) =>
//         prevMessages.map((msg) =>
//           msg.id === messageId
//             ? {
//                 ...msg,
//                 content: errorMessage,
//                 isLoading: false,
//                 metadata: fallbackRegulation,
//               }
//             : msg
//         )
//       );
//     } else {
//       // Generate a unique ID for the fallback message if no ID was provided
//       const fallbackMessageId = `assistant-fallback-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const fallbackMessage: ChatMessage = {
//         id: fallbackMessageId,
//         content: errorMessage,
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: fallbackRegulation,
//       };

//       setContextMessages((prevMessages) => [...prevMessages, fallbackMessage]);
//     }

//     onFetchReferences([]);

//     // Reset loading state
//     setIsLoading(false);
//     setLoadingPhase(null);
//     isProcessingRef.current = false;
//   };

//   const createOrFindRegulation = (apiResponse: any): RegulationInfo => {
//     if (apiResponse.metadata && apiResponse.metadata.length > 0) {
//       const topMetadata = apiResponse.metadata[0];
//       return {
//         id: topMetadata.doc_code || "unknown",
//         title: topMetadata.title || "Regulation",
//         content: apiResponse.response,
//         date: topMetadata.revision_date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.departments)
//           ? topMetadata.departments[0].replace(/[\[\]"]/g, "")
//           : "RBI",
//         source: topMetadata.type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: apiResponse.response,
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   const chatStyle = showDetailView
//     ? { maxWidth: "100%" }
//     : { maxWidth: "800px" };

//   return (
//     <div style={chatStyle} className="w-full mx-auto">
//       <ChatView
//         messages={contextMessages}
//         currentRegulation={currentRegulation}
//         selectedDocCode={selectedDocCode}
//         setselectedDocCode={setselectedDocCode}
//         setCurrentRegulation={setCurrentRegulation}
//         inputValue={inputValue}
//         setInputValue={setInputValue}
//         handleSubmit={handleSubmit}
//         onViewDetail={onViewDetail}
//         isLoading={isLoading || isContextLoading}
//         loadingPhase={loadingPhase}
//         showDetailView={showDetailView}
//         onShowReferences={handleShowReferences}
//       />
//       <div ref={messagesEndRef} />
//     </div>
//   );
// }

// import { useState, useEffect, useRef } from "react";
// import { ChatView } from "./ChatView";
// import type { ChatMessage, ReferenceItem, RegulationInfo } from "@/types/types";
// import { regulationsData } from "@/data/regulations";
// import { fetchQueryResponse } from "@/services/queryservice";
// import { UPDAET_SESSION_ENDPOINT } from "@/app/utils/Api";
// import { useAuth } from "@/contexts/AuthContext";
// import { useChat } from "@/contexts/ChatContext";

// interface ChatSectionProps {
//   setActiveView: (view: "home" | "chat") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   initialQuery: string | null;
//   setInitialQuery: (query: string | null) => void;
//   showDetailView: boolean;
//   onFetchReferences: (references: ReferenceItem[]) => void;
//   setSelectedSpecificRef: (ref: ReferenceItem | null) => void; // Add this line
// }

// export function ChatSection({
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   initialQuery,
//   setInitialQuery,
//   showDetailView,
//   onFetchReferences,
//   setSelectedSpecificRef, // Add this line
// }: ChatSectionProps) {
//   const { user } = useAuth();
//   const {
//     currentSessionId,
//     messages: contextMessages,
//     setMessages: setContextMessages,
//     isLoading: isContextLoading,
//     updateSessionTitle,
//   } = useChat();

//   const [inputValue, setInputValue] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [loadingPhase, setLoadingPhase] = useState<
//     "processing" | "fetching" | "analyzing" | "generating" | null
//   >(null);
//   const [sseProcessingSteps, setSSEProcessingSteps] = useState<
//     Map<string, ProcessingStep[]>
//   >(new Map());
//   const [sseEvents, setSSEEvents] = useState<Map<string, SSEEvent[]>>(
//     new Map()
//   );

//   const isProcessingRef = useRef(false);
//   const processedQueriesRef = useRef<Set<string>>(new Set());
//   const mountedRef = useRef(false);
//   const messagesEndRef = useRef<HTMLDivElement>(null);

//   const username = user?.username;

//   // Initial setup
//   useEffect(() => {
//     mountedRef.current = true;

//     // Set the active session in local storage when ChatSection mounts
//     if (currentSessionId && typeof window !== "undefined") {
//       localStorage.setItem("currentSessionId", currentSessionId);
//       localStorage.setItem("activeView", "chat");
//     }

//     return () => {
//       mountedRef.current = false;
//       isProcessingRef.current = false;
//     };
//   }, [currentSessionId]);

//   // When messages load, set initial regulation if available
//   useEffect(() => {
//     if (contextMessages.length > 0 && !currentRegulation) {
//       // Look for the latest assistant message with metadata
//       const latestAssistantMsg = [...contextMessages]
//         .reverse()
//         .find((msg) => msg.sender === "assistant" && msg.metadata);

//       if (latestAssistantMsg && latestAssistantMsg.metadata) {
//         // If the metadata is a RegulationInfo, use it directly
//         if (latestAssistantMsg.regulation) {
//           setCurrentRegulation(latestAssistantMsg.regulation);
//         }
//         // If metadata is array of ReferenceItems, use it for references
//         else if (Array.isArray(latestAssistantMsg.metadata)) {
//           onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);

//           // Create a regulation from the first reference
//           if (latestAssistantMsg.metadata.length > 0) {
//             const meta = latestAssistantMsg.metadata[0];
//             setCurrentRegulation({
//               id: meta.document_code || "unknown",
//               title: meta.title || "Regulation",
//               content: Array.isArray(latestAssistantMsg.content)
//                 ? (latestAssistantMsg.content as any[]).map(part => part.answer_part).join('\n\n')
//                 : typeof latestAssistantMsg.content === 'string'
//                   ? latestAssistantMsg.content
//                   : JSON.stringify(latestAssistantMsg.content),
//               date: meta.date_of_issue || new Date().toDateString(),
//               department:
//                 typeof meta.dept === "string"
//                   ? meta.dept
//                   : Array.isArray(meta.dept) && meta.dept.length > 0
//                     ? meta.dept[0]
//                     : "RBI",
//               source: meta.Type || "Master Direction",
//             });
//           }
//         }
//       }
//     }
//   }, [
//     contextMessages,
//     currentRegulation,
//     setCurrentRegulation,
//     onFetchReferences,
//   ]);

//   // Process initial query if provided
//   useEffect(() => {
//     if (
//       initialQuery &&
//       !isProcessingRef.current &&
//       !processedQueriesRef.current.has(initialQuery)
//     ) {
//       isProcessingRef.current = true;
//       processedQueriesRef.current.add(initialQuery);
//       const queryToProcess = initialQuery;
//       setInitialQuery(null);
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing initialQuery:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   }, [initialQuery]);

//   // Scroll to bottom when messages change
//   useEffect(() => {
//     if (messagesEndRef.current) {
//       messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
//     }
//   }, [contextMessages]);

//   // State to track the currently visible message ID (from both scroll and click)
//   const [currentVisibleMessageId, setCurrentVisibleMessageId] = useState<
//     string | null
//   >(null);

//   // Function to handle reference updates

// // And update the handleShowReferences function in ChatSection to:
// const handleShowReferences = (
//   references: ReferenceItem[],
//   messageId?: string,
//   specificRef?: ReferenceItem
// ) => {
//   // Check if references is an array
//   if (references && Array.isArray(references)) {
//     // Update the current visible message ID
//     if (messageId) {
//       setCurrentVisibleMessageId(messageId);
//     }

//     // Update the references in the sidebar
//     onFetchReferences(references);

//     // If a specific reference was clicked, set it as the selected document AND store the specific reference
//     if (specificRef) {
//       setselectedDocCode(specificRef.document_id || specificRef.document_code || specificRef.id);
//       setSelectedSpecificRef(specificRef); // Add this line
//     }
//   } else {
//     console.warn("References data is not in expected format:", references);
//   }
// };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim() && !isProcessingRef.current) {
//       isProcessingRef.current = true;
//       const queryToProcess = inputValue.trim();
//       setInputValue("");
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing query:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   };

//   const processQuery = async (query: string) => {
//     if (!query.trim() || !mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     // Reset visible message ID when a new query is processed
//     setCurrentVisibleMessageId(null);

//     onFetchReferences([]);

//     const now = new Date();
//     const isDuplicate = contextMessages.some(
//       (msg) =>
//         msg.sender === "user" &&
//         msg.content === query &&
//         now.getTime() - msg.timestamp.getTime() < 500
//     );

//     // Check if this is the initial query for the session
//     const isInitialQuery = contextMessages.length === 0;

//     if (!isDuplicate) {
//       // Generate unique IDs for user message
//       const userMessageId = `user-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const userMessage: ChatMessage = {
//         id: userMessageId,
//         content: query,
//         sender: "user",
//         timestamp: new Date(),
//       };

//       // Update the UI immediately to show the user message
//       setContextMessages([...contextMessages, userMessage]);

//       // Update session title if this is the initial query
//       if (isInitialQuery && currentSessionId && username) {
//         try {
//           // Create a title from the first 15 characters of the query
//           const sessionTitle = query;
//           // updateSessionTitle(currentSessionId, sessionTitle);
//           // Call the update session endpoint
//           const response = await fetch(UPDAET_SESSION_ENDPOINT, {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({
//               session_id: currentSessionId,
//               title: sessionTitle,
//               username: username,
//             }),
//           });
//           const data = await response.json();
//           updateSessionTitle(currentSessionId, data.title);
//         } catch (err) {
//           console.error("Error updating session title:", err);
//           // Continue with query processing even if title update fails
//         }
//       }
//     }

//     try {
//       // Use SSE for querying instead of regular fetch
//       const assistantMessageId = `assistant-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       // Clear existing SSE data for the new response
//       setSSEProcessingSteps((prev) => {
//         const newMap = new Map(prev);
//         newMap.delete(assistantMessageId);
//         return newMap;
//       });

//       setSSEEvents((prev) => {
//         const newMap = new Map(prev);
//         newMap.delete(assistantMessageId);
//         return newMap;
//       });

//       // Create a placeholder for assistant response
//       const placeholderMessage: ChatMessage = {
//         id: assistantMessageId,
//         content: "I'm processing your request...",
//         sender: "assistant",
//         timestamp: new Date(),
//         sessionId: currentSessionId,
//         isLoading: true,
//         sseUpdates: [], // Initialize with empty arrays
//         sseEvents: [],
//       };

//       // Add the placeholder message to the chat
//       setContextMessages((prevMessages) => [
//         ...prevMessages,
//         placeholderMessage,
//       ]);

//       // Set up SSE event handlers
//       const abortSSE = fetchQuerySSE(
//         query,
//         currentSessionId,
//         username || "anonymous",
//         {
//           onPipelineStarted: (event) => {
//             // console.log("Pipeline started:", event);
//             // First store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               newMap.set(assistantMessageId, [...events, event]);
//               return newMap;
//             });

//             // Then update the message with both the new content and the updated events
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                       ...msg,
//                       content: "Processing your question...",
//                       sseEvents: [event], // Use the event directly
//                     }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onPlanCreated: (event) => {
//             // console.log("Plan created:", event);

//             // Store the processing steps
//             const steps = event.plan.steps;
//             setSSEProcessingSteps((prev) => {
//               const newMap = new Map(prev);
//               newMap.set(assistantMessageId, steps);
//               return newMap;
//             });

//             // Store the event
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update loading phase
//             setLoadingPhase("analyzing");

//             // Update the placeholder message with the direct references to the new data
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                       ...msg,
//                       content: "Analyzing relevant regulations...",
//                       sseUpdates: steps, // Use steps directly
//                       sseEvents: [...(msg.sseEvents || []), event], // Combine existing events with new event
//                     }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeStarted: (event) => {
//             // console.log("Node started:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update the message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                       ...msg,
//                       sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                     }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeCompleted: (event) => {
//             // console.log("Node completed:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update the message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                       ...msg,
//                       sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                     }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onNodeOutput: (event) => {
//             // console.log("Node output:", event);

//             // Store the event
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Update loading phase based on node status
//             setLoadingPhase("generating");

//             // Update the placeholder message with the new event directly
//             setTimeout(() => {
//               setContextMessages((prevMessages) =>
//                 prevMessages.map((msg) =>
//                   msg.id === assistantMessageId
//                     ? {
//                       ...msg,
//                       content: "Generating your answer...",
//                       sseEvents: [...(msg.sseEvents || []), event], // Add the new event to existing events
//                     }
//                     : msg
//                 )
//               );
//             }, 0);
//           },
//           onExecutionCompleted: (event) => {
//             // console.log("Execution completed:", event);

//             // Store the event in state
//             setSSEEvents((prev) => {
//               const newMap = new Map(prev);
//               const events = newMap.get(assistantMessageId) || [];
//               const updatedEvents = [...events, event];
//               newMap.set(assistantMessageId, updatedEvents);
//               return newMap;
//             });

//             // Get current SSE processing steps
//             const currentSteps =
//               sseProcessingSteps.get(assistantMessageId) || [];

//             // Get all events including the new completion event
//             const allEvents = [
//               ...(sseEvents.get(assistantMessageId) || []),
//               event,
//             ];

//             // Process the completed response with explicit SSE data
//             const data = {
//               answer: event.answer, // This will be the array of answer parts
//               sources: event.sources, // Include any global metadata
//               sseData: {
//                 steps: currentSteps,
//                 events: allEvents,
//               },
//             };

//             processSSEResponse(data, assistantMessageId);
//           },
//           onError: (error) => {
//             console.error("SSE error:", error);
//             handleQueryError(assistantMessageId);
//           },
//         }
//       );
//     } catch (error) {
//       console.error("Error fetching response from API:", error);
//       const fallbackRegulation = regulationsData[0];

//       // Generate a unique ID for the fallback message
//       const fallbackMessageId = `assistant-fallback-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const fallbackMessage: ChatMessage = {
//         id: fallbackMessageId,
//         content:
//           "I'm sorry, I couldn't process your request at the moment. Please try again later.",
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: fallbackRegulation,
//       };

//       setContextMessages((prevMessages) => [...prevMessages, fallbackMessage]);
//       onFetchReferences([]);
//     } finally {
//       if (mountedRef.current) {
//         setIsLoading(false);
//         setLoadingPhase(null);
//         isProcessingRef.current = false;
//       }
//     }
//   };

//   // Process SSE response data
//   const processSSEResponse = async (data: any, messageId: string) => {
//     try {
//       // Extract content and metadata from the response
//       let messageContent;
//       let metadata: ReferenceItem[] = [];

//       // console.log("Processing SSE response:", data);

//       // Check if the response has answer.response format (which is an array of answer parts)
//       if (data.answer && data.answer.response && Array.isArray(data.answer.response)) {
//         // This is the new format with array of answer_parts in answer.response
//         messageContent = data.answer.response;
//       } else if (data.answer && typeof data.answer.response === 'string') {
//         // Handle old format with string response
//         messageContent = data.answer.response;
//       } else {
//         // Fallback for any other format
//         console.error("Unexpected response format:", data.answer);
//         messageContent = "Unable to process the response. Please try again.";
//       }

//       // Extract metadata from the response
//       // Extract references from answer_part_references and group by doc_id + page_number
//       if (data.answer && data.answer.response && Array.isArray(data.answer.response)) {
//         // Collect all answer_part_references
//         let allReferences: any[] = [];

//         data.answer.response.forEach((part: any) => {
//           if (part.answer_part_references && Array.isArray(part.answer_part_references) && part.answer_part_references.length > 0) {
//             allReferences = [...allReferences, ...part.answer_part_references];
//           }
//         });

//         if (allReferences.length > 0) {
//           // Group references by document_id + page_number
//           const groupMap = new Map<string, any>();

//           allReferences.forEach(ref => {
//             const docId = ref.document_id;
//             const pageNum = ref.page_number?.toString() || "1";
//             const key = `${docId}-${pageNum}`;

//             if (!docId) return;

//             if (!groupMap.has(key)) {
//               // Find full metadata for this document
//               let fullMetadata = null;
//               if (data.answer && data.answer.metadata && Array.isArray(data.answer.metadata)) {
//                 fullMetadata = data.answer.metadata.find((meta: any) => {
//                   const cleanedDocId = ref.document_id?.trim();
//                   return (
//                     meta.document_id?.trim() === cleanedDocId ||
//                     meta.document_id?.trim()?.startsWith(cleanedDocId)
//                   );
//                 });
//               }

//               // Skip references without s3_url
//               if (!fullMetadata?.s3_url) {
//                 return;
//               }

//               // Create the grouped reference
//               groupMap.set(key, {
//                 date_of_issue: fullMetadata["revision_date_of_issue"],
//                 dept: fullMetadata["applicable_departments"],
//                 document_code: fullMetadata["document_id"],
//                 chunk_id: parseInt(pageNum) || 0,
//                 section_summary: fullMetadata["document_summary"],
//                 pdf_filename: fullMetadata["document_title"],
//                 pdf_link: fullMetadata["revision_pdf_link"],
//                 revision_s3_url: fullMetadata["s3_url"],
//                 Type: fullMetadata["document_type"],
//                 title: fullMetadata["document_title"],
//                 id: fullMetadata["document_id"],
//                 addressee: fullMetadata["addressee"],
//                 positions: ref.bbox ? [{
//                   page: parseInt(pageNum) || 0,
//                   bbox: ref.bbox
//                 }] : [],
//               });
//             } else {
//               // If this document and page already exists, add any new bbox positions if they don't already exist
//               const existing = groupMap.get(key);
//               if (ref.bbox) {
//                 // Check if this bbox already exists
//                 const bboxExists = existing.positions.some((pos: any) =>
//                   JSON.stringify(pos.bbox) === JSON.stringify(ref.bbox)
//                 );

//                 if (!bboxExists) {
//                   existing.positions.push({
//                     page: parseInt(pageNum) || 0,
//                     bbox: ref.bbox
//                   });
//                 }
//               }
//             }
//           });

//           // Convert map to array
//           metadata = Array.from(groupMap.values());
//         }
//       }

//       // console.log("grouped metadata at chatsection", metadata);
//       // Pass the grouped metadata to the parent component to update the sidebar
//       onFetchReferences(metadata);

//       // Get SSE data
//       const sseSteps = data.sseData?.steps || sseProcessingSteps.get(messageId) || [];
//       const sseAllEvents = data.sseData?.events || sseEvents.get(messageId) || [];

//       // console.log("Updating final message with content:", messageContent);

//       // Update the message with the appropriate content
//       setContextMessages((prevMessages) =>
//         prevMessages.map((msg) =>
//           msg.id === messageId
//             ? {
//               ...msg,
//               content: messageContent,
//               metadata: metadata,
//               isLoading: false,
//               sseUpdates: sseSteps,
//               sseEvents: sseAllEvents,
//             }
//             : msg
//         )
//       );

//       // Create a regulation from the metadata
//       const relevantRegulation = createOrFindRegulation(data);
//       setCurrentRegulation(relevantRegulation);

//       // Set the new message as the visible one
//       setCurrentVisibleMessageId(messageId);

//       // Reset loading state
//       setIsLoading(false);
//       setLoadingPhase(null);
//       isProcessingRef.current = false;
//     } catch (error) {
//       console.error("Error processing SSE response:", error);
//       handleQueryError(messageId);
//     }
//   };

//   // Handle query errors
//   const handleQueryError = (messageId?: string) => {
//     const fallbackRegulation = regulationsData[0];
//     const errorMessage =
//       "I'm sorry, I couldn't process your request at the moment. Please try again later.";

//     if (messageId && messageId.includes("assistant")) {
//       // Update the existing message with the error
//       setContextMessages((prevMessages) =>
//         prevMessages.map((msg) =>
//           msg.id === messageId
//             ? {
//               ...msg,
//               content: errorMessage,
//               isLoading: false,
//               metadata: fallbackRegulation,
//             }
//             : msg
//         )
//       );
//     } else {
//       // Generate a unique ID for the fallback message if no ID was provided
//       const fallbackMessageId = `assistant-fallback-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const fallbackMessage: ChatMessage = {
//         id: fallbackMessageId,
//         content: errorMessage,
//         sender: "assistant",
//         timestamp: new Date(),
//         metadata: fallbackRegulation,
//       };

//       setContextMessages((prevMessages) => [...prevMessages, fallbackMessage]);
//     }

//     onFetchReferences([]);

//     // Reset loading state
//     setIsLoading(false);
//     setLoadingPhase(null);
//     isProcessingRef.current = false;
//   };

//   const createOrFindRegulation = (apiResponse: any): RegulationInfo => {
//     if (apiResponse.metadata && apiResponse.metadata.length > 0) {
//       const topMetadata = apiResponse.metadata[0];
//       return {
//         id: topMetadata.document_code || topMetadata.document_id || "unknown",
//         title: topMetadata.title || "Regulation",
//         content: Array.isArray(apiResponse.answer)
//           ? apiResponse.answer.map((part: any) => part.answer_part).join('\n\n')
//           : typeof apiResponse.answer === 'object' && apiResponse.answer?.response
//             ? apiResponse.answer.response
//             : "Regulation information not available",
//         date: topMetadata.date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.dept)
//           ? topMetadata.dept[0].replace(/[\[\]"]/g, "")
//           : typeof topMetadata.dept === "string"
//             ? topMetadata.dept.replace(/[\[\]"]/g, "")
//             : "RBI",
//         source: topMetadata.Type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: Array.isArray(apiResponse.answer)
//         ? apiResponse.answer.map((part: any) => part.answer_part).join('\n\n')
//         : typeof apiResponse.answer === 'object' && apiResponse.answer?.response
//           ? apiResponse.answer.response
//           : "Regulation information not available",
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   const chatStyle = showDetailView
//     ? { maxWidth: "100%" }
//     : { maxWidth: "800px" };

//   return (
//     <div style={chatStyle} className="w-full mx-auto">
//       <ChatView
//   messages={contextMessages}
//   currentRegulation={currentRegulation}
//   selectedDocCode={selectedDocCode}
//   setselectedDocCode={setselectedDocCode}
//   setCurrentRegulation={setCurrentRegulation}
//   inputValue={inputValue}
//   setInputValue={setInputValue}
//   handleSubmit={handleSubmit}
//   onViewDetail={onViewDetail}
//   isLoading={isLoading || isContextLoading}
//   loadingPhase={loadingPhase}
//   showDetailView={showDetailView}
//   onShowReferences={handleShowReferences} // This now handles the 3rd parameter
// />
//       <div ref={messagesEndRef} />
//     </div>
//   );
// }

// import { useState, useEffect, useRef } from "react";
// import { ChatView } from "./ChatView";
// import type { ChatMessage, ReferenceItem, RegulationInfo } from "@/types/types";
// import { regulationsData } from "@/data/regulations";
// import { fetchQuerySSE, ExecutionCompletedEvent } from "@/services/sseQueryService";
// import { UPDAET_SESSION_ENDPOINT } from "@/app/utils/Api";
// import { useAuth } from "@/contexts/AuthContext";
// import { useChat } from "@/contexts/ChatContext";

// interface ChatSectionProps {
//   setActiveView: (view: "home" | "chat") => void;
//   currentRegulation: RegulationInfo | null;
//   selectedDocCode: string | null;
//   setselectedDocCode: (doccode: string | null) => void;
//   setCurrentRegulation: (regulation: RegulationInfo | null) => void;
//   onViewDetail: () => void;
//   initialQuery: string | null;
//   setInitialQuery: (query: string | null) => void;
//   showDetailView: boolean;
//   onFetchReferences: (references: ReferenceItem[]) => void;
//   setSelectedSpecificRef: (ref: ReferenceItem | null) => void;
// }

// export function ChatSection({
//   setActiveView,
//   currentRegulation,
//   selectedDocCode,
//   setselectedDocCode,
//   setCurrentRegulation,
//   onViewDetail,
//   initialQuery,
//   setInitialQuery,
//   showDetailView,
//   onFetchReferences,
//   setSelectedSpecificRef,
// }: ChatSectionProps) {
//   const { user } = useAuth();
//   const {
//     currentSessionId,
//     messages: contextMessages,
//     setMessages: setContextMessages,
//     updateSessionTitle,
//   } = useChat();

//   const [inputValue, setInputValue] = useState("");

//   const isProcessingRef = useRef(false);
//   const processedQueriesRef = useRef<Set<string>>(new Set());
//   const mountedRef = useRef(false);
//   const messagesEndRef = useRef<HTMLDivElement>(null);

//   const username = user?.username;

//   // Initial setup
//   useEffect(() => {
//     mountedRef.current = true;

//     // Set the active session in local storage when ChatSection mounts
//     if (currentSessionId && typeof window !== "undefined") {
//       localStorage.setItem("currentSessionId", currentSessionId);
//       localStorage.setItem("activeView", "chat");
//     }

//     return () => {
//       mountedRef.current = false;
//       isProcessingRef.current = false;
//     };
//   }, [currentSessionId]);

//   // When messages load, set initial regulation if available
//   useEffect(() => {
//     if (contextMessages.length > 0 && !currentRegulation) {
//       // Look for the latest assistant message with metadata
//       const latestAssistantMsg = [...contextMessages]
//         .reverse()
//         .find((msg) => msg.sender === "assistant" && msg.metadata);

//       if (latestAssistantMsg && latestAssistantMsg.metadata) {
//         // If the metadata is a RegulationInfo, use it directly
//         if (latestAssistantMsg.regulation) {
//           setCurrentRegulation(latestAssistantMsg.regulation);
//         }
//         // If metadata is array of ReferenceItems, use it for references
//         else if (Array.isArray(latestAssistantMsg.metadata)) {
//           onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);

//           // Create a regulation from the first reference
//           if (latestAssistantMsg.metadata.length > 0) {
//             const meta = latestAssistantMsg.metadata[0];
//             setCurrentRegulation({
//               id: meta.document_code || "unknown",
//               title: meta.title || "Regulation",
//               content: Array.isArray(latestAssistantMsg.content)
//                 ? (latestAssistantMsg.content as any[]).map(part => part.answer_part).join('\n\n')
//                 : typeof latestAssistantMsg.content === 'string'
//                   ? latestAssistantMsg.content
//                   : JSON.stringify(latestAssistantMsg.content),
//               date: meta.date_of_issue || new Date().toDateString(),
//               department:
//                 typeof meta.dept === "string"
//                   ? meta.dept
//                   : Array.isArray(meta.dept) && meta.dept.length > 0
//                     ? meta.dept[0]
//                     : "RBI",
//               source: meta.Type || "Master Direction",
//             });
//           }
//         }
//       }
//     }
//   }, [
//     contextMessages,
//     currentRegulation,
//     setCurrentRegulation,
//     onFetchReferences,
//   ]);

//   // Process initial query if provided
//   useEffect(() => {
//     if (
//       initialQuery &&
//       !isProcessingRef.current &&
//       !processedQueriesRef.current.has(initialQuery)
//     ) {
//       isProcessingRef.current = true;
//       processedQueriesRef.current.add(initialQuery);
//       const queryToProcess = initialQuery;
//       setInitialQuery(null);
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing initialQuery:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   }, [initialQuery]);

//   // Scroll to bottom when messages change
//   useEffect(() => {
//     if (messagesEndRef.current) {
//       messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
//     }
//   }, [contextMessages]);

//   // State to track the currently visible message ID (from both scroll and click)
//   const [currentVisibleMessageId, setCurrentVisibleMessageId] = useState<
//     string | null
//   >(null);

//   // Function to handle reference updates
//   const handleShowReferences = (
//     references: ReferenceItem[],
//     messageId?: string,
//     specificRef?: ReferenceItem
//   ) => {
//     // Check if references is an array
//     if (references && Array.isArray(references)) {
//       // Update the current visible message ID
//       if (messageId) {
//         setCurrentVisibleMessageId(messageId);
//       }

//       // Update the references in the sidebar
//       onFetchReferences(references);

//       // If a specific reference was clicked, set it as the selected document AND store the specific reference
//       if (specificRef) {
//         setselectedDocCode(specificRef.document_id || specificRef.document_code || specificRef.id);
//         setSelectedSpecificRef(specificRef);
//       }
//     } else {
//       console.warn("References data is not in expected format:", references);
//     }
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (inputValue.trim() && !isProcessingRef.current) {
//       isProcessingRef.current = true;
//       const queryToProcess = inputValue.trim();
//       setInputValue("");
//       processQuery(queryToProcess).catch((err) => {
//         console.error("Error processing query:", err);
//         isProcessingRef.current = false;
//       });
//     }
//   };

//   const processQuery = async (query: string) => {
//     if (!query.trim() || !mountedRef.current) {
//       isProcessingRef.current = false;
//       return;
//     }

//     // Reset visible message ID when a new query is processed
//     setCurrentVisibleMessageId(null);
//     onFetchReferences([]);

//     const now = new Date();
//     const isDuplicate = contextMessages.some(
//       (msg) =>
//         msg.sender === "user" &&
//         msg.content === query &&
//         now.getTime() - msg.timestamp.getTime() < 500
//     );

//     // Check if this is the initial query for the session
//     const isInitialQuery = contextMessages.length === 0;

//     if (!isDuplicate) {
//       // Generate unique IDs for user message
//       const userMessageId = `user-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       const userMessage: ChatMessage = {
//         id: userMessageId,
//         content: query,
//         sender: "user",
//         timestamp: new Date(),
//       };

//       // Update the UI immediately to show the user message
//       setContextMessages([...contextMessages, userMessage]);

//       // Update session title if this is the initial query
//       if (isInitialQuery && currentSessionId && username) {
//         try {
//           // Create a title from the query
//           const sessionTitle = query;
//           // Call the update session endpoint
//           const response = await fetch(UPDAET_SESSION_ENDPOINT, {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({
//               session_id: currentSessionId,
//               title: sessionTitle,
//               username: username,
//             }),
//           });
//           const data = await response.json();
//           updateSessionTitle(currentSessionId, data.title);
//         } catch (err) {
//           console.error("Error updating session title:", err);
//           // Continue with query processing even if title update fails
//         }
//       }
//     }

//     try {
//       // Generate unique ID for assistant response
//       const assistantMessageId = `assistant-${Date.now()}-${Math.random()
//         .toString(36)
//         .substring(2, 9)}`;

//       // Set up SSE event handlers - only handle execution_completed
//       const abortSSE = fetchQuerySSE(
//         query,
//         currentSessionId,
//         username || "anonymous",
//         {
//           onExecutionCompleted: (event: ExecutionCompletedEvent) => {
//             console.log("Execution completed:", event);
//             processSSEResponse(event, assistantMessageId);
//           },
//           onError: (error: Error) => {
//             console.error("SSE error:", error);
//             handleQueryError(assistantMessageId);
//           },
//         }
//       );
//     } catch (error) {
//       console.error("Error fetching response from API:", error);
//       handleQueryError();
//     }
//   };

//   // Process SSE response data - simplified for execution_completed only
//   const processSSEResponse = async (event: ExecutionCompletedEvent, messageId: string) => {
//     try {
//       // Extract content and metadata from the response
//       let messageContent;
//       let metadata: ReferenceItem[] = [];

//       console.log("Processing SSE response:", event);

//       // Check if the response has answer.response format (which is an array of answer parts)
//       if (event.answer && event.answer.response && Array.isArray(event.answer.response)) {
//         // This is the new format with array of answer_parts in answer.response
//         messageContent = event.answer.response;
//       } else if (event.answer && typeof event.answer.response === 'string') {
//         // Handle old format with string response
//         messageContent = event.answer.response;
//       } else {
//         // Fallback for any other format
//         console.error("Unexpected response format:", event.answer);
//         messageContent = "Unable to process the response. Please try again.";
//       }

//       // Extract metadata from the response
//       if (event.answer && event.answer.response && Array.isArray(event.answer.response)) {
//         // Collect all answer_part_references
//         let allReferences: any[] = [];

//         event.answer.response.forEach((part: any) => {
//           if (part.answer_part_references && Array.isArray(part.answer_part_references) && part.answer_part_references.length > 0) {
//             allReferences = [...allReferences, ...part.answer_part_references];
//           }
//         });

//         if (allReferences.length > 0) {
//           // Group references by document_id + page_number
//           const groupMap = new Map<string, any>();

//           allReferences.forEach(ref => {
//             const docId = ref.document_id;
//             const pageNum = ref.page_number?.toString() || "1";
//             const key = `${docId}-${pageNum}`;

//             if (!docId) return;

//             if (!groupMap.has(key)) {
//               // Find full metadata for this document
//               let fullMetadata = null;
//               if (event.answer && event.answer.metadata && Array.isArray(event.answer.metadata)) {
//                 fullMetadata = event.answer.metadata.find((meta: any) => {
//                   const cleanedDocId = ref.document_id?.trim();
//                   return (
//                     meta.document_id?.trim() === cleanedDocId ||
//                     meta.document_id?.trim()?.startsWith(cleanedDocId)
//                   );
//                 });
//               }

//               // Skip references without s3_url
//               if (!fullMetadata?.s3_url) {
//                 return;
//               }

//               // Create the grouped reference
//               groupMap.set(key, {
//                 date_of_issue: fullMetadata["revision_date_of_issue"],
//                 dept: fullMetadata["applicable_departments"],
//                 document_code: fullMetadata["document_id"],
//                 chunk_id: parseInt(pageNum) || 0,
//                 section_summary: fullMetadata["document_summary"],
//                 pdf_filename: fullMetadata["document_title"],
//                 pdf_link: fullMetadata["revision_pdf_link"],
//                 revision_s3_url: fullMetadata["s3_url"],
//                 Type: fullMetadata["document_type"],
//                 title: fullMetadata["document_title"],
//                 id: fullMetadata["document_id"],
//                 addressee: fullMetadata["addressee"],
//                 positions: ref.bbox ? [{
//                   page: parseInt(pageNum) || 0,
//                   bbox: ref.bbox
//                 }] : [],
//               });
//             } else {
//               // If this document and page already exists, add any new bbox positions
//               const existing = groupMap.get(key);
//               if (ref.bbox) {
//                 // Check if this bbox already exists
//                 const bboxExists = existing.positions.some((pos: any) =>
//                   JSON.stringify(pos.bbox) === JSON.stringify(ref.bbox)
//                 );

//                 if (!bboxExists) {
//                   existing.positions.push({
//                     page: parseInt(pageNum) || 0,
//                     bbox: ref.bbox
//                   });
//                 }
//               }
//             }
//           });

//           // Convert map to array
//           metadata = Array.from(groupMap.values());
//         }
//       }

//       // Pass the grouped metadata to the parent component to update the sidebar
//       onFetchReferences(metadata);

//       // Update the message with the final content
//       setContextMessages((prevMessages) => [
//         ...prevMessages,
//         {
//           id: messageId,
//           content: messageContent,
//           metadata: metadata,
//           sender: "assistant",
//           timestamp: new Date(),
//           sessionId: currentSessionId,
//         }
//       ]);

//       // Create a regulation from the metadata
//       const relevantRegulation = createOrFindRegulation(event);
//       setCurrentRegulation(relevantRegulation);

//       // Set the new message as the visible one
//       setCurrentVisibleMessageId(messageId);

//       // Reset processing state
//       isProcessingRef.current = false;
//     } catch (error) {
//       console.error("Error processing SSE response:", error);
//       handleQueryError(messageId);
//     }
//   };

//   // Handle query errors
//   const handleQueryError = (messageId?: string) => {
//     const fallbackRegulation = regulationsData[0];
//     const errorMessage =
//       "I'm sorry, I couldn't process your request at the moment. Please try again later.";

//     // Generate a unique ID for the error message
//     const errorMessageId = messageId || `assistant-error-${Date.now()}-${Math.random()
//       .toString(36)
//       .substring(2, 9)}`;

//     const errorChatMessage: ChatMessage = {
//       id: errorMessageId,
//       content: errorMessage,
//       sender: "assistant",
//       timestamp: new Date(),
//       metadata: fallbackRegulation,
//     };

//     setContextMessages((prevMessages) => [...prevMessages, errorChatMessage]);
//     onFetchReferences([]);

//     // Reset processing state
//     isProcessingRef.current = false;
//   };

//   const createOrFindRegulation = (event: ExecutionCompletedEvent): RegulationInfo => {
//     if (event.answer && event.answer.metadata && event.answer.metadata.length > 0) {
//       const topMetadata = event.answer.metadata[0];
//       return {
//         id: topMetadata.document_code || topMetadata.document_id || "unknown",
//         title: topMetadata.title || "Regulation",
//         content: Array.isArray(event.answer.response)
//           ? event.answer.response.map((part: any) => part.answer_part).join('\n\n')
//           : typeof event.answer.response === 'string'
//             ? event.answer.response
//             : "Regulation information not available",
//         date: topMetadata.date_of_issue || new Date().toDateString(),
//         department: Array.isArray(topMetadata.dept)
//           ? topMetadata.dept[0].replace(/[\[\]"]/g, "")
//           : typeof topMetadata.dept === "string"
//             ? topMetadata.dept.replace(/[\[\]"]/g, "")
//             : "RBI",
//         source: topMetadata.Type || "Master Direction",
//       };
//     }
//     return {
//       id: `regulation-${Date.now()}`,
//       title: "Regulation Information",
//       content: Array.isArray(event.answer?.response)
//         ? event.answer.response.map((part: any) => part.answer_part).join('\n\n')
//         : typeof event.answer?.response === 'string'
//           ? event.answer.response
//           : "Regulation information not available",
//       date: new Date().toDateString(),
//       department: "RBI",
//       source: "Master Direction",
//     };
//   };

//   const chatStyle = showDetailView
//     ? { maxWidth: "100%" }
//     : { maxWidth: "800px" };

//   return (
//     <div style={chatStyle} className="w-full mx-auto">
//       <ChatView
//         messages={contextMessages}
//         currentRegulation={currentRegulation}
//         selectedDocCode={selectedDocCode}
//         setselectedDocCode={setselectedDocCode}
//         setCurrentRegulation={setCurrentRegulation}
//         inputValue={inputValue}
//         setInputValue={setInputValue}
//         handleSubmit={handleSubmit}
//         onViewDetail={onViewDetail}
//         showDetailView={showDetailView}
//         onShowReferences={handleShowReferences}
//       />
//       <div ref={messagesEndRef} />
//     </div>
//   );
// }


//chatsection.tsx
import { useState, useEffect, useRef } from "react";
import { ChatView } from "./ChatView";
import type { ChatMessage, ReferenceItem, RegulationInfo, ChatSectionProps} from "@/types/main_types";
import { regulationsData } from "@/data/regulations";
import {fetchQuerySSE, ExecutionCompletedEvent,} from "@/services/ssequeryservice";
import { UPDAET_SESSION_ENDPOINT } from "@/app/utils/Api";
import { useAuth } from "@/contexts/AuthContext";
import { useChat } from "@/contexts/ChatContext";

export function ChatSection({
  setActiveView,
  currentRegulation,
  selectedDocCode,
  setselectedDocCode,
  setCurrentRegulation,
  onViewDetail,
  initialQuery,
  setInitialQuery,
  showDetailView,
  onFetchReferences,
  setSelectedSpecificRef,
  chatContainerRef,
  onClose,
}: ChatSectionProps) {
  const { user } = useAuth();
  const {
    currentSessionId,
    messages: contextMessages,
    setMessages: setContextMessages,
    updateSessionTitle,
  } = useChat();

  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPhase, setLoadingPhase] = useState<
    "processing" | "fetching" | "analyzing" | "generating" | null
  >(null);

  const isProcessingRef = useRef(false);
  const processedQueriesRef = useRef<Set<string>>(new Set());
  const mountedRef = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const loadingPhaseTimerRef = useRef<NodeJS.Timeout | null>(null);

  const username = user?.username;

  // Initial setup
  useEffect(() => {
    mountedRef.current = true;

    // Set the active session in local storage when ChatSection mounts
    if (currentSessionId && typeof window !== "undefined") {
      localStorage.setItem("currentSessionId", currentSessionId);
      localStorage.setItem("activeView", "chat");
    }

    return () => {
      mountedRef.current = false;
      isProcessingRef.current = false;
      // Clear all timers on unmount
      if (loadingPhaseTimerRef.current) {
        clearTimeout(loadingPhaseTimerRef.current);
        loadingPhaseTimerRef.current = null;
      }
    };
  }, [currentSessionId]);

  // When messages load, set initial regulation if available
  useEffect(() => {
    if (contextMessages.length > 0 && !currentRegulation) {
      // Look for the latest assistant message with metadata
      const latestAssistantMsg = [...contextMessages]
        .reverse()
        .find((msg) => msg.sender === "assistant" && msg.metadata);

      if (latestAssistantMsg && latestAssistantMsg.metadata) {
        // If the metadata is a RegulationInfo, use it directly
        if (latestAssistantMsg.regulation) {
          setCurrentRegulation(latestAssistantMsg.regulation);
        }
        // If metadata is array of ReferenceItems, use it for references
        else if (Array.isArray(latestAssistantMsg.metadata)) {
          onFetchReferences(latestAssistantMsg.metadata as ReferenceItem[]);

          // Create a regulation from the first reference
          if (latestAssistantMsg.metadata.length > 0) {
            const meta = latestAssistantMsg.metadata[0];
            setCurrentRegulation({
              id: meta.document_code || "unknown",
              title: meta.title || "Regulation",
              content: Array.isArray(latestAssistantMsg.content)
                ? (latestAssistantMsg.content as any[])
                    .map((part) => part.answer_part)
                    .join("\n\n")
                : typeof latestAssistantMsg.content === "string"
                ? latestAssistantMsg.content
                : JSON.stringify(latestAssistantMsg.content),
              date: meta.date_of_issue || new Date().toDateString(),
              department:
                typeof meta.dept === "string"
                  ? meta.dept
                  : Array.isArray(meta.dept) && meta.dept.length > 0
                  ? meta.dept[0]
                  : "RBI",
              source: meta.Type || "Master Direction",
            });
          }
        }
      }
    }
  }, [
    contextMessages,
    currentRegulation,
    setCurrentRegulation,
    onFetchReferences,
  ]);

  // Process initial query if provided
  useEffect(() => {
    if (
      initialQuery &&
      !isProcessingRef.current &&
      !processedQueriesRef.current.has(initialQuery)
    ) {
      processedQueriesRef.current.add(initialQuery);
      const queryToProcess = initialQuery;
      setInitialQuery(null);

      // Set processing ref to true AFTER starting loading phases for initial query
      isProcessingRef.current = true;

      processQuery(queryToProcess).catch((err) => {
        console.error("Error processing initialQuery:", err);
        stopLoadingPhases();
        isProcessingRef.current = false;
      });
    }
  }, [initialQuery]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [contextMessages]);

  // State to track the currently visible message ID (from both scroll and click)
  const [currentVisibleMessageId, setCurrentVisibleMessageId] = useState<
    string | null
  >(null);

  // Function to handle reference updates
  const handleShowReferences = (
    references: ReferenceItem[],
    messageId?: string,
    specificRef?: ReferenceItem
  ) => {
    // Check if references is an array
    if (references && Array.isArray(references)) {
      // Update the current visible message ID
      if (messageId) {
        setCurrentVisibleMessageId(messageId);
      }

      // Update the references in the sidebar
      onFetchReferences(references);

      // If a specific reference was clicked, set it as the selected document AND store the specific reference
      if (specificRef) {
        setselectedDocCode(
          specificRef.document_id || specificRef.document_code || specificRef.id
        );
        setSelectedSpecificRef(specificRef);
      }
    } else {
      console.warn("References data is not in expected format:", references);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isProcessingRef.current) {
      isProcessingRef.current = true;
      const queryToProcess = inputValue.trim();
      setInputValue("");
      processQuery(queryToProcess).catch((err) => {
        console.error("Error processing query:", err);
        isProcessingRef.current = false;
      });
    }
  };

  // Function to start loading phase progression
  const startLoadingPhases = () => {
    setIsLoading(true);
    setLoadingPhase("fetching");

    // Clear any existing timers
    if (loadingPhaseTimerRef.current) {
      clearTimeout(loadingPhaseTimerRef.current);
    }

    // Phase 1: Retrieving documents (6 seconds)
    loadingPhaseTimerRef.current = setTimeout(() => {
      if (mountedRef.current && isProcessingRef.current) {
        // console.log("📝 Phase 2: processing");
        setLoadingPhase("processing");

        // Phase 2: Processing information (6 seconds)
        loadingPhaseTimerRef.current = setTimeout(() => {
          // console.log("⏰ Phase 2 timer - checking conditions:");
          // console.log("mountedRef.current:", mountedRef.current);
          // console.log("isProcessingRef.current:", isProcessingRef.current);

          if (mountedRef.current && isProcessingRef.current) {
            // console.log("📝 Phase 3: analyzing");
            setLoadingPhase("analyzing");

            // Phase 3: Analyzing information (6 seconds)
            loadingPhaseTimerRef.current = setTimeout(() => {
              // console.log("⏰ Phase 3 timer - checking conditions:");
              // console.log("mountedRef.current:", mountedRef.current);
              // console.log("isProcessingRef.current:", isProcessingRef.current);

              if (mountedRef.current && isProcessingRef.current) {
                // console.log("📝 Phase 4: generating");
                setLoadingPhase("generating");
                // Phase 4: Generating answer (continues until completion)
              }
            }, 6000);
          }
        }, 6000);
      } else {
        // console.log("❌ Phase 1 timer conditions not met");
      }
    }, 6000);
  };

  // Function to stop loading phases
  const stopLoadingPhases = () => {
    setIsLoading(false);
    setLoadingPhase(null);
    isProcessingRef.current = false;

    // Clear all timers
    if (loadingPhaseTimerRef.current) {
      clearTimeout(loadingPhaseTimerRef.current);
      loadingPhaseTimerRef.current = null;
    }
  };

  const processQuery = async (query: string) => {
    onClose();
    if (!query.trim() || !mountedRef.current) {
      isProcessingRef.current = false;
      return;
    }

    // console.log("🎯 Processing query:", query);

    // Reset visible message ID when a new query is processed
    setCurrentVisibleMessageId(null);
    onFetchReferences([]);

    const now = new Date();
    const isDuplicate = contextMessages.some(
      (msg) =>
        msg.sender === "user" &&
        msg.content === query &&
        now.getTime() - msg.timestamp.getTime() < 500
    );

    // Check if this is the initial query for the session
    const isInitialQuery = contextMessages.length === 0;

    if (!isDuplicate) {
      // Generate unique IDs for user message
      const userMessageId = `user-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;

      const userMessage: ChatMessage = {
        id: userMessageId,
        content: query,
        sender: "user",
        timestamp: new Date(),
      };

      // Update the UI immediately to show the user message
      setContextMessages([...contextMessages, userMessage]);

      // Update session title if this is the initial query
      if (isInitialQuery && currentSessionId && username) {
        try {
          // Create a title from the query
          const sessionTitle = query;
          // Call the update session endpoint
          const response = await fetch(UPDAET_SESSION_ENDPOINT, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              session_id: currentSessionId,
              title: sessionTitle,
              username: username,
            }),
          });
          const data = await response.json();
          updateSessionTitle(currentSessionId, data.title);
        } catch (err) {
          console.error("Error updating session title:", err);
          // Continue with query processing even if title update fails
        }
      }
    }

    try {
      // Start loading phases and ensure isProcessingRef is true for timer conditions
      // console.log("🚀 Starting loading phases for query:", query);
      // console.log("isProcessingRef.current before startLoadingPhases:", isProcessingRef.current);

      // For regular queries, set processing ref to true here
      if (!isProcessingRef.current) {
        isProcessingRef.current = true;
      }

      startLoadingPhases();

      // Generate unique ID for assistant response
      const assistantMessageId = `assistant-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;

      // Set up SSE event handlers - only handle execution_completed
      const abortSSE = fetchQuerySSE(
        query,
        currentSessionId,
        username || "anonymous",
        {
          onExecutionCompleted: (event: ExecutionCompletedEvent) => {
            // console.log("✅ Execution completed:", event);
            // Stop loading phases when we get the response
            stopLoadingPhases();
            processSSEResponse(event, assistantMessageId);
          },
          onError: (error: Error) => {
            // console.error("❌ SSE error:", error);
            // Stop loading phases on error
            stopLoadingPhases();
            handleQueryError(assistantMessageId);
          },
        }
      );
    } catch (error) {
      console.error("Error fetching response from API:", error);
      stopLoadingPhases();
      handleQueryError();
    }
  };

  // Process SSE response data - simplified for execution_completed only
  const processSSEResponse = async (
    event: ExecutionCompletedEvent,
    messageId: string
  ) => {
    try {
      // Extract content and metadata from the response
      let messageContent;
      let metadata: ReferenceItem[] = [];

      console.log("Processing SSE response:", event);

      // Check if the response has answer.response format (which is an array of answer parts)
      if (
        event.answer &&
        event.answer.response &&
        Array.isArray(event.answer.response)
      ) {
        // This is the new format with array of answer_parts in answer.response
        messageContent = event.answer.response;
      } else if (event.answer && typeof event.answer.response === "string") {
        // Handle old format with string response
        messageContent = event.answer.response;
      } else {
        // Fallback for any other format
        console.error("Unexpected response format:", event.answer);
        messageContent = "Unable to process the response. Please try again.";
      }

      // Extract metadata from the response
      if (
        event.answer &&
        event.answer.response &&
        Array.isArray(event.answer.response)
      ) {
        // Collect all answer_part_references
        let allReferences: any[] = [];

        event.answer.response.forEach((part: any) => {
          if (
            part.answer_part_references &&
            Array.isArray(part.answer_part_references) &&
            part.answer_part_references.length > 0
          ) {
            allReferences = [...allReferences, ...part.answer_part_references];
          }
        });

        if (allReferences.length > 0) {
          // Group references by document_id + page_number
          const groupMap = new Map<string, any>();

          allReferences.forEach((ref) => {
            const docId = ref.document_id;
            const pageNum = ref.page_number?.toString() || "1";
            const key = `${docId}-${pageNum}`;

            if (!docId) return;

            if (!groupMap.has(key)) {
              // Find full metadata for this document
              let fullMetadata = null;
              if (
                event.answer &&
                event.answer.metadata &&
                Array.isArray(event.answer.metadata)
              ) {
                fullMetadata = event.answer.metadata.find((meta: any) => {
                  const cleanedDocId = ref.document_id?.trim();
                  return (
                    meta.document_id?.trim() === cleanedDocId ||
                    meta.document_id?.trim()?.startsWith(cleanedDocId)
                  );
                });
              }

              // Skip references without s3_url
              if (!fullMetadata?.s3_url) {
                return;
              }

              // Create the grouped reference
              groupMap.set(key, {
                date_of_issue: fullMetadata["revision_date_of_issue"],
                dept: fullMetadata["applicable_departments"],
                document_code: fullMetadata["document_number"],
                chunk_id: parseInt(pageNum) || 0,
                section_summary: fullMetadata["document_summary"],
                pdf_filename: fullMetadata["document_title"],
                pdf_link: fullMetadata["revision_pdf_link"],
                revision_s3_url: fullMetadata["s3_url"],
                Type: fullMetadata["document_type"],
                title: fullMetadata["document_title"],
                id: fullMetadata["document_id"],
                addressee: fullMetadata["addressee"],
                positions: ref.bboxes
                  ? [
                      {
                        page: parseInt(pageNum) || 0,
                        bboxes: ref.bboxes,
                      },
                    ]
                  : [],
              });
            } else {
              // If this document and page already exists, add any new bbox positions
              const existing = groupMap.get(key);
              if (ref.bbox) {
                // Check if this bbox already exists
                const bboxExists = existing.positions.some(
                  (pos: any) =>
                    JSON.stringify(pos.bboxes) === JSON.stringify(ref.bboxes)
                );

                if (!bboxExists) {
                  existing.positions.push({
                    page: parseInt(pageNum) || 0,
                    bboxes: ref.bboxes,
                  });
                }
              }
            }
          });

          // Convert map to array
          metadata = Array.from(groupMap.values());
        }
      }

      // Pass the grouped metadata to the parent component to update the sidebar
      onFetchReferences(metadata);

      // Update the message with the final content
      setContextMessages((prevMessages) => [
        ...prevMessages,
        {
          id: messageId,
          content: messageContent,
          metadata: metadata,
          sender: "assistant",
          timestamp: new Date(),
          sessionId: currentSessionId,
        },
      ]);

      // Create a regulation from the metadata
      const relevantRegulation = createOrFindRegulation(event);
      setCurrentRegulation(relevantRegulation);

      // Set the new message as the visible one
      setCurrentVisibleMessageId(messageId);

      // Reset processing state
      isProcessingRef.current = false;
    } catch (error) {
      console.error("Error processing SSE response:", error);
      handleQueryError(messageId);
    }
  };

  // Handle query errors
  const handleQueryError = (messageId?: string) => {
    const fallbackRegulation = regulationsData[0];
    const errorMessage =
      "I'm sorry, I couldn't process your request at the moment. Please try again later.";

    // Generate a unique ID for the error message
    const errorMessageId =
      messageId ||
      `assistant-error-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;

    const errorChatMessage: ChatMessage = {
      id: errorMessageId,
      content: errorMessage,
      sender: "assistant",
      timestamp: new Date(),
      metadata: fallbackRegulation,
    };

    setContextMessages((prevMessages) => [...prevMessages, errorChatMessage]);
    onFetchReferences([]);

    // Reset processing state
    isProcessingRef.current = false;
  };

  const createOrFindRegulation = (
    event: ExecutionCompletedEvent
  ): RegulationInfo => {
    if (
      event.answer &&
      event.answer.metadata &&
      event.answer.metadata.length > 0
    ) {
      const topMetadata = event.answer.metadata[0];
      return {
        id: topMetadata.document_code || topMetadata.document_id || "unknown",
        title: topMetadata.title || "Regulation",
        content: Array.isArray(event.answer.response)
          ? event.answer.response
              .map((part: any) => part.answer_part)
              .join("\n\n")
          : typeof event.answer.response === "string"
          ? event.answer.response
          : "Regulation information not available",
        date: topMetadata.date_of_issue || new Date().toDateString(),
        department: Array.isArray(topMetadata.dept)
          ? topMetadata.dept[0].replace(/[\[\]"]/g, "")
          : typeof topMetadata.dept === "string"
          ? topMetadata.dept.replace(/[\[\]"]/g, "")
          : "RBI",
        source: topMetadata.Type || "Master Direction",
      };
    }
    return {
      id: `regulation-${Date.now()}`,
      title: "Regulation Information",
      content: Array.isArray(event.answer?.response)
        ? event.answer.response
            .map((part: any) => part.answer_part)
            .join("\n\n")
        : typeof event.answer?.response === "string"
        ? event.answer.response
        : "Regulation information not available",
      date: new Date().toDateString(),
      department: "RBI",
      source: "Master Direction",
    };
  };

  const chatStyle = showDetailView
    ? { maxWidth: "100%" }
    : { maxWidth: "800px" };

  return (
    <div style={chatStyle} className="w-full mx-auto">
      <ChatView
        messages={contextMessages}
        currentRegulation={currentRegulation}
        selectedDocCode={selectedDocCode}
        setselectedDocCode={setselectedDocCode}
        setCurrentRegulation={setCurrentRegulation}
        inputValue={inputValue}
        setInputValue={setInputValue}
        handleSubmit={handleSubmit}
        onViewDetail={onViewDetail}
        isLoading={isLoading}
        loadingPhase={loadingPhase}
        showDetailView={showDetailView}
        onShowReferences={handleShowReferences}
        chatContainerRef={chatContainerRef}
      />
      <div ref={messagesEndRef} />
    </div>
  );
}
