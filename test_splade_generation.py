#!/usr/bin/env python3
"""
Test SPLADE vector generation to see if it's working
"""

def test_splade_generation():
    """Test if SPLADE vector generation is working"""

    # Configure HTTP backend for Hugging Face to bypass SSL issues
    try:
        import requests
        from huggingface_hub import configure_http_backend

        def create_session_factory():
            def session_factory():
                session = requests.Session()
                session.verify = False  # Bypass SSL verification
                return session
            return session_factory

        # Configure the HTTP backend
        configure_http_backend(backend_factory=create_session_factory())
        print("Configured Hugging Face HTTP backend to bypass SSL verification")
    except ImportError:
        print("Could not configure Hugging Face HTTP backend - SSL issues may occur")

    try:
        from transformers import AutoTokenizer, AutoModelForMaskedLM
        import torch
        from qdrant_client import models

        print("Testing SPLADE vector generation...")

        # Initialize SPLADE model
        model_name = "naver/efficient-splade-VI-BT-large-doc"
        print(f"Loading model: {model_name}")

        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForMaskedLM.from_pretrained(model_name)

        # Test text
        test_text = "This is a test document about banking regulations and compliance requirements."
        print(f"Test text: {test_text}")

        # Tokenize
        inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=512)

        # Generate SPLADE vector
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits

            # Apply ReLU and log transformation
            sparse_vector = torch.log(1 + torch.relu(logits)) * inputs.attention_mask.unsqueeze(-1)
            sparse_vector = torch.max(sparse_vector, dim=1)[0].squeeze()

            # Convert to sparse format
            indices = torch.nonzero(sparse_vector).squeeze(-1).tolist()
            values = sparse_vector[indices].tolist()

        # Create Qdrant SparseVector
        qdrant_sparse_vector = models.SparseVector(indices=indices, values=values)

        print(f"✅ SPLADE vector generated successfully!")
        print(f"   - Type: {type(qdrant_sparse_vector)}")
        print(f"   - Number of indices: {len(qdrant_sparse_vector.indices)}")
        print(f"   - Number of values: {len(qdrant_sparse_vector.values)}")
        print(f"   - Sample indices: {qdrant_sparse_vector.indices[:10]}")
        print(f"   - Sample values: {qdrant_sparse_vector.values[:10]}")

        return True

    except Exception as e:
        print(f"❌ SPLADE vector generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_splade_generation()
