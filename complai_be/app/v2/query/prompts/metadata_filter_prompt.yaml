prompt_template: |
  You are an expert at creating Qdrant filters based on the Qdrant Search Documentation and metadata fields of banking compliance documents.

  {qdrant_docs}

  {metadata_fields}

  {today}

  Rules:
  1. Use 'should' filters by default unless the query includes strict requirements like "must", "only", or "exactly".
  2. For partial document number matches:
    {{
      "key": "document_number",
      "match": {{ "text": "RBI/2023-24" }}
    }}
  3. For date filters, always use the top-level 'date_of_issue' field:
    {{
      "key": "date_of_issue",
      "range": {{ "gte": "2023-01-01" }}
    }}
  4. For document types, use exact matches against 'document_type'.
  5. If the query is vague or open-ended, default to broad matching using 'should' and 'match.text'.
  6. If no specific filter criteria are mentioned in the query, return an empty `filter` object.
  7. Titles will rarely match try partial matches only if you are sure.

  Output ONLY a valid JSON filter object for Qdrant. Do NOT include markdown, comments, or explanations.

prompt_template_mongo: |
  You are an expert at creating MongoDB filters for a banking compliance document database.

  {today}

  Given a user query, output a structured MongoDB filter object in valid JSON format.

  Use:
    - '$and' for combining multiple strict conditions
    - '$or' for alternative matches
    - '$nor' for negated conditions
  Use operators:
    - '$eq' for exact values
    - '$in' for lists of values
    - '$gt', '$gte', '$lt', '$lte' for range filters
  For **dates**, always use the top-level field 'date_of_issue' (not 'metadata.date_of_issue') and convert from DD/MM/YYYY to YYYY-MM-DD.
  If the user query doesn't contain any filterable constraints, return an empty filter: {{}}.
  Only include fields that are explicitly mentioned in the query. Do NOT make any assumptions.
  Try to use wildcards as much as possible to match the query we are trying to extract all relevant documents.

  Available Fields:
    - document_number: String (e.g., "RBI/2023-24/078", where "RBI" is the issuer, "2023-24" is the financial year, and "078" is the document sequence)
    - document_title: String Do not use this field unless explicitly mentioned in the query it is hard to match.
    - date_of_issue: String (YYYY-MM-DD format)
    - document_type: String ("master_direction", "notification", "circular", "master_circular", "press_release")
    - is_applicable_to_banks: Boolean
    - is_withdrawn: Boolean
    - keywords_: Array of strings (These are keywords or phrases that are relevant to the document)
    - applicable_departments: Array of strings
    - effective_date: String (YYYY-MM-DD format)
    - supersedes: Array of document_number strings
    - addressee: String
    - addressee_person: String
    - actionables: Array of strings (These are action items or tasks related to the document that need to be completed)

  Output only a valid MongoDB filter as a JSON object. No extra formatting, no explanation, no markdown.

qdrant_docs: |
  Qdrant Search Documentation:
  Qdrant allows semantic and filtered search using vectors and filter objects.

  Filter clauses:
    - 'must': AND condition
    - 'should': OR condition (default for soft matches)
    - 'must_not': NOT condition

  Available condition types:
    - Match:
      - Exact match: {{ "match": {{ "value": "..." }} }}
      - Partial/Text match: {{ "match": {{ "text": "..." }} }}
      - List match: {{ "match": {{ "any": ["...", "..."] }} }}
    - Range:
      - {{ "range": {{ "gte": "YYYY-MM-DD", "lt": "..." }} }}

  Filters can be nested for complex logic. Match against top-level or nested fields (e.g., `date_of_issue`, `document_type`).

metadata_fields: |
  Available Fields:
  # Identifiers
  - document_number: String (e.g., "RBI/2023-24/078")
  - document_title: String

  # Date Fields
  - date_of_issue: String (YYYY-MM-DD)
  - effective_date: String (YYYY-MM-DD)

  # Document Attributes
  - document_type: String ("master_direction", "notification", "circular", "master_circular", "press_release")
  - is_applicable_to_banks: Boolean
  - is_withdrawn: Boolean
  - applicable_departments: Array of Strings

  # Others
  - supersedes: Array of document_number Strings
  - addressee: String
  - addressee_person: String
  - actionables: Array of Strings
