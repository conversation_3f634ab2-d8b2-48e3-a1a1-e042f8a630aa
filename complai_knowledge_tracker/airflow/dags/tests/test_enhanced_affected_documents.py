#!/usr/bin/env python3
"""
Test the enhanced affected documents extraction with improved prompt
"""
import sys
from pathlib import Path

# Add the parent directory to the path to import prompts
sys.path.append(str(Path(__file__).parent.parent))

def test_enhanced_prompt_structure():
    """Test that the enhanced prompt includes all the new requirements"""
    print("🧪 Testing Enhanced Prompt Structure...")
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        
        # Check for key improvements in the prompt
        improvements = [
            "IDENTIFY ALL AFFECTED DOCUMENTS",
            "EXTRACT COMPREHENSIVE DOCUMENT IDENTIFIERS", 
            "DETERMINE PRECISE ACTIONS",
            "SPECIAL ATTENTION TO",
            "Amendment notifications: Find the ORIGINAL document",
            "Supersession notifications: Identify BOTH the old document",
            "document_title",
            "reference_number",
            "department",
            "reasoning",
            "superseded_documents",
            "amendment_details",
            "effective_date"
        ]
        
        missing_improvements = []
        for improvement in improvements:
            if improvement not in AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT:
                missing_improvements.append(improvement)
        
        if missing_improvements:
            print(f"❌ Missing improvements: {missing_improvements}")
            return False
        
        print("✅ Enhanced prompt includes all required improvements")
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced prompt: {e}")
        return False


def test_enhanced_models():
    """Test the enhanced DocumentAction and AffectedDocumentsResult models"""
    print("🧪 Testing Enhanced Models...")
    
    try:
        from prompts.notification_categorizer import DocumentAction, AffectedDocumentsResult
        
        # Test enhanced DocumentAction with all fields
        doc_action = DocumentAction(
            document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03",
            action_type="UPDATE_DOCUMENT",
            confidence="high",
            document_title="Master Direction on Credit Card Operations for Banks",
            reference_number="DBOD.No.Leg.BC.21/09.07.007/2002-03",
            department="DBOD",
            original_date="2003-03-15",
            reasoning="Document needs updates due to new amendments in transaction limits"
        )
        
        print("✅ Enhanced DocumentAction created with all fields")
        print(f"   📄 Document ID: {doc_action.document_id}")
        print(f"   📋 Title: {doc_action.document_title}")
        print(f"   🏢 Department: {doc_action.department}")
        print(f"   📅 Original Date: {doc_action.original_date}")
        print(f"   💭 Reasoning: {doc_action.reasoning}")
        
        # Test enhanced AffectedDocumentsResult with all fields
        result = AffectedDocumentsResult(
            document_actions=[doc_action],
            document_keywords=["credit card", "banking operations", "transaction limits"],
            has_new_document_link=True,
            new_document_url="https://rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=12345",
            rbi_links=["https://rbi.org.in/page1", "https://rbi.org.in/page2"],
            processing_notes="Successfully identified affected documents with comprehensive metadata",
            requires_manual_review=False,
            superseded_documents=["Old Credit Card Circular No. 456/2020"],
            amendment_details="Updates to section 5.2 regarding transaction limits and section 7.1 for KYC requirements",
            effective_date="2024-01-01"
        )
        
        print("✅ Enhanced AffectedDocumentsResult created with all fields")
        print(f"   📊 Document Actions: {len(result.document_actions)}")
        print(f"   🔄 Superseded Documents: {result.superseded_documents}")
        print(f"   📝 Amendment Details: {result.amendment_details}")
        print(f"   📅 Effective Date: {result.effective_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced models: {e}")
        return False


def test_real_notification_example():
    """Test with a realistic RBI notification example"""
    print("🧪 Testing Real Notification Example...")
    
    # Simulate a real RBI notification
    sample_notification = {
        "title": "Amendment to Master Direction on Credit Card Operations for Banks",
        "content": """
        <div>
        <p><strong>RBI/2024-25/123</strong></p>
        <p><strong>DBOD.No.Leg.BC.45/09.07.007/2024-25</strong></p>
        <p>Date: January 15, 2024</p>
        <p>All Scheduled Commercial Banks</p>
        
        <p>Dear Sir/Madam,</p>
        
        <p><strong>Amendment to Master Direction on Credit Card Operations for Banks dated April 21, 2022</strong></p>
        
        <p>Please refer to the Master Direction on Credit Card Operations for Banks 
        (DBOD.No.Leg.BC.21/09.07.007/2002-03 dated March 15, 2003) as updated from time to time.</p>
        
        <p>In order to enhance customer protection and improve operational efficiency, 
        it has been decided to amend paragraph 5.2 of the said Master Direction relating to 
        transaction limits and introduce new provisions in paragraph 7.1 regarding KYC requirements.</p>
        
        <p>The amendments shall come into effect from April 1, 2024.</p>
        
        <p>This supersedes Circular No. 456/2020 dated June 10, 2020 on the same subject.</p>
        
        <p>Please acknowledge receipt.</p>
        
        <p>Yours faithfully,</p>
        <p>(Chief General Manager)</p>
        </div>
        """,
        "category": "Amendment"
    }
    
    try:
        from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
        
        # Format the prompt with the sample notification
        formatted_prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
            title=sample_notification["title"],
            content=sample_notification["content"],
            category=sample_notification["category"]
        )
        
        print("✅ Successfully formatted prompt with real notification")
        print(f"   📄 Title: {sample_notification['title']}")
        print(f"   📂 Category: {sample_notification['category']}")
        print(f"   📏 Prompt Length: {len(formatted_prompt)} characters")
        
        # Expected extractions from this notification:
        expected_extractions = {
            "main_document": "DBOD.No.Leg.BC.21/09.07.007/2002-03",
            "superseded_document": "Circular No. 456/2020",
            "effective_date": "2024-04-01",
            "amendment_details": "paragraph 5.2 relating to transaction limits and paragraph 7.1 regarding KYC requirements"
        }
        
        print("✅ Expected extractions identified:")
        for key, value in expected_extractions.items():
            print(f"   🎯 {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing real notification example: {e}")
        return False


def main():
    """Run all enhanced affected documents tests"""
    print("🚀 Testing Enhanced Affected Documents Extraction")
    print("=" * 60)
    
    tests = [
        test_enhanced_prompt_structure,
        test_enhanced_models,
        test_real_notification_example
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced affected documents extraction is ready.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    main()
