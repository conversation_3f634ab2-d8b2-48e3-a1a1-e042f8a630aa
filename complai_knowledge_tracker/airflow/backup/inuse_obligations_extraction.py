"""
dag_rbi_obligations_extraction.py
---------------------------------
An Airflow DAG to:
  - Query DocumentDB for RBI regulation documents (master_direction/master_circular).
  - Process the Markdown content using a LangGraph-based flow to extract obligations and generate action items.
  - Compile the results into an Excel (.xlsx) file.
  - Upload the Excel file to S3.
  - Update the DocumentDB record with the S3 URL.
"""

import os
import uuid
import json
import logging
from datetime import datetime
from typing import List
import requests

from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
import boto3
from pymongo import MongoClient
from openpyxl import Workbook
from pydantic import BaseModel
from openai import OpenAI

# ------------------------------------------------------------------------------
# Global Clients and Configuration
# ------------------------------------------------------------------------------
# Airflow Variables (set these in your Airflow environment)
openai_api_key = Variable.get("openai_api_key")
documentdb_uri = Variable.get("documentdb_uri")  # e.g., "************************:port/"
s3_bucket_name = Variable.get("s3_bucket_name")   # e.g., "rbi-actionables"
aws_access_key_id = Variable.get("aws_access_key_id")
aws_secret_access_key = Variable.get("aws_secret_access_key")

# Initialize OpenAI client
client_openai = OpenAI(api_key=openai_api_key)

# DocumentDB configuration
DOCUMENTDB_DB = "rbi_regulations_db"
DOCUMENTDB_COLLECTION = "rbi_regulations"
# Logger
logger = logging.getLogger("airflow.task")

try:
    response = requests.get("https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem")
    response.raise_for_status()
    with open("/tmp/global-bundle.pem", "wb") as file:
        file.write(response.content)
    logger.info("Downloaded TLS CA file successfully.")
except Exception as e:
    logger.error(f"Error downloading TLS CA file: {e}")
    raise


# ------------------------------------------------------------------------------
# Pydantic Models & Extraction Functions
# ------------------------------------------------------------------------------
class ObligationExtraction(BaseModel):
    circular_number: str
    circular_title: str
    para: str
    obligation: str

def extract_obligations_from_markdown(markdown_text: str) -> List[ObligationExtraction]:
    """
    Sends the entire markdown text to OpenAI structured output API call and extracts a JSON array of obligations.
    Each obligation has the fields:
      - 'Circular Number'
      - 'Circular Title'
      - 'Para'
      - 'Obligation'
    """
    prompt = (
    "Role: You are an expert compliance analyst with extensive experience in RBI regulatory documents. "
    "Your task is to extract all obligations from the provided RBI regulation markdown document.\n\n"
    
    "Goal: Extract a JSON array where each object contains exactly the following fields:\n"
    "  - 'Circular Number': the reference number of the circular (string)\n"
    "  - 'Circular Title': the full title of the regulation (string)\n"
    "  - 'Para': the paragraph number or section identifier (string)\n"
    "  - 'Obligation': the text of the obligation (string)\n\n"
    
    "Instructions:\n"
    "1. Read and understand the entire markdown document provided below.\n"
    "2. Identify the Circular Number and Circular Title from the document header.\n"
    "3. Process the document to locate each paragraph or section that contains a regulatory obligation. "
    "An obligation is typically indicated by language such as 'shall', 'must', 'required to', 'obligated', or 'need to'.\n"
    "4. For each obligation, capture:\n"
    "   - 'Para': the paragraph or section identifier (e.g., '2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3')\n"
    "   - 'Obligation': the complete text of the obligation statement.\n"
    "5. Ensure that the output JSON array contains objects with exactly the following keys: 'Circular Number', "
    "'Circular Title', 'Para', and 'Obligation'. Do not include any additional fields.\n"
    "6. If a paragraph contains multiple obligations, list each obligation as a separate object while repeating the "
    "Circular Number and Circular Title.\n\n"
    
    "Example:\n\n"
    "Consider the following sample regulation details:\n\n"
    "---------------------------------------------------------\n"
    "Circular Number: RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\n"
    "Circular Title: Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\n"
    "Para: 2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3\n"
    "Obligation: Banknotes tendered over the counter shall be examined for authenticity through machines. "
    "No credit to customer's account is to be given for Counterfeit Notes, if any, detected in the tender received over the counter or at the back-office / currency chest. "
    "In no case, the Counterfeit Notes should be returned to the tenderer or destroyed by the bank branches / treasuries. "
    "Failure of the banks to impound Counterfeit Notes detected at their end will be construed as wilful involvement of the bank concerned in circulating Counterfeit Notes and penalty will be imposed.\n"
    "---------------------------------------------------------\n\n"
    
    "Another sample entry:\n\n"
    "---------------------------------------------------------\n"
    "Circular Number: RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\n"
    "Circular Title: Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\n"
    "Para: 3. Impounding of Counterfeit Notes\n"
    "Obligation: Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE'.\n"
    "---------------------------------------------------------\n\n"
    
    "Based on the above, your output should be a JSON array like the following:\n\n"
    
    "[\n"
    "  {\n"
    "    \"Circular Number\": \"RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\",\n"
    "    \"Circular Title\": \"Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\",\n"
    "    \"Para\": \"2. Detection of Counterfeit Notes and subsections: 2.1, 2.2, 2.3\",\n"
    "    \"Obligation\": \"Banknotes tendered over the counter shall be examined for authenticity through machines. No credit to customer's account is to be given for Counterfeit Notes, if any, detected in the tender received over the counter or at the back-office / currency chest. In no case, the Counterfeit Notes should be returned to the tenderer or destroyed by the bank branches / treasuries. Failure of the banks to impound Counterfeit Notes detected at their end will be construed as wilful involvement of the bank concerned in circulating Counterfeit Notes and penalty will be imposed.\"\n"
    "  },\n"
    "  {\n"
    "    \"Circular Number\": \"RBI/DCM/2024-25/115 DCM (FNVD)/G4/16.01.05/2024-25\",\n"
    "    \"Circular Title\": \"Master Direction on Counterfeit Notes, 2024 - Detection, Reporting and Monitoring\",\n"
    "    \"Para\": \"3. Impounding of Counterfeit Notes\",\n"
    "    \"Obligation\": \"Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE'.\"\n"
    "  }\n"
    "]\n\n"
    
    "Go through the markdown vigilently and generate the obligations and actions all the time "
    "Markdown Document:\n"
    f"{markdown_text}"
    )

    
    completion = client_openai.beta.chat.completions.parse(
        model="gpt-4.1-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert at extracting structured information from regulatory documents."},
            {"role": "user", "content": prompt}
        ],
        response_format=List[ObligationExtraction],
    )
    obligations = completion.choices[0].message.parsed
    print("obligations : ", obligations)
    return obligations

def generate_action_item(obligation_text: str) -> str:
    """
    Generates an action item for a given obligation using an OpenAI API call.
    """
    prompt = (
        f"Based on the following regulatory obligation, generate a concise action item for the compliance office to verify and implement:\n\n"
        f"Obligation: {obligation_text}\n\n"
        "Return only the action item as plain text."
    )
    completion = client_openai.beta.chat.completions.create(
        model="gpt-4.1-2025-04-14",
        messages=[
            {"role": "system", "content": "You are an expert compliance advisor."},
            {"role": "user", "content": prompt}
        ]
    )
    action_item = completion.choices[0].message.content.strip()
    return action_item

# ------------------------------------------------------------------------------
# Airflow DAG Definition
# ------------------------------------------------------------------------------
default_args = {
    "owner": "airflow",
    "start_date": datetime(2025, 1, 1),
    "retries": 0,
}

with DAG(
    "rbi_obligations_extraction",
    default_args=default_args,
    schedule_interval="@daily",
    catchup=False,
    description="Extract obligations and generate action items from RBI regulation documents",
) as dag:
    
    @task
    def fetch_rbi_documents() -> List[dict]:
        """
        Connects to DocumentDB and fetches RBI regulation documents of type master_direction or master_circular.
        """
        client = MongoClient(documentdb_uri,
            tls=True,
            tlsCAFile="/tmp/global-bundle.pem",
            replicaSet="rs0",
            readPreference="secondaryPreferred",
            retryWrites=False
        )
        db = client["rbi_regulations_db"]
        collection = db["rbi_regulations"]
        query = {"document_type": {"$in": ["master_direction", "master_circular"]},
                "actionables_excel_url": {"$exists": False},
                "markdown": {"$exists": True, "$ne": ""}}
        documents = list(collection.find(query))
        client.close()
        logger.info(f"Fetched {len(documents)} documents from DocumentDB.")
        return documents

    @task
    def process_documents(documents: List[dict]) -> List[dict]:
        """
        For each document, process the markdown content to extract obligations and generate action items.
        Returns a list of dictionaries with each document's id and its list of processed obligations.
        """
        processed_results = []
        for doc in documents:
            markdown_text = doc.get("markdown", "")
            if not markdown_text:
                continue
            try:
                print("got markdown_text : ", markdown_text)
                obligations = extract_obligations_from_markdown(markdown_text)
            except Exception as e:
                logger.error(f"Error extracting obligations for document {doc.get('_id')}: {e}")
                obligations = []
            processed_obligations = []
            for obj in obligations:
                action_item = generate_action_item(obj.obligation)
                processed_obligations.append({
                    "circular_number": obj.circular_number,
                    "circular_title": obj.circular_title,
                    "para": obj.para,
                    "obligation": obj.obligation,
                    "action_item": action_item
                })
            processed_results.append({
                "document_id": str(doc.get("_id")),
                "obligations": processed_obligations
            })
        return processed_results

    @task
    def compile_excel(processed_docs: List[dict]) -> str:
        """
        Compiles the processed obligations and action items into an Excel (.xlsx) file.
        Returns the local file path of the generated Excel file.
        """
        wb = Workbook()
        ws = wb.active
        ws.title = "Obligations & Action Items"
        # Header row
        headers = ["Document ID", "Circular Number", "Circular Title", "Para", "Obligation", "Action Item"]
        ws.append(headers)
        for doc in processed_docs:
            doc_id = doc.get("document_id")
            for obligation in doc.get("obligations", []):
                row = [
                    doc_id,
                    obligation.get("circular_number"),
                    obligation.get("circular_title"),
                    obligation.get("para"),
                    obligation.get("obligation"),
                    obligation.get("action_item")
                ]
                ws.append(row)
        file_path = f"/tmp/rbi_obligations_{uuid.uuid4()}.xlsx"
        wb.save(file_path)
        logger.info(f"Excel file saved to {file_path}")
        return file_path

    @task
    def upload_excel_to_s3(file_path: str) -> str:
        """
        Uploads the generated Excel file to S3 and returns its S3 URL.
        """
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        file_name = os.path.basename(file_path)
        s3_key = f"actionables/{file_name}"
        s3_client.upload_file(file_path, s3_bucket_name, s3_key)
        s3_url = f"https://{s3_bucket_name}.s3.amazonaws.com/{s3_key}"
        logger.info(f"Uploaded Excel file to {s3_url}")
        return s3_url

    @task
    def update_documentdb_with_excel(s3_url: str, documents: List[dict]) -> None:
        """
        Updates each DocumentDB record with the S3 URL of the generated Excel file.
        """
        client = MongoClient(documentdb_uri,
            tls=True,
            tlsCAFile="/tmp/global-bundle.pem",
            replicaSet="rs0",
            readPreference="secondaryPreferred",
            retryWrites=False
        )
        db = client[DOCUMENTDB_DB]
        collection = db[DOCUMENTDB_COLLECTION]
        for doc in documents:
            if doc.get("document_type") in ["master_direction", "master_circular"]:
                collection.update_one({"_id": doc.get("_id")}, {"$set": {"actionables_excel_url": s3_url}})
        client.close()
        logger.info("Updated DocumentDB records with the Excel file S3 URL.")
    
    # DAG flow: fetch -> process -> compile -> upload -> update
    documents = fetch_rbi_documents()
    processed_docs = process_documents(documents)
    excel_file = compile_excel(processed_docs)
    s3_url = upload_excel_to_s3(excel_file)
    update_documentdb_with_excel(s3_url, documents)

# End of DAG
