import logging
from app.v2.query.retrieval.mongo_client import get_mongo_collection
from app.v2.query.models import DocumentChunk
from typing import List, Dict, Any

class ContextExpander:
    """
    Expands context by fetching full document and paragraph details from MongoDB
    for each retrieved point. Used to enrich the context for downstream answer generation.
    Generated by Copilot.
    """
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.mongo_coll = get_mongo_collection()

    def run(self, retrieved_points: List[Dict[str, Any]], mongo_filter: Dict[str, Any] = None) -> list:
        """
        For each retrieved point, fetches the corresponding document from MongoDB,
        extracts the relevant paragraph, and builds a context dictionary.
        Generated by Copilot.
        """
        if mongo_filter is None:
            mongo_filter = {}
        self.logger.info(f"Expanding context for {len(retrieved_points)} retrieved points")
        self.logger.info(f"Using MongoDB filter: {mongo_filter}")

        contexts = []
        for pt in retrieved_points:
            try:
                # Validate and parse DocumentChunk if needed
                if not isinstance(pt, DocumentChunk):
                    doc_chunk = DocumentChunk.model_validate({
                        "metadata": pt.get("metadata", {}),
                        "page_content": pt.get("page_content", ""),
                        **pt
                    })
                else:
                    doc_chunk = pt

                doc_id = doc_chunk.metadata.document_id
                para_index = getattr(doc_chunk.metadata, "paragraph_index", None) or getattr(doc_chunk.metadata, "chunk_index", 0) or 0

                # Combine document ID filter with generated metadata filter
                combined_filter = {"_id": doc_id}
                combined_filter.update(mongo_filter)
                print(f"Combined filter for MongoDB: {combined_filter}")
                doc = self.mongo_coll.find_one(combined_filter)
                print(f"MongoDB document: {doc}")
                if not doc:
                    self.logger.warning(f"Document not found in MongoDB: {doc_id} with filter {combined_filter}")
                    continue

                paragraphs = doc.get("content", "").split("\n\n")
                text = paragraphs[para_index] if para_index < len(paragraphs) else ""

                doc_metadata = doc.get("metadata", {})
                context = {
                    "document_id": doc_id,
                    "document_title": doc.get("document_title", doc_chunk.metadata.document_title or doc_id),
                    "paragraph_index": para_index,
                    "text": text,
                    "s3_url": doc.get("s3_url", ""),
                    "metadata": {
                        "document_type": doc_metadata.get("document_type", doc_chunk.metadata.document_type),
                        "is_applicable_to_banks": doc_metadata.get("is_applicable_to_banks", doc_chunk.metadata.is_applicable_to_banks),
                        "date_of_issue": doc_metadata.get("date_of_issue", doc_chunk.metadata.date_of_issue)
                    }
                }
                # print(f"Context from ContextExpander: {context}")
                self.logger.debug(f"Expanded context for document: {doc_id}, paragraph: {para_index}")
                contexts.append(context)
            except Exception as e:
                self.logger.error(f"Error expanding context for point: {pt} - {e}")

        self.logger.info(f"Context expansion complete. Total contexts: {len(contexts)}")
        return contexts
# Generated by Copilot