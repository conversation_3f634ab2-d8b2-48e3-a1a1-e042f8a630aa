from airflow import DAG
from airflow.operators.python_operator import PythonOperator  # Keep existing import style
from airflow.models import Variable
from datetime import datetime, timedelta
import boto3
import logging
import json
import requests
from pymongo import MongoClient
import ast

from utils.doc_db import download_tls_ca_file
from utils.mistral_utils import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Default arguments
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 1, 1),
    'retries': 1,
    'retry_delay': timedelta(minutes=5)
}

def list_s3_files():
    """List all files in the S3 bucket"""
    logger.info("Starting S3 file listing process")
    aws_access_key_id = Variable.get("aws_access_key_id")
    aws_secret_access_key = Variable.get("aws_secret_access_key")
    S3_BUCKET_NAME = "airflowdump"
    
    logger.info(f"Connecting to S3 bucket: {S3_BUCKET_NAME}")
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )

    try:
        logger.info("Initiating pagination for S3 objects")
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(Bucket=S3_BUCKET_NAME)
        
        files = []
        page_count = 0
        for page in page_iterator:
            page_count += 1
            logger.info(f"Processing page {page_count}")
            if 'Contents' in page:
                for obj in page['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat()
                    })
        
        logger.info(f"Found {len(files)} files in S3 bucket {S3_BUCKET_NAME}")
        logger.info(f"Processed {page_count} pages total")
        for file in files[:10]:  # Print first 10 files as sample
            logger.info(f"File: {file['key']} ({file['size']} bytes, last modified: {file['last_modified']})")
        
        return files[:3]
    except Exception as e:
        logger.error(f"Error listing S3 files: {e}")
        logger.exception("Full traceback:")
        raise

def store_in_mongodb(structured_data):
    """Store structured output in MongoDB"""
    logger.info("Starting MongoDB storage process")
    download_tls_ca_file()
    client = None
    try:
        logger.info("Connecting to MongoDB")
        client = MongoClient(
            # "mongodb://host.docker.internal:27017",
            "mongodb://localhost:27017",
        )
        
        db = client.rss_feed_db
        collection = db.structured_outputs
        logger.info(f"Connected to database: {db.name}, collection: {collection.name}")
        
        logger.info("Inserting document into MongoDB")
        result = collection.insert_one(structured_data)
        logger.info(f"Successfully inserted document with ID: {result.inserted_id}")
    except Exception as e:
        logger.error(f"Failed to store in MongoDB: {e}")
        logger.exception("Full traceback:")
        raise
    finally:
        if client:
            logger.info("Closing MongoDB connection")
            client.close()

def ensure_list(value):
    """Ensure the value is a list"""
    if isinstance(value, str):
        try:
            value = ast.literal_eval(value)
        except:
            value = [value]
    return value if isinstance(value, (list, tuple)) else [value]

# Define the processing DAG
with DAG("process_circular", 
         default_args=default_args, 
         schedule_interval=timedelta(days=1),
         catchup=False) as dag:
    
    list_files_task = PythonOperator(
        task_id='list_s3_files',
        python_callable=list_s3_files,
        dag=dag
    )
    
    def upload_pdf_task(**context):
        logger.info("Starting PDF upload task")
        # Get list of files from previous task
        s3_files = ensure_list(context['task_instance'].xcom_pull(task_ids='list_s3_files'))
        
        if not s3_files:
            logger.error("No files found in S3 bucket")
            raise ValueError("No files found in S3 bucket")
            
        # Get AWS credentials from Airflow Variables
        aws_access_key_id = Variable.get("aws_access_key_id")
        aws_secret_access_key = Variable.get("aws_secret_access_key")
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        
        # Process each PDF file found
        file_ids = []
        for s3_file in s3_files:
            if s3_file['key'].endswith('.pdf'):
                logger.info(f"Processing PDF file: {s3_file['key']}")
                
                # Download file from S3 to local filesystem
                local_path = Path("/tmp") / s3_file['key']
                local_path.parent.mkdir(parents=True, exist_ok=True)
                
                logger.info(f"Downloading {s3_file['key']} to {local_path}")
                s3_client.download_file(
                    Bucket="airflowdump",
                    Key=s3_file['key'],
                    Filename=str(local_path)
                )
                
                # Upload the local file to Mistral OCR
                file_id = upload_pdf(local_path)
                logger.info(f"Successfully uploaded PDF. File ID: {file_id}")
                file_ids.append(file_id)
        
        logger.info(f"Completed PDF upload task. Processed {len(file_ids)} files")
        return file_ids
    
    def process_ocr_task(file_ids, **context):
        logger.info("Starting OCR processing task")
        results = []
        file_ids = ensure_list(file_ids)
        
        logger.info(f"Processing {len(file_ids)} files")
        logger.info(f"File IDs to process: {file_ids}")
        
        for file_id in file_ids:
            logger.info(f"Processing OCR for file ID: {file_id}")
            combined_markdown = process_ocr(file_id)
            markdown_file = f"combined_markdown_{file_id}.md"
            logger.info(f"Writing markdown to file: {markdown_file}")
            with open(markdown_file, "w") as f:
                f.write(combined_markdown)
            results.append(markdown_file)
        logger.info(f"Completed OCR processing for {len(results)} files")
        return results
    
    def generate_structured_output_task(markdown_files, **context):
        logger.info("Starting structured output generation task")
        markdown_files = ensure_list(markdown_files)
        all_outputs = []
        
        for markdown_file in markdown_files:
            logger.info(f"Processing markdown file: {markdown_file}")
            with open(markdown_file, "r") as f:
                combined_markdown = f.read()
            logger.info("Generating structured output")
            structured_output = generate_structured_output(combined_markdown)
            output_file = f"structured_output_{markdown_file}.json"
            logger.info(f"Writing structured output to file: {output_file}")
            # with open(output_file, "w") as f:
            #     json.dump(structured_output, f, indent=4)
            
            logger.info("Storing structured output in MongoDB")
            store_in_mongodb(structured_output)
            all_outputs.append(output_file)
        
        logger.info(f"Completed structured output generation for {len(all_outputs)} files")
        return all_outputs

    upload_task = PythonOperator(
        task_id="upload_pdf",
        python_callable=upload_pdf_task,
        provide_context=True,
        dag=dag
    )
    
    ocr_task = PythonOperator(
        task_id="process_ocr",
        python_callable=process_ocr_task,
        op_args=["{{ ti.xcom_pull(task_ids='upload_pdf') }}"],
        provide_context=True,
        dag=dag
    )
    
    structured_task = PythonOperator(
        task_id="generate_structured_output",
        python_callable=generate_structured_output_task,
        op_args=["{{ ti.xcom_pull(task_ids='process_ocr') }}"],
        provide_context=True,
        dag=dag
    )

    # Add MongoDB storage task
    mongo_task = PythonOperator(
        task_id="store_in_mongodb",
        python_callable=lambda **context: None,  # Now handled within generate_structured_output_task
        provide_context=True,
        dag=dag
    )

    list_files_task >> upload_task >> ocr_task >> structured_task >> mongo_task
