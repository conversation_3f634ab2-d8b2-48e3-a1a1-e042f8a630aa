import yaml
from app.v2.query.utils.completion_client import completion_client

class MultiQueryGenerator:
    def __init__(self, model: str, prompt_file: str, num_variations: int = 3):
        self.model = model
        self.prompt = self._load_prompt(prompt_file)
        self.num_variations = num_variations

    def _load_prompt(self, prompt_file: str) -> str:
        with open(prompt_file, "r") as f:
            data = yaml.safe_load(f)
        return data.get("prompt", "")
    
    def run(self, query: str) -> list:
        final_prompt = self.prompt.format(query=query, num_variations=self.num_variations)
        messages = [{"role": "system", "content": final_prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model, )
        text = response.strip()
        queries = [line.strip(" -0123456789.•\t") for line in text.splitlines() if line.strip()]
        queries.append(query)
        return list(dict.fromkeys([q for q in queries if q]))
