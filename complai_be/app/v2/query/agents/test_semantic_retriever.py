import pytest
from unittest.mock import MagicMock, patch
from app.v2.query.agents.semantic_retriever import SemanticRetriever, DocumentType

@pytest.fixture
def mock_dependencies():

    with patch('app.v2.query.agents.semantic_retriever.QdrantClient') as MockQdrantClient, \
         patch('app.v2.query.agents.semantic_retriever.AzureOpenAIEmbeddings') as MockEmbeddings, \
         patch('app.v2.query.agents.semantic_retriever.FastEmbedSparse') as MockSparse, \
         patch('app.v2.query.agents.semantic_retriever.completion_client') as mock_completion_client, \
         patch('app.v2.query.agents.semantic_retriever.QdrantVectorStore') as MockVectorStore:
        # Setup mocks
        mock_client = MockQdrantClient.return_value
        mock_embeddings = MockEmbeddings.return_value
        mock_sparse = MockSparse.return_value
        mock_completion_client.chat_client = MagicMock()
        mock_vector_store = MockVectorStore.return_value
        yield {
            'client': mock_client,
            'embeddings': mock_embeddings,
            'sparse': mock_sparse,
            'completion_client': mock_completion_client,
            'vector_store': mock_vector_store,
            'MockVectorStore': MockVectorStore
        }

def test_constructor_initializes_vectorstores(mock_dependencies):
    print("[TEST] Initializing SemanticRetriever with mock dependencies...")
    retriever = SemanticRetriever(
        client=mock_dependencies['client'],
        collection='test_collection',
        embed_model='embed_model',
        chat_model='chat_model',
    )
    print(f"[TEST] Vector stores initialized: {list(retriever.vector_stores.keys())}")
    # Should have a vector store for each document type
    for doc_type in DocumentType:
        print(f"[TEST] Checking vector store for doc_type: {doc_type.value}")
        assert doc_type.value in retriever.vector_stores

def test_run_returns_unique_docs(mock_dependencies):
    print("[TEST] Initializing SemanticRetriever for run test...")
    retriever = SemanticRetriever(
        client=mock_dependencies['client'],
        collection='test_collection',
        embed_model='embed_model',
        chat_model='chat_model',
    )
    # Patch the MultiQueryRetriever to return mock docs
    mock_doc = MagicMock()
    mock_doc.metadata = {'document_id': 'doc1', 'positions': [1,2], 'source': 'src'}
    mock_doc.page_content = 'content1'
    with patch('app.v2.query.agents.semantic_retriever.MultiQueryRetriever') as MockMQR:
        instance = MockMQR.from_llm.return_value
        instance.invoke.return_value = [mock_doc]
        # Patch as_retriever to return a dummy retriever
        for vs in retriever.vector_stores.values():
            vs.as_retriever = MagicMock(return_value=MagicMock())
        print("[TEST] Running retriever.run('test query')...")
        docs, positions = retriever.run('test query')
        print(f"[TEST] Docs returned: {docs}")
        print(f"[TEST] Positions returned: {positions}")
        assert len(docs) == 1
        assert docs[0]['metadata']['source'] == 'src'
        assert positions['doc1'] == [1,2]
