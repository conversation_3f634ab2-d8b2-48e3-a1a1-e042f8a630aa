// //signin/page.tsx
// "use client"

// import { useEffect } from "react"
// import { useRouter } from "next/navigation"
// import { useAuth } from "@/contexts/AuthContext"
// import SignIn from "@/components/Signin"

// export default function SignInPage() {
//   const router = useRouter()
//   const { user } = useAuth()

//   // If user is authenticated, redirect to home
//   useEffect(() => {
//     if (user) {
//       router.push("/")
//     }
//   }, [user, router])

//   if (user) {
//     return <div className="h-screen flex items-center justify-center">Redirecting...</div>
//   }

//   return (
//     <div className="h-screen flex items-center justify-center bg-gray-100">
//       <SignIn
//         onSuccess={() => {
//           router.push("/")
//         }}
//       />
//     </div>
//   )
// }
// //signin/page.tsx
// "use client"
// import { useEffect } from "react"
// import { useRouter } from "next/navigation"
// import { useAuth } from "@/contexts/AuthContext"
// import { useChat } from "@/contexts/ChatContext"
// import SignIn from "@/components/Signin"

// export default function SignInPage() {
//   const router = useRouter()
//   const { user } = useAuth()
//   const { createNewChat, currentSessionId } = useChat()
  
//   // If user is authenticated, redirect to home
//   useEffect(() => {
//     const handleSuccessfulAuth = async () => {
//       if (user) {
//         // If no active session exists, create one before redirecting
//         if (!currentSessionId) {
//           try {
//             console.log("Creating new session after signin")
//             await createNewChat()
//           } catch (error) {
//             console.error("Failed to create session after signin:", error)
//           }
//         }
        
//         router.push("/")
//       }
//     }
    
//     handleSuccessfulAuth()
//   }, [user, router, createNewChat, currentSessionId])

//   if (user) {
//     return <div className="h-screen flex items-center justify-center">Redirecting...</div>
//   }

//   return (
//     <div className="h-screen flex items-center justify-center bg-gray-100">
//       <SignIn
//         onSuccess={() => {
//           // The redirection is handled by the useEffect above
//           // This just completes the auth flow
//         }}
//       />
//     </div>
//   )
// }
"use client"
import { useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { useChat } from "@/contexts/ChatContext"
import SignIn from "@/components/Signin"

export default function SignInPage() {
  const router = useRouter()
  const { user } = useAuth()
  const { createNewChat, currentSessionId } = useChat()
  const isProcessingAuth = useRef(false)
  
  // If user is authenticated, redirect to home
  useEffect(() => {
    const handleSuccessfulAuth = async () => {
      // Prevent multiple execution
      if (user && !isProcessingAuth.current) {
        isProcessingAuth.current = true
        
        try {
          // If no active session exists, create one before redirecting
          if (!currentSessionId) {
            // console.log("Creating new session after signin")
            await createNewChat()
          }
          
          // Redirect regardless of session creation result
          router.push("/")
        } catch (error) {
          console.error("Failed to create session after signin:", error)
          // Still redirect to home page even if session creation fails
          router.push("/")
        } finally {
          // Reset processing flag after completion
          setTimeout(() => {
            isProcessingAuth.current = false
          }, 500)
        }
      }
    }
    
    handleSuccessfulAuth()
  }, [user, router, createNewChat, currentSessionId])

  if (user) {
    return <div className="h-screen flex items-center justify-center">Redirecting...</div>
  }

  return (
    <div className="h-screen flex items-center justify-center bg-gray-100">
      <SignIn
        onSuccess={() => {
          // The redirection is handled by the useEffect above
          // This just completes the auth flow
        }}
      />
    </div>
  )
}