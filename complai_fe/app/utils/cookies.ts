// app/utils/cookies.ts
export function setCookie(name: string, value: string, days: number) {
    if (typeof document === "undefined") return
  
    const expires = new Date()
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000)
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
  }
  
  export function getCookie(name: string): string | null {
    if (typeof document === "undefined") return null
  
    const nameEQ = `${name}=`
    const ca = document.cookie.split(";")
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i].trim()
      if (c.indexOf(nameEQ) === 0) {
        return c.substring(nameEQ.length, c.length)
      }
    }
    return null
  }
  
  export function eraseCookie(name: string) {
    if (typeof document === "undefined") return
  
    document.cookie = `${name}=; Max-Age=-99999999; path=/`
  }
  