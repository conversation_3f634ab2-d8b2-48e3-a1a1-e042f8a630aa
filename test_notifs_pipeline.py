import json
import logging
import os
from pathlib import Path
import requests
from datetime import datetime

# Mock the Airflow-dependent modules for standalone testing
class MockQdrantManager:
    def __init__(self):
        self.client = None

    def add_document(self, *args, **kwargs):
        return {"success": True, "message": "Mock document added"}

class MockDBManager:
    def insert_document(self, *args, **kwargs):
        return True

class MockOpenAIManager:
    def get_completion(self, prompt):
        return "Mock LLM response"

# Create mock instances
qdrant_manager = MockQdrantManager()
db_manager = MockDBManager()

def strip_data_attributes(html_content):
    """Mock function to strip data attributes"""
    return html_content

def parse_pdf_to_html_ast(pdf_content):
    """Mock PDF parsing function"""
    return "<html><body>Mock PDF content</body></html>"

def chunk_html_ast(html_content):
    """Mock HTML chunking function"""
    return [{"content": html_content, "metadata": {}}]

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def download_and_ingest_document_with_history(pdf_url: str, notification_data: dict) -> bool:
    """
    Mock implementation of document ingestion with history tracking
    """
    try:
        logger.info(f"📥 Mock downloading document from: {pdf_url[:50]}...")

        # Simulate download validation
        if not pdf_url.startswith('https://rbidocs.rbi.org.in/'):
            logger.error(f"Invalid RBI URL format")
            return False

        # Simulate PDF processing
        logger.info(f"🔄 Mock processing PDF content...")

        # Simulate chunking and vectorization
        logger.info(f"✂️ Mock chunking document...")

        # Simulate adding to knowledge base
        logger.info(f"💾 Mock adding to knowledge base...")

        # Simulate database insertion
        logger.info(f"🗄️ Mock updating database...")

        return True

    except Exception as e:
        logger.error(f"Error in mock ingestion: {e}")
        return False


def process_json_entries(json_filename: str = "rbi_notifications.json"):
    """
    Read a JSON file containing RBI notification entries and process them
    through the mock ingestion pipeline.

    Each entry must include:
      - "Section", "Year", "Date", "Title", "PDF Link", "Local Path", "Watermark"
    """
    base_folder = Path(__file__).parent
    json_path = base_folder / json_filename

    logger.info(f"📥 Loading JSON entries from {json_path}")
    if not json_path.exists():
        logger.error(f"JSON file not found at {json_path}")
        return 0

    with open(json_path, "r", encoding="utf-8") as f:
        entries = json.load(f)

    # Process only first 5 entries for testing
    test_entries = entries[:5]
    logger.info(f"🧪 Testing with {len(test_entries)} entries (out of {len(entries)} total)")

    processed = 0
    for i, entry in enumerate(test_entries, 1):
        title = entry.get('Title', 'Unknown Title')
        pdf_url = entry.get('PDF Link', '')
        watermark = entry.get('Watermark', '')
        published = entry.get('Date') or f"{entry.get('Year', '2025')}-01-01"

        logger.info(f"\n🔄 Processing {i}/{len(test_entries)}: {title[:60]}...")
        logger.info(f"   📄 PDF URL: {pdf_url[:80]}...")
        logger.info(f"   🏷️ Watermark: {watermark[:50]}...")

        # Build notification_data for history tracking
        notification_data = {
            'title': title,
            'link': pdf_url,
            'published_date': published,
            'id': watermark or f"notification_{i}",
            'llm_analysis': {'requires_kb_update': True}  # Assume KB update needed for testing
        }

        # Ingest via mock pipeline
        success = download_and_ingest_document_with_history(pdf_url, notification_data)
        if success:
            processed += 1
            logger.info(f"   ✅ Successfully processed: {title[:40]}...")
        else:
            logger.error(f"   ❌ Failed to process: {title[:40]}...")

    logger.info(f"\n📊 Processing Summary:")
    logger.info(f"   Total entries in file: {len(entries)}")
    logger.info(f"   Entries tested: {len(test_entries)}")
    logger.info(f"   Successfully processed: {processed}")
    logger.info(f"   Success rate: {(processed/len(test_entries)*100):.1f}%")

    return processed


if __name__ == "__main__":
    count = process_json_entries()
    logger.info(f"Total entries processed: {count}")

