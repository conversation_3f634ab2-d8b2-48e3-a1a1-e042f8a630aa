import logging
import json
import asyncio
from typing import Callable, Dict, Any, AsyncGenerator

from config import settings

from app.v2.query.nodes import *
from app.v2.query.utils.memory_manager import memory_manager
from app.v2.query.retrieval.qdrant_client import get_qdrant_client

# from nodes import PlannerNode
from app.v2.query.agents.planner import ExecutionPlan

logger = logging.getLogger(__name__)

# need to run certain nodes in parallel and also some post sending the
# response to frontend
# also need to be able to stream


async def generate_sse_events(user_query: str, session_id: str, extra_context: dict, username: str = None) -> AsyncGenerator[str, None]:
    """
    Generator function for SSE events from pipeline execution.
    Yields formatted SSE events for each step of the pipeline.
    """
    # Queue to receive updates from pipeline execution
    event_queue = asyncio.Queue()

    # Callback function to put events into the queue
    def pipeline_callback(data: Dict[str, Any]):
        event_queue.put_nowait(data)

    # Start pipeline execution in a background task
    pipeline_task = asyncio.create_task(
        asyncio.to_thread(
            run_langgraph_pipeline,
            user_query,
            session_id,
            extra_context,
            username,
            pipeline_callback
        )
    )

    try:
        # Format and yield SSE events as they come in
        while True:
            try:
                # Get event with timeout to allow for checking if task is done
                event_data = await asyncio.wait_for(event_queue.get(), timeout=0.1)

                # Convert event data to json and format as SSE
                event_str = f"data: {json.dumps(event_data)}\n\n"
                yield event_str

                # If this is the final event, break
                if event_data.get("event") == "execution_completed":
                    break

                # If this is the final event, break
                if event_data.get("event") == "execution_error":
                    break

            except asyncio.TimeoutError:
                # Check if pipeline task is done and queue is empty
                if pipeline_task.done() and event_queue.empty():
                    # Final event
                    yield f"data: {json.dumps({'event': 'pipeline_completed'})}\n\n"
                    break
    finally:
        # Ensure task is properly cleaned up
        if not pipeline_task.done():
            pipeline_task.cancel()
            try:
                await pipeline_task
            except (asyncio.CancelledError, Exception):
                pass


def run_pipeline(nodes: list, initial_state: dict, callback=None) -> dict:
    logger.info("Starting pipeline execution")
    state = initial_state
    for node in nodes:
        node_name = node.__class__.__name__
        logger.debug("Executing node: %s", node_name)
        try:
            if callback:
                callback({
                    "event": "node_started",
                    "node": node_name,
                })
            state = node.run(state)
            state = node.post_run(state)  # Post-processing hook

            if callback:
                callback({
                    "event": "node_completed",
                    "node": node_name,
                })

            if state.get("llm_follow_up"):
                logger.info(f"Pipeline paused by {node.__class__.__name__}")
                return state

        except Exception as e:
            logger.error("Error in node %s: %s", node_name, str(e))
            if callback:
                callback({
                    "event": "execution_error",
                    "node": node_name,
                    "status": "error",
                    "error": str(e)
                })
            raise
    logger.info("Pipeline execution completed")
    if callback:
        callback({
            "event": "pipeline_completed",
            "status": "success"
        })
    return state


def run_langgraph_pipeline(user_query: str, session_id: str, extra_context: dict, username: str = None, callback=None) -> dict:
    logger.info("Starting pipeline for session %s", session_id)
    chat_history = memory_manager.get_conversation_context(session_id)
    condensed_context = memory_manager.get_condensed_context(session_id)
    initial_state = {
        "query": user_query,
        "session_id": session_id,
        "chat_history": chat_history,
        "extra_context": extra_context,
        "condensed_context": condensed_context
    }

    logger.info("Initial state: %s", initial_state)

    # Send initial state if callback provided
    if callback:
        callback({
            "event": "pipeline_started",
            "query": user_query,
            "session_id": session_id
        })

    # Create plan first
    planner_node = PlannerNode(settings.OPENAI_STRUCTURED_OUTPUT_MODEL)
    try:
        state = planner_node.run(initial_state)
        plan: ExecutionPlan = state["execution_plan"]
    except Exception as e:
        logger.error("Error in planner node: %s", str(e))
        if callback:
            callback({
                "event": "execution_error",
                "status": "error",
                "error": str(e)
            })
        raise

    # Notify about plan creation
    if callback:
        callback({
            "event": "plan_created",
            "plan": plan.details(),
        })

    # Build dynamic execution pipeline based on plan
    q_client = get_qdrant_client()
    node_map = {
        "QueryRewriter": lambda: QueryRewriterNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/query_rewriter.yaml", extra_context, callback),
        # "AmbiguityResolver": lambda: AmbiguityResolverNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/ambiguity_resolver.yaml"),
        # "MultiQueryGenerator": lambda: MultiQueryGeneratorNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/multi_query_generator.yaml"),
        # "QdrantFilterGenerator": lambda: QdrantFilterGeneratorNode(settings.OPENAI_STRUCTURED_OUTPUT_MODEL),
        "ConversationalAgent": lambda: MeetAndGreetAgentNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/conversational_agent.yaml"),
        "MongoFilterBuilder": lambda: MongoFilterBuilderNode(settings.OPENAI_STRUCTURED_OUTPUT_MODEL, "app/v2/query/prompts/metadata_filter_prompt.yaml"),
        "ContextExpander": lambda: ContextExpanderNode(),
        "SemanticRetriever": lambda: SemanticRetrieverNode(q_client, settings.QDRANT_COLLECTION, settings.OPENAI_EMBEDDING_MODEL, settings.OPENAI_CHAT_MODEL),
        "Summarizer": lambda: SummarizerNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/recursive_summarizer.yaml", session_id,  callback),
        "AnswerGenerator": lambda: AnswerGeneratorNode(settings.OPENAI_ANSWER_GENERATOR_MODEL, "app/v2/query/prompts/answer_generator.yaml"),
        # "CriticAgent": lambda: CriticAgentNode(settings.OPENAI_CHAT_MODEL),
        # "AnnotationGenerator": lambda: AnnotationGeneratorNode(),
        "ProactiveSuggestor": lambda: ProactiveSuggestorNode(settings.OPENAI_CHAT_MODEL),
        "MemoryUpdate": lambda: MemoryUpdateNode("Assistant", "final_answer", session_id, username),
        "DefinitionRetriever": lambda: DictionaryAgentNode(settings.OPENAI_EMBEDDING_MODEL, settings.QDRANT_DICT_COLLECTION),
        "ContextExpanderWithFilter": lambda: ContextExpanderWithFilterNode(settings.OPENAI_CHAT_MODEL, "app/v2/query/prompts/metadata_filter_prompt.yaml"),
    }

    # Create ordered node list based on execution plan
    # removed calling memory update node twice
    nodes = []  # Always start with memory update
    for step in plan.steps:
        if step.agent in node_map:
            nodes.append(node_map[step.agent]())

    # Add final memory update
    nodes.append(node_map["MemoryUpdate"]())

    # Execute the dynamic pipeline with callback
    final_state = run_pipeline(nodes, state, callback)
    if callback:
        if state.get("llm_follow_up"):
            callback({
                "event": "execution_completed",
                "status": "completed",
                "answer": {
                    "query": state.get("query"),
                    "response": state.get("llm_follow_up"),
                    "metadata": []
                },
                "sources": {},
                # "session_id": request.session_id,
                "suggestions": state.get("suggestions")
            })
        else:
            sources = []  # Optionally, extract document titles from contexts.
            callback({
                "event": "execution_completed",
                "status": "completed",
                "answer": state.get("final_answer"),
                "sources": sources,
                "suggestions": state.get("suggestions")
            })

    return final_state
