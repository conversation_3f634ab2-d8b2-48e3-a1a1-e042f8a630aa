from datetime import datetime
import logging
import yaml
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, ValidationError
from qdrant_client import models
from openai import OpenAI
import instructor
from app.v2.query.utils.completion_client import completion_client

# ---------------------------
# Logging Configuration
# ---------------------------
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ---------------------------
# Filter Models
# ---------------------------


class FilterCondition(BaseModel):
    key: str
    match: Optional[dict] = None
    range: Optional[dict] = None
    must: Optional[List['FilterCondition']] = None
    should: Optional[List['FilterCondition']] = None
    must_not: Optional[List['FilterCondition']] = None


# Resolve forward references for recursive types
# FilterCondition.update_forward_refs()
FilterCondition.model_rebuild()


class FilterModel(BaseModel):
    must: List[FilterCondition] = []
    should: List[FilterCondition] = []
    must_not: List[FilterCondition] = []

# ---------------------------
# Qdrant Filter Generator
# ---------------------------


class QdrantFilterGenerator:
    def __init__(self, model: str, prompt_file: str = "app/v2/query/prompts/metadata_filter_prompt.yaml"):
        self.model = model
        self.prompt_data = self._load_prompt(prompt_file)
        self.client = instructor.from_openai(OpenAI())
        self.chain = self._create_chain()
        self.completion_client = completion_client

    def _load_prompt(self, prompt_file: str) -> dict:
        try:
            with open(prompt_file, "r") as f:
                data = yaml.safe_load(f)

            return {
                "system_template": data.get("prompt_template", ""),
                "qdrant_docs": data.get("qdrant_docs", ""),
                "metadata_fields": data.get("metadata_fields", ""),
                "today": datetime.now().strftime("%d %m %Y")
            }
        except Exception as e:
            logger.error(f"Failed to load prompt file: {e}")
            return {"system_template": "", "qdrant_docs": "", "metadata_fields": ""}

    def _create_chain(self):
        prompt = [
            {"role": "system", "content": self.prompt_data["system_template"].format(
                qdrant_docs=self.prompt_data["qdrant_docs"],
                metadata_fields=self.prompt_data["metadata_fields"],
                today=self.prompt_data["today"]
            )},
            {"role": "user", "content": "Parse this query into a Qdrant filter: {query}"}
        ]
        return prompt

    def run(self, query, mongo_filter: dict = {}) -> models.Filter:
        try:
            if mongo_filter:
                mongo_filter = str(mongo_filter)
                messages = [
                    {"role": "system", "content": self.prompt_data["system_template"].format(
                        qdrant_docs=self.prompt_data["qdrant_docs"],
                        metadata_fields=self.prompt_data["metadata_fields"],
                        today=self.prompt_data["today"]
                    )},
                    {"role": "user", "content": f"Parse this Mongo filter into a Qdrant filter: {mongo_filter}"}
                ]
                # Use structured completion to return a FilterModel directly
                filter_response = self.completion_client.completion_structured_as_answer_model(
                    required_model=models.Filter,
                    messages=messages,
                    model=self.model,
                )
                print(
                    f"QdrantFilterGenerator: Raw filter response: {filter_response}")
                return filter_response
            else:
                messages = [
                    {"role": "system", "content": self.prompt_data["system_template"].format(
                        qdrant_docs=self.prompt_data["qdrant_docs"],
                        metadata_fields=self.prompt_data["metadata_fields"],
                        today=self.prompt_data["today"]
                    )},
                    {"role": "user", "content": f"Parse this query into a Qdrant filter: {query}"}
                ]
                # Use structured completion to return a FilterModel directly
                filter_response = self.completion_client.completion_structured_as_answer_model(
                    required_model=models.Filter,
                    messages=messages,
                    model=self.model,
                )
                print(
                    f"QdrantFilterGenerator: Raw filter response: {filter_response}")
                return filter_response
        except Exception as e:
            logger.error(f"Filter generation failed: {str(e)}")

        return models.Filter(must=[], should=[], must_not=[])

# ---------------------------
# MongoDB Filter Builder
# ---------------------------


class MongoFilterBuilder:
    def __init__(self, model: str, prompt_file: str):
        self.model = model
        self.prompt_data = self._load_prompt(prompt_file)
        self.client = instructor.from_openai(OpenAI())
        # logger.info("MongoFilterBuilder: Initialized with model=%s, prompt_file=%s", model, prompt_file)

    def _load_prompt(self, prompt_file: str) -> dict:
        try:
            with open(prompt_file, "r") as f:
                data = yaml.safe_load(f)
            logger.info(
                "MongoFilterBuilder: Loaded prompt file %s successfully", prompt_file)
            return {
                "system_template": data.get("prompt_template_mongo", ""),
                "today": datetime.now().strftime("%d %m %Y")
            }
        except Exception as e:
            logger.error(
                f"MongoFilterBuilder: Failed to load Mongo prompt: {e}")
            return {"system_template": "", "today": ""}

    def _create_chain(self, query: str):
        prompt = [
            {"role": "system", "content": self.prompt_data["system_template"].format(
                today=self.prompt_data["today"])},
            {"role": "user",
                "content": f"Parse this query into a MongoDB filter: {query}, today's date is {self.prompt_data['today']}"}
        ]
        logger.debug("MongoFilterBuilder: Created prompt chain: %s", prompt)
        return prompt

    def run(self, query: str) -> Dict[str, Any]:
        logger.info(
            "MongoFilterBuilder: Running filter generation for query: %s", query)
        try:
            messages = self._create_chain(query)
            logger.debug(
                "MongoFilterBuilder: Messages sent to model: %s", messages)

            mongo_filter = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                response_model=None
            )

            logger.info(
                f"MongoFilterBuilder: Generated MongoDB filter: {mongo_filter}")

            # Extract and parse the filter content
            if hasattr(mongo_filter, "choices") and mongo_filter.choices:
                content = mongo_filter.choices[0].message.content
                print(
                    f"MongoFilterBuilder: Raw content from response: {content}")
                logger.debug(
                    f"MongoFilterBuilder: Extracted content from response: {content}")
                try:
                    import json
                    filter_dict = json.loads(content)
                    logger.info(
                        f"MongoFilterBuilder: Parsed MongoDB filter dict: {filter_dict}")
                    return filter_dict
                except json.JSONDecodeError as e:
                    logger.error(
                        f"MongoFilterBuilder: Failed to parse filter content as JSON: {e}")

            logger.warning(
                "MongoFilterBuilder: Generated MongoDB filter is empty or invalid for query: %s", query)
            return {}
        except Exception as e:
            logger.error(
                f"MongoFilterBuilder: MongoDB filter generation failed: {str(e)}")
            return {}
