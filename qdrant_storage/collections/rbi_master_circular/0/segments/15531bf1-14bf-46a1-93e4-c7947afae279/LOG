2025/06/30-11:26:13.492125 10109 RocksDB version: 9.9.3
2025/06/30-11:26:13.492204 10109 Compile date 2024-12-05 01:25:31
2025/06/30-11:26:13.492216 10109 DB SUMMARY
2025/06/30-11:26:13.492217 10109 Host name (Env):  3ed34e58cde5
2025/06/30-11:26:13.492217 10109 DB Session ID:  HLH6JCEFCE6BMGI16FO4
2025/06/30-11:26:13.492713 10109 CURRENT file:  CURRENT
2025/06/30-11:26:13.492716 10109 MANIFEST file:  MANIFEST-000381 size: 6406 Bytes
2025/06/30-11:26:13.492717 10109 SST files in ./storage/collections/rbi_master_circular/0/segments/15531bf1-14bf-46a1-93e4-c7947afae279 dir, Total Num: 64, files: 000011.sst 000017.sst 000023.sst 000030.sst 000037.sst 000043.sst 000049.sst 000055.sst 000063.sst 
2025/06/30-11:26:13.492718 10109 Write Ahead Log file in ./storage/collections/rbi_master_circular/0/segments/15531bf1-14bf-46a1-93e4-c7947afae279: 
2025/06/30-11:26:13.492719 10109                         Options.error_if_exists: 0
2025/06/30-11:26:13.492719 10109                       Options.create_if_missing: 1
2025/06/30-11:26:13.492720 10109                         Options.paranoid_checks: 1
2025/06/30-11:26:13.492720 10109             Options.flush_verify_memtable_count: 1
2025/06/30-11:26:13.492721 10109          Options.compaction_verify_record_count: 1
2025/06/30-11:26:13.492721 10109                               Options.track_and_verify_wals_in_manifest: 0
2025/06/30-11:26:13.492722 10109        Options.verify_sst_unique_id_in_manifest: 1
2025/06/30-11:26:13.492723 10109                                     Options.env: 0xffff86231620
2025/06/30-11:26:13.492738 10109                                      Options.fs: PosixFileSystem
2025/06/30-11:26:13.492739 10109                                Options.info_log: 0xffff8fa7a000
2025/06/30-11:26:13.492748 10109                Options.max_file_opening_threads: 16
2025/06/30-11:26:13.492749 10109                              Options.statistics: (nil)
2025/06/30-11:26:13.492750 10109                               Options.use_fsync: 0
2025/06/30-11:26:13.492750 10109                       Options.max_log_file_size: 1048576
2025/06/30-11:26:13.492751 10109                  Options.max_manifest_file_size: 1073741824
2025/06/30-11:26:13.492792 10109                   Options.log_file_time_to_roll: 0
2025/06/30-11:26:13.492793 10109                       Options.keep_log_file_num: 1
2025/06/30-11:26:13.492794 10109                    Options.recycle_log_file_num: 0
2025/06/30-11:26:13.492794 10109                         Options.allow_fallocate: 1
2025/06/30-11:26:13.492795 10109                        Options.allow_mmap_reads: 0
2025/06/30-11:26:13.492795 10109                       Options.allow_mmap_writes: 0
2025/06/30-11:26:13.492796 10109                        Options.use_direct_reads: 0
2025/06/30-11:26:13.492796 10109                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/30-11:26:13.492797 10109          Options.create_missing_column_families: 1
2025/06/30-11:26:13.492797 10109                              Options.db_log_dir: 
2025/06/30-11:26:13.492798 10109                                 Options.wal_dir: 
2025/06/30-11:26:13.492798 10109                Options.table_cache_numshardbits: 6
2025/06/30-11:26:13.492799 10109                         Options.WAL_ttl_seconds: 0
2025/06/30-11:26:13.492799 10109                       Options.WAL_size_limit_MB: 0
2025/06/30-11:26:13.492800 10109                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/30-11:26:13.492800 10109             Options.manifest_preallocation_size: 4194304
2025/06/30-11:26:13.492801 10109                     Options.is_fd_close_on_exec: 1
2025/06/30-11:26:13.492801 10109                   Options.advise_random_on_open: 1
2025/06/30-11:26:13.492802 10109                    Options.db_write_buffer_size: 0
2025/06/30-11:26:13.492802 10109                    Options.write_buffer_manager: 0xffff8fa19600
2025/06/30-11:26:13.492874 10109           Options.random_access_max_buffer_size: 1048576
2025/06/30-11:26:13.492875 10109                      Options.use_adaptive_mutex: 0
2025/06/30-11:26:13.492875 10109                            Options.rate_limiter: (nil)
2025/06/30-11:26:13.492876 10109     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/30-11:26:13.492877 10109                       Options.wal_recovery_mode: 0
2025/06/30-11:26:13.492877 10109                  Options.enable_thread_tracking: 0
2025/06/30-11:26:13.492878 10109                  Options.enable_pipelined_write: 0
2025/06/30-11:26:13.492879 10109                  Options.unordered_write: 0
2025/06/30-11:26:13.492879 10109         Options.allow_concurrent_memtable_write: 1
2025/06/30-11:26:13.492880 10109      Options.enable_write_thread_adaptive_yield: 1
2025/06/30-11:26:13.492880 10109             Options.write_thread_max_yield_usec: 100
2025/06/30-11:26:13.492881 10109            Options.write_thread_slow_yield_usec: 3
2025/06/30-11:26:13.492881 10109                               Options.row_cache: None
2025/06/30-11:26:13.492882 10109                              Options.wal_filter: None
2025/06/30-11:26:13.492883 10109             Options.avoid_flush_during_recovery: 0
2025/06/30-11:26:13.492883 10109             Options.allow_ingest_behind: 0
2025/06/30-11:26:13.492884 10109             Options.two_write_queues: 0
2025/06/30-11:26:13.492884 10109             Options.manual_wal_flush: 0
2025/06/30-11:26:13.492885 10109             Options.wal_compression: 0
2025/06/30-11:26:13.492885 10109             Options.background_close_inactive_wals: 0
2025/06/30-11:26:13.492886 10109             Options.atomic_flush: 0
2025/06/30-11:26:13.492886 10109             Options.avoid_unnecessary_blocking_io: 0
2025/06/30-11:26:13.492887 10109             Options.prefix_seek_opt_in_only: 0
2025/06/30-11:26:13.492887 10109                 Options.persist_stats_to_disk: 0
2025/06/30-11:26:13.492888 10109                 Options.write_dbid_to_manifest: 1
2025/06/30-11:26:13.492888 10109                 Options.write_identity_file: 1
2025/06/30-11:26:13.492889 10109                 Options.log_readahead_size: 0
2025/06/30-11:26:13.492889 10109                 Options.file_checksum_gen_factory: Unknown
2025/06/30-11:26:13.492890 10109                 Options.best_efforts_recovery: 0
2025/06/30-11:26:13.492890 10109                Options.max_bgerror_resume_count: 2147483647
2025/06/30-11:26:13.492891 10109            Options.bgerror_resume_retry_interval: 1000000
2025/06/30-11:26:13.492891 10109             Options.allow_data_in_errors: 0
2025/06/30-11:26:13.492892 10109             Options.db_host_id: __hostname__
2025/06/30-11:26:13.492892 10109             Options.enforce_single_del_contracts: true
2025/06/30-11:26:13.492893 10109             Options.metadata_write_temperature: kUnknown
2025/06/30-11:26:13.492893 10109             Options.wal_write_temperature: kUnknown
2025/06/30-11:26:13.492894 10109             Options.max_background_jobs: 2
2025/06/30-11:26:13.492894 10109             Options.max_background_compactions: -1
2025/06/30-11:26:13.492895 10109             Options.max_subcompactions: 1
2025/06/30-11:26:13.492895 10109             Options.avoid_flush_during_shutdown: 0
2025/06/30-11:26:13.492896 10109           Options.writable_file_max_buffer_size: 1048576
2025/06/30-11:26:13.492896 10109             Options.delayed_write_rate : 16777216
2025/06/30-11:26:13.492897 10109             Options.max_total_wal_size: 0
2025/06/30-11:26:13.492897 10109             Options.delete_obsolete_files_period_micros: 180000000
2025/06/30-11:26:13.492898 10109                   Options.stats_dump_period_sec: 600
2025/06/30-11:26:13.492898 10109                 Options.stats_persist_period_sec: 600
2025/06/30-11:26:13.492899 10109                 Options.stats_history_buffer_size: 1048576
2025/06/30-11:26:13.492899 10109                          Options.max_open_files: 256
2025/06/30-11:26:13.492900 10109                          Options.bytes_per_sync: 0
2025/06/30-11:26:13.492900 10109                      Options.wal_bytes_per_sync: 0
2025/06/30-11:26:13.492901 10109                   Options.strict_bytes_per_sync: 0
2025/06/30-11:26:13.492901 10109       Options.compaction_readahead_size: 2097152
2025/06/30-11:26:13.492902 10109                  Options.max_background_flushes: -1
2025/06/30-11:26:13.492903 10109 Options.daily_offpeak_time_utc: 
2025/06/30-11:26:13.492903 10109 Compression algorithms supported:
2025/06/30-11:26:13.492904 10109 	kZSTD supported: 0
2025/06/30-11:26:13.492904 10109 	kXpressCompression supported: 0
2025/06/30-11:26:13.492905 10109 	kBZip2Compression supported: 0
2025/06/30-11:26:13.492905 10109 	kZSTDNotFinalCompression supported: 0
2025/06/30-11:26:13.492906 10109 	kLZ4Compression supported: 1
2025/06/30-11:26:13.492907 10109 	kZlibCompression supported: 0
2025/06/30-11:26:13.492907 10109 	kLZ4HCCompression supported: 1
2025/06/30-11:26:13.492908 10109 	kSnappyCompression supported: 1
2025/06/30-11:26:13.492908 10109 Fast CRC32 supported: Not supported on x86
2025/06/30-11:26:13.492909 10109 DMutex implementation: pthread_mutex_t
2025/06/30-11:26:13.492909 10109 Jemalloc supported: 0
2025/06/30-11:26:13.493825 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493826 10109           Options.merge_operator: None
2025/06/30-11:26:13.493826 10109        Options.compaction_filter: None
2025/06/30-11:26:13.493827 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493827 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.493828 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.493829 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.493836 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.493837 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.493839 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.493839 10109          Options.compression: LZ4
2025/06/30-11:26:13.493840 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.493840 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.493841 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.493841 10109             Options.num_levels: 7
2025/06/30-11:26:13.493842 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.493842 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.493843 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.493844 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.493844 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.493845 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.493845 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493846 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493846 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493847 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.493848 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493848 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493849 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.493849 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.493850 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.493850 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.493852 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.493853 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.493854 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.493854 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.493855 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.493855 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.493856 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.493857 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.493857 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.493858 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.493858 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.493859 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.493861 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.493862 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.493863 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.493863 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.493864 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.493864 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.493865 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.493866 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.493866 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.493867 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.493868 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.493868 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.493869 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.493869 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.493870 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.493871 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.493871 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.493872 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.493873 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.493873 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.493874 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.493875 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.493875 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.493876 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.493876 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.493877 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.493878 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.493879 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.493880 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.493881 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.493881 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.493882 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.493882 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.493883 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.493883 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.493884 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.493885 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.493886 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.493887 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.493887 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.493888 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.493888 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.493889 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.493889 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.493890 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.493891 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.493891 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.493892 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.493892 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.493893 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.493894 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.493895 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.493897 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.493898 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.493995 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.493996 10109           Options.merge_operator: None
2025/06/30-11:26:13.493996 10109        Options.compaction_filter: None
2025/06/30-11:26:13.493997 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.493998 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494002 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494002 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494008 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494011 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494011 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494012 10109          Options.compression: LZ4
2025/06/30-11:26:13.494013 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494013 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494014 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494015 10109             Options.num_levels: 7
2025/06/30-11:26:13.494015 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494016 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494016 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494017 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494018 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494018 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494019 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494020 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494021 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494023 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494023 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494024 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494025 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494026 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494026 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494027 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494027 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494028 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494028 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494029 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494030 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494031 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494031 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494032 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494033 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494033 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494034 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494035 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494035 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494036 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494037 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494037 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494038 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494040 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494041 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494042 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494042 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494043 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494044 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494045 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494046 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494046 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494047 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494048 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494048 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494049 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494050 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494050 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494051 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494052 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494052 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494053 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494053 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494054 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494055 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494056 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494056 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494057 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494061 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494062 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.494063 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494063 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494064 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494064 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494065 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494069 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494083 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.494087 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494088 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494095 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494095 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494096 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.494097 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.494098 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494098 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494099 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494100 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494103 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494104 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494109 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494111 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494117 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494195 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494201 10109           Options.merge_operator: None
2025/06/30-11:26:13.494201 10109        Options.compaction_filter: None
2025/06/30-11:26:13.494202 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494204 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494204 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494205 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494220 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494222 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494222 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494223 10109          Options.compression: LZ4
2025/06/30-11:26:13.494224 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494224 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494225 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494226 10109             Options.num_levels: 7
2025/06/30-11:26:13.494226 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494227 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494227 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494228 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494228 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494229 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494229 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494230 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494231 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494231 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494232 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494232 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494233 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494233 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494234 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494234 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494235 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494235 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494236 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494243 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494244 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494245 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494245 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494246 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494247 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494247 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494248 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494249 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494249 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494250 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494250 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494251 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494251 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494252 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494256 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494257 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494258 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494258 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494259 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494259 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494260 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494266 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494267 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494267 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494268 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494268 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494269 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494269 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494270 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494271 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494271 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494272 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494273 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494274 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494275 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494275 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494276 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494278 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494278 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494279 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.494280 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494280 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494281 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494281 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494282 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494283 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494283 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.494284 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494284 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494286 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494286 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494287 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.494288 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.494288 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494289 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494290 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494290 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494291 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494291 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494292 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494293 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494294 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494540 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494541 10109           Options.merge_operator: None
2025/06/30-11:26:13.494542 10109        Options.compaction_filter: None
2025/06/30-11:26:13.494542 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494543 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494544 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494544 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494550 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494550 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494551 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494551 10109          Options.compression: LZ4
2025/06/30-11:26:13.494552 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494552 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494553 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494554 10109             Options.num_levels: 7
2025/06/30-11:26:13.494554 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494555 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494555 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494556 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494556 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494557 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494558 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494559 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494560 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494560 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494561 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494562 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494562 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494563 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494564 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494564 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494565 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494565 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494566 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494567 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494567 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494568 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494568 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494569 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494570 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494570 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494571 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494571 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494572 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494573 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494573 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494574 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494575 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494575 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494576 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494576 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494577 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494578 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494578 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494579 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494579 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494580 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494581 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494581 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494582 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494582 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494583 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494584 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494584 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494585 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494585 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494586 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494587 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494589 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494589 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494590 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494590 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494591 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494592 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494593 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.494593 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494594 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494594 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494595 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494596 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494596 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494597 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.494597 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494598 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494599 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494599 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494600 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.494600 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.494601 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494602 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494602 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494603 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494604 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494604 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494605 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494605 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494606 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494640 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494641 10109           Options.merge_operator: None
2025/06/30-11:26:13.494642 10109        Options.compaction_filter: None
2025/06/30-11:26:13.494642 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494643 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494643 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494644 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494649 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494653 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494654 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494655 10109          Options.compression: LZ4
2025/06/30-11:26:13.494656 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494656 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494657 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494657 10109             Options.num_levels: 7
2025/06/30-11:26:13.494658 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494658 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494659 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494659 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494660 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494661 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494661 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494662 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494663 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494663 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494664 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494664 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494665 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494665 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494666 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494666 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494667 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494667 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494668 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494668 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494669 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494669 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494670 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494671 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494671 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494672 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494672 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494673 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494673 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494674 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494674 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494675 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494675 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494676 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494677 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494677 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494678 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494679 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494679 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494680 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494681 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494681 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494682 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494682 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494683 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494683 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494684 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494685 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494685 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494686 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494686 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494687 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494687 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494688 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494689 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494689 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494690 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494690 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494691 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494691 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.494692 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494692 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494693 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494693 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494694 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494694 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494695 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.494696 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494696 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494697 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494697 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494698 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.494698 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.494699 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494699 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494700 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494700 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494701 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494701 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494702 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494703 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494703 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.494740 10109               Options.comparator: leveldb.BytewiseComparator
2025/06/30-11:26:13.494742 10109           Options.merge_operator: None
2025/06/30-11:26:13.494742 10109        Options.compaction_filter: None
2025/06/30-11:26:13.494743 10109        Options.compaction_filter_factory: None
2025/06/30-11:26:13.494743 10109  Options.sst_partitioner_factory: None
2025/06/30-11:26:13.494744 10109         Options.memtable_factory: SkipListFactory
2025/06/30-11:26:13.494744 10109            Options.table_factory: BlockBasedTable
2025/06/30-11:26:13.494751 10109            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8fa00d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8fa190d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/30-11:26:13.494752 10109        Options.write_buffer_size: 10485760
2025/06/30-11:26:13.494753 10109  Options.max_write_buffer_number: 2
2025/06/30-11:26:13.494753 10109          Options.compression: LZ4
2025/06/30-11:26:13.494754 10109                  Options.bottommost_compression: Disabled
2025/06/30-11:26:13.494754 10109       Options.prefix_extractor: nullptr
2025/06/30-11:26:13.494790 10109   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/30-11:26:13.494795 10109             Options.num_levels: 7
2025/06/30-11:26:13.494796 10109        Options.min_write_buffer_number_to_merge: 1
2025/06/30-11:26:13.494796 10109     Options.max_write_buffer_number_to_maintain: 0
2025/06/30-11:26:13.494797 10109     Options.max_write_buffer_size_to_maintain: 0
2025/06/30-11:26:13.494797 10109            Options.bottommost_compression_opts.window_bits: -14
2025/06/30-11:26:13.494798 10109                  Options.bottommost_compression_opts.level: 32767
2025/06/30-11:26:13.494798 10109               Options.bottommost_compression_opts.strategy: 0
2025/06/30-11:26:13.494799 10109         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494799 10109         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494800 10109         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494801 10109                  Options.bottommost_compression_opts.enabled: false
2025/06/30-11:26:13.494801 10109         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494802 10109         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494802 10109            Options.compression_opts.window_bits: -14
2025/06/30-11:26:13.494803 10109                  Options.compression_opts.level: 32767
2025/06/30-11:26:13.494803 10109               Options.compression_opts.strategy: 0
2025/06/30-11:26:13.494804 10109         Options.compression_opts.max_dict_bytes: 0
2025/06/30-11:26:13.494804 10109         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/30-11:26:13.494805 10109         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/30-11:26:13.494805 10109         Options.compression_opts.parallel_threads: 1
2025/06/30-11:26:13.494806 10109                  Options.compression_opts.enabled: false
2025/06/30-11:26:13.494808 10109         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/30-11:26:13.494813 10109      Options.level0_file_num_compaction_trigger: 4
2025/06/30-11:26:13.494813 10109          Options.level0_slowdown_writes_trigger: 20
2025/06/30-11:26:13.494815 10109              Options.level0_stop_writes_trigger: 36
2025/06/30-11:26:13.494816 10109                   Options.target_file_size_base: 67108864
2025/06/30-11:26:13.494816 10109             Options.target_file_size_multiplier: 1
2025/06/30-11:26:13.494817 10109                Options.max_bytes_for_level_base: 268435456
2025/06/30-11:26:13.494817 10109 Options.level_compaction_dynamic_level_bytes: 1
2025/06/30-11:26:13.494818 10109          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/30-11:26:13.494819 10109 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/30-11:26:13.494819 10109 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/30-11:26:13.494820 10109 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/30-11:26:13.494821 10109 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/30-11:26:13.494821 10109 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/30-11:26:13.494822 10109 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/30-11:26:13.494822 10109 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/30-11:26:13.494823 10109       Options.max_sequential_skip_in_iterations: 8
2025/06/30-11:26:13.494823 10109                    Options.max_compaction_bytes: 1677721600
2025/06/30-11:26:13.494824 10109                        Options.arena_block_size: 1048576
2025/06/30-11:26:13.494824 10109   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/30-11:26:13.494825 10109   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/30-11:26:13.494825 10109                Options.disable_auto_compactions: 0
2025/06/30-11:26:13.494826 10109                        Options.compaction_style: kCompactionStyleLevel
2025/06/30-11:26:13.494827 10109                          Options.compaction_pri: kMinOverlappingRatio
2025/06/30-11:26:13.494827 10109 Options.compaction_options_universal.size_ratio: 1
2025/06/30-11:26:13.494828 10109 Options.compaction_options_universal.min_merge_width: 2
2025/06/30-11:26:13.494828 10109 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/30-11:26:13.494829 10109 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/30-11:26:13.494830 10109 Options.compaction_options_universal.compression_size_percent: -1
2025/06/30-11:26:13.494830 10109 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/30-11:26:13.494831 10109 Options.compaction_options_universal.max_read_amp: -1
2025/06/30-11:26:13.494831 10109 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/30-11:26:13.494832 10109 Options.compaction_options_fifo.allow_compaction: 0
2025/06/30-11:26:13.494833 10109                   Options.table_properties_collectors: 
2025/06/30-11:26:13.494834 10109                   Options.inplace_update_support: 0
2025/06/30-11:26:13.494834 10109                 Options.inplace_update_num_locks: 10000
2025/06/30-11:26:13.494835 10109               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/30-11:26:13.494836 10109               Options.memtable_whole_key_filtering: 0
2025/06/30-11:26:13.494836 10109   Options.memtable_huge_page_size: 0
2025/06/30-11:26:13.494837 10109                           Options.bloom_locality: 0
2025/06/30-11:26:13.494838 10109                    Options.max_successive_merges: 0
2025/06/30-11:26:13.494838 10109             Options.strict_max_successive_merges: 0
2025/06/30-11:26:13.494839 10109                Options.optimize_filters_for_hits: 0
2025/06/30-11:26:13.494839 10109                Options.paranoid_file_checks: 0
2025/06/30-11:26:13.494840 10109                Options.force_consistency_checks: 1
2025/06/30-11:26:13.494840 10109                Options.report_bg_io_stats: 0
2025/06/30-11:26:13.494841 10109                               Options.ttl: 2592000
2025/06/30-11:26:13.494852 10109          Options.periodic_compaction_seconds: 0
2025/06/30-11:26:13.494853 10109                        Options.default_temperature: kUnknown
2025/06/30-11:26:13.494853 10109  Options.preclude_last_level_data_seconds: 0
2025/06/30-11:26:13.494854 10109    Options.preserve_internal_time_seconds: 0
2025/06/30-11:26:13.494854 10109                       Options.enable_blob_files: false
2025/06/30-11:26:13.494855 10109                           Options.min_blob_size: 0
2025/06/30-11:26:13.494855 10109                          Options.blob_file_size: 268435456
2025/06/30-11:26:13.494856 10109                   Options.blob_compression_type: NoCompression
2025/06/30-11:26:13.494856 10109          Options.enable_blob_garbage_collection: false
2025/06/30-11:26:13.494857 10109      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/30-11:26:13.494858 10109 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/30-11:26:13.494858 10109          Options.blob_compaction_readahead_size: 0
2025/06/30-11:26:13.494859 10109                Options.blob_file_starting_level: 0
2025/06/30-11:26:13.494860 10109         Options.experimental_mempurge_threshold: 0.000000
2025/06/30-11:26:13.494861 10109            Options.memtable_max_range_deletions: 0
2025/06/30-11:26:13.548896 10109 DB pointer 0xffff8fbe4c00
