from fastapi import HTTPException
from app.db.database import get_action_db
from bson import ObjectId
import pandas as pd
from fastapi.responses import FileResponse
import tempfile, os
from docx import Document  
import json
from datetime import datetime, date
from docx.shared import Inches
import re
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
from rapidfuzz import fuzz
from collections import defaultdict

class ActionItemService:
    """Service for managing regulatory document action items."""
    
    def __init__(self):
        """Initialize the service with database collections."""
        try:
            db = get_action_db(database_name="rbi")
            self.collection = db["rbi_regulations"]
            self.user_collection = db["user_rbi_regulations"]
        except Exception as e:
            print(f"Failed to initialize ActionItemService: {e}")
            raise

    def search_document(self, query, threshold=70):
        """
        Perform fuzzy search for documents by title or document number,
        filtered to only include documents of type 'master_direction'.

        Args:
            query (str): Search string to match against document titles or numbers
            threshold (int): Minimum similarity score (0-100) for a match

        Returns:
            List[Dict]: Matching documents with minimal information
        """
        # Fetch only 'master_direction' documents (with required fields)
        all_docs = list(self.collection.find(
            {"document_type": "master_direction"},
            {"_id": 1, "document_title": 1, "document_number": 1}
        ))

        fuzzy_matches = []
        for doc in all_docs:
            title = doc.get("document_title", "")
            number = doc.get("document_number", "")

            # Calculate fuzzy similarity scores
            title_score = fuzz.partial_ratio(query.lower(), title.lower())
            number_score = fuzz.partial_ratio(query.lower(), number.lower())

            # If either score passes the threshold, include it
            if max(title_score, number_score) >= threshold:
                fuzzy_matches.append({
                    "_id": str(doc["_id"]),
                    "document_title": title,
                    "document_number": number
                })

        return fuzzy_matches


    def get_document(self, doc_id, username: str = None):
        """
        Get a document by ID, with optional username filter.
        
        Args:
            doc_id: Document ID to retrieve
            username: Optional username to check user-specific documents first
            
        Returns:
            Document data including obligations/actionables if found
        """
        # Define the fields to project - including all necessary fields
        projection = {
            "_id": 0,
            "document_number": 1,
            "document_title": 1,
            "obligations": 1,  # Include obligations
            "actionables": 1,   # Include actionables
            "s3_url": 1,        # Required field
            "changed_date": 1   # Include changed_date for user documents

        }
        
        # Check user-specific collection first if username provided
        if username:
            result = self.user_collection.find_one(
                {"document_number": doc_id, "username": username},
                projection
            )
            if result:
                return result

        # Fall back to main collection if not found in user collection
        result = self.collection.find_one(
            {"document_number": doc_id},
            projection
        )

        if not result:
            raise HTTPException(status_code=404, detail="Document not found")

        return result

    def get_all_action_items(self, username: str = None):
        """
        Get all documents associated with a username or all documents if username is None
        
        Args:
            username: Optional username to filter documents
            
        Returns:
            List of documents matching the criteria
        """
        source = self.user_collection if username else self.collection
        query = {"username": username} if username else {}
        
        # Projection to include only specific fields
        projection = {
            "_id": 1,
            "document_number": 1,
            "document_title": 1, 
            "s3_url": 1,
            "changed_date": 1
        }
        
        results = list(source.find(query, projection))
        
        # Convert ObjectId to string for JSON serialization
        for doc in results:
            if '_id' in doc:
                doc['_id'] = str(doc['_id'])
                
        return results

    def edit_action_item(self, username: str, updated_actionables: dict, changed_date: datetime):
        """
        Edit or create a user-specific document with action items.
        
        Args:
            username: Username associated with the document
            updated_actionables: Complete document data to store
            changed_date: Date and time when the document was changed
            
        Returns:
            Status and document ID of the updated document
        """
        # Ensure document_number exists in the payload
        document_number = updated_actionables.get("document_number")
        if not document_number:
            raise HTTPException(status_code=400, detail="document_number is required")
            
        # Ensure s3_url is present
        if "s3_url" not in updated_actionables:
            raise HTTPException(status_code=400, detail="s3_url is required")

        # Ensure username is in the document
        updated_actionables["username"] = username
        updated_actionables["changed_date"] = changed_date

        # Query by username and document_number
        query = {"username": username, "document_number": document_number}

        # Check if a document already exists
        existing_doc = self.user_collection.find_one(query)

        if existing_doc:
            # Handle the _id field correctly for existing documents
            if '_id' in existing_doc:
                # If existing_doc has an _id of ObjectId type, use it directly
                if isinstance(existing_doc["_id"], ObjectId):
                    updated_actionables["_id"] = existing_doc["_id"]
                # If _id is a string (maybe from an earlier conversion), convert it back to ObjectId
                elif isinstance(existing_doc["_id"], str):
                    updated_actionables["_id"] = ObjectId(existing_doc["_id"])
        else:
            # For new documents, remove _id if present in updated_actionables to let MongoDB generate it
            if '_id' in updated_actionables:
                del updated_actionables['_id']

        # Perform upsert (update or insert)
        result = self.user_collection.replace_one(query, updated_actionables, upsert=True)
        
        # Return the result with the document ID (either existing or new)
        document_id = str(updated_actionables.get("_id", ""))

        if not document_id and result.upserted_id:
            document_id = str(result.upserted_id)
            
        return {"status": "success", "document_id": document_id}

    def generate_word(self, doc_id: str, username: str = None):
        """
        Generate a Word document with action items formatted hierarchically.

        Args:
            doc_id: Document ID to generate report for
            username: Optional username for authorization

        Returns:
            FileResponse with the generated Word document
        """
        doc = self.get_document(doc_id, username)
        title = doc.get("document_title", "Untitled Document")

        # Prefer actionables; fallback to obligations if present
        actionables = doc.get("actionables", [])
        if not actionables and "obligations" in doc:
            actionables = doc.get("obligations", [])


        if not actionables:
            raise HTTPException(status_code=404, detail="No action items found")

        document = Document()
        document.add_heading(f"Action Items: {title}", level=1)

        # Add document metadata
        document.add_paragraph(f"Document ID: {doc_id}")
        document.add_paragraph(f"Number of Action Items: {len(actionables)}")
        document.add_paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Add changed_date if available
        changed_date = doc.get("changed_date")
        if changed_date:
            if isinstance(changed_date, datetime):
                formatted_date = changed_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_date = str(changed_date)
            document.add_paragraph(f"Last Modified: {formatted_date}")

        document.add_heading("Action Items", level=2)

        table = document.add_table(rows=1, cols=3)
        table.style = 'Table Grid'

        # Set header row
        hdr_cells = table.rows[0].cells
        headers = ['Guidelines', 'Obligations', 'Action Items']
        for i, header in enumerate(headers):
            hdr_cells[i].text = header
            for run in hdr_cells[i].paragraphs[0].runs:
                run.bold = True

        # Group items by guideline → obligation
        grouped = defaultdict(lambda: defaultdict(list))
        for item in actionables:
            guideline = str(item.get("guideline", "")).strip()
            obligation = str(item.get("obligation", "")).strip()
            action_item = str(item.get("action_item", "")).strip()
            grouped[guideline][obligation].append(action_item)

        # Fill table rows
        for guideline, obligations in grouped.items():
            guideline_start_row = len(table.rows)
            
            for obligation, actions in obligations.items():
                obligation_start_row = len(table.rows)
                
                # Add rows for each action item
                for action in actions:
                    row = table.add_row().cells
                    row[2].text = action
                
                # Set obligation text on the first row of this obligation group
                table.cell(obligation_start_row, 1).text = obligation
                
                # Merge obligation cells if there are multiple action items
                if len(actions) > 1:
                    obligation_end_row = len(table.rows) - 1
                    table.cell(obligation_start_row, 1).merge(table.cell(obligation_end_row, 1))
            
            # Set guideline text on the first row of this guideline group
            table.cell(guideline_start_row, 0).text = guideline
            
            # Merge guideline cells if there are multiple obligations or action items
            guideline_end_row = len(table.rows) - 1
            if guideline_end_row > guideline_start_row:
                table.cell(guideline_start_row, 0).merge(table.cell(guideline_end_row, 0))

        # Set column widths
        for cell in table.columns[0].cells:
            cell.width = Inches(2.0)
        for cell in table.columns[1].cells:
            cell.width = Inches(2.5)
        for cell in table.columns[2].cells:
            cell.width = Inches(3.0)

        # Apply formatting
        for row in table.rows:
            for cell in row.cells:
                cell.vertical_alignment = 1  # center
                for paragraph in cell.paragraphs:
                    paragraph.alignment = 1
                    for run in paragraph.runs:
                        run.font.size = Inches(0.12)  # 12pt approximation

        document.add_page_break()

        # Save and return Word file

        temp_dir = tempfile.mkdtemp()
        safe_doc_id = re.sub(r'[\\/*?:"<>|]', '_', doc_id)
        docx_path = os.path.join(temp_dir, f"{safe_doc_id}_action_items.docx")
        document.save(docx_path)

        return FileResponse(
            docx_path,
            media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            filename=f"{safe_doc_id}_action_items.docx"

        )

    def generate_excel(self, doc_id: str, username: str = None):
        """
        Generate a beautifully formatted Excel document with action items

        Args:
            doc_id: Document ID to generate report for
            username: Optional username for authorization

        Returns:
            FileResponse with the generated Excel document
        """

        doc = self.get_document(doc_id, username)
        title = doc.get("document_title", "Untitled Document")

        actionables = doc.get("actionables", [])
        if not actionables and "obligations" in doc:
            actionables = doc.get("obligations", [])

        if not actionables:
            raise HTTPException(status_code=404, detail="No action items found")


        wb = Workbook()
        ws = wb.active
        ws.title = "Action Items"

        # Styles
        header_font = Font(name='Calibri', size=14, bold=True, color="FFFFFF")
        normal_font = Font(name='Calibri', size=11)
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        alt_row_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Column widths
        ws.column_dimensions['A'].width = 40
        ws.column_dimensions['B'].width = 60
        ws.column_dimensions['C'].width = 40

        # Title

        ws.merge_cells('A1:C1')
        title_cell = ws['A1']
        title_cell.value = f"Action Items: {title}"
        title_cell.font = Font(name='Calibri', size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')

        # Metadata

        ws['A2'] = "Document ID:"
        ws['B2'] = doc_id
        ws['A3'] = "Number of Action Items:"
        ws['B3'] = len(actionables)
        ws['A4'] = "Generated on:"
        ws['B4'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Add changed_date if available
        changed_date = doc.get("changed_date")
        if changed_date:
            ws['A5'] = "Last Modified:"
            if isinstance(changed_date, datetime):
                ws['B5'] = changed_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                ws['B5'] = str(changed_date)
            gap_row = 6
        else:
            gap_row = 5

        for cell in ws[f'A2:A{gap_row-1}']:
            if isinstance(cell, tuple):
                cell[0].font = Font(bold=True)
            else:
                cell.font = Font(bold=True)

        ws.append([])  # Add gap

        # Headers
        headers = ['Guidelines', 'Obligations', 'Action Items']
        row_num = gap_row + 1

        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = border

        # Grouping structure: guideline -> obligation -> list of action items
        guideline_structure = defaultdict(lambda: defaultdict(list))
        for item in actionables:
            guideline = str(item.get("guideline", "")).strip()
            obligation = str(item.get("obligation", "")).strip()
            action_item = str(item.get("action_item", "")).strip()
            guideline_structure[guideline][obligation].append(action_item)

        current_row = row_num
        for guideline, obligations in guideline_structure.items():
            guideline_start_row = current_row + 1
            
            for obligation, action_items in obligations.items():
                obligation_start_row = current_row + 1
                
                for action_item in action_items:
                    current_row += 1
                    # Action Item
                    ws.cell(row=current_row, column=3).value = action_item

                    # Styling for all columns
                    for col in range(1, 4):
                        cell = ws.cell(row=current_row, column=col)
                        cell.font = normal_font
                        cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                        cell.border = border
                        if (current_row - row_num) % 2 == 0:
                            cell.fill = alt_row_fill

                # Set obligation text and merge cells if needed
                ws.cell(row=obligation_start_row, column=2).value = obligation
                if len(action_items) > 1:
                    ws.merge_cells(start_row=obligation_start_row, start_column=2, end_row=current_row, end_column=2)
                ws.cell(row=obligation_start_row, column=2).alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

            # Set guideline text and merge cells if needed
            ws.cell(row=guideline_start_row, column=1).value = guideline
            if current_row > guideline_start_row:
                ws.merge_cells(start_row=guideline_start_row, start_column=1, end_row=current_row, end_column=1)
            ws.cell(row=guideline_start_row, column=1).alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # Save to file

        temp_dir = tempfile.mkdtemp()
        safe_doc_id = re.sub(r'[\\/*?:"<>|]', '_', doc_id)
        xlsx_path = os.path.join(temp_dir, f"{safe_doc_id}_action_items.xlsx")
        wb.save(xlsx_path)

        # Return file
        return FileResponse(
            xlsx_path,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheet',
            filename=f"{safe_doc_id}_action_items.xlsx"
        )

    def get_recent_documents(self, limit: int = 5):
        """
        Get the most recent documents sorted by date_of_issue from main collection only.
        
        Args:
            limit: Number of recent documents to return (default: 5)
            
        Returns:
            List of recent documents with id, title, and date
        """
        # Only use main collection, filter for master_direction documents with date_of_issue and bank applicability
        query = {
            "document_type": "master_direction",
            "date_of_issue": {"$exists": True, "$ne": None},
            "is_applicable_to_banks": True,
            "is_exclusive_to_co_operative_banks": False,
            "is_exclusive_to_nbfc": False,
            "is_withdrawn": False
        }
        
        # Project only the required fields
        projection = {
            "_id": 1,
            "document_title": 1,
            "date_of_issue": 1
        }
        
        # Sort by date_of_issue in descending order (most recent first) and limit results
        results = list(self.collection.find(query, projection).sort("date_of_issue", -1).limit(limit))
        
        # Format the response with clean field names
        formatted_results = []
        for doc in results:
            formatted_results.append({
                "id": str(doc["_id"]),
                "title": doc.get("document_title", ""),
                "date": doc.get("date_of_issue", "")
            })
        
        return formatted_results


# Create an instance to import
action_item_service = ActionItemService()