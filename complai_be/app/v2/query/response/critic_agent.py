import openai
from app.v2.query.utils.completion_client import completion_client

class CriticAgent:
    def __init__(self, model: str):
        self.model = model
    
    def run(self, query: str, context: str, answer: str) -> str:
        prompt = (
            f"Review the answer below for completeness and correctness with respect to the given context.\n"
            f"Question: {query}\nContext: {context}\nAnswer: {answer}\n"
            "Respond with 'OK' if the answer is fully correct; otherwise, list any issues."
        )
        messages = [{"role": "user", "content": prompt}]
        response, _, _ = completion_client.completion(messages=messages, model=self.model)
        return response.strip()
