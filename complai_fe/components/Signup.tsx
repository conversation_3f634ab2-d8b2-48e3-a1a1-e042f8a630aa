// //Signup.tsx
// "use client"

// import { useState } from "react"
// import { useRouter } from "next/navigation"
// import { useAuth } from "@/contexts/AuthContext"
// import { SIGNUP_ENDPOINT } from "@/app/utils/Api"
// import { cn } from "@/lib/utils"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent } from "@/components/ui/card"
// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"

// export default function SignUp({ onSuccess }: { onSuccess: () => void }) {
//   const [username, setUsername] = useState("")
//   const [email, setEmail] = useState("")
//   const [password, setPassword] = useState("")
//   const [error, setError] = useState("")
//   const router = useRouter()
//   const { login } = useAuth()

//   const handleSignUp = async (e: React.FormEvent) => {
//     e.preventDefault()
//     setError("") // Clear any previous error

//     try {
//       const response = await fetch(SIGNUP_ENDPOINT, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ username, email, password }),
//       })

//       if (response.ok) {
//         const data = await response.json()
//         const userProfile = {
//           username: data.username,
//           email: data.email,
//         }
//         login(userProfile) // Store user in context & cookie
//         localStorage.setItem("token", data.token) // Store token in localStorage
//         onSuccess()
//         router.push("/")
//       } else {
//         const errorData = await response.json()
//         setError(errorData.detail || "Error signing up")
//       }
//     } catch (err) {
//       console.error("Error during sign-up:", err)
//       setError("An error occurred. Please try again.")
//     }
//   }

//   return (
//     <div className="flex flex-col gap-6">
//       <Card className="overflow-hidden">
//         <CardContent className="grid p-0 md:grid-cols-2">
//           <form onSubmit={handleSignUp} className="p-6 md:p-8">
//             <div className="flex flex-col gap-6">
//               <div className="flex flex-col items-center text-center">
//                 <h1 className="text-2xl font-bold">Create your account</h1>
//                 <p className="text-balance text-muted-foreground">
//                   Join Selkea AI and get started today
//                 </p>
//               </div>
//               {error && <p className="text-red-500 text-center">{error}</p>}
//               <div className="grid gap-2">
//                 <Label htmlFor="username">Username</Label>
//                 <Input
//                   id="username"
//                   type="text"
//                   placeholder="Enter your username"
//                   value={username}
//                   onChange={(e) => setUsername(e.target.value)}
//                   required
//                 />
//               </div>
//               <div className="grid gap-2">
//                 <Label htmlFor="email">Email</Label>
//                 <Input
//                   id="email"
//                   type="email"
//                   placeholder="<EMAIL>"
//                   value={email}
//                   onChange={(e) => setEmail(e.target.value)}
//                   required
//                 />
//               </div>
//               <div className="grid gap-2">
//                 <Label htmlFor="password">Password</Label>
//                 <Input
//                   id="password"
//                   type="password"
//                   placeholder="Create a strong password"
//                   value={password}
//                   onChange={(e) => setPassword(e.target.value)}
//                   required
//                 />
//               </div>
//               <Button type="submit" className="w-full">
//                 Sign Up
//               </Button>
//               <div className="text-center text-sm">
//                 Already have an account?{" "}
//                 <a
//                   href="#"
//                   className="underline underline-offset-4 cursor-pointer"
//                   onClick={() => router.push("/signin")}
//                 >
//                   Sign in
//                 </a>
//               </div>
//             </div>
//           </form>
//           <div className="relative hidden bg-muted md:block">
//             <img
//               src="/logo2.png"
//               alt="Image"
//               className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
//             />
//           </div>
//         </CardContent>
//       </Card>
//       <div className="text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
//         By signing up, you agree to our{" "}
//         <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
//       </div>
//     </div>
//   )
// }
"use client"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { SIGNUP_ENDPOINT } from "@/app/utils/Api"
import authframe from "@/components/assets/icons/auth-frame.png" // Make sure the image path matches your actual file

export default function SignUp({ onSuccess }: { onSuccess: () => void }) {
  const [username, setUsername] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()
  const { login } = useAuth()

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }

    try {
      const response = await fetch(SIGNUP_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, email, password }),
      })

      if (response.ok) {
        const data = await response.json()
        const userProfile = {
          username: data.username,
          email: data.email,
        }
        login(userProfile)
        localStorage.setItem("token", data.token)
        onSuccess()
        router.push("/")
      } else {
        const errorData = await response.json()
        setError(errorData.detail || "Error signing up")
      }
    } catch (err) {
      console.error("Error during sign-up:", err)
      setError("An error occurred. Please try again.")
    }
  }

  return (
    <div className="relative w-screen h-screen overflow-hidden">
      {/* Background Image */}
      <img 
        src={authframe.src}
        alt="Background"
        className="absolute inset-0 w-full h-full object-cover z-0"
      />

      {/* Overlay Content */}
      <div className="absolute inset-0 flex flex-col justify-center px-6 md:px-[103px] z-10">
        <div className="w-full max-w-[441px]">
          {/* Heading */}
            <h1 className="text-[#FF6B1C] text-[40px] leading-[48px] font-bold mb-1">Complai</h1>
            <h2 className="text-[#494949] text-[32px] leading-[40px] font-semibold mb-10">
                Create Account
            </h2>

          {/* Form */}
          <form onSubmit={handleSignUp} className="flex flex-col gap-3">
            {error && (
              <div className="text-red-500 text-sm text-center">
                {error}
              </div>
            )}
            <div className="flex flex-col gap-1">
                <label htmlFor="email" className="text-sm text-[#494949] pl-[4px]">
                    Username
                </label>
                <input
                    type="text"
                    placeholder="Username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
                    />
            </div>
            <div className="flex flex-col gap-1">
                <label htmlFor="email" className="text-sm text-[#494949] pl-[4px]">
                    Email
                </label>
                <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
                />
            </div>
            <div className="flex flex-col gap-1">
                <label htmlFor="email" className="text-sm text-[#494949] pl-[4px]">
                Password
                </label>
                <input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
            />
            </div>
            <div className="flex flex-col gap-1">
                <label htmlFor="email" className="text-sm text-[#494949] pl-[4px]">
                confirmPassword
                </label>
                <input
              type="password"
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="h-[63px] w-full px-[24px] py-[20px] text-[#494949] text-base font-medium rounded-[6px] border border-[#A5998F] bg-[rgba(255,255,255,0.10)] focus:outline-none focus:border-[#FF6B1C]"
            />
            </div>
            <button
              type="submit"
              className="h-[48px] bg-[#FF6B35] text-white font-bold rounded-md hover:bg-[#e65928] transition-colors"
            >
              Sign Up
            </button>

            <div className="text-sm text-[#3B3B3B] text-center">
              Already have an account?{" "}
              <a
                href="/signin"
                className="text-[#FF6B35] font-semibold hover:underline"
                onClick={(e) => {
                  e.preventDefault()
                  router.push("/signin")
                }}
              >
                Sign in
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
