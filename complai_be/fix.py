import boto3

# Initialize S3 client
s3 = boto3.client('s3')
S3_BUCKET_NAME = "airflowdump"
aws_access_key_id = "********************"
aws_secret_access_key = "2FfZsDlPIYjAz8EEMlxOrX54hYMN90qB1Kk0pDJ+"
s3 = boto3.client(
        "s3",
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )

# Replace with your S3 bucket name
bucket_name = 'airflowdump'

def update_s3_headers():
    # List all objects in the bucket
    paginator = s3.get_paginator('list_objects_v2')

    for page in paginator.paginate(Bucket=bucket_name):
        if 'Contents' not in page:
            continue

        for obj in page['Contents']:
            key = obj['Key']

            # Only process PDF files
            if not key.endswith('.pdf'):
                continue

            # Get metadata of the object
            head_response = s3.head_object(Bucket=bucket_name, Key=key)
            current_metadata = head_response.get('Metadata', {})  # Preserve existing metadata
            current_content_type = head_response.get('ContentType', '')
            current_content_disposition = head_response.get('ContentDisposition', '')

            # Check if updates are needed
            update_needed = False
            new_content_type = "application/pdf"
            new_content_disposition = "inline"

            if current_content_type != new_content_type:
                update_needed = True

            if not current_content_disposition or current_content_disposition == "attachment":
                update_needed = True

            if update_needed:
                print(f"Updating headers for: {key}")

                # Copy object to itself with updated metadata
                s3.copy_object(
                    Bucket=bucket_name,
                    CopySource={'Bucket': bucket_name, 'Key': key},
                    Key=key,
                    Metadata=current_metadata,  # Preserve existing metadata
                    ContentType=new_content_type,
                    ContentDisposition=new_content_disposition,
                    MetadataDirective="REPLACE"
                )
            else:
                print(f"Skipping (already correct): {key}")

    print("\nProcess completed. All applicable files have been updated.")

# Run the function
update_s3_headers()