// // sseQueryService.ts
// import { QUERY_SSE_ENDPOINT } from "@/app/utils/Api";

// export interface ProcessingStep {
//   agent: string;
//   description: string;
// }

// export interface PipelineStartedEvent {
//   event: "pipeline_started";
//   query: string;
//   session_id: string;
// }

// export interface PlanCreatedEvent {
//   event: "plan_created";
//   plan: {
//     steps: ProcessingStep[];
//   };
// }

// export interface NodeStartedEvent {
//   event: "node_started";
//   node: string;
// }

// export interface NodeCompletedEvent {
//   event: "node_completed";
//   node: string;
// }

// export interface NodeOutputEvent {
//   event: "node_output";
//   node: string;
//   status: string;
//   state: string[];
// }

// export interface NodeCompletedEvent {
//   event: "node_completed";
//   node: string;
// }

// export interface ExecutionCompletedEvent {
//   event: "execution_completed";
//   answer: {
//     query: string;
//     response: string;
//     metadata: any[];
//   };
//   sources: any[];
//   suggestions: string;
// }

// export type SSEEvent =
//   | PipelineStartedEvent
//   | PlanCreatedEvent
//   | NodeStartedEvent
//   | NodeCompletedEvent
//   | NodeOutputEvent
//   | ExecutionCompletedEvent;

// export interface SSEEventHandlers {
//   onPipelineStarted?: (event: PipelineStartedEvent) => void;
//   onPlanCreated?: (event: PlanCreatedEvent) => void;
//   onNodeStarted?: (event: NodeStartedEvent) => void;
//   onNodeCompleted?: (event: NodeCompletedEvent) => void;
//   onNodeOutput?: (event: NodeOutputEvent) => void;
//   onExecutionCompleted?: (event: ExecutionCompletedEvent) => void;
//   onError?: (error: Error) => void;
// }

// /**
//  * Fetches query response using Server-Sent Events (SSE) for real-time updates
//  * @param message The query message
//  * @param session_id The session ID
//  * @param username The username
//  * @param eventHandlers Object with callback functions for different SSE events
//  * @returns A function to abort the SSE connection
//  */
// export function fetchQuerySSE(
//   message: string,
//   session_id: string,
//   username: string,
//   eventHandlers: SSEEventHandlers,
// ): () => void {
//   const controller = new AbortController();
//   const { signal } = controller;

//   const requestBody = {
//     query: message,
//     session_id: session_id,
//     username: username,
//   };

//   (async () => {
//     try {
//       const response = await fetch(QUERY_SSE_ENDPOINT, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Accept: "text/event-stream",
//         },
//         body: JSON.stringify(requestBody),
//         signal,
//       });

//       if (!response.ok) {
//         throw new Error(`SSE request failed with status ${response.status}`);
//       }

//       // For browser compatibility, manually parse the SSE stream
//       const reader = response.body?.getReader();
//       if (!reader) {
//         throw new Error("Response body cannot be read as a stream");
//       }

//       const decoder = new TextDecoder();
//       let buffer = "";

//       while (true) {
//         const { done, value } = await reader.read();
//         if (done) break;

//         buffer += decoder.decode(value, { stream: true });

//         // Process complete events in the buffer
//         const lines = buffer.split("\n");
//         buffer = lines.pop() || ""; // Keep the last incomplete line in the buffer

//         for (const line of lines) {
//           if (line.trim() === "") continue;
//           if (!line.startsWith("data: ")) continue;

//           const eventData = line.substring(6); // Remove "data: " prefix
//           try {
//             const parsedEvent = JSON.parse(eventData) as SSEEvent;

//             // Call the appropriate event handler based on event type
//             switch (parsedEvent.event) {
//               case "pipeline_started":
//                 eventHandlers.onPipelineStarted?.(parsedEvent);
//                 break;
//               case "plan_created":
//                 eventHandlers.onPlanCreated?.(parsedEvent);
//                 break;
//               case "node_started":
//                 eventHandlers.onNodeStarted?.(parsedEvent);
//                 break;
//               case "node_completed":
//                 eventHandlers.onNodeCompleted?.(parsedEvent);
//                 break;
//               case "node_output":
//                 eventHandlers.onNodeOutput?.(parsedEvent);
//                 break;
//               case "execution_completed":
//                 eventHandlers.onExecutionCompleted?.(parsedEvent);
//                 break;
//             }
//           } catch (e) {
//             console.error("Error parsing SSE event:", e, eventData);
//           }
//         }
//       }
//     } catch (error: unknown) {
//       if (error instanceof Error && error.name !== "AbortError") {
//         console.error("Error in SSE stream:", error);
//         eventHandlers.onError?.(error);
//       } else if (!(error instanceof Error)) {
//         const convertedError = new Error(String(error));
//         console.error("Error in SSE stream:", convertedError);
//         eventHandlers.onError?.(convertedError);
//       }
//     }
//   })();

//   // Return abort function
//   return () => controller.abort();
// }


// sseQueryService.ts
import { QUERY_SSE_ENDPOINT } from "@/app/utils/Api";

export interface ExecutionCompletedEvent {
  event: "execution_completed";
  answer: {
    query: string;
    response: string;
    metadata: any[];
  };
  sources: any[];
  suggestions: string;
}

export interface SSEEventHandlers {
  onExecutionCompleted?: (event: ExecutionCompletedEvent) => void;
  onError?: (error: Error) => void;
}

/**
 * Fetches query response using Server-Sent Events (SSE) for real-time updates
 * @param message The query message
 * @param session_id The session ID
 * @param username The username
 * @param eventHandlers Object with callback functions for different SSE events
 * @returns A function to abort the SSE connection
 */
export function fetchQuerySSE(
  message: string,
  session_id: string,
  username: string,
  eventHandlers: SSEEventHandlers,
): () => void {
  const controller = new AbortController();
  const { signal } = controller;

  const requestBody = {
    query: message,
    session_id: session_id,
    username: username,
  };

  (async () => {
    try {
      const response = await fetch(QUERY_SSE_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
        },
        body: JSON.stringify(requestBody),
        signal,
      });

      if (!response.ok) {
        throw new Error(`SSE request failed with status ${response.status}`);
      }

      // For browser compatibility, manually parse the SSE stream
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Response body cannot be read as a stream");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // Process complete events in the buffer
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // Keep the last incomplete line in the buffer

        for (const line of lines) {
          if (line.trim() === "") continue;
          if (!line.startsWith("data: ")) continue;

          const eventData = line.substring(6); // Remove "data: " prefix
          try {
            const parsedEvent = JSON.parse(eventData);

            // Only handle execution_completed event
            if (parsedEvent.event === "execution_completed") {
              eventHandlers.onExecutionCompleted?.(parsedEvent as ExecutionCompletedEvent);
            }
            // Ignore all other events
          } catch (e) {
            console.error("Error parsing SSE event:", e, eventData);
          }
        }
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name !== "AbortError") {
        console.error("Error in SSE stream:", error);
        eventHandlers.onError?.(error);
      } else if (!(error instanceof Error)) {
        const convertedError = new Error(String(error));
        console.error("Error in SSE stream:", convertedError);
        eventHandlers.onError?.(convertedError);
      }
    }
  })();

  // Return abort function
  return () => controller.abort();
}