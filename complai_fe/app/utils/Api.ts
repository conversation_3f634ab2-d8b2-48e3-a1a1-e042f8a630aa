// const API_BASE_URL = "https://dev-api.complai-genie.online";
// const API_BASE_URL_NOTI = "https://api.complai-genie.online";
// Assuming this is in a file like src/api/endpoints.js or similar

// Read API_BASE_URL from environment variables
// const API_BASE_URL = "https://dev-api.complai-genie.online";
// const API_BASE_URL_NOTI = "https://api.complai-genie.online";

const API_BASE_URL = "http://localhost:8000";
const API_BASE_URL_NOTI = "http://localhost:8000";

export const QUERY_ENDPOINT = `${API_BASE_URL}/v2/query`;
export const QUERY_SSE_ENDPOINT = `${API_BASE_URL}/v2/query-sse`;
export const SESSION_ENDPOINT = `${API_BASE_URL}/chat/session`;
export const CREATE_SESSION_LIMIT = `${API_BASE_URL}/session/create`;
export const CREATE_SESSION_ENDPOINT = `${API_BASE_URL}/chat/create_session`;
export const UPDAET_SESSION_ENDPOINT = `${API_BASE_URL}/chat/update_session`;
export const SESSION_CHATS_ENDPOINT = `${API_BASE_URL}/chat/session_chats`;
export const SIGNIN_ENDPOINT = `${API_BASE_URL}/auth/signin`;
export const SIGNUP_ENDPOINT = `${API_BASE_URL}/auth/signup`;
export const DELETE_ENDPOINT = `${API_BASE_URL}/chat/delete_query`;
export const METADATA_ENDPOINT = `${API_BASE_URL}/chat/metadata_retrival`;
export const FEEDBACK_ENDPOINT = `${API_BASE_URL}/chat/feedback`;
export const NOTIFICATION_ENDPOINT = `${API_BASE_URL_NOTI}/notification/list_recent`;
export const LIMIT_ENDPOINT = `${API_BASE_URL}/query/limits`;
export const FEEDBACK_RETRIVAL_ENDPOINT = `${API_BASE_URL}/chat/feedback_retrival`;
export const ACTION_ITEMS_EDIT_ENDPOINT = `${API_BASE_URL}/action-items/edit-user-item`;
export const ACTION_ITEMS_DOWNLOAD_EXCEL_ENDPOINT = `${API_BASE_URL}/action-items/download/xlsx`;
export const ACTION_ITEMS_DOWNLOAD_WORD_ENDPOINT = `${API_BASE_URL}/action-items/download/docx`;
export const ACTION_ITEMS_ACTION_ENDPOINT = `${API_BASE_URL}/action-items/action-items`;
export const ACTION_ITEMS_ITEM_ENDPOINT = `${API_BASE_URL}/action-items/item`;
export const ACTION_ITEMS_SEARCH_ENDPOINT = `${API_BASE_URL}/action-items/search`;
export const ACTION_ITEMS_RECENT_DOCS_ENDPOINT = `${API_BASE_URL}/action-items/recent-docs`