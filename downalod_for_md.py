import os
import requests

# List of RBI Master Directions URLs
urls = [
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/137MDETPJune162025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/136MDFrameworkofincentivesforCurrencyDistribution.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MDRBIInterestRateonDepositsDirections2025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MDMARCH2025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/132MDCounterfeitNotesDetectionReportingandMonitoring01042025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/126RevisedMD_May082025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MD_RBIAssetReconstructionCompaniesDirections2024_UpdatedasonApril232025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/106MDNBFCS_May052025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/104MasterDirectiononInvestmentPortfolio142025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/83RBIMDPresentationandDisclosure142025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MDHFCupdatedason05052025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MDKYCAmendment_UpdatedasonJune122025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/50MasterDirections-MortgageGuarantee%28Updated+as+on+May+05%2C+2025%29.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/42+Primary+Dealers%28Updated+as+on+May+05%2C+2025%29.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/39MDCoreInvestmentCompanies%28Updated+as+on+May+05%2C+2025%29.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/31MDRiskManagmentandInterBankDealingsUpdatedasonApril162025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MDonDepositsandAccounts160425.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/MD+Import+of+Goods+and+Services_13June2025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.amazonaws.com/Master Directions/2025/11MDExportofGoodsandServices_UpdatedasonApril292025.pdf",
    "https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Master+Directions/2025/Master+Directions+-+Reserve+Bank+of+India+_Priority+Sector+Lending+_+Targets+and+Classification_+Directions_+2025.pdf"
]

# Create output folder
output_folder = "rbi_master_directions"
os.makedirs(output_folder, exist_ok=True)

# Download each file
for url in urls:
    try:
        filename = url.split("/")[-1].split("?")[0].replace("+", "_").replace("%28", "(").replace("%29", ")")
        filepath = os.path.join(output_folder, filename)
        response = requests.get(url)
        with open(filepath, "wb") as f:
            f.write(response.content)
        print(f"✅ Downloaded: {filename}")
    except Exception as e:
        print(f"❌ Failed to download from: {url} | Error: {e}")
