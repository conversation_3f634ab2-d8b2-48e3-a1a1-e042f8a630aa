
"""convert_pipeline_results.py

Usage:
    python convert_pipeline_results.py </path/to/real_pipeline_results.json> </output/summary.csv>
"""

import json
import pandas as pd
import sys
from pathlib import Path

def make_summary(input_json: Path, output_csv: Path):
    with open(input_json, 'r') as f:
        data = json.load(f)

    rows = []
    for r in data['results']:
        affected = r.get('affected_documents', {})
        rows.append({
            "notification_id": r['notification_id'],
            "title": r['title'],
            "category": r['llm_analysis']['category'],
            "processing_status": r['processing_status'],
            "needs_manual_review": affected.get('requires_manual_review') 
                                   or r.get('update_actions', {}).get('requires_manual_review', False),
            "has_new_document_link": affected.get('has_new_document_link', False),
            "new_document_url": affected.get('new_document_url', ""),
            "num_document_actions": len(affected.get('document_actions', [])),
        })

    df = pd.DataFrame(rows)
    df.to_csv(output_csv, index=False)
    print(f"✓ Summary written to {output_csv}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_pipeline_results.py <input_json> <output_csv>")
        sys.exit(1)
    make_summary(Path(sys.argv[1]), Path(sys.argv[2]))
